# Executive Summary Slide Components Design Specification

## Component Architecture

This document details the specific component designs and interaction patterns for the PowerPoint-like slide interface, complementing the main design specification.

## 1. Component Hierarchy

```
SlidePresentation/
├── SlideContainer.tsx              # Main container with overlay
├── SlideProvider.tsx              # Context provider for slide state
├── components/
│   ├── SlideHeader.tsx            # Header with title and metadata
│   ├── SlideContent.tsx           # Main content wrapper
│   ├── KpiMetricsPanel.tsx        # Left panel with large metrics
│   ├── ChartVisualizationPanel.tsx # Right panel with enhanced charts
│   ├── SlideNavigation.tsx        # Bottom navigation controls
│   ├── SlideIndicators.tsx        # Dot navigation indicators
│   ├── SlideTransition.tsx        # Transition wrapper component
│   └── SlideOverlay.tsx           # Background overlay for modal
├── hooks/
│   ├── useSlideNavigation.ts      # Navigation state and logic
│   ├── useSlideTransitions.ts     # Animation and transition logic
│   ├── useKeyboardControls.ts     # Keyboard event handling
│   ├── useSlideData.ts           # Data processing for slides
│   └── useSlideExport.ts         # PDF export functionality
└── types/
    ├── slide-types.ts            # TypeScript interfaces
    └── animation-types.ts        # Animation configuration types
```

## 2. Core Components Design

### SlideContainer Component

```typescript
interface SlideContainerProps {
  kpiData: KpiSummaryData[];
  currency: ValidCurrency;
  period: Period;
  initialSlide?: number;
  onClose: () => void;
  className?: string;
}
```

**Design Specifications:**
- **Layout**: Full-screen overlay with centered slide content
- **Background**: Semi-transparent backdrop (bg-black/50)
- **Container**: Max-width 1200px, centered with padding
- **Z-index**: 50 (above other content)
- **Escape Handling**: Click outside or ESC key to close

**Visual Structure:**
```
┌─────────────────────────────────────────────────────┐
│ Overlay Background (semi-transparent)              │
│  ┌───────────────────────────────────────────────┐  │
│  │ Slide Container (white/dark background)      │  │
│  │  ┌─────────────────────────────────────────┐  │  │
│  │  │ Slide Header                            │  │  │
│  │  ├─────────────────────────────────────────┤  │  │
│  │  │                                         │  │  │
│  │  │ Slide Content                           │  │  │
│  │  │                                         │  │  │
│  │  ├─────────────────────────────────────────┤  │  │
│  │  │ Slide Navigation                        │  │  │
│  │  └─────────────────────────────────────────┘  │  │
│  └───────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────┘
```

### SlideHeader Component

```typescript
interface SlideHeaderProps {
  kpiName: string;
  period: Period;
  currentSlide: number;
  totalSlides: number;
  onClose: () => void;
  brandContext?: string;
}
```

**Design Elements:**
- **Height**: 80px fixed
- **Layout**: Flex with space-between alignment
- **Close Button**: Top-right, 32px × 32px with X icon
- **Title Section**: Left-aligned with KPI name and context
- **Slide Counter**: Right-aligned with current/total format

**Typography:**
```css
.slide-header-title {
  font-size: 28px;
  font-weight: 700;
  color: hsl(var(--foreground));
  line-height: 1.2;
}

.slide-header-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  margin-top: 4px;
}

.slide-counter {
  font-size: 14px;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  background: hsl(var(--muted));
  padding: 4px 12px;
  border-radius: 12px;
}
```

### KpiMetricsPanel Component

```typescript
interface KpiMetricsPanelProps {
  kpiData: KpiSummaryData;
  currency: ValidCurrency;
  period: Period;
  className?: string;
}
```

**Layout Structure:**
```
┌─────────────────────────────┐
│ Primary Value               │
│ $2,450,000                  │ ← 72px, ultra-bold
│                             │
│ ┌─────────────────────────┐ │
│ │ Budget Comparison       │ │
│ │ ↗ +12.5% vs Budget     │ │ ← 24px, semibold
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │ YoY Comparison          │ │
│ │ ↗ +8.3% vs Last Year   │ │ ← 24px, semibold
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │ Additional Context      │ │
│ │ Period: Nov 2024        │ │ ← 16px, regular
│ │ Last Updated: 2h ago    │ │
│ └─────────────────────────┘ │
└─────────────────────────────┘
```

**Styling Specifications:**
```css
.metrics-panel {
  padding: 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
  justify-content: center;
}

.primary-value {
  font-size: 72px;
  font-weight: 800;
  line-height: 0.9;
  color: hsl(var(--foreground));
  margin-bottom: 8px;
}

.comparison-card {
  background: hsl(var(--muted) / 0.5);
  border: 1px solid hsl(var(--border));
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.trend-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.comparison-text {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.2;
}

.context-info {
  background: hsl(var(--muted) / 0.3);
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  color: hsl(var(--muted-foreground));
}
```

### ChartVisualizationPanel Component

```typescript
interface ChartVisualizationPanelProps {
  timeSeries: TimeSeriesItem[];
  kpiName: string;
  currency: ValidCurrency;
  period: Period;
  chartType?: 'bar' | 'line' | 'area';
  showControls?: boolean;
}
```

**Chart Container Design:**
```
┌─────────────────────────────────────┐
│ Chart Header                        │
│ ┌─────────────┐ ┌─────────────────┐ │
│ │ Chart Title │ │ View Controls   │ │
│ └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│                                     │
│                                     │
│        Enhanced Chart               │
│         (500px height)              │
│                                     │
│                                     │
├─────────────────────────────────────┤
│ Chart Footer                        │
│ Legend • Data Summary • Export      │
└─────────────────────────────────────┘
```

**Chart Configuration:**
```typescript
const enhancedChartConfig = {
  height: 500,
  margin: { top: 20, right: 30, bottom: 60, left: 80 },
  
  // Visual styling
  bar: {
    radius: [4, 4, 0, 0],
    gradient: {
      start: 'hsl(var(--chart-1))',
      end: 'hsl(var(--chart-1) / 0.6)'
    },
    hoverEffect: {
      scale: 1.05,
      brightness: 1.1
    }
  },
  
  // Grid and axes
  grid: {
    horizontal: true,
    vertical: false,
    color: 'hsl(var(--border))',
    strokeDasharray: '3 3'
  },
  
  axes: {
    x: {
      fontSize: 12,
      color: 'hsl(var(--muted-foreground))',
      tickRotation: -45
    },
    y: {
      fontSize: 12,
      color: 'hsl(var(--muted-foreground))',
      formatFunction: (value) => formatCurrency(value, currency)
    }
  },
  
  // Animations
  animations: {
    initial: { duration: 800, ease: 'easeOutCubic' },
    update: { duration: 400, ease: 'easeInOut' },
    hover: { duration: 150, ease: 'easeOut' }
  },
  
  // Tooltips
  tooltip: {
    background: 'hsl(var(--popover))',
    border: '1px solid hsl(var(--border))',
    borderRadius: '8px',
    padding: '12px',
    fontSize: '14px',
    shadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
  }
};
```

### SlideNavigation Component

```typescript
interface SlideNavigationProps {
  currentSlide: number;
  totalSlides: number;
  onPrevious: () => void;
  onNext: () => void;
  onSlideSelect: (index: number) => void;
  disabled?: boolean;
}
```

**Navigation Layout:**
```
┌─────────────────────────────────────────────────────┐
│  ┌──────────┐    ●●●○○○○○    ┌──────────┐  │
│  │ Previous │    Indicators    │   Next   │  │
│  │    ←     │                  │    →     │  │
│  └──────────┘                  └──────────┘  │
└─────────────────────────────────────────────────────┘
```

**Button Styling:**
```css
.nav-button {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-button:hover {
  background: hsl(var(--primary) / 0.9);
  transform: scale(1.05);
}

.nav-button:disabled {
  background: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  cursor: not-allowed;
  transform: none;
}

.slide-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid hsl(var(--muted-foreground));
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slide-indicator.active {
  background: hsl(var(--primary));
  border-color: hsl(var(--primary));
  transform: scale(1.2);
}

.slide-indicator:hover {
  border-color: hsl(var(--primary));
  transform: scale(1.1);
}
```

## 3. Interaction Patterns

### Slide Transitions

**Horizontal Slide Animation:**
```css
.slide-enter {
  transform: translateX(100%);
  opacity: 0;
}

.slide-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;
}

.slide-exit {
  transform: translateX(0);
  opacity: 1;
}

.slide-exit-active {
  transform: translateX(-100%);
  opacity: 0;
  transition: transform 300ms ease-in-out, opacity 300ms ease-in-out;
}
```

**Fade Transition (Accessibility):**
```css
@media (prefers-reduced-motion: reduce) {
  .slide-enter {
    transform: none;
    opacity: 0;
  }
  
  .slide-enter-active {
    transform: none;
    opacity: 1;
    transition: opacity 200ms ease-in-out;
  }
  
  .slide-exit-active {
    transform: none;
    opacity: 0;
    transition: opacity 200ms ease-in-out;
  }
}
```

### Touch/Swipe Gestures

**Swipe Detection:**
```typescript
interface SwipeConfig {
  threshold: 50; // Minimum distance for swipe
  velocity: 0.3; // Minimum velocity
  directional: true; // Only horizontal swipes
  preventScrollOnSwipe: true;
}

const swipeHandlers = {
  onSwipedLeft: () => nextSlide(),
  onSwipedRight: () => previousSlide(),
  onSwiping: (eventData) => {
    // Show swipe indicator
    setSwipeProgress(eventData.deltaX);
  }
};
```

### Keyboard Navigation

**Key Bindings:**
```typescript
const keyboardControls = {
  'ArrowLeft': () => previousSlide(),
  'ArrowRight': () => nextSlide(),
  'Home': () => goToSlide(0),
  'End': () => goToSlide(totalSlides - 1),
  'Escape': () => exitSlideMode(),
  'Space': () => nextSlide(),
  'Backspace': () => previousSlide(),
  // Number keys for direct navigation
  '1': () => goToSlide(0),
  '2': () => goToSlide(1),
  // ... up to 9
};
```

## 4. Responsive Behavior

### Breakpoint-Specific Layouts

**Desktop (≥1200px):**
```css
.slide-content {
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 32px;
  height: 720px;
}

.metrics-panel {
  padding: 32px;
}

.chart-panel {
  padding: 24px;
}
```

**Tablet (768px - 1199px):**
```css
.slide-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: auto;
  min-height: 600px;
}

.metrics-panel {
  padding: 24px;
  min-height: 200px;
}

.chart-panel {
  padding: 20px;
  flex: 1;
}

.primary-value {
  font-size: 56px;
}

.comparison-text {
  font-size: 20px;
}
```

**Mobile (≤767px):**
```css
.slide-container {
  margin: 16px;
  border-radius: 16px;
}

.slide-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: auto;
}

.metrics-panel {
  padding: 20px;
  text-align: center;
}

.chart-panel {
  padding: 16px;
}

.primary-value {
  font-size: 48px;
}

.comparison-text {
  font-size: 18px;
}

.nav-button {
  width: 56px;
  height: 56px;
}
```

## 5. State Management

### Slide Context Provider

```typescript
interface SlideContextValue {
  // State
  currentSlide: number;
  totalSlides: number;
  isTransitioning: boolean;
  slideData: KpiSummaryData[];
  
  // Navigation
  nextSlide: () => void;
  previousSlide: () => void;
  goToSlide: (index: number) => void;
  
  // Configuration
  transitionDuration: number;
  autoAdvance: boolean;
  showIndicators: boolean;
  
  // Export
  exportToPDF: () => Promise<void>;
  exportCurrentSlide: () => Promise<void>;
}
```

### URL State Synchronization

```typescript
const useSlideURL = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const updateURL = (slideIndex: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('view', 'slides');
    params.set('slide', String(slideIndex + 1));
    router.push(`?${params.toString()}`, { shallow: true });
  };
  
  const getInitialSlide = () => {
    const slideParam = searchParams.get('slide');
    return slideParam ? Math.max(0, parseInt(slideParam) - 1) : 0;
  };
  
  return { updateURL, getInitialSlide };
};
```

## 6. Animation Specifications

### Chart Animations

**Initial Load Animation:**
```typescript
const chartAnimations = {
  bars: {
    initial: { scaleY: 0, transformOrigin: 'bottom' },
    animate: { scaleY: 1 },
    transition: { duration: 0.8, ease: 'easeOutCubic', delay: 0.1 }
  },
  
  axes: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.4, delay: 0.6 }
  },
  
  labels: {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.3, delay: 0.8 }
  }
};
```

**Data Update Animation:**
```typescript
const updateAnimations = {
  bars: {
    transition: { duration: 0.4, ease: 'easeInOut' }
  },
  
  values: {
    initial: { scale: 1 },
    animate: { scale: [1, 1.1, 1] },
    transition: { duration: 0.3 }
  }
};
```

### Slide Entrance Effects

**Staggered Content Animation:**
```typescript
const slideEntranceStagger = {
  header: { delay: 0 },
  metrics: { delay: 0.1 },
  chart: { delay: 0.2 },
  navigation: { delay: 0.3 }
};
```

This component specification provides detailed implementation guidance for creating the PowerPoint-like slide interface, ensuring consistency with the overall design vision while providing specific technical details for development.