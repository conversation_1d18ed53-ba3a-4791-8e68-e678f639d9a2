# Executive Summary KPI Slide Interface Design Specification

## Overview

This specification outlines the design for transforming the current compact KPI cards into a PowerPoint-like slide presentation interface. The design maintains the existing data structure while creating a presentation-ready format suitable for executive viewing and PDF export.

## Current State Analysis

### Existing KPI Card Structure
- **Dimensions**: 200px height, responsive grid (1-2-3-5 columns)
- **Data Elements**: 
  - Primary value (MTD/QTD/YTD)
  - Budget comparison (vs budget %)
  - Year-over-year comparison (vs last year %)
  - Mini time series chart (80px height)
- **Visual Design**: Slate gradients, green/red trend indicators, compact layout
- **Chart Type**: Mini bar charts using Recharts with adaptive grouping (daily/weekly/monthly)

## Design Requirements

### 1. Slide Layout Structure

#### Primary Slide Container
```
Dimensions: Full viewport (responsive)
- Desktop: 1200px × 800px (16:10 aspect ratio)
- Tablet: 1024px × 768px (4:3 aspect ratio)  
- Mobile: 390px × 844px (portrait)

Layout Structure:
┌─────────────────────────────────────┐
│ Slide Header (80px)                 │
├─────────────────────────────────────┤
│                                     │
│ KPI Content Area (720px)            │
│                                     │
├─────────────────────────────────────┤
│ Slide Navigation (80px)             │
└─────────────────────────────────────┘
```

#### Slide Header
- **Height**: 80px
- **Content**: 
  - Slide title (KPI name)
  - Period indicator (MTD/QTD/YTD)
  - Brand/filter context
  - Slide counter (e.g., "3 of 8")
- **Typography**: 
  - Title: 28px font-weight-700
  - Subtitle: 16px font-weight-500 text-muted-foreground

#### KPI Content Area
- **Height**: 720px
- **Layout**: Split into primary metrics (left) and chart visualization (right)
- **Proportions**: 40% metrics, 60% chart

### 2. Enhanced KPI Card Components

#### Primary Metrics Panel (Left 40%)
```
┌─────────────────────────┐
│ Primary Value           │
│ $2.4M                   │ ← 72px font-size, font-weight-800
│                         │
│ Budget Comparison       │
│ ↗ +12.5% vs Budget     │ ← 24px font-size
│                         │
│ YoY Comparison          │
│ ↗ +8.3% vs Last Year   │ ← 24px font-size
│                         │
│ Additional Context      │
│ Period: Nov 2024        │ ← 16px font-size
│ Last Updated: 2h ago    │
└─────────────────────────┘
```

**Typography Hierarchy:**
- Primary Value: 72px font-weight-800 text-foreground
- Comparison Values: 24px font-weight-600
- Trend Indicators: 20px icons with color coding
- Context Text: 16px font-weight-400 text-muted-foreground

**Color Coding:**
- Positive trends: text-green-600 dark:text-green-400
- Negative trends: text-red-600 dark:text-red-400
- Neutral/No data: text-muted-foreground

#### Chart Visualization Panel (Right 60%)
```
┌─────────────────────────────────────┐
│ Chart Title & Controls              │
├─────────────────────────────────────┤
│                                     │
│                                     │
│     Enhanced Time Series Chart      │
│            (500px height)           │
│                                     │
│                                     │
├─────────────────────────────────────┤
│ Chart Legend & Data Summary         │
└─────────────────────────────────────┘
```

**Chart Enhancements:**
- **Height**: 500px (vs current 80px)
- **Chart Types**: 
  - Primary: Enhanced bar chart with gradient fills
  - Secondary: Line chart overlay for trends
  - Optional: Area chart for cumulative metrics
- **Interactive Elements**:
  - Hover tooltips with detailed breakdowns
  - Clickable data points for drill-down
  - Time period selector (daily/weekly/monthly)
- **Visual Improvements**:
  - Gradient backgrounds
  - Drop shadows for depth
  - Smooth animations on data changes
  - Grid lines for better readability

### 3. Slide Navigation System

#### Navigation Controls
```
┌─────────────────────────────────────┐
│ ← Previous    ●●●○○    Next →       │
│   Slide       Indicators   Slide    │
└─────────────────────────────────────┘
```

**Components:**
- **Previous/Next Buttons**: 
  - Size: 48px × 48px
  - Icons: ChevronLeft, ChevronRight (24px)
  - Hover states with background color changes
- **Slide Indicators**:
  - Dots representing each KPI slide
  - Active state: filled circle (12px)
  - Inactive state: outline circle (8px)
  - Clickable for direct navigation
- **Keyboard Navigation**:
  - Arrow keys for previous/next
  - Number keys for direct slide access
  - Escape key to exit slide mode

#### Slide Transition Animations
- **Type**: Horizontal slide transition
- **Duration**: 300ms ease-in-out
- **Direction**: Left-to-right for next, right-to-left for previous
- **Alternative**: Fade transition (200ms) for accessibility

### 4. Responsive Design Specifications

#### Desktop (≥1200px)
- Full slide layout as specified above
- Side-by-side metrics and chart layout
- All navigation controls visible

#### Tablet (768px - 1199px)
- Stacked layout: metrics above chart
- Reduced font sizes:
  - Primary value: 56px
  - Comparisons: 20px
- Chart height: 400px
- Touch-friendly navigation controls (56px)

#### Mobile (≤767px)
- Portrait-optimized layout
- Metrics panel: full width, reduced height
- Chart: full width, 300px height
- Swipe gestures for navigation
- Simplified navigation (dots only)
- Font sizes:
  - Primary value: 48px
  - Comparisons: 18px

### 5. Chart Enhancement Specifications

#### Enhanced Bar Chart
```typescript
interface EnhancedChartConfig {
  height: 500;
  margin: { top: 20, right: 30, bottom: 40, left: 60 };
  barRadius: [4, 4, 0, 0];
  barGradient: {
    start: 'hsl(var(--primary))',
    end: 'hsl(var(--primary) / 0.6)'
  };
  gridLines: {
    horizontal: true,
    vertical: false,
    color: 'hsl(var(--border))'
  };
  animations: {
    initial: { duration: 800, ease: 'easeOutCubic' },
    update: { duration: 400, ease: 'easeInOut' }
  };
}
```

#### Chart Features
- **Data Density**: Show all available data points (no aggregation unless necessary)
- **Tooltips**: Rich tooltips with:
  - Exact values with currency formatting
  - Date/period information
  - Percentage changes from previous period
  - Comparison to budget/target if available
- **Zoom/Pan**: For time series with many data points
- **Export**: Chart export functionality (PNG/SVG)

### 6. Slide Mode Activation

#### Entry Points
1. **"Slide View" Button**: In current KPI grid header
2. **Double-click**: On any KPI card
3. **Keyboard Shortcut**: 'S' key when focused on grid

#### Exit Points
1. **Close Button**: Top-right corner (×)
2. **Keyboard**: Escape key
3. **Click Outside**: Click on overlay background
4. **"Grid View" Button**: Return to original layout

#### URL State Management
- **Slide Mode**: `/executive-summary?view=slides&slide=3`
- **Grid Mode**: `/executive-summary?view=grid`
- **Deep Linking**: Direct links to specific slides
- **Browser History**: Back/forward navigation support

### 7. Visual Design Guidelines

#### Color Palette
```css
/* Primary Colors */
--slide-background: hsl(var(--background));
--slide-card: hsl(var(--card));
--slide-primary: hsl(var(--primary));

/* Status Colors */
--trend-positive: hsl(142 76% 36%);
--trend-negative: hsl(0 84% 60%);
--trend-neutral: hsl(var(--muted-foreground));

/* Chart Colors */
--chart-primary: hsl(var(--chart-1));
--chart-secondary: hsl(var(--chart-2));
--chart-accent: hsl(var(--chart-3));
```

#### Typography Scale
```css
/* Slide Typography */
.slide-title { font-size: 28px; font-weight: 700; }
.slide-subtitle { font-size: 16px; font-weight: 500; }
.kpi-primary { font-size: 72px; font-weight: 800; }
.kpi-comparison { font-size: 24px; font-weight: 600; }
.kpi-context { font-size: 16px; font-weight: 400; }
.chart-title { font-size: 20px; font-weight: 600; }
.chart-label { font-size: 14px; font-weight: 400; }
```

#### Spacing System
```css
/* Slide Spacing */
--slide-padding: 32px;
--content-gap: 24px;
--metric-gap: 16px;
--element-gap: 8px;
```

#### Shadow & Elevation
```css
/* Slide Shadows */
--slide-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 
                0 10px 10px -5px rgb(0 0 0 / 0.04);
--card-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 
               0 2px 4px -1px rgb(0 0 0 / 0.06);
--chart-shadow: inset 0 1px 0 0 rgb(255 255 255 / 0.1);
```

### 8. Accessibility Specifications

#### Keyboard Navigation
- **Tab Order**: Header → Metrics → Chart → Navigation
- **Focus Indicators**: 2px solid ring with primary color
- **Screen Reader**: Comprehensive ARIA labels and descriptions

#### ARIA Implementation
```html
<div role="region" aria-label="KPI Slide Presentation">
  <header aria-label="Slide Header">
    <h1 aria-level="1">Revenue</h1>
    <span aria-label="Slide 3 of 8">3 / 8</span>
  </header>
  
  <main aria-label="KPI Content">
    <section aria-label="Primary Metrics">
      <div aria-label="Current Value">$2.4M</div>
      <div aria-label="Budget Comparison">12.5% above budget</div>
    </section>
    
    <section aria-label="Chart Visualization">
      <div role="img" aria-label="Revenue trend chart showing upward trend">
        <!-- Chart content -->
      </div>
    </section>
  </main>
  
  <nav aria-label="Slide Navigation">
    <button aria-label="Previous slide">Previous</button>
    <button aria-label="Next slide">Next</button>
  </nav>
</div>
```

#### Color Contrast
- **Text on Background**: Minimum 4.5:1 ratio
- **Interactive Elements**: Minimum 3:1 ratio
- **Focus Indicators**: Minimum 3:1 ratio against background

### 9. Performance Specifications

#### Loading Strategy
- **Initial Load**: Load current slide + adjacent slides
- **Lazy Loading**: Load remaining slides on demand
- **Chart Rendering**: Progressive enhancement with loading states
- **Image Optimization**: WebP format with fallbacks

#### Animation Performance
- **GPU Acceleration**: Use transform3d for slide transitions
- **Reduced Motion**: Respect prefers-reduced-motion setting
- **Frame Rate**: Target 60fps for all animations

### 10. Export & Print Specifications

#### PDF Export
- **Page Size**: 16:10 landscape (1920×1200px at 150dpi)
- **Margins**: 0.5 inch on all sides
- **Quality**: Vector graphics where possible
- **File Size**: Optimize for <2MB per slide

#### Print Layout
```css
@media print {
  .slide-container {
    width: 100vw;
    height: 100vh;
    page-break-after: always;
  }
  
  .slide-navigation {
    display: none;
  }
  
  .chart-container {
    print-color-adjust: exact;
  }
}
```

### 11. Implementation Phases

#### Phase 1: Core Slide Infrastructure
- Slide container and navigation system
- Basic slide transitions
- Responsive layout foundation

#### Phase 2: Enhanced KPI Display
- Large-format metrics display
- Improved typography and spacing
- Color-coded trend indicators

#### Phase 3: Chart Enhancements
- Full-size chart implementations
- Interactive features and tooltips
- Animation and transition effects

#### Phase 4: Advanced Features
- PDF export functionality
- Keyboard navigation and accessibility
- Performance optimizations

#### Phase 5: Polish & Testing
- Cross-browser testing
- Accessibility audit and fixes
- Performance optimization
- User testing and refinements

## Technical Implementation Notes

### Component Architecture
```
SlidePresentation/
├── SlideContainer.tsx          # Main slide wrapper
├── SlideHeader.tsx            # Header with title and context
├── SlideContent.tsx           # Main content area
├── KpiMetricsPanel.tsx        # Left panel with metrics
├── ChartVisualizationPanel.tsx # Right panel with charts
├── SlideNavigation.tsx        # Navigation controls
├── SlideIndicators.tsx        # Dot indicators
└── hooks/
    ├── useSlideNavigation.ts  # Navigation logic
    ├── useSlideTransitions.ts # Animation logic
    └── useKeyboardControls.ts # Keyboard handling
```

### State Management
- **Current Slide Index**: Track active slide
- **Transition State**: Manage animation states
- **Chart Data**: Cache chart data for performance
- **User Preferences**: Remember view mode, transition speed

### Data Flow
1. **KPI Data**: Reuse existing data structure from [`KpiCardsGrid`](components/executive-summary/KpiCardsGrid.tsx:1)
2. **Time Series**: Leverage existing chart data processing from [`KpiCard`](components/executive-summary/KpiCard.tsx:42)
3. **Responsive Logic**: Extend current responsive grid logic
4. **Currency Formatting**: Reuse existing utility functions from [`utils`](components/executive-summary/utils.ts:1)

This specification provides a comprehensive foundation for implementing a PowerPoint-like slide interface that transforms the existing compact KPI cards into presentation-ready slides suitable for executive viewing and PDF export.