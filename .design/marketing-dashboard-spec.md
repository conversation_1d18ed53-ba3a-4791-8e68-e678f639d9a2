# Marketing Dashboard UX/UI Specification

**Version:** 1.0
**Date:** 2025-05-16
**Author:** UX Specialist AI

## 1. Overview

This document outlines the UX/UI design for the new Marketing Dashboard page ([`app/marketing-dashboard/page.tsx`](app/marketing-dashboard/page.tsx)). The dashboard aims to provide a comprehensive view of marketing campaign performance, leveraging detailed data from the updated API ([`app/api/marketing/campaign-data/route.ts`](app/api/marketing/campaign-data/route.ts)). The design prioritizes ease of use for filtering, sorting, and navigating potentially large datasets.

## 2. Goals

*   Display all campaigns per brand, per day, per channel, with all available metrics.
*   Enable users to easily filter data by brand, date range, and sales channel.
*   Provide clear visualizations for key performance indicators (KPIs) and trends.
*   Allow for flexible data grouping and aggregation (daily, weekly, monthly; totals per brand/channel).
*   Ensure a user-friendly and intuitive interface.

## 3. Page Layout ([`app/marketing-dashboard/page.tsx`](app/marketing-dashboard/page.tsx))

The page will be structured as follows:

1.  **Page Title:** "Marketing Dashboard"
2.  **Global Filters Section:**
    *   Component: `GlobalFilters` (adapted from [`components/dashboard-filters.tsx`](components/dashboard-filters.tsx))
    *   Filters:
        *   Date Range Picker (with presets: "Last 7 days", "Last 30 days", "Month to Date", "Custom Range")
        *   Brand Selector (multi-select dropdown)
        *   Sales Channel Selector (multi-select dropdown)
3.  **KPI Summary Section:**
    *   Component: `OverallKPICards` (adapted from [`components/marketing-kpi-cards.tsx`](components/marketing-kpi-cards.tsx))
    *   Displays key aggregated metrics based on global filter selections.
        *   Total Spend
        *   Total Impressions
        *   Total Clicks
        *   Total Conversions
        *   Total Conversion Value
        *   Overall ROAS (Return On Ad Spend)
        *   Overall CPA (Cost Per Acquisition)
4.  **Data Aggregation Controls:**
    *   Toggle group (Daily, Weekly, Monthly) to change the time granularity of data presented in charts and the main table.
5.  **Visualizations Section (Optional - could be integrated or separate tabs):**
    *   Component: `PerformanceTrendChart` (Line chart: Spend, Conversions, ROAS over time)
    *   Component: `SpendBreakdownCharts` (Pie/Bar charts: Spend by Channel, Platform, Campaign Type)
6.  **Main Data Table Section:**
    *   Component: `CampaignDataTable` (new or heavily adapted from existing table components)
    *   Displays detailed campaign data, sortable, filterable, and groupable.

## 4. Component Breakdown

### 4.1. `GlobalFilters`
*   **Source:** Adapt [`components/dashboard-filters.tsx`](components/dashboard-filters.tsx).
*   **Functionality:** Allows users to set global filters for Date Range, Brand(s), and Sales Channel(s).
*   **Interaction:** Changes here will update the data fetched and displayed in all subsequent sections (KPIs, Charts, Table).

### 4.2. `OverallKPICards`
*   **Source:** Adapt [`components/marketing-kpi-cards.tsx`](components/marketing-kpi-cards.tsx).
*   **Functionality:** Displays high-level aggregated KPIs based on the current filter settings and selected time aggregation (Daily/Weekly/Monthly).
*   **Metrics:**
    *   Total Spend
    *   Total Impressions
    *   Total Clicks
    *   Total Conversions
    *   Total Conversion Value
    *   Overall ROAS (Calculated: Total Conversion Value / Total Spend)
    *   Overall CPA (Calculated: Total Spend / Total Conversions)
    *   (Consider adding CTR: Total Clicks / Total Impressions)

### 4.3. `CampaignDataTable` (Primary Data Display)
*   **Source:** New component, potentially extending [`components/data-table.tsx`](components/data-table.tsx) or heavily modifying [`components/marketing-campaigns.tsx`](components/marketing-campaigns.tsx).
*   **Functionality:** Displays detailed campaign performance data with extensive interaction capabilities.
*   **Key Features:**
    *   **Column Display:** All metrics from the API should be available as columns.
        *   `date`
        *   `campaign_name`
        *   `brand_name`
        *   `sales_channel_type`
        *   Advertising platform-specific spend (e.g., `FacebookSpend`, `GoogleSpend`)
        *   `total_spend`
        *   Campaign type-specific spend (e.g., `AwarenessSpend`, `ConversionSpend`)
        *   `impressions`
        *   `clicks`
        *   `CTR` (Calculated: `clicks` / `impressions`)
        *   `conversions`
        *   `conversion_value`
        *   `ROAS` (API provided, or re-calculated for aggregated views)
        *   `CPA` (API provided, or re-calculated for aggregated views)
        *   Other existing spend metrics.
    *   **Column Visibility Toggle:** Users can show/hide columns to customize their view.
    *   **Sorting:** Clickable column headers for ascending/descending sort. Multi-column sort (advanced).
    *   **Filtering:**
        *   Global filters apply.
        *   Per-column filtering (e.g., text search for `campaign_name`, dropdown for `brand_name`).
    *   **Grouping:**
        *   Users can define grouping hierarchy (e.g., by Brand, then Channel, then Date).
        *   Grouped rows should show sub-totals for numeric metrics.
        *   Expand/collapse functionality for groups.
    *   **Pagination:** For handling large datasets.
    *   **Aggregation Display:** Data rows will reflect the selected time aggregation (Daily, Weekly, Monthly).
        *   For Weekly/Monthly: `Spend`, `Impressions`, `Clicks`, `Conversions`, `Conversion Value` are summed. `ROAS`, `CPA`, `CTR` are recalculated.
    *   **Export:** Option to export the current table view (CSV/Excel).

### 4.4. `PerformanceTrendChart`
*   **Source:** New component, using [`components/ui/chart.tsx`](components/ui/chart.tsx).
*   **Type:** Line chart.
*   **Functionality:** Visualizes trends for key metrics (e.g., `Total Spend`, `Conversions`, `ROAS`) over the selected date range and aggregation period.
*   **Interaction:** Hover to see specific data points. Clicking a series could highlight it or filter other components (advanced).

### 4.5. `SpendBreakdownChart`
*   **Source:** New component, using [`components/ui/chart.tsx`](components/ui/chart.tsx).
*   **Type:** Pie chart or Bar chart.
*   **Functionality:** Shows the distribution of `Total Spend` by dimensions like:
    *   `Sales Channel Type`
    *   Advertising Platform (derived from `FacebookSpend`, `GoogleSpend`, etc.)
    *   `Campaign Type` (derived from `AwarenessSpend`, `ConversionSpend`, etc.)
*   **Interaction:** Clicking a segment could filter the `CampaignDataTable` (advanced).

## 5. Data Flow and Interaction

1.  User lands on [`app/marketing-dashboard/page.tsx`](app/marketing-dashboard/page.tsx). Default filters (e.g., last 30 days, all brands, all channels) are applied.
2.  Data is fetched from [`app/api/marketing/campaign-data/route.ts`](app/api/marketing/campaign-data/route.ts) based on current filters.
3.  `OverallKPICards` display aggregated totals.
4.  `PerformanceTrendChart` and `SpendBreakdownCharts` visualize the data.
5.  `CampaignDataTable` displays detailed data, initially at 'Daily' granularity unless changed.
6.  User interacts with `GlobalFilters`:
    *   Data is re-fetched.
    *   All sections (KPIs, Charts, Table) update.
7.  User interacts with Aggregation Controls (Daily/Weekly/Monthly):
    *   Data in `CampaignDataTable` and `PerformanceTrendChart` is re-aggregated.
    *   KPIs in `OverallKPICards` are recalculated based on the new aggregation.
8.  User interacts with `CampaignDataTable` (sorting, column filtering, grouping, column visibility):
    *   Table view updates dynamically on the client-side where possible.
    *   Grouping might require re-processing of the displayed subset of data.

## 6. Filtering and Grouping Strategy

### 6.1. Filtering
*   **Global Filters (Top Level):**
    *   **Date Range:** Applied to the initial data fetch.
    *   **Brand(s):** Applied to the initial data fetch.
    *   **Sales Channel(s):** Applied to the initial data fetch.
*   **Table-Level Filters:**
    *   Applied client-side to the data already fetched and displayed in `CampaignDataTable`.
    *   Examples: Filter `campaign_name` containing "Retargeting", filter `ROAS` > 2.

### 6.2. Grouping and Aggregation
*   **Time Aggregation (User Selectable: Daily, Weekly, Monthly):**
    *   **Daily:** Default view. Each row in the table can represent a campaign's performance for a specific day (if not further grouped by campaign first).
    *   **Weekly/Monthly:** Data is aggregated.
        *   Sums: `Spend` (all types), `Impressions`, `Clicks`, `Conversions`, `Conversion Value`.
        *   Recalculated: `ROAS`, `CPA`, `CTR`.
        *   `Date` column in the table would show week start date or month.
*   **Categorical Grouping (in `CampaignDataTable`):**
    *   Users can drag column headers to a grouping area or select from a menu to group data.
    *   Example hierarchy: `Brand Name` -> `Sales Channel Type` -> `Campaign Name`.
    *   Each group level will have expandable/collapsible rows.
    *   Sub-totals for numeric metrics will be displayed for each group.
*   **Totals:**
    *   Overall totals reflected in `OverallKPICards`.
    *   Sub-totals per group in `CampaignDataTable`.
    *   A grand total row at the bottom of `CampaignDataTable` summarizing the currently displayed (and filtered) data.

## 7. Ease of Use Considerations

*   **Clear Visual Hierarchy:** Filters -> KPIs -> Charts/Table.
*   **Sensible Defaults:** Load with common date ranges and aggregations.
*   **Performance:** Optimize data fetching and client-side rendering, especially for the table. Use virtualization for the table if many rows are expected.
*   **Column Management:** Easy way to show/hide columns in the `CampaignDataTable` as there are many metrics.
*   **Persistent Settings (Optional/Future):** Allow users to save their preferred filter, column, and grouping configurations.
*   **Tooltips:** Use [`components/ui/tooltip.tsx`](components/ui/tooltip.tsx) for metric definitions, especially calculated ones like ROAS, CPA, CTR, or for explaining filter/grouping behaviors.
*   **Loading States:** Utilize [`components/ui/skeleton.tsx`](components/ui/skeleton.tsx) during data loads.
*   **No Data States:** Clear messages when filters result in no data.

## 8. Future Enhancements (Out of Scope for Initial Design)

*   Saving custom views/reports.
*   Direct drill-down from charts to filtered table views.
*   Advanced analytics features (e.g., cohort analysis, contribution margin).
*   Comparison periods (e.g., compare to previous period).