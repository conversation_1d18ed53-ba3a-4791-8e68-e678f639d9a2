# Pages Router to App Router Migration Plan

## Current State Analysis

### Overview
The project currently has a **hybrid setup** with both Pages Router and App Router implementations:

- **Pages Router**: Located in `src/pages/` - Contains admin functionality, authentication, and API routes
- **App Router**: Located in `app/` - Contains main dashboard functionality and some API routes
- **Components**: Split between `components/` (main) and `src/components/` (admin-specific)

### Current Router Usage

#### Pages Router (`src/pages/`)
**Pages:**
- `src/pages/_app.tsx` - App wrapper with providers
- `src/pages/admin/index.tsx` - Admin dashboard overview
- `src/pages/admin/users.tsx` - User management
- `src/pages/admin/roles.tsx` - Role management  
- `src/pages/admin/permissions.tsx` - Permission management
- `src/pages/admin/groups.tsx` - Group management
- `src/pages/admin/brands.tsx` - Brand management
- `src/pages/admin/settings.tsx` - Admin settings
- `src/pages/admin/backups.tsx` - Backup management
- `src/pages/admin/db-structure.tsx` - Database structure viewer
- `src/pages/auth/signin.tsx` - Custom sign-in page
- `src/pages/auth/logo.tsx` - Auth logo component

**API Routes:**
- `src/pages/api/auth/[...nextauth].ts` - NextAuth configuration
- `src/pages/api/admin/*` - All admin-related API endpoints (users, roles, permissions, groups, brands, settings, backups, impersonation)

#### App Router (`app/`)
**Pages:**
- `app/page.tsx` - Root page (redirects to dashboard)
- `app/layout.tsx` - Root layout
- `app/providers.tsx` - Client-side providers
- `app/dashboard/page.tsx` - Main dashboard
- `app/ai-assistant/page.tsx` - AI assistant
- `app/budget/page.tsx` - Budget page
- `app/brand-deep-dive/page.tsx` - Brand deep dive
- `app/executive-summary/page.tsx` - Executive summary
- `app/marketing-dashboard/page.tsx` - Marketing dashboard
- `app/marketing-dashboard/[campaignId]/page.tsx` - Campaign details

**API Routes:**
- `app/api/dashboard/*` - Dashboard-related APIs
- `app/api/marketing/*` - Marketing-related APIs
- `app/api/budget/route.ts` - Budget API
- `app/api/ai-assistant/route.ts` - AI assistant API
- `app/api/test-*` - Various test endpoints

### Component Structure
- **Main Components**: `components/` - Used by App Router pages
- **Admin Components**: `src/components/admin/` - Used by Pages Router admin pages
- **UI Components**: Both `components/ui/` and `src/components/ui/` exist
- **Shared Components**: Some duplication between locations

### Configuration Issues
- **next.config.ts**: Contains complex webpack aliases trying to bridge both systems
- **Import Paths**: Inconsistent use of aliases vs relative paths
- **Provider Setup**: Duplicated between `_app.tsx` and `app/providers.tsx`

---

## Migration Strategy

### Phase 1: Preparation and Cleanup

#### 1.1 Consolidate Component Structure
**Goal**: Unify all components under a single location with consistent import paths

**Actions:**
1. **Move all components to `components/` directory**:
   ```bash
   # Move admin components
   mv src/components/admin/* components/admin/
   
   # Consolidate UI components (merge src/components/ui/ into components/ui/)
   # Check for duplicates and conflicts first
   
   # Move any other components from src/components/
   mv src/components/campaign-detail/* components/campaign-detail/
   ```

2. **Update all import statements** in moved components to use path aliases:
   - Replace relative imports with `@/components/*` aliases
   - Ensure consistent import patterns across all components

3. **Remove duplicate components**:
   - Compare `components/ui/` and `src/components/ui/` for duplicates
   - Keep the most recent/complete version
   - Update any references to removed components

#### 1.2 Consolidate Library Code
**Goal**: Unify all library code under `lib/` directory

**Actions:**
1. **Move `src/lib/*` to `lib/`**:
   ```bash
   mv src/lib/* lib/
   ```

2. **Update import paths** throughout the codebase:
   - Change `@/src/lib/*` to `@/lib/*`
   - Update all components and pages using these imports

#### 1.3 Clean Up Configuration
**Goal**: Simplify webpack aliases and prepare for App Router-only setup

**Actions:**
1. **Simplify `next.config.ts`**:
   - Remove complex webpack aliases
   - Keep only essential path mappings for App Router
   - Prepare configuration for App Router-only setup

2. **Update `tsconfig.json` paths**:
   - Ensure path mappings align with new structure
   - Remove references to `src/` directories that will be eliminated

### Phase 2: Migrate Authentication

#### 2.1 Move NextAuth Configuration
**Goal**: Migrate authentication to App Router API routes

**Actions:**
1. **Create new auth configuration**:
   ```
   app/api/auth/[...nextauth]/route.ts
   ```

2. **Move and adapt authentication logic**:
   - Copy `src/pages/api/auth/[...nextauth].ts` content
   - Convert to App Router API route format
   - Update import paths to use new structure
   - Ensure database connections work with new paths

3. **Update authentication pages**:
   - Move `src/pages/auth/signin.tsx` to `app/auth/signin/page.tsx`
   - Convert to App Router page format
   - Update any auth-related components

#### 2.2 Test Authentication
**Goal**: Ensure authentication works correctly with new structure

**Actions:**
1. **Verify OAuth flow**:
   - Test Google OAuth sign-in
   - Verify user creation/lookup in database
   - Test session management

2. **Test role-based access**:
   - Verify admin role assignment
   - Test permission checking
   - Ensure impersonation functionality works

### Phase 3: Migrate Admin Functionality

#### 3.1 Create Admin Layout in App Router
**Goal**: Establish admin section structure in App Router

**Actions:**
1. **Create admin layout**:
   ```
   app/admin/layout.tsx
   ```
   - Move `AdminLayout` component logic
   - Ensure proper authentication checks
   - Set up admin-specific providers if needed

2. **Create admin dashboard**:
   ```
   app/admin/page.tsx
   ```
   - Migrate `src/pages/admin/index.tsx` content
   - Convert to App Router page format
   - Update import paths

#### 3.2 Migrate Admin Pages
**Goal**: Move all admin pages to App Router

**Actions:**
1. **Create admin page structure**:
   ```
   app/admin/users/page.tsx
   app/admin/roles/page.tsx
   app/admin/permissions/page.tsx
   app/admin/groups/page.tsx
   app/admin/brands/page.tsx
   app/admin/settings/page.tsx
   app/admin/backups/page.tsx
   app/admin/db-structure/page.tsx
   ```

2. **For each admin page**:
   - Copy content from corresponding `src/pages/admin/*.tsx`
   - Remove `NextPage` type and default export wrapper
   - Convert to App Router page component format
   - Update all import paths to use new structure
   - Remove any Pages Router-specific code
   - Ensure proper TypeScript types

3. **Update admin components**:
   - Ensure all admin components work with new import paths
   - Update any hardcoded routes to use App Router paths
   - Test component functionality

#### 3.3 Migrate Admin API Routes
**Goal**: Move all admin API endpoints to App Router

**Actions:**
1. **Create admin API structure**:
   ```
   app/api/admin/users/route.ts
   app/api/admin/users/[id]/route.ts
   app/api/admin/roles/route.ts
   app/api/admin/roles/[id]/route.ts
   app/api/admin/permissions/route.ts
   app/api/admin/permissions/[id]/route.ts
   app/api/admin/groups/route.ts
   app/api/admin/groups/[id]/route.ts
   app/api/admin/brands/route.ts
   app/api/admin/brands/[id]/route.ts
   app/api/admin/brands/sync-slugs/route.ts
   app/api/admin/settings/route.ts
   app/api/admin/backups/route.ts
   app/api/admin/backups/[backupName]/route.ts
   app/api/admin/dashboard-stats/route.ts
   app/api/admin/db-structure/route.ts
   app/api/admin/impersonation/start/route.ts
   app/api/admin/impersonation/stop/route.ts
   ```

2. **For each API route**:
   - Copy logic from corresponding `src/pages/api/admin/*.ts`
   - Convert to App Router API route format (GET, POST, PUT, DELETE functions)
   - Update import paths for database and utility functions
   - Ensure proper error handling and response formats
   - Test all CRUD operations

### Phase 4: Update Providers and Context

#### 4.1 Consolidate Provider Setup
**Goal**: Ensure all providers work correctly in App Router-only setup

**Actions:**
1. **Update `app/providers.tsx`**:
   - Ensure all necessary providers are included
   - Verify FilterProvider works with new import paths
   - Test SessionProvider with migrated auth

2. **Update FilterContext**:
   - Ensure `src/lib/contexts/filter-context.tsx` (now `lib/contexts/filter-context.tsx`) works
   - Update any API calls to use new admin API routes
   - Test filter functionality across all pages

#### 4.2 Test Provider Integration
**Goal**: Verify all context and providers work correctly

**Actions:**
1. **Test session management**:
   - Verify user sessions persist correctly
   - Test authentication state across page navigation
   - Ensure admin permissions work

2. **Test filter context**:
   - Verify filters work on dashboard pages
   - Test filter persistence
   - Ensure API calls to new routes work

### Phase 5: Clean Up and Remove Pages Router

#### 5.1 Remove Pages Router Files
**Goal**: Clean up all Pages Router remnants

**Actions:**
1. **Remove Pages Router structure**:
   ```bash
   rm -rf src/pages/
   rm -rf src/components/
   rm -rf src/lib/
   rm -rf src/config/
   rm -rf src/
   ```

2. **Update configuration files**:
   - Clean up `next.config.ts` webpack aliases
   - Remove any `src/` references from `tsconfig.json`
   - Update any scripts or configuration that reference `src/`

#### 5.2 Update All Import Statements
**Goal**: Ensure all imports use correct paths

**Actions:**
1. **Global find and replace**:
   - Replace any remaining `@/src/*` imports with `@/*`
   - Replace any remaining `src/*` imports with appropriate aliases
   - Update any relative imports that may have broken

2. **Test all functionality**:
   - Run the application and test all pages
   - Verify all API endpoints work
   - Test authentication and authorization
   - Verify admin functionality
   - Test dashboard and marketing features

### Phase 6: Final Testing and Optimization

#### 6.1 Comprehensive Testing
**Goal**: Ensure entire application works correctly

**Actions:**
1. **Test all user flows**:
   - Authentication (sign in/out)
   - Dashboard navigation
   - Admin functionality (CRUD operations)
   - Marketing dashboard features
   - Executive summary generation
   - Budget management

2. **Test API endpoints**:
   - All admin APIs
   - Dashboard APIs
   - Marketing APIs
   - Authentication APIs

3. **Test responsive design**:
   - Verify all pages work on different screen sizes
   - Test mobile navigation
   - Ensure admin interface is responsive

#### 6.2 Performance Optimization
**Goal**: Optimize App Router setup

**Actions:**
1. **Optimize imports**:
   - Ensure proper tree shaking
   - Use dynamic imports where appropriate
   - Optimize component loading

2. **Review and optimize layouts**:
   - Ensure proper layout nesting
   - Optimize loading states
   - Review error boundaries

3. **Database optimization**:
   - Ensure database connections are properly managed
   - Optimize queries if needed
   - Test connection pooling

---

## Detailed File Migration Map

### Components to Move
```
src/components/admin/ → components/admin/
src/components/ui/ → components/ui/ (merge with existing)
src/components/campaign-detail/ → components/campaign-detail/
```

### Library Code to Move
```
src/lib/ → lib/
src/config/ → lib/config/
```

### Pages to Migrate
```
src/pages/admin/index.tsx → app/admin/page.tsx
src/pages/admin/users.tsx → app/admin/users/page.tsx
src/pages/admin/roles.tsx → app/admin/roles/page.tsx
src/pages/admin/permissions.tsx → app/admin/permissions/page.tsx
src/pages/admin/groups.tsx → app/admin/groups/page.tsx
src/pages/admin/brands.tsx → app/admin/brands/page.tsx
src/pages/admin/settings.tsx → app/admin/settings/page.tsx
src/pages/admin/backups.tsx → app/admin/backups/page.tsx
src/pages/admin/db-structure.tsx → app/admin/db-structure/page.tsx
src/pages/auth/signin.tsx → app/auth/signin/page.tsx
```

### API Routes to Migrate
```
src/pages/api/auth/[...nextauth].ts → app/api/auth/[...nextauth]/route.ts
src/pages/api/admin/ → app/api/admin/ (convert to route.ts format)
```

### Files to Remove After Migration
```
src/pages/_app.tsx (functionality moved to app/layout.tsx and app/providers.tsx)
src/pages/ (entire directory)
src/components/ (entire directory)
src/lib/ (entire directory)
src/config/ (entire directory)
src/ (entire directory)
```

---

## Import Path Updates Required

### Current Problematic Imports
- `@/src/lib/*` → `@/lib/*`
- `components/admin/AdminLayout` → `@/components/admin/AdminLayout`
- `@/components/admin/*` → `@/components/admin/*`
- Relative imports in moved components

### New Import Structure
```typescript
// Components
import { Button } from '@/components/ui/button'
import AdminLayout from '@/components/admin/AdminLayout'

// Library code
import { getDb } from '@/lib/api/db'
import { useFilters } from '@/lib/contexts/filter-context'

// Types
import type { User } from '@/lib/types'
```

---

## Configuration Updates Required

### next.config.ts
```typescript
import type { NextConfig } from "next";
import path from "path";

const nextConfig: NextConfig = {
  webpack: (config) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.join(__dirname, './'),
      '@/components': path.join(__dirname, './components'),
      '@/lib': path.join(__dirname, './lib'),
      '@/app': path.join(__dirname, './app'),
    };
    return config;
  },
};

export default nextConfig;
```

### tsconfig.json paths
```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["./components/*"],
      "@/lib/*": ["./lib/*"],
      "@/app/*": ["./app/*"]
    }
  }
}
```

---

## Risk Assessment and Mitigation

### High Risk Areas
1. **Authentication Flow**: NextAuth migration requires careful testing
2. **Database Connections**: Ensure all DB calls work with new paths
3. **Admin Permissions**: Critical that RBAC continues to work
4. **API Route Conversion**: Pages Router to App Router API format changes

### Mitigation Strategies
1. **Incremental Migration**: Migrate in phases, test each phase thoroughly
2. **Backup Strategy**: Create full backup before starting migration
3. **Parallel Testing**: Keep Pages Router running until App Router is fully tested
4. **Rollback Plan**: Maintain ability to revert changes if issues arise

### Testing Checklist
- [ ] Authentication (sign in/out)
- [ ] User management (CRUD)
- [ ] Role management (CRUD)
- [ ] Permission management (CRUD)
- [ ] Group management (CRUD)
- [ ] Brand management (CRUD)
- [ ] Dashboard functionality
- [ ] Marketing dashboard
- [ ] Executive summary
- [ ] Budget management
- [ ] AI assistant
- [ ] Brand deep dive
- [ ] Responsive design
- [ ] Database operations
- [ ] API endpoints
- [ ] Error handling
- [ ] Loading states
- [ ] Navigation
- [ ] Session management
- [ ] Impersonation functionality

---

## Timeline Estimate

### Phase 1: Preparation (1-2 days)
- Component consolidation
- Library code migration
- Configuration cleanup

### Phase 2: Authentication (1 day)
- NextAuth migration
- Auth testing

### Phase 3: Admin Migration (2-3 days)
- Admin pages migration
- Admin API routes migration
- Admin functionality testing

### Phase 4: Provider Updates (0.5 days)
- Provider consolidation
- Context testing

### Phase 5: Cleanup (0.5 days)
- Remove Pages Router files
- Final import updates

### Phase 6: Testing (1-2 days)
- Comprehensive testing
- Performance optimization
- Bug fixes

**Total Estimated Time: 6-9 days**

---

## Success Criteria

1. **All functionality preserved**: Every feature that worked in Pages Router works in App Router
2. **No broken imports**: All import statements resolve correctly
3. **Authentication works**: Sign in/out, sessions, permissions all function correctly
4. **Admin panel functional**: All CRUD operations work in admin interface
5. **Dashboard features work**: All dashboard and marketing features function
6. **Performance maintained**: No significant performance degradation
7. **Clean codebase**: No remnants of Pages Router, consistent import patterns
8. **Tests pass**: All existing tests continue to pass (update as needed)

This migration will result in a cleaner, more maintainable codebase using only the App Router, with consistent import patterns and a unified component structure.
