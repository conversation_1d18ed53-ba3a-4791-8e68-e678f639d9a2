# NOLK v4 - Business Intelligence Dashboard

A comprehensive business intelligence platform built with Next.js 15, featuring real-time analytics, multi-brand management, and advanced reporting capabilities.

## 🚀 Features

- **Real-time Analytics**: Live KPI tracking and performance monitoring
- **Multi-brand Management**: Support for multiple brands with role-based access
- **Advanced Reporting**: Executive summaries, brand deep-dives, and marketing analytics
- **AI Assistant**: Claude-powered insights and data analysis
- **Secure Authentication**: NextAuth.js with Google OAuth and role-based permissions
- **Performance Optimized**: Sub-500ms database queries and optimized rendering

## 🏗️ Architecture

- **Frontend**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **Backend**: Next.js API routes with PostgreSQL/Redshift integration
- **Database**: SQLite for user management, Redshift for analytics data
- **Authentication**: NextAuth.js with JWT sessions
- **Testing**: Comprehensive test suite with performance monitoring

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn
- Access to Redshift database (for analytics)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd nolk-v4

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# Run the development server
npm run dev
```

The application will be available at [http://localhost:6699](http://localhost:6699).

### Environment Variables

Create a `.env.local` file with the following variables:

```bash
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:6699
NEXTAUTH_SECRET=your-secret-key

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Redshift Database
REDSHIFT_HOST=your-redshift-host
REDSHIFT_PORT=5439
REDSHIFT_DATABASE=your-database
REDSHIFT_USER=your-username
REDSHIFT_PASSWORD=your-password
```

## 📊 Performance Standards

- **Dashboard Load**: < 3 seconds
- **API Response**: < 1 second
- **Database Queries**: < 500ms
- **Chart Rendering**: < 2 seconds
- **Memory Usage**: < 200MB per session

## 🧪 Testing

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:performance

# Run tests with coverage
npm run test:coverage

# Performance monitoring
npm run monitor:performance
```

## 📁 Project Structure

```
├── app/                    # Next.js App Router pages and API routes
├── components/             # Reusable React components
├── lib/                   # Utility functions and configurations
├── tests/                 # Comprehensive test suite
├── docs/                  # Documentation
└── scripts/               # Utility scripts
```

## 🔒 Security Features

- **Authentication**: Secure JWT-based sessions
- **Authorization**: Role-based access control (RBAC)
- **Input Validation**: Zod schema validation
- **SQL Injection Protection**: Parameterized queries
- **CSRF Protection**: Built-in NextAuth.js protection

## 🚀 Deployment

### Production Build

```bash
npm run build
npm start
```

### Environment-specific Configurations

- **Development**: `npm run dev` (port 6699)
- **Production**: `npm start` (port 6699)

## 📈 Monitoring & Analytics

- **Performance Monitoring**: Built-in performance tracking
- **Error Tracking**: Comprehensive error handling
- **Database Monitoring**: Query performance tracking
- **User Analytics**: Session and usage tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📚 Documentation

- [Admin Documentation](docs/admin_documentation.md)
- [Testing Strategy](docs/comprehensive_testing_strategy.md)
- [Database Documentation](database_documentation.md)
- [Performance Testing](tests/README_PHASE5.md)

## 🔧 Development Tools

- **TypeScript**: Full type safety
- **ESLint**: Code linting and formatting
- **Jest**: Testing framework
- **Tailwind CSS**: Utility-first styling
- **Shadcn/ui**: Component library

## 📞 Support

For technical support or questions, please refer to the documentation or create an issue in the repository.

## Learn More

To learn more about the technologies used:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
