import AdminLayout from '../../components/admin/AdminLayout';
import { authOptions } from '@/lib/auth-options';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';

export default async function AdminRootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession(authOptions);

  // Check if session exists first, then check roles
  if (!session) {
    redirect('/auth/signin');
  }

  // Allow access for users with either "Admin" or "Super Admin" roles
  const userRoles = (session.user as { roles?: string[] })?.roles || [];
  const hasAdminAccess = userRoles.includes('Admin') || userRoles.includes('Super Admin');

  if (!hasAdminAccess) {
    redirect('/auth/signin');
  }
  return <AdminLayout>{children}</AdminLayout>;
}
