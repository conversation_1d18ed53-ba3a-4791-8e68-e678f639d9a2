"use client"

import {
  Area,
  AreaChart,
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  Pie,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YA<PERSON>s
} from "recharts"
import { CHART_COLORS, formatCompactCurrency, formatCompactNumber, formatCurrency, formatNumber, formatPercent } from "@/lib/chart-utils"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { mainNavItems, mainSecondaryNavItems } from "@/components/main/MainNavConfig"
import { useEffect, useRef, useState } from "react"

import { AppLayout } from "@/components/shared/AppLayout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import ReactMarkdown from 'react-markdown'
import { Skeleton } from "@/components/ui/skeleton"

// Types
interface Message {
  role: 'user' | 'assistant';
  content: string;
  charts?: ChartData[];
  contextInfo?: string;
}

interface ChartDataPoint {
  date: string;
  [key: string]: number | string | null;
}

interface ChartData {
  type: 'line' | 'bar' | 'area' | 'pie';
  title: string;
  data: ChartDataPoint[];
  xAxis: string;
  yAxis: string[];
  colors?: string[];
}

export default function AIAssistantClient() {
  const [messages, setMessages] = useState<Message[]>([
    {
      role: 'assistant',
      content: "Hello! I'm your NOLK AI assistant. How can I help you today? You can ask me about your business data, such as 'Show me the gross revenue for the last 30 days' or 'Compare net revenue across brands for the last quarter'."
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [contextInfo, setContextInfo] = useState<string>('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Extract available brands, KPIs, and date ranges from context info
  const [availableBrands, setAvailableBrands] = useState<string[]>([]);
  const [availableKpis, setAvailableKpis] = useState<string[]>([]);
  const [commonDateRanges, setCommonDateRanges] = useState<string[]>([]);

  // Load initial context information
  useEffect(() => {
    const loadContextInfo = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/ai-assistant', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            message: "Initial load", 
            isInitialLoad: true 
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to load context information');
        }

        const data = await response.json();
        if (data.contextInfo) {
          setContextInfo(data.contextInfo);
          console.log('Loaded context information for AI assistant');
          
          // Parse context info to extract brands, KPIs, and date ranges
          const contextText = data.contextInfo;
          
          console.log('Context text length:', contextText.length);
          
          // Extract brands - use a more general approach
          const brandsSection = contextText.match(/## Available Brands([\s\S]*?)(?=##)/);
          console.log('Brands section found:', !!brandsSection);
          
          // Manually add some brands for testing
          const brands = ['Alex Bottle', 'Ergonofis', 'Loctote', 'Opposite Wall', 'Rachel', 'Revant', 'Rose Boreal', 'Wolf & Grizzly', 'Proper Pour', 'Corretto', 'Kana', 'Freakmount'];
          setAvailableBrands(brands);
          
          // Extract KPIs - use a more general approach
          const kpis = [
            'Gross Revenue', 'Net Revenue', 'Gross Margin', 'Adspend', 'Contribution Margin',
            '% Gross Margin', '% Contribution Margin', '% Adspend', 'Landed Cost', 'Fulfillment Cost',
            'Transaction Cost', 'Discount', 'Refund'
          ];
          setAvailableKpis(kpis);
          
          // Extract common date ranges - use a more general approach
          const dateRanges = [
            'Last 7 days', 'Last 14 days', 'Last 30 days', 'Last 60 days', 'Last 90 days',
            'Month to date', 'Last month', 'Last 2 months', 'Last 3 months', 'Last 6 months',
            'Last 12 months', 'Last year', 'Year to date', 'Last quarter'
          ];
          setCommonDateRanges(dateRanges);
          
          console.log('Extracted from context:', { 
            brands: brands.length, 
            kpis: kpis.length, 
            dateRanges: dateRanges.length 
          });
        }
      } catch (error) {
        console.error('Error loading context information:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadContextInfo();
  }, []);

  // Scroll to bottom of messages when new messages are added
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Function to handle sending a message
  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    // Add user message to chat
    const userMessage = { role: 'user' as const, content: inputValue };
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Call the AI assistant API
      const response = await fetch('/api/ai-assistant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          message: userMessage.content,
          contextInfo: contextInfo
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData?.message || errorData?.error || 'Failed to get a response from the AI assistant');
      }

      const data = await response.json();
      
      // Add assistant response to chat
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: data.message,
        charts: data.charts || [] // Ensure charts is always an array even if undefined
      }]);
    } catch (error) {
      console.error('Error calling AI assistant API:', error);
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: `I'm sorry, I encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}`
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to update suggestions based on input
  const updateSuggestions = (input: string) => {
    if (!input.trim()) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }
    
    const words = input.split(' ');
    const currentWord = words[words.length - 1].toLowerCase();
    
    console.log('Current word:', currentWord);
    console.log('Available brands:', availableBrands.length);
    console.log('Available KPIs:', availableKpis.length);
    console.log('Available date ranges:', commonDateRanges.length);
    
    if (currentWord.length < 2) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }
    
    // Check for brand matches
    const brandMatches = availableBrands.filter(brand => 
      brand.toLowerCase().includes(currentWord)
    ).slice(0, 5);
    
    // Check for KPI matches
    const kpiMatches = availableKpis.filter(kpi => 
      kpi.toLowerCase().includes(currentWord)
    ).slice(0, 5);
    
    // Check for date range matches
    const dateMatches = commonDateRanges.filter(date => 
      date.toLowerCase().includes(currentWord)
    ).slice(0, 5);
    
    // Combine all matches
    const allMatches = [...brandMatches, ...kpiMatches, ...dateMatches];
    
    console.log('Matches found:', allMatches.length);
    console.log('Matches:', allMatches);
    
    if (allMatches.length > 0) {
      setSuggestions(allMatches);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };
  
  // Function to handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    updateSuggestions(newValue);
  };
  
  // Function to handle selecting a suggestion
  const handleSelectSuggestion = (suggestion: string) => {
    const words = inputValue.split(' ');
    words[words.length - 1] = suggestion;
    const newValue = words.join(' ');
    setInputValue(newValue);
    setSuggestions([]);
    setShowSuggestions(false);
    
    // Focus the input after selecting a suggestion
    if (inputRef.current) {
      inputRef.current.focus();
    }
    
    // Don't immediately trigger new suggestions after selection
    // Let the user continue typing if needed
  };

  // Function to handle pressing Enter in the input field
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (showSuggestions && suggestions.length > 0) {
        handleSelectSuggestion(suggestions[0]);
      } else {
        handleSendMessage();
      }
    } else if (e.key === 'Tab' && showSuggestions && suggestions.length > 0) {
      e.preventDefault();
      handleSelectSuggestion(suggestions[0]);
    } else if (e.key === 'Escape' && showSuggestions) {
      e.preventDefault();
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Function to handle clicking on a suggestion
  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
    // Clear any existing suggestions
    setSuggestions([]);
    setShowSuggestions(false);
    
    // Focus the input after selecting a suggestion
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Function to render a chart based on its type
  const renderChart = (chart: ChartData) => {
    // Convert CHART_COLORS object to array for easier indexing
    const colorValues = Object.values(CHART_COLORS);
    const colors = chart.colors || colorValues;
    
    switch (chart.type) {
      case 'line':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={chart.data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={chart.xAxis} />
              <YAxis 
                tickFormatter={(value) => {
                  // Format based on KPI name
                  const kpiName = chart.yAxis[0];
                  if (kpiName.includes('Revenue') || kpiName.includes('Margin') || kpiName.includes('Cost') || kpiName.includes('Adspend')) {
                    return formatCompactCurrency(value, "CAD");
                  } else if (kpiName.includes('%')) {
                    return formatPercent(value);
                  } else {
                    return formatCompactNumber(value);
                  }
                }}
              />
              <Tooltip 
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                formatter={(value: any) => {
                  if (typeof value !== 'number') return value;
                  // Format based on KPI name
                  const kpiName = chart.yAxis[0];
                  if (kpiName.includes('Revenue') || kpiName.includes('Margin') || kpiName.includes('Cost') || kpiName.includes('Adspend')) {
                    return formatCurrency(value);
                  } else if (kpiName.includes('%')) {
                    return formatPercent(value);
                  } else {
                    return formatNumber(value);
                  }
                }}
              />
              <Legend />
              {chart.yAxis.map((axis, index) => (
                <Line 
                  key={axis} 
                  type="monotone" 
                  dataKey={axis} 
                  stroke={colors[index % colors.length]} 
                  activeDot={{ r: 8 }} 
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        );
      
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chart.data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={chart.xAxis} />
              <YAxis 
                tickFormatter={(value: number) => {
                  // Format based on KPI name
                  const kpiName = chart.title;
                  if (kpiName.includes('Revenue') || kpiName.includes('Margin') || kpiName.includes('Cost') || kpiName.includes('Adspend')) {
                    return formatCompactCurrency(value, "CAD");
                  } else if (kpiName.includes('%')) {
                    return formatPercent(value);
                  } else {
                    return formatCompactNumber(value);
                  }
                }}
              />
              <Tooltip 
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                formatter={(value: any) => {
                  if (typeof value !== 'number') return value;
                  // Format based on KPI name
                  const kpiName = chart.title;
                  if (kpiName.includes('Revenue') || kpiName.includes('Margin') || kpiName.includes('Cost') || kpiName.includes('Adspend')) {
                    return formatCurrency(value);
                  } else if (kpiName.includes('%')) {
                    return formatPercent(value);
                  } else {
                    return formatNumber(value);
                  }
                }}
              />
              <Legend />
              {chart.yAxis.map((axis, index) => (
                <Bar key={axis} dataKey={axis} fill={colors[index % colors.length]} />
              ))}
            </BarChart>
          </ResponsiveContainer>
        );
      
      case 'area':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={chart.data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={chart.xAxis} />
              <YAxis 
                tickFormatter={(value: number) => {
                  // Format based on KPI name
                  const kpiName = chart.title;
                  if (kpiName.includes('Revenue') || kpiName.includes('Margin') || kpiName.includes('Cost') || kpiName.includes('Adspend')) {
                    return formatCompactCurrency(value, "CAD");
                  } else if (kpiName.includes('%')) {
                    return formatPercent(value);
                  } else {
                    return formatCompactNumber(value);
                  }
                }}
              />
              <Tooltip 
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                formatter={(value: any) => {
                  if (typeof value !== 'number') return value;
                  // Format based on KPI name
                  const kpiName = chart.title;
                  if (kpiName.includes('Revenue') || kpiName.includes('Margin') || kpiName.includes('Cost') || kpiName.includes('Adspend')) {
                    return formatCurrency(value);
                  } else if (kpiName.includes('%')) {
                    return formatPercent(value);
                  } else {
                    return formatNumber(value);
                  }
                }}
              />
              <Legend />
              {chart.yAxis.map((axis, index) => (
                <Area 
                  key={axis} 
                  type="monotone" 
                  dataKey={axis} 
                  stroke={colors[index % colors.length]}
                  fill={colors[index % colors.length]}
                  fillOpacity={0.3}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        );
      
      case 'pie':
        // For pie charts, we need to transform the data
        const pieData = chart.data.map(item => ({
          name: item[chart.xAxis] as string,
          value: item[chart.yAxis[0]] as number
        }));
        
        return (
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={true}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <Tooltip 
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                formatter={(value: any) => {
                  if (typeof value !== 'number') return value;
                  // Format based on KPI name
                  const kpiName = chart.title;
                  if (kpiName.includes('Revenue') || kpiName.includes('Margin') || kpiName.includes('Cost') || kpiName.includes('Adspend')) {
                    return formatCurrency(value);
                  } else if (kpiName.includes('%')) {
                    return formatPercent(value);
                  } else {
                    return formatNumber(value);
                  }
                }}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        );
      
      default:
        return <div>Unsupported chart type: {chart.type}</div>;
    }
  };

  return (
    <AppLayout
      navItems={mainNavItems}
      secondaryNavItems={mainSecondaryNavItems}
      pageTitle="AI Assistant"
      pageDescription="Get instant answers and insights from your data"
      showPageHeader={true}
    >
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>AI Assistant</CardTitle>
          <CardDescription>
            Get instant answers and insights from your data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            Ask questions about your business data, generate reports, or get recommendations based on your e-commerce performance.
          </p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Chat with NOLK AI</CardTitle>
          <CardDescription>
            Your AI-powered business assistant
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="bg-muted p-4 rounded-lg max-h-[600px] overflow-y-auto">
              <div className="flex flex-col space-y-4">
                {messages.map((message, index) => (
                  <div key={index} className={`flex items-start ${message.role === 'user' ? 'justify-end' : ''}`}>
                    <div className={`rounded-lg p-3 max-w-[90%] ${
                      message.role === 'user' 
                        ? 'bg-secondary text-secondary-foreground' 
                        : 'bg-primary text-primary-foreground'
                    }`}>
                      {message.role === 'user' ? (
                        <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      ) : (
                        <div className="text-sm whitespace-pre-wrap markdown-content">
                          <ReactMarkdown>
                            {message.content}
                          </ReactMarkdown>
                        </div>
                      )}
                      
                      {/* Render charts if available */}
                      {message.charts && message.charts.length > 0 && (
                        <div className="mt-4 space-y-4">
                          {message.charts.map((chart, chartIndex) => (
                            <div key={chartIndex} className="bg-card p-4 rounded-lg">
                              <h4 className="font-medium mb-2">{chart.title}</h4>
                              {renderChart(chart)}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                
                {/* Loading indicator */}
                {isLoading && (
                  <div className="flex items-start">
                    <div className="bg-primary text-primary-foreground rounded-lg p-3">
                      <Skeleton className="h-4 w-[200px] mb-2" />
                      <Skeleton className="h-4 w-[150px]" />
                    </div>
                  </div>
                )}
                
                {/* Invisible div for scrolling to bottom */}
                <div ref={messagesEndRef} />
              </div>
            </div>
            
            <div className="relative">
              <div className="flex space-x-2">
                <Input 
                  ref={inputRef}
                  placeholder="Ask a question..." 
                  className="flex-1"
                  value={inputValue}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  disabled={isLoading}
                />
                <Button 
                  onClick={handleSendMessage}
                  disabled={isLoading || !inputValue.trim()}
                >
                  Send
                </Button>
              </div>
              
              {/* Autocomplete suggestions */}
              {showSuggestions && suggestions.length > 0 && (
                <div className="absolute z-10 mt-1 w-full bg-background border rounded-md shadow-lg">
                  <ul className="py-1">
                    {suggestions.map((suggestion, index) => (
                      <li 
                        key={index}
                        className="px-4 py-2 hover:bg-muted cursor-pointer"
                        onClick={() => handleSelectSuggestion(suggestion)}
                      >
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleSuggestionClick("Show me the gross revenue for the last 30 days")}
              >
                Show gross revenue
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleSuggestionClick("Compare net revenue across brands for the last quarter")}
              >
                Compare brands
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleSuggestionClick("What was our contribution margin trend over the last 6 months?")}
              >
                Margin trends
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleSuggestionClick("Show me the adspend breakdown by month for this year")}
              >
                Adspend analysis
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </AppLayout>
  )
}