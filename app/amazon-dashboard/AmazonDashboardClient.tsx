"use client";

import { AmazonKpiCards } from "@/components/amazon-kpi-cards"
import { EnhancedDashboardFilters } from "@/components/enhanced-dashboard-filters"
import { FilterProvider } from "@/lib/contexts/filter-context"

// Create a wrapper component that uses the FilterProvider
function AmazonDashboardContent() {
  return (
    <>
      {/* Dashboard Filters */}
      <EnhancedDashboardFilters
        showCountryFilter={false}
        showCurrencyFilter={true}
        showGroupByFilter={true}
        pageType="amazon-dashboard"
      />

      {/* Amazon KPI Summary Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Amazon Performance Metrics</h3>
        <AmazonKpiCards />
      </div>

      {/* Future sections can be added here */}
      {/* Amazon Inventory Section */}
      {/* Amazon Advertising Performance Charts */}
      {/* Amazon Marketplace Breakdown */}
    </>
  );
}

export function AmazonDashboardClient() {
  return (
    <FilterProvider>
      <AmazonDashboardContent />
    </FilterProvider>
  );
}
