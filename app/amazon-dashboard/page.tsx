import { mainNavItems, mainSecondaryNavItems } from "@/components/main/MainNavConfig"

import { AmazonDashboardClient } from "./AmazonDashboardClient"
import { AppLayout } from "@/components/shared/AppLayout"
import React from "react"
import { authOptions } from "@/lib/auth-options";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";

export default async function AmazonDashboardPage() {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    redirect("/auth/signin");
  }

  return (
    <AppLayout
      navItems={mainNavItems}
      secondaryNavItems={mainSecondaryNavItems}
      pageTitle="Amazon Dashboard"
      pageDescription="Amazon marketplace performance and advertising metrics"
      showPageHeader={true}
    >
      <AmazonDashboardClient />
    </AppLayout>
  )
}
