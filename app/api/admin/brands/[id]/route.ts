import { NextRequest, NextResponse } from 'next/server';

import { Pool } from 'pg';
import { authOptions } from '@/lib/auth-options';
import { getRedshiftPool } from '@/lib/api/redshift';
import { getServerSession } from 'next-auth';
import { hasAdminAccess } from '@/lib/api/admin-auth';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  let redshiftPool: Pool | null;
  try {
    redshiftPool = getRedshiftPool();
    if (!redshiftPool) {
      console.error('Failed to get Redshift pool.');
      return NextResponse.json({ error: 'Database connection error' }, { status: 500 });
    }

    const client = await redshiftPool.connect();
    try {
      const result = await client.query(
        'SELECT brand_id, code, name, description, abbreviation, logo_image_url, status, stage, "group" FROM dwh_ai.ai_reporting_brands WHERE brand_id = $1',
        [id]
      );

      if (result.rows.length === 0) {
        return NextResponse.json({ error: 'Brand not found' }, { status: 404 });
      }

      return NextResponse.json(result.rows[0]);
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error fetching brand:', error);
    return NextResponse.json({ error: 'Failed to fetch brand' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  let redshiftPool: Pool | null;
  try {
    const body = await request.json();
    const { name, code, description, abbreviation, logo_image_url, status, stage, group } = body;

    // Basic validation
    if (!name || !code) {
      return NextResponse.json({ error: 'Name and code are required' }, { status: 400 });
    }

    redshiftPool = getRedshiftPool();
    if (!redshiftPool) {
      console.error('Failed to get Redshift pool.');
      return NextResponse.json({ error: 'Database connection error' }, { status: 500 });
    }

    const client = await redshiftPool.connect();
    try {
      // Update brand
      const result = await client.query(
        `UPDATE dwh_ai.ai_reporting_brands 
         SET code = $2, name = $3, description = $4, abbreviation = $5, 
             logo_image_url = $6, status = $7, stage = $8, "group" = $9
         WHERE brand_id = $1 
         RETURNING brand_id, code, name, description, abbreviation, logo_image_url, status, stage, "group"`,
        [id, code, name, description, abbreviation, logo_image_url, status, stage, group]
      );

      if (result.rows.length === 0) {
        return NextResponse.json({ error: 'Brand not found' }, { status: 404 });
      }

      return NextResponse.json(result.rows[0]);
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error updating brand:', error);
    return NextResponse.json({ error: 'Failed to update brand' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  let redshiftPool: Pool | null;
  try {
    redshiftPool = getRedshiftPool();
    if (!redshiftPool) {
      console.error('Failed to get Redshift pool.');
      return NextResponse.json({ error: 'Database connection error' }, { status: 500 });
    }

    const client = await redshiftPool.connect();
    try {
      // Check if brand exists first
      const checkResult = await client.query(
        'SELECT brand_id FROM dwh_ai.ai_reporting_brands WHERE brand_id = $1',
        [id]
      );

      if (checkResult.rows.length === 0) {
        return NextResponse.json({ error: 'Brand not found' }, { status: 404 });
      }

      // Delete brand
      await client.query(
        'DELETE FROM dwh_ai.ai_reporting_brands WHERE brand_id = $1',
        [id]
      );

      return NextResponse.json({ message: 'Brand deleted successfully' });
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error deleting brand:', error);
    return NextResponse.json({ error: 'Failed to delete brand' }, { status: 500 });
  }
}
