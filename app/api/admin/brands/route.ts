import { NextRequest, NextResponse } from 'next/server';

import { Pool } from 'pg';
import { authOptions } from '@/lib/auth-options';
import { getRedshiftPool } from '@/lib/api/redshift';
import { getServerSession } from 'next-auth';
import { hasAdminAccess } from '@/lib/api/admin-auth';

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  // Ensure user is authenticated and has the 'admin' role (or 'Admin' if that's the actual role name)
  // Based on previous findings, the role is 'Admin'
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  let redshiftPool: Pool | null;
  try {
    redshiftPool = getRedshiftPool();
    if (!redshiftPool) {
      console.error('Failed to get Redshift pool.');
      return NextResponse.json({ error: 'Database connection error' }, { status: 500 });
    }

    const client = await redshiftPool.connect();
    try {
      // Get search parameters from URL
      const { searchParams } = new URL(request.url);
      const search = searchParams.get('search') || '';
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '50');
      const offset = (page - 1) * limit;

      // Build the base query
      let query = 'SELECT brand_id, code, name, description, abbreviation, logo_image_url, status, stage, "group" FROM dwh_ai.ai_reporting_brands';
      let countQuery = 'SELECT COUNT(*) as total FROM dwh_ai.ai_reporting_brands';
      const queryParams: (string | number)[] = [];
      let paramIndex = 1;

      // Add search filter if provided
      if (search) {
        const searchCondition = ` WHERE (
          LOWER(name) LIKE LOWER($${paramIndex}) OR 
          LOWER(code) LIKE LOWER($${paramIndex + 1}) OR 
          LOWER(description) LIKE LOWER($${paramIndex + 2}) OR 
          LOWER(abbreviation) LIKE LOWER($${paramIndex + 3}) OR 
          LOWER(status) LIKE LOWER($${paramIndex + 4}) OR 
          LOWER(stage) LIKE LOWER($${paramIndex + 5}) OR 
          LOWER("group") LIKE LOWER($${paramIndex + 6})
        )`;
        
        query += searchCondition;
        countQuery += searchCondition;
        
        const searchPattern = `%${search}%`;
        queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern);
        paramIndex += 7;
      }

      // Add pagination
      query += ` ORDER BY name LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      queryParams.push(limit, offset);

      // Execute both queries
      const [dataResult, countResult] = await Promise.all([
        client.query(query, queryParams),
        client.query(countQuery, search ? queryParams.slice(0, -2) : [])
      ]);

      const total = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(total / limit);

      return NextResponse.json({
        data: dataResult.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      });
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error fetching brands:', error);
    return NextResponse.json({ error: 'Failed to fetch brands' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  let redshiftPool: Pool | null;
  try {
    const body = await request.json();
    const { name, code, description, abbreviation, logo_image_url, status, stage, group } = body;

    // Basic validation
    if (!name || !code) {
      return NextResponse.json({ error: 'Name and code are required' }, { status: 400 });
    }

    redshiftPool = getRedshiftPool();
    if (!redshiftPool) {
      console.error('Failed to get Redshift pool.');
      return NextResponse.json({ error: 'Database connection error' }, { status: 500 });
    }

    const client = await redshiftPool.connect();
    try {
      // Generate a new brand_id (you might want to use UUID or auto-increment)
      const brand_id = `brand_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Insert new brand
      const result = await client.query(
        `INSERT INTO dwh_ai.ai_reporting_brands 
         (brand_id, code, name, description, abbreviation, logo_image_url, status, stage, "group") 
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) 
         RETURNING brand_id, code, name, description, abbreviation, logo_image_url, status, stage, "group"`,
        [brand_id, code, name, description, abbreviation, logo_image_url, status, stage, group]
      );

      return NextResponse.json(result.rows[0], { status: 201 });
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error creating brand:', error);
    return NextResponse.json({ error: 'Failed to create brand' }, { status: 500 });
  }
}
