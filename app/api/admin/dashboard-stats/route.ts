import { NextResponse } from 'next/server';
import { authOptions } from '@/lib/auth-options';
import { getDb } from '@/lib/api/db';
import { getRedshiftPool } from '@/lib/api/redshift';
import { getServerSession } from 'next-auth';
import { hasAdminAccess } from '@/lib/api/admin-auth';

export async function GET() {
  const session = await getServerSession(authOptions);

  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const db = await getDb();
    const redshiftPool = getRedshiftPool();

    // Get counts from local SQLite database
    const [userCountResult] = await Promise.all([
      db.get('SELECT COUNT(*) as count FROM Users'),
    ]);

    const [roleCountResult] = await Promise.all([
      db.get('SELECT COUNT(*) as count FROM Roles'),
    ]);

    const [groupCountResult] = await Promise.all([
      db.get('SELECT COUNT(*) as count FROM Groups'),
    ]);

    const [permissionCountResult] = await Promise.all([
      db.get('SELECT COUNT(*) as count FROM Permissions'),
    ]);

    // Get brand count from Redshift
    let brandCount = 0;
    if (redshiftPool) {
      try {
        const redshiftClient = await redshiftPool.connect();
        try {
          const brandResult = await redshiftClient.query('SELECT COUNT(*) as count FROM dwh_ai.ai_reporting_brands');
          brandCount = parseInt(brandResult.rows[0].count);
        } finally {
          redshiftClient.release();
        }
      } catch (redshiftError) {
        console.error('Error fetching brand count from Redshift:', redshiftError);
        // Fallback to local brands table if Redshift fails
        const localBrandResult = await db.get('SELECT COUNT(*) as count FROM Brands');
        brandCount = localBrandResult.count;
      }
    } else {
      // Fallback to local brands table if Redshift is not available
      const localBrandResult = await db.get('SELECT COUNT(*) as count FROM Brands');
      brandCount = localBrandResult.count;
    }

    // Get recent user additions (last 10 users)
    const latestUserAdditions = await db.all(`
      SELECT id, name, email, createdAt
      FROM Users
      ORDER BY createdAt DESC
      LIMIT 10
    `);

    // Get recent user login activities
    // Note: We don't have a login tracking table yet, so we'll return empty for now
    // In a real implementation, you'd have a UserSessions or LoginHistory table
    const recentUserLogins: { id: number; name: string; email: string; lastLoginAt: string }[] = [];

    const stats = {
      userCount: userCountResult.count,
      brandCount: brandCount,
      roleCount: roleCountResult.count,
      groupCount: groupCountResult.count,
      permissionCount: permissionCountResult.count,
      latestUserAdditions: latestUserAdditions.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        createdAt: user.createdAt
      })),
      recentUserLogins: recentUserLogins
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard stats' },
      { status: 500 }
    );
  }
}
