import { NextRequest, NextResponse } from 'next/server';

import { authOptions } from '@/lib/auth-options';
import { getDb } from '@/lib/api/db';
import { getServerSession } from 'next-auth';
import { hasAdminAccess } from '@/lib/api/admin-auth';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const db = await getDb();
    
    // Get group by ID
    const group = await db.get(`
      SELECT id, name, description
      FROM Groups
      WHERE id = ?
    `, [id]);

    if (!group) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 });
    }

    // Get group users
    const users = await db.all(`
      SELECT u.id, u.name, u.email
      FROM Users u
      INNER JOIN UserGroups ug ON u.id = ug.user_id
      WHERE ug.group_id = ?
    `, [id]);

    // Get group brands
    const brands = await db.all(`
      SELECT b.id, b.name, b.slug
      FROM Brands b
      INNER JOIN BrandGroups bg ON b.id = bg.brand_id
      WHERE bg.group_id = ?
    `, [id]);

    const groupWithAssociations = {
      ...group,
      id: group.id.toString(),
      user_ids: users.map(user => user.id.toString()),
      brand_ids: brands.map(brand => brand.id.toString()),
      users: users.map(user => ({ 
        id: user.id.toString(), 
        name: user.name, 
        email: user.email 
      })),
      brands: brands.map(brand => ({ 
        id: brand.id.toString(), 
        name: brand.name, 
        slug: brand.slug 
      }))
    };

    return NextResponse.json(groupWithAssociations);
  } catch (error) {
    console.error('Error fetching group:', error);
    return NextResponse.json(
      { error: 'Failed to fetch group' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    const { name, description, user_ids = [], brand_ids = [] } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'Group name is required' },
        { status: 400 }
      );
    }

    const db = await getDb();

    // Check if group exists
    const existingGroup = await db.get('SELECT id FROM Groups WHERE id = ?', [id]);
    if (!existingGroup) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 });
    }

    // Check if another group with the same name exists (excluding current group)
    const duplicateGroup = await db.get('SELECT id FROM Groups WHERE name = ? AND id != ?', [name, id]);
    if (duplicateGroup) {
      return NextResponse.json(
        { error: 'Group with this name already exists' },
        { status: 409 }
      );
    }

    // Update group
    await db.run(
      'UPDATE Groups SET name = ?, description = ? WHERE id = ?',
      [name, description || null, id]
    );

    // Remove existing user associations
    await db.run('DELETE FROM UserGroups WHERE group_id = ?', [id]);

    // Add new user associations
    if (user_ids.length > 0) {
      for (const userId of user_ids) {
        await db.run(
          'INSERT INTO UserGroups (user_id, group_id) VALUES (?, ?)',
          [userId, id]
        );
      }
    }

    // Remove existing brand associations
    await db.run('DELETE FROM BrandGroups WHERE group_id = ?', [id]);

    // Add new brand associations
    if (brand_ids.length > 0) {
      for (const brandId of brand_ids) {
        await db.run(
          'INSERT INTO BrandGroups (brand_id, group_id) VALUES (?, ?)',
          [brandId, id]
        );
      }
    }

    return NextResponse.json({ message: 'Group updated successfully' });
  } catch (error) {
    console.error('Error updating group:', error);
    return NextResponse.json(
      { error: 'Failed to update group' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const db = await getDb();

    // Check if group exists
    const existingGroup = await db.get('SELECT id, name FROM Groups WHERE id = ?', [id]);
    if (!existingGroup) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 });
    }

    // Delete group (cascade will handle junction table cleanup)
    await db.run('DELETE FROM Groups WHERE id = ?', [id]);

    return NextResponse.json({ message: 'Group deleted successfully' });
  } catch (error) {
    console.error('Error deleting group:', error);
    return NextResponse.json(
      { error: 'Failed to delete group' },
      { status: 500 }
    );
  }
}
