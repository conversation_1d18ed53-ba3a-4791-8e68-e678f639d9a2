import { NextRequest, NextResponse } from 'next/server';

import { authOptions } from '@/lib/auth-options';
import { getDb } from '@/lib/api/db';
import { getServerSession } from 'next-auth';
import { hasAdminAccess } from '@/lib/api/admin-auth';

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const db = await getDb();
    
    // Get search parameters from URL
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    // Build the base query
    let query = `
      SELECT 
        g.id,
        g.name,
        g.description
      FROM Groups g
    `;
    let countQuery = 'SELECT COUNT(*) as total FROM Groups g';
    const queryParams: (string | number)[] = [];

    // Add search filter if provided
    if (search) {
      const searchCondition = ` WHERE (
        LOWER(g.name) LIKE LOWER(?) OR 
        LOWER(g.description) LIKE LOWER(?)
      )`;
      
      query += searchCondition;
      countQuery += searchCondition;
      
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern);
    }

    // Add ordering and pagination
    query += ` ORDER BY g.name ASC LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    // Execute both queries
    const [groups, countResult] = await Promise.all([
      db.all(query, queryParams),
      db.get(countQuery, search ? queryParams.slice(0, -2) : [])
    ]);

    const total = countResult.total;
    const totalPages = Math.ceil(total / limit);

    // Get users and brands for each group
    const groupsWithAssociations = await Promise.all(
      groups.map(async (group) => {
        // Get group users
        const users = await db.all(`
          SELECT u.id, u.name, u.email
          FROM Users u
          INNER JOIN UserGroups ug ON u.id = ug.user_id
          WHERE ug.group_id = ?
        `, [group.id]);

        // Get group brands
        const brands = await db.all(`
          SELECT b.id, b.name, b.slug
          FROM Brands b
          INNER JOIN BrandGroups bg ON b.id = bg.brand_id
          WHERE bg.group_id = ?
        `, [group.id]);

        return {
          ...group,
          id: group.id.toString(),
          user_ids: users.map(user => user.id.toString()),
          brand_ids: brands.map(brand => brand.id.toString()),
          users: users.map(user => ({ 
            id: user.id.toString(), 
            name: user.name, 
            email: user.email 
          })),
          brands: brands.map(brand => ({ 
            id: brand.id.toString(), 
            name: brand.name, 
            slug: brand.slug 
          }))
        };
      })
    );

    return NextResponse.json({
      data: groupsWithAssociations,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching groups:', error);
    return NextResponse.json(
      { error: 'Failed to fetch groups' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    const { name, description, user_ids = [], brand_ids = [] } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'Group name is required' },
        { status: 400 }
      );
    }

    const db = await getDb();

    // Check if group already exists
    const existingGroup = await db.get('SELECT id FROM Groups WHERE name = ?', [name]);
    if (existingGroup) {
      return NextResponse.json(
        { error: 'Group with this name already exists' },
        { status: 409 }
      );
    }

    // Insert group
    const result = await db.run(
      'INSERT INTO Groups (name, description) VALUES (?, ?)',
      [name, description || null]
    );

    const groupId = result.lastID;

    // Add user associations
    if (user_ids.length > 0) {
      for (const userId of user_ids) {
        await db.run(
          'INSERT INTO UserGroups (user_id, group_id) VALUES (?, ?)',
          [userId, groupId]
        );
      }
    }

    // Add brand associations
    if (brand_ids.length > 0) {
      for (const brandId of brand_ids) {
        await db.run(
          'INSERT INTO BrandGroups (brand_id, group_id) VALUES (?, ?)',
          [brandId, groupId]
        );
      }
    }

    return NextResponse.json(
      { message: 'Group created successfully', groupId },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating group:', error);
    return NextResponse.json(
      { error: 'Failed to create group' },
      { status: 500 }
    );
  }
}
