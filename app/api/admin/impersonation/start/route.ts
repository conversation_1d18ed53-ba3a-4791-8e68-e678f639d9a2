import { NextRequest, NextResponse } from 'next/server';

import { authOptions } from '@/lib/auth-options';
import { getDb } from '@/lib/api/db';
import { getServerSession } from 'next-auth';
import { hasSuperAdminAccess } from '@/lib/api/admin-auth';

export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and has Super Admin role
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!hasSuperAdminAccess(session)) {
      return NextResponse.json(
        { error: 'Super Admin role required for impersonation' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { userIdToImpersonate } = body;

    // Validate input
    if (!userIdToImpersonate) {
      return NextResponse.json(
        { error: 'Target user ID is required' },
        { status: 400 }
      );
    }

    // Convert to string for consistent handling
    const targetUserId = String(userIdToImpersonate).trim();
    
    if (!targetUserId) {
      return NextResponse.json(
        { error: 'Invalid target user ID' },
        { status: 400 }
      );
    }

    // Prevent self-impersonation
    if (session.user.id === targetUserId) {
      return NextResponse.json(
        { error: 'Cannot impersonate yourself' },
        { status: 400 }
      );
    }

    // Check if already impersonating
    if (session.user.isImpersonating) {
      return NextResponse.json(
        { error: 'Already impersonating another user. Stop current impersonation first.' },
        { status: 400 }
      );
    }

    // Validate that target user exists in database
    const db = await getDb();
    const targetUser = await db.get(
      'SELECT id, name, email FROM Users WHERE id = ?',
      [targetUserId]
    );

    if (!targetUser) {
      return NextResponse.json(
        { error: 'Target user not found' },
        { status: 404 }
      );
    }

    // Return success response
    // The actual impersonation logic is handled in the JWT callback in auth-options.ts
    // when the client calls updateSession with impersonateTargetUserId
    return NextResponse.json({
      success: true,
      message: `Impersonation started for user: ${targetUser.name || targetUser.email}`,
      targetUser: {
        id: targetUser.id,
        name: targetUser.name,
        email: targetUser.email
      }
    });

  } catch (error) {
    console.error('Error starting impersonation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}