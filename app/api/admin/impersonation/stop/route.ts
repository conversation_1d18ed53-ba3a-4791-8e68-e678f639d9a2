import { NextResponse } from 'next/server';
import { authOptions } from '@/lib/auth-options';
import { getServerSession } from 'next-auth';

export async function POST() {
  try {
    // Get the current session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if currently impersonating
    if (!session.user.isImpersonating) {
      return NextResponse.json(
        { error: 'Not currently impersonating any user' },
        { status: 400 }
      );
    }

    // Verify original user information exists
    if (!session.user.originalUser) {
      return NextResponse.json(
        { error: 'Original user information not found. Cannot revert impersonation.' },
        { status: 400 }
      );
    }

    // Return success response
    // The actual reversion logic is handled in the JWT callback in auth-options.ts
    // when the client calls updateSession with revertImpersonation: true
    return NextResponse.json({
      success: true,
      message: `Impersonation stopped. Reverted to original user: ${session.user.originalUser.name || session.user.originalUser.email}`,
      originalUser: {
        id: session.user.originalUser.id,
        name: session.user.originalUser.name,
        email: session.user.originalUser.email
      }
    });

  } catch (error) {
    console.error('Error stopping impersonation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}