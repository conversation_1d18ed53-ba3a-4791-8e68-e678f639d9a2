import { NextRequest, NextResponse } from 'next/server';

import { authOptions } from '@/lib/auth-options';
import { getDb } from '@/lib/api/db';
import { getServerSession } from 'next-auth';
import { hasAdminAccess } from '@/lib/api/admin-auth';

export async function PUT(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const params = await context.params;
    const { id } = params;
    const body = await request.json();
    const { action, resource, description, role_ids = [] } = body;

    if (!action || !resource) {
      return NextResponse.json(
        { error: 'Action and resource are required' },
        { status: 400 }
      );
    }

    const db = await getDb();

    // Check if permission exists
    const existingPermission = await db.get('SELECT id FROM Permissions WHERE id = ?', [id]);
    if (!existingPermission) {
      return NextResponse.json(
        { error: 'Permission not found' },
        { status: 404 }
      );
    }

    // Check if another permission with the same action and resource exists (excluding current permission)
    const duplicatePermission = await db.get('SELECT id FROM Permissions WHERE action = ? AND resource = ? AND id != ?', [action, resource, id]);
    if (duplicatePermission) {
      return NextResponse.json(
        { error: 'Permission with this action and resource already exists' },
        { status: 409 }
      );
    }

    // Update permission
    await db.run(
      'UPDATE Permissions SET action = ?, resource = ?, description = ? WHERE id = ?',
      [action, resource, description || null, id]
    );

    // Remove existing associations
    await db.run('DELETE FROM RolePermissions WHERE permission_id = ?', [id]);

    // Add new role associations
    if (role_ids.length > 0) {
      for (const roleId of role_ids) {
        await db.run(
          'INSERT INTO RolePermissions (role_id, permission_id) VALUES (?, ?)',
          [roleId, id]
        );
      }
    }

    return NextResponse.json({ message: 'Permission updated successfully' });
  } catch (error) {
    console.error('Error updating permission:', error);
    return NextResponse.json(
      { error: 'Failed to update permission' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const params = await context.params;
    const { id } = params;
    const db = await getDb();

    // Check if permission exists
    const existingPermission = await db.get('SELECT id FROM Permissions WHERE id = ?', [id]);
    if (!existingPermission) {
      return NextResponse.json(
        { error: 'Permission not found' },
        { status: 404 }
      );
    }

    // Remove associations first
    await db.run('DELETE FROM RolePermissions WHERE permission_id = ?', [id]);

    // Delete permission
    await db.run('DELETE FROM Permissions WHERE id = ?', [id]);

    return NextResponse.json({ message: 'Permission deleted successfully' });
  } catch (error) {
    console.error('Error deleting permission:', error);
    return NextResponse.json(
      { error: 'Failed to delete permission' },
      { status: 500 }
    );
  }
}
