import { NextRequest, NextResponse } from 'next/server';

import { authOptions } from '@/lib/auth-options';
import { getDb } from '@/lib/api/db';
import { getServerSession } from 'next-auth';
import { hasAdminAccess } from '@/lib/api/admin-auth';

export async function GET() {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const db = await getDb();
    
    // Get all permissions with their associated roles
    const permissions = await db.all(`
      SELECT 
        p.id,
        p.action,
        p.resource,
        p.description
      FROM Permissions p
      ORDER BY p.action ASC, p.resource ASC
    `);

    // Get roles for each permission
    const permissionsWithAssociations = await Promise.all(
      permissions.map(async (permission) => {
        // Get permission roles
        const roles = await db.all(`
          SELECT r.id, r.name, r.description
          FROM Roles r
          INNER JOIN RolePermissions rp ON r.id = rp.role_id
          WHERE rp.permission_id = ?
        `, [permission.id]);

        return {
          ...permission,
          id: permission.id.toString(),
          name: `${permission.action}:${permission.resource}`, // Create a display name
          role_ids: roles.map(role => role.id.toString()),
          roles: roles.map(role => ({ 
            id: role.id.toString(), 
            name: role.name, 
            description: role.description 
          }))
        };
      })
    );

    return NextResponse.json(permissionsWithAssociations);
  } catch (error) {
    console.error('Error fetching permissions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch permissions' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    const { action, resource, description, role_ids = [] } = body;

    if (!action || !resource) {
      return NextResponse.json(
        { error: 'Action and resource are required' },
        { status: 400 }
      );
    }

    const db = await getDb();

    // Check if permission already exists
    const existingPermission = await db.get('SELECT id FROM Permissions WHERE action = ? AND resource = ?', [action, resource]);
    if (existingPermission) {
      return NextResponse.json(
        { error: 'Permission with this action and resource already exists' },
        { status: 409 }
      );
    }

    // Insert permission
    const result = await db.run(
      'INSERT INTO Permissions (action, resource, description) VALUES (?, ?, ?)',
      [action, resource, description || null]
    );

    const permissionId = result.lastID;

    // Add role associations
    if (role_ids.length > 0) {
      for (const roleId of role_ids) {
        await db.run(
          'INSERT INTO RolePermissions (role_id, permission_id) VALUES (?, ?)',
          [roleId, permissionId]
        );
      }
    }

    return NextResponse.json(
      { message: 'Permission created successfully', permissionId },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating permission:', error);
    return NextResponse.json(
      { error: 'Failed to create permission' },
      { status: 500 }
    );
  }
}
