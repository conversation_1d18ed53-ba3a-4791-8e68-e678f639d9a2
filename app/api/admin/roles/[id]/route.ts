import { NextRequest, NextResponse } from 'next/server';

import { authOptions } from '@/lib/auth-options';
import { getDb } from '@/lib/api/db';
import { getServerSession } from 'next-auth';
import { hasAdminAccess } from '@/lib/api/admin-auth';

export async function PUT(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const params = await context.params;
    const { id } = params;
    const body = await request.json();
    const { name, description, permission_ids = [], user_ids = [] } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'Role name is required' },
        { status: 400 }
      );
    }

    const db = await getDb();

    // Check if role exists
    const existingRole = await db.get('SELECT id FROM Roles WHERE id = ?', [id]);
    if (!existingRole) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }

    // Check if another role with the same name exists (excluding current role)
    const duplicateRole = await db.get('SELECT id FROM Roles WHERE name = ? AND id != ?', [name, id]);
    if (duplicateRole) {
      return NextResponse.json(
        { error: 'Role with this name already exists' },
        { status: 409 }
      );
    }

    // Update role
    await db.run(
      'UPDATE Roles SET name = ?, description = ? WHERE id = ?',
      [name, description || null, id]
    );

    // Remove existing associations
    await db.run('DELETE FROM RolePermissions WHERE role_id = ?', [id]);
    await db.run('DELETE FROM UserRoles WHERE role_id = ?', [id]);

    // Add new permission associations
    if (permission_ids.length > 0) {
      for (const permissionId of permission_ids) {
        await db.run(
          'INSERT INTO RolePermissions (role_id, permission_id) VALUES (?, ?)',
          [id, permissionId]
        );
      }
    }

    // Add new user associations
    if (user_ids.length > 0) {
      for (const userId of user_ids) {
        await db.run(
          'INSERT INTO UserRoles (user_id, role_id) VALUES (?, ?)',
          [userId, id]
        );
      }
    }

    return NextResponse.json({ message: 'Role updated successfully' });
  } catch (error) {
    console.error('Error updating role:', error);
    return NextResponse.json(
      { error: 'Failed to update role' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const params = await context.params;
    const { id } = params;
    const db = await getDb();

    // Check if role exists
    const existingRole = await db.get('SELECT id FROM Roles WHERE id = ?', [id]);
    if (!existingRole) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }

    // Remove associations first
    await db.run('DELETE FROM RolePermissions WHERE role_id = ?', [id]);
    await db.run('DELETE FROM UserRoles WHERE role_id = ?', [id]);

    // Delete role
    await db.run('DELETE FROM Roles WHERE id = ?', [id]);

    return NextResponse.json({ message: 'Role deleted successfully' });
  } catch (error) {
    console.error('Error deleting role:', error);
    return NextResponse.json(
      { error: 'Failed to delete role' },
      { status: 500 }
    );
  }
}
