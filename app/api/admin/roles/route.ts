import { NextRequest, NextResponse } from 'next/server';

import { authOptions } from '@/lib/auth-options';
import { getDb } from '@/lib/api/db';
import { getServerSession } from 'next-auth';
import { hasAdminAccess } from '@/lib/api/admin-auth';

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const db = await getDb();
    
    // Get search parameters from URL
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    // Build the base query
    let query = `
      SELECT 
        r.id,
        r.name,
        r.description
      FROM Roles r
    `;
    let countQuery = 'SELECT COUNT(*) as total FROM Roles r';
    const queryParams: (string | number)[] = [];

    // Add search filter if provided
    if (search) {
      const searchCondition = ` WHERE (
        LOWER(r.name) LIKE LOWER(?) OR 
        LOWER(r.description) LIKE LOWER(?)
      )`;
      
      query += searchCondition;
      countQuery += searchCondition;
      
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern);
    }

    // Add ordering and pagination
    query += ` ORDER BY r.name ASC LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    // Execute both queries
    const [roles, countResult] = await Promise.all([
      db.all(query, queryParams),
      db.get(countQuery, search ? queryParams.slice(0, -2) : [])
    ]);

    const total = countResult.total;
    const totalPages = Math.ceil(total / limit);

    // Get permissions and users for each role
    const rolesWithAssociations = await Promise.all(
      roles.map(async (role) => {
        // Get role permissions
        const permissions = await db.all(`
          SELECT p.id, p.action, p.resource, p.description
          FROM Permissions p
          INNER JOIN RolePermissions rp ON p.id = rp.permission_id
          WHERE rp.role_id = ?
        `, [role.id]);

        // Get role users
        const users = await db.all(`
          SELECT u.id, u.name, u.email
          FROM Users u
          INNER JOIN UserRoles ur ON u.id = ur.user_id
          WHERE ur.role_id = ?
        `, [role.id]);

        return {
          ...role,
          id: role.id.toString(),
          permission_ids: permissions.map(permission => permission.id.toString()),
          user_ids: users.map(user => user.id.toString()),
          permissions: permissions.map(permission => ({ 
            id: permission.id.toString(), 
            name: `${permission.action} ${permission.resource}`, 
            description: permission.description 
          })),
          users: users.map(user => ({ 
            id: user.id.toString(), 
            name: user.name, 
            email: user.email 
          }))
        };
      })
    );

    return NextResponse.json({
      data: rolesWithAssociations,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching roles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch roles' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    const { name, description, permission_ids = [], user_ids = [] } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'Role name is required' },
        { status: 400 }
      );
    }

    const db = await getDb();

    // Check if role already exists
    const existingRole = await db.get('SELECT id FROM Roles WHERE name = ?', [name]);
    if (existingRole) {
      return NextResponse.json(
        { error: 'Role with this name already exists' },
        { status: 409 }
      );
    }

    // Insert role
    const result = await db.run(
      'INSERT INTO Roles (name, description) VALUES (?, ?)',
      [name, description || null]
    );

    const roleId = result.lastID;

    // Add permission associations
    if (permission_ids.length > 0) {
      for (const permissionId of permission_ids) {
        await db.run(
          'INSERT INTO RolePermissions (role_id, permission_id) VALUES (?, ?)',
          [roleId, permissionId]
        );
      }
    }

    // Add user associations
    if (user_ids.length > 0) {
      for (const userId of user_ids) {
        await db.run(
          'INSERT INTO UserRoles (user_id, role_id) VALUES (?, ?)',
          [userId, roleId]
        );
      }
    }

    return NextResponse.json(
      { message: 'Role created successfully', roleId },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating role:', error);
    return NextResponse.json(
      { error: 'Failed to create role' },
      { status: 500 }
    );
  }
}
