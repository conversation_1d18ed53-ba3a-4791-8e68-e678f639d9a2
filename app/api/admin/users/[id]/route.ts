import { NextRequest, NextResponse } from 'next/server';

import { authOptions } from '@/lib/auth-options';
import bcrypt from 'bcrypt';
import { getDb } from '@/lib/api/db';
import { getServerSession } from 'next-auth';
import { hasAdminAccess } from '@/lib/api/admin-auth';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const db = await getDb();
    
    // Get user with their roles and groups
    const user = await db.get(`
      SELECT 
        u.id,
        u.name,
        u.email,
        u.createdAt,
        u.updatedAt
      FROM Users u
      WHERE u.id = ?
    `, [id]);

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get user roles
    const roles = await db.all(`
      SELECT r.id, r.name
      FROM Roles r
      INNER JOIN UserRoles ur ON r.id = ur.role_id
      WHERE ur.user_id = ?
    `, [id]);

    // Get user groups
    const groups = await db.all(`
      SELECT g.id, g.name
      FROM Groups g
      INNER JOIN UserGroups ug ON g.id = ug.group_id
      WHERE ug.user_id = ?
    `, [id]);

    const userWithAssociations = {
      ...user,
      roles: roles.map(role => ({ id: role.id.toString(), name: role.name })),
      groups: groups.map(group => ({ id: group.id.toString(), name: group.name }))
    };

    return NextResponse.json(userWithAssociations);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    const { name, email, password, roleIds = [], groupIds = [] } = body;

    if (!name || !email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      );
    }

    const db = await getDb();

    // Check if user exists
    const existingUser = await db.get('SELECT id FROM Users WHERE id = ?', [id]);
    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if another user with the same email exists (excluding current user)
    const duplicateUser = await db.get('SELECT id FROM Users WHERE email = ? AND id != ?', [email, id]);
    if (duplicateUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Update user basic info
    if (password) {
      // Update with new password
      const passwordHash = await bcrypt.hash(password, 10);
      await db.run(
        'UPDATE Users SET name = ?, email = ?, password_hash = ? WHERE id = ?',
        [name, email, passwordHash, id]
      );
    } else {
      // Update without changing password
      await db.run(
        'UPDATE Users SET name = ?, email = ? WHERE id = ?',
        [name, email, id]
      );
    }

    // Remove existing associations
    await db.run('DELETE FROM UserRoles WHERE user_id = ?', [id]);
    await db.run('DELETE FROM UserGroups WHERE user_id = ?', [id]);

    // Add new role associations
    if (roleIds.length > 0) {
      for (const roleId of roleIds) {
        await db.run(
          'INSERT INTO UserRoles (user_id, role_id) VALUES (?, ?)',
          [id, roleId]
        );
      }
    }

    // Add new group associations
    if (groupIds.length > 0) {
      for (const groupId of groupIds) {
        await db.run(
          'INSERT INTO UserGroups (user_id, group_id) VALUES (?, ?)',
          [id, groupId]
        );
      }
    }

    return NextResponse.json({ message: 'User updated successfully' });
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const db = await getDb();

    // Check if user exists
    const existingUser = await db.get('SELECT id FROM Users WHERE id = ?', [id]);
    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Remove associations first
    await db.run('DELETE FROM UserRoles WHERE user_id = ?', [id]);
    await db.run('DELETE FROM UserGroups WHERE user_id = ?', [id]);

    // Delete user
    await db.run('DELETE FROM Users WHERE id = ?', [id]);

    return NextResponse.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
