import { NextRequest, NextResponse } from 'next/server';

import { authOptions } from '@/lib/auth-options';
import bcrypt from 'bcrypt';
import { getDb } from '@/lib/api/db';
import { getServerSession } from 'next-auth';
import { hasAdminAccess } from '@/lib/api/admin-auth';

export async function GET() {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const db = await getDb();
    
    // Get all users with their roles and groups
    const users = await db.all(`
      SELECT 
        u.id,
        u.name,
        u.email,
        u.createdAt,
        u.updatedAt
      FROM Users u
      ORDER BY u.name ASC
    `);

    // Get roles for each user
    const usersWithAssociations = await Promise.all(
      users.map(async (user) => {
        // Get user roles
        const roles = await db.all(`
          SELECT r.id, r.name
          FROM Roles r
          INNER JOIN UserRoles ur ON r.id = ur.role_id
          WHERE ur.user_id = ?
        `, [user.id]);

        // Get user groups
        const groups = await db.all(`
          SELECT g.id, g.name
          FROM Groups g
          INNER JOIN UserGroups ug ON g.id = ug.group_id
          WHERE ug.user_id = ?
        `, [user.id]);

        return {
          ...user,
          roles: roles.map(role => ({ id: role.id.toString(), name: role.name })),
          groups: groups.map(group => ({ id: group.id.toString(), name: group.name }))
        };
      })
    );

    return NextResponse.json(usersWithAssociations);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!hasAdminAccess(session)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    const { name, email, password, roleIds = [], groupIds = [] } = body;

    if (!name || !email || !password) {
      return NextResponse.json(
        { error: 'Name, email, and password are required' },
        { status: 400 }
      );
    }

    const db = await getDb();

    // Check if user already exists
    const existingUser = await db.get('SELECT id FROM Users WHERE email = ?', [email]);
    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 10);

    // Insert user
    const result = await db.run(
      'INSERT INTO Users (name, email, password_hash) VALUES (?, ?, ?)',
      [name, email, passwordHash]
    );

    const userId = result.lastID;

    // Add role associations
    if (roleIds.length > 0) {
      for (const roleId of roleIds) {
        await db.run(
          'INSERT INTO UserRoles (user_id, role_id) VALUES (?, ?)',
          [userId, roleId]
        );
      }
    }

    // Add group associations
    if (groupIds.length > 0) {
      for (const groupId of groupIds) {
        await db.run(
          'INSERT INTO UserGroups (user_id, group_id) VALUES (?, ?)',
          [userId, groupId]
        );
      }
    }

    return NextResponse.json(
      { message: 'User created successfully', userId },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}
