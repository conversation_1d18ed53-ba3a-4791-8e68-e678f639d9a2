import { NextRequest, NextResponse } from 'next/server';

import { getRedshiftPool } from '@/lib/api/redshift';
import { getToken } from 'next-auth/jwt';

// Types
interface AIAssistantRequest {
  message: string;
  isInitialLoad?: boolean; // Flag to indicate if this is the initial load
}

interface ChartDataPoint {
  date: string;
  [key: string]: number | string | null;
}

interface ChartData {
  type: 'line' | 'bar' | 'area' | 'pie';
  title: string;
  data: ChartDataPoint[];
  xAxis: string;
  yAxis: string[];
  colors?: string[];
}

interface AIAssistantResponse {
  message: string;
  charts?: ChartData[];
  error?: string;
}

// Helper function to extract date range from user query
function extractDateRange(query: string): { startDate?: string; endDate?: string; timeRange?: string } {
  // Common time range patterns
  const timeRangePatterns = [
    { regex: /last\s+7\s+days/i, timeRange: 'last7days' },
    { regex: /last\s+14\s+days/i, timeRange: 'last14days' },
    { regex: /last\s+30\s+days/i, timeRange: 'last30days' },
    { regex: /last\s+60\s+days/i, timeRange: 'last60days' },
    { regex: /last\s+90\s+days/i, timeRange: 'last90days' },
    { regex: /month\s+to\s+date/i, timeRange: 'monthToDate' },
    { regex: /last\s+month/i, timeRange: 'lastMonth' },
    { regex: /last\s+2\s+months/i, timeRange: 'last2months' },
    { regex: /last\s+3\s+months/i, timeRange: 'last3months' },
    { regex: /last\s+6\s+months/i, timeRange: 'last6months' },
    { regex: /last\s+12\s+months/i, timeRange: 'last12months' },
    { regex: /last\s+year/i, timeRange: 'last12months' },
    { regex: /year\s+to\s+date/i, timeRange: 'yearToDate' },
    { regex: /last\s+quarter/i, timeRange: 'lastQuarter' },
  ];

  // Check for time range patterns
  for (const pattern of timeRangePatterns) {
    if (pattern.regex.test(query)) {
      return { timeRange: pattern.timeRange };
    }
  }

  // Check for specific date ranges (YYYY-MM-DD format)
  const dateRangeRegex = /from\s+(\d{4}-\d{2}-\d{2})\s+to\s+(\d{4}-\d{2}-\d{2})/i;
  const dateRangeMatch = query.match(dateRangeRegex);
  if (dateRangeMatch) {
    return {
      startDate: dateRangeMatch[1],
      endDate: dateRangeMatch[2]
    };
  }

  // Default to last 30 days if no date range is specified
  return { timeRange: 'last30days' };
}

// Helper function to extract KPIs from user query
function extractKPIs(query: string): string[] {
  const kpiPatterns = [
    { regex: /gross\s+revenue/i, kpi: 'Gross Revenue' },
    { regex: /net\s+revenue/i, kpi: 'Net Revenue' },
    { regex: /gross\s+margin/i, kpi: 'Gross Margin' },
    { regex: /\badspend\b/i, kpi: 'Adspend' },
    { regex: /contribution\s+margin/i, kpi: 'Contribution Margin' },
    { regex: /\% gross margin/i, kpi: '% Gross Margin' },
    { regex: /\% contribution margin/i, kpi: '% Contribution Margin' },
    { regex: /\% adspend/i, kpi: '% Adspend' },
    { regex: /landed\s+cost/i, kpi: 'Landed Cost' },
    { regex: /fulfillment\s+cost/i, kpi: 'Fulfillment Cost' },
    { regex: /transaction\s+cost/i, kpi: 'Transaction Cost' },
    { regex: /\bdiscount\b/i, kpi: 'Discount' },
    { regex: /\brefund\b/i, kpi: 'Refund' },
    { regex: /\% landed cost/i, kpi: '% Landed Cost' },
    { regex: /\% fulfillment cost/i, kpi: '% Fulfillment Cost' },
    { regex: /\% transaction cost/i, kpi: '% Transaction Cost' },
    { regex: /\% discount/i, kpi: '% Discount' },
    { regex: /\% refund/i, kpi: '% Refund' },
    { regex: /\bacos\b/i, kpi: 'ACOS' },
    { regex: /\btacos\b/i, kpi: 'TACOS' },
    { regex: /\btcac\b/i, kpi: 'TCAC' },
    { regex: /website\s+traffic/i, kpi: 'Website Traffic' },
    { regex: /conversion\s+rate/i, kpi: 'Conversion Rate' },
    { regex: /organic\s+traffic/i, kpi: 'Organic Traffic' },
    { regex: /paid\s+traffic/i, kpi: 'Paid Traffic' },
    // Add more KPI patterns as needed
  ];

  // Check for KPIs in the query
  const foundKPIs: string[] = [];
  for (const pattern of kpiPatterns) {
    if (pattern.regex.test(query)) {
      foundKPIs.push(pattern.kpi);
    }
  }

  // Check for general KPI requests
  if (
    /kpis|metrics|performance|results|numbers|stats|statistics/i.test(query) &&
    foundKPIs.length === 0
  ) {
    // Return common KPIs if no specific ones were mentioned
    return ['Gross Revenue', 'Net Revenue', 'Gross Margin', 'Adspend', 'Contribution Margin'];
  }

  return foundKPIs;
}

// Helper function to extract brands from user query
function extractBrands(query: string, availableBrands: string[]): string[] {
  const foundBrands: string[] = [];

  // Check for each available brand in the query
  for (const brand of availableBrands) {
    const brandRegex = new RegExp(`\\b${brand}\\b`, 'i');
    if (brandRegex.test(query)) {
      foundBrands.push(brand);
    }
  }

  return foundBrands;
}

// Helper function to extract grouping information from user query
function extractGrouping(query: string): { 
  groupByTime?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  groupByDimension?: 'brand' | 'brandGroup';
} {
  const result: {
    groupByTime?: 'day' | 'week' | 'month' | 'quarter' | 'year';
    groupByDimension?: 'brand' | 'brandGroup';
  } = {};

  // Check for time grouping
  if (/by\s+day/i.test(query)) {
    result.groupByTime = 'day';
  } else if (/by\s+week/i.test(query)) {
    result.groupByTime = 'week';
  } else if (/by\s+month/i.test(query)) {
    result.groupByTime = 'month';
  } else if (/by\s+quarter/i.test(query)) {
    result.groupByTime = 'quarter';
  } else if (/by\s+year/i.test(query)) {
    result.groupByTime = 'year';
  } else {
    // Default to month if comparing over time
    if (/over\s+time|trend|trends|historical|history/i.test(query)) {
      result.groupByTime = 'month';
    }
  }

  // Check for dimension grouping
  if (/by\s+brand|compare\s+brands|across\s+brands/i.test(query)) {
    result.groupByDimension = 'brand';
  } else if (/by\s+brand\s+group|compare\s+brand\s+groups|across\s+brand\s+groups/i.test(query)) {
    result.groupByDimension = 'brandGroup';
  }

  return result;
}

// Helper function to determine chart type based on query and data
function determineChartType(query: string, kpis: string[], hasGroupByDimension: boolean): 'line' | 'bar' | 'area' | 'pie' {
  // Default to line chart for time series data
  let chartType: 'line' | 'bar' | 'area' | 'pie' = 'line';

  // If comparing multiple KPIs over time, use line chart
  if (kpis.length > 1 && !hasGroupByDimension && /over\s+time|trend|trends|historical|history/i.test(query)) {
    chartType = 'line';
  }
  // If comparing brands or brand groups, use bar chart
  else if (hasGroupByDimension) {
    chartType = 'bar';
  }
  // If looking at breakdown or composition, use pie chart
  else if (/breakdown|composition|distribution|split/i.test(query)) {
    chartType = 'pie';
  }
  // If looking at growth or cumulative metrics, use area chart
  else if (/growth|cumulative|accumulated|total/i.test(query)) {
    chartType = 'area';
  }

  return chartType;
}

// KPI definitions with descriptions
const KPI_DEFINITIONS: Record<string, string> = {
  'Gross Revenue': 'Total revenue before any deductions, representing the total sales value.',
  'Net Revenue': 'Revenue after deductions like discounts, returns, and allowances.',
  'Gross Margin': 'The difference between Net Revenue and the cost of goods sold (COGS).',
  'Adspend': 'Total amount spent on advertising and marketing campaigns.',
  'Contribution Margin': 'Gross Margin minus Adspend, showing profit after marketing costs.',
  '% Gross Margin': 'Gross Margin as a percentage of Net Revenue.',
  '% Contribution Margin': 'Contribution Margin as a percentage of Net Revenue.',
  '% Adspend': 'Adspend as a percentage of Net Revenue.',
  'Landed Cost': 'The total cost of a product delivered to its destination, including shipping and import duties.',
  'Fulfillment Cost': 'Costs associated with storing, processing, and delivering orders.',
  'Transaction Cost': 'Costs associated with payment processing and financial transactions.',
  'Discount': 'Reduction in price offered to customers.',
  'Refund': 'Money returned to customers for returned or unsatisfactory products.',
  '% Landed Cost': 'Landed Cost as a percentage of Net Revenue.',
  '% Fulfillment Cost': 'Fulfillment Cost as a percentage of Net Revenue.',
  '% Transaction Cost': 'Transaction Cost as a percentage of Net Revenue.',
  '% Discount': 'Discount as a percentage of Gross Revenue.',
  '% Refund': 'Refund as a percentage of Gross Revenue.',
  'ACOS': 'Advertising Cost of Sale - Adspend divided by attributed sales.',
  'TACOS': 'Total Advertising Cost of Sale - Adspend divided by total sales.',
  'TCAC': 'Total Customer Acquisition Cost - Total cost to acquire a new customer.',
  'Website Traffic': 'Number of visitors to the website.',
  'Conversion Rate': 'Percentage of website visitors who make a purchase.',
  'Organic Traffic': 'Website visitors who arrive through unpaid search results.',
  'Paid Traffic': 'Website visitors who arrive through paid advertising.',
};

// Quarter definitions
const QUARTER_DEFINITIONS: Record<string, { months: string[], description: string }> = {
  'Q1': { 
    months: ['January', 'February', 'March'], 
    description: 'First quarter (January-March)'
  },
  'Q2': { 
    months: ['April', 'May', 'June'], 
    description: 'Second quarter (April-June)'
  },
  'Q3': { 
    months: ['July', 'August', 'September'], 
    description: 'Third quarter (July-September)'
  },
  'Q4': { 
    months: ['October', 'November', 'December'], 
    description: 'Fourth quarter (October-December)'
  }
};

// Month definitions with number of days
const MONTH_DEFINITIONS: Record<string, { days: number, quarter: string }> = {
  'January': { days: 31, quarter: 'Q1' },
  'February': { days: 28, quarter: 'Q1' }, // 29 in leap years
  'March': { days: 31, quarter: 'Q1' },
  'April': { days: 30, quarter: 'Q2' },
  'May': { days: 31, quarter: 'Q2' },
  'June': { days: 30, quarter: 'Q2' },
  'July': { days: 31, quarter: 'Q3' },
  'August': { days: 31, quarter: 'Q3' },
  'September': { days: 30, quarter: 'Q3' },
  'October': { days: 31, quarter: 'Q4' },
  'November': { days: 30, quarter: 'Q4' },
  'December': { days: 31, quarter: 'Q4' }
};

// Helper function to format data for charts
interface KpiTimeSeriesPoint {
  date: string;
  value: number | null;
}

// Simple KPI data (not grouped by dimension)
interface KpiData {
  summary: {
    value: number | null;
  };
  timeSeries: KpiTimeSeriesPoint[];
}

// KPI data grouped by dimension
interface DimensionKpiData {
  [dimension: string]: {
    summary: {
      value: number | null;
    };
    timeSeries: KpiTimeSeriesPoint[];
  };
}

// Response when NOT grouping by dimension
interface SimpleKpiResponse {
  [kpi: string]: KpiData;
}

// Response WHEN grouping by dimension
interface GroupedKpiResponse {
  [kpi: string]: DimensionKpiData;
}

// Union type for the final response
type FlexibleKpiResponse = SimpleKpiResponse | GroupedKpiResponse;

// Type guard to check if response is grouped by dimension
function isGroupedResponse(data: FlexibleKpiResponse, kpi: string): data is GroupedKpiResponse {
  if (!data[kpi]) return false;
  // If it has a timeSeries property directly, it's a simple response
  // Otherwise, it's a grouped response
  return !('timeSeries' in data[kpi]);
}

function formatChartData(data: FlexibleKpiResponse, kpis: string[], groupByDimension?: 'brand' | 'brandGroup'): ChartData[] {
  const charts: ChartData[] = [];

  if (!data) {
    return charts;
  }

  // If we're grouping by dimension (brand or brandGroup)
  if (groupByDimension) {
    // Create one chart per KPI
    for (const kpi of kpis) {
      if (data[kpi] && isGroupedResponse(data, kpi)) {
        const kpiData = data[kpi] as DimensionKpiData;
        const dimensions = Object.keys(kpiData);
        
        // Prepare data for the chart
        const formattedData: ChartDataPoint[] = [];
        const allDates = new Set<string>();
        
        // Collect all dates across all dimensions
        dimensions.forEach(dimension => {
          if (kpiData[dimension] && kpiData[dimension].timeSeries) {
            kpiData[dimension].timeSeries.forEach((point: KpiTimeSeriesPoint) => {
              allDates.add(point.date);
            });
          }
        });
        
        // Sort dates
        const sortedDates = Array.from(allDates).sort();
        
        // Create data points for each date
        sortedDates.forEach(date => {
          const dataPoint: ChartDataPoint = { date };
          
          dimensions.forEach(dimension => {
            if (kpiData[dimension] && kpiData[dimension].timeSeries) {
              const timeSeriesPoint = kpiData[dimension].timeSeries.find((p: KpiTimeSeriesPoint) => p.date === date);
              dataPoint[dimension] = timeSeriesPoint ? timeSeriesPoint.value : null;
            } else {
              dataPoint[dimension] = null;
            }
          });
          
          formattedData.push(dataPoint);
        });
        
        charts.push({
          type: 'line', // Default to line for time series
          title: kpi,
          data: formattedData,
          xAxis: 'date',
          yAxis: dimensions
        });
      }
    }
  } else {
    // Not grouping by dimension, create one chart with multiple KPIs
    const formattedData: ChartDataPoint[] = [];
    const allDates = new Set<string>();
    
    // Collect all dates across all KPIs
    kpis.forEach(kpi => {
      if (data[kpi] && !isGroupedResponse(data, kpi)) {
        const kpiData = data[kpi] as KpiData;
        kpiData.timeSeries.forEach((point: KpiTimeSeriesPoint) => {
          allDates.add(point.date);
        });
      }
    });
    
    // Sort dates
    const sortedDates = Array.from(allDates).sort();
    
    // Create data points for each date
    sortedDates.forEach(date => {
      const dataPoint: ChartDataPoint = { date };
      
      kpis.forEach(kpi => {
        if (data[kpi] && !isGroupedResponse(data, kpi)) {
          const kpiData = data[kpi] as KpiData;
          const timeSeriesPoint = kpiData.timeSeries.find((p: KpiTimeSeriesPoint) => p.date === date);
          dataPoint[kpi] = timeSeriesPoint ? timeSeriesPoint.value : null;
        } else {
          dataPoint[kpi] = null;
        }
      });
      
      formattedData.push(dataPoint);
    });
    
    charts.push({
      type: 'line', // Default to line for time series
      title: 'KPI Comparison',
      data: formattedData,
      xAxis: 'date',
      yAxis: kpis.filter(kpi => data[kpi] && !isGroupedResponse(data, kpi)) // Only include KPIs that have data
    });
  }

  return charts;
}

// Define types for database rows and query parameters
interface DbRow {
  [key: string]: string | number | boolean | null;
}

// Define specific row types for better type safety
interface BrandRow {
  name: string;
  brand_group: string;
  [key: string]: string | number | boolean | null;
}

interface SalesChannelRow {
  sales_channel_type: string;
  [key: string]: string | number | boolean | null;
}

interface CountryRow {
  country_name: string;
  has_sales: boolean;
  [key: string]: string | number | boolean | null;
}

// Define types for the client and token
interface RedshiftClient {
  query: <T = DbRow>(query: string, params?: (string | number | null)[]) => Promise<{ rows: T[] }>;
  release: () => void;
}

interface AuthToken {
  sub?: string;
  id?: string;
  [key: string]: unknown;
}

// Function to generate context information for the AI assistant
async function generateContextInformation(client: RedshiftClient, token: AuthToken): Promise<string> {
  try {
    // Fetch available brands for the current user
    const brandsQuery = `
      SELECT b.name, b."group" as brand_group
      FROM dwh_ai.ai_reporting_brands b
      JOIN dwh_ai.user_brand_access uba ON b.id = uba.brand_id
      WHERE b.stage = 'Selling Actively'
      AND uba.user_id = $1
      ORDER BY b.name;
    `;

    // Get user ID from token
    const userId = token.sub || token.id || '';
    const brandsResult = await client.query<BrandRow>(brandsQuery, [userId]);
    const availableBrands = brandsResult.rows;
    
    // Group brands by brand group
    const brandsByGroup: Record<string, string[]> = {};
    availableBrands.forEach(brand => {
      if (!brandsByGroup[brand.brand_group]) {
        brandsByGroup[brand.brand_group] = [];
      }
      brandsByGroup[brand.brand_group].push(brand.name);
    });
    
    // Fetch available sales channels
    const salesChannelsQuery = `
      SELECT DISTINCT sales_channel_type
      FROM dwh_ai.ai_reporting_ds_kpis
      WHERE sales_channel_type IS NOT NULL
      ORDER BY sales_channel_type;
    `;
    
    const salesChannelsResult = await client.query<SalesChannelRow>(salesChannelsQuery);
    const availableSalesChannels = salesChannelsResult.rows.map(row => row.sales_channel_type);
    
    // Fetch available countries
    const countriesQuery = `
      SELECT DISTINCT country_name, COUNT(*) > 0 as has_sales
      FROM dwh_ai.ai_reporting_ds_kpis
      WHERE country_name IS NOT NULL
      GROUP BY country_name
      ORDER BY country_name;
    `;
    
    const countriesResult = await client.query<CountryRow>(countriesQuery);
    const availableCountries = countriesResult.rows.map(row => ({
      name: row.country_name,
      hasSales: row.has_sales
    }));
    
    // Get current date information
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth(); // 0-11
    const monthNames = Object.keys(MONTH_DEFINITIONS);
    const currentMonthName = monthNames[currentMonth];
    const currentQuarter = MONTH_DEFINITIONS[currentMonthName].quarter;
    
    // Build context information
    const context = `
# NOLK Business Intelligence Context

## Available Brands
${Object.entries(brandsByGroup).map(([group, brands]) => 
  `### ${group}\n${brands.map(b => `- ${b}`).join('\n')}`
).join('\n\n')}

## Available KPIs
${Object.entries(KPI_DEFINITIONS).map(([kpi, description]) => 
  `- **${kpi}**: ${description}`
).join('\n')}

## Date Context
- Current date: ${now.toISOString().split('T')[0]}
- Current year: ${currentYear}
- Current month: ${currentMonthName}
- Current quarter: ${currentQuarter}

### Quarters
${Object.entries(QUARTER_DEFINITIONS).map(([quarter, { description, months }]) => 
  `- **${quarter}**: ${description} (${months.join(', ')})`
).join('\n')}

### Months
${Object.entries(MONTH_DEFINITIONS).map(([month, { days, quarter }]) => 
  `- **${month}**: ${days} days, part of ${quarter}`
).join('\n')}

## Available Sales Channels
${availableSalesChannels.map((channel: string) => `- ${channel}`).join('\n')}

## Available Countries
${availableCountries.filter((country: { name: string, hasSales: boolean }) => country.hasSales).map((country: { name: string }) => `- ${country.name}`).join('\n')}

## Common Time Periods
- "Last 7 days": The 7 days preceding today
- "Last 14 days": The 14 days preceding today
- "Last 30 days": The 30 days preceding today
- "Last 60 days": The 60 days preceding today
- "Last 90 days": The 90 days preceding today
- "Month to date": From the beginning of the current month to today
- "Year to date": From the beginning of the current year to today
- "Last month": The entire previous month
- "Last 2 months": The previous 2 months
- "Last 3 months": The previous 3 months
- "Last 6 months": The previous 6 months
- "Last 12 months": The previous 12 months
- "Last quarter": The entire previous quarter

## Grouping Options
- **day**: Group data by day
- **week**: Group data by week
- **month**: Group data by month
- **quarter**: Group data by quarter
- **year**: Group data by year

## Filtering Options
- **brands**: Filter by specific brand names
- **brandGroups**: Filter by brand groups
- **salesChannels**: Filter by sales channels
- **countryNames**: Filter by countries

## Database Structure
The data is stored in Redshift with the following schema:
- KPI data is stored in the dwh_ai.ai_reporting_ds_kpis table
- Brand information is stored in the dwh_ai.ai_reporting_brands table
- Data can be filtered by brand, date range, sales channel, and country
- Data can be grouped by time (day, week, month, quarter, year) and dimension (brand, brandGroup)

## Example Queries
- "Show me the gross revenue for the last 30 days"
- "Compare net revenue across brands for the last quarter"
- "What was our contribution margin trend over the last 6 months?"
- "Show me the adspend breakdown by month for this year"
- "Show me the gross margin by week for the last 60 days"
- "Compare net revenue for Brand A and Brand B by quarter for the last year"
- "What's the conversion rate trend by month for the last 6 months?"
- "Show me the sales breakdown by country for the last quarter"
- "Compare adspend across sales channels for the last 3 months"
- "What's our gross margin percentage by brand group for this year?"
`;

    return context;
  } catch (error) {
    console.error('[AI Assistant API] Error generating context information:', error);
    return "Error generating context information.";
  }
}

// Function to analyze user query with Claude AI
async function analyzeUserQuery(
  userMessage: string, 
  contextInfo: string
): Promise<{
  dateRange: { startDate?: string; endDate?: string; timeRange?: string };
  kpis: string[];
  brands: string[];
  grouping: { 
    groupByTime?: 'day' | 'week' | 'month' | 'quarter' | 'year';
    groupByDimension?: 'brand' | 'brandGroup';
  };
  needsTopBrands?: boolean;
  topBrandsCount?: number;
  needsAdditionalData?: boolean;
  additionalDataQuery?: string;
}> {
  try {
    if (!process.env.ANTHROPIC_API_KEY) {
      throw new Error('ANTHROPIC_API_KEY is not defined in environment variables');
    }

    const claudeModel = process.env.CLAUDE_MODEL || 'claude-3-7-sonnet-20250219';
    console.log(`[AI Assistant API] Using Claude model for query analysis: ${claudeModel}`);

    // Prepare the system prompt for query analysis
    const systemPrompt = `
You are an AI business intelligence assistant for NOLK, an e-commerce company.
Your task is to analyze the user's query and extract structured information to help retrieve the right data.
DO NOT answer the user's question directly - just analyze what they're asking for.

Here is important context about the business data:
${contextInfo}

Based on the user's query, extract the following information in JSON format:
1. dateRange: Identify the time period they're asking about
   - If they mention a specific date range, extract startDate and endDate in YYYY-MM-DD format
   - If they mention a relative time period like "last 30 days", "last quarter", etc., extract the timeRange
2. kpis: Identify the KPIs they're asking about (e.g., "Gross Revenue", "Net Revenue", etc.)
3. brands: Identify specific brands they're asking about
4. grouping: Identify how they want to group the data
   - groupByTime: 'day', 'week', 'month', 'quarter', or 'year'
   - groupByDimension: 'brand' or 'brandGroup' if they want to compare across brands/groups
5. needsTopBrands: true if they're asking about "top brands" and we need to query for that first
6. topBrandsCount: The number of top brands they're asking for (e.g., "top 5 brands" would be 5)
7. needsAdditionalData: true if we need to query for additional data before answering
8. additionalDataQuery: Description of what additional data we need to query for

IMPORTANT: Your response must be valid JSON that can be parsed. Do not include any explanations outside the JSON.
`;

    // Call the Anthropic API
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.ANTHROPIC_API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: claudeModel,
        system: systemPrompt,
        messages: [
          {
            role: 'user',
            content: userMessage
          }
        ],
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Claude API error: ${JSON.stringify(errorData)}`);
    }

    const claudeResponse = await response.json();
    const analysisText = claudeResponse.content[0].text;
    
    // Parse the JSON response
    try {
      // Extract JSON from the response (in case Claude added any text around it)
      const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Could not extract JSON from Claude response');
      }
      
      const jsonStr = jsonMatch[0];
      const analysis = JSON.parse(jsonStr);
      
      console.log('[AI Assistant API] Query analysis result:', analysis);
      return analysis;
    } catch (parseError) {
      console.error('[AI Assistant API] Error parsing Claude analysis:', parseError);
      console.error('[AI Assistant API] Raw Claude response:', analysisText);
      
      // Fall back to basic extraction methods
      return {
        dateRange: extractDateRange(userMessage),
        kpis: extractKPIs(userMessage),
        brands: [], // Will be populated later
        grouping: extractGrouping(userMessage),
        needsTopBrands: userMessage.toLowerCase().includes('top') && userMessage.toLowerCase().includes('brand'),
        topBrandsCount: 5, // Default to top 5 if not specified
        needsAdditionalData: false
      };
    }
  } catch (error) {
    console.error('[AI Assistant API] Error analyzing user query with Claude AI:', error);
    
    // Fall back to basic extraction methods
    return {
      dateRange: extractDateRange(userMessage),
      kpis: extractKPIs(userMessage),
      brands: [], // Will be populated later
      grouping: extractGrouping(userMessage),
      needsTopBrands: userMessage.toLowerCase().includes('top') && userMessage.toLowerCase().includes('brand'),
      topBrandsCount: 5, // Default to top 5 if not specified
      needsAdditionalData: false
    };
  }
}

// Helper function to check if a date range is large (more than 90 days)
function isLargeDateRange(dateRange: { startDate?: string; endDate?: string; timeRange?: string }): boolean {
  // If timeRange is specified, check against known large ranges
  if (dateRange.timeRange) {
    return ['last6months', 'last12months', 'yearToDate'].includes(dateRange.timeRange);
  }
  
  // If startDate and endDate are specified, calculate the difference
  if (dateRange.startDate && dateRange.endDate) {
    const start = new Date(dateRange.startDate);
    const end = new Date(dateRange.endDate);
    const diffDays = Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    return diffDays > 90;
  }
  
  // Default to false if we can't determine
  return false;
}

// Helper function to check if a date range is medium (between 30 and 90 days)
function isMediumDateRange(dateRange: { startDate?: string; endDate?: string; timeRange?: string }): boolean {
  // If timeRange is specified, check against known medium ranges
  if (dateRange.timeRange) {
    return ['last30days', 'last60days', 'last90days', 'lastMonth', 'last2months', 'last3months', 'lastQuarter'].includes(dateRange.timeRange);
  }
  
  // If startDate and endDate are specified, calculate the difference
  if (dateRange.startDate && dateRange.endDate) {
    const start = new Date(dateRange.startDate);
    const end = new Date(dateRange.endDate);
    const diffDays = Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    return diffDays > 30 && diffDays <= 90;
  }
  
  // Default to false if we can't determine
  return false;
}

// Helper function to convert timeRange to startDate and endDate
function convertTimeRangeToDates(timeRange: string | undefined): { startDate?: string; endDate?: string } {
  if (!timeRange) return { startDate: undefined, endDate: undefined };

  const today = new Date();
  let startDateInstance: Date;
  let endDateInstance: Date = new Date(today); // endDate is often today

  // Helper to format date as YYYY-MM-DD
  const formatDate = (date: Date): string => {
    // Adjust for timezone to ensure date isn't skewed
    const offset = date.getTimezoneOffset();
    const adjustedDate = new Date(date.getTime() - (offset*60*1000));
    return adjustedDate.toISOString().split('T')[0];
  };
  
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth(); // 0-11

  switch (timeRange) {
    case 'last7days':
      startDateInstance = new Date(today);
      startDateInstance.setDate(today.getDate() - 6); // Inclusive of today, so 6 days back + today = 7 days
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last14days':
      startDateInstance = new Date(today);
      startDateInstance.setDate(today.getDate() - 13);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last30days':
      startDateInstance = new Date(today);
      startDateInstance.setDate(today.getDate() - 29);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last60days':
      startDateInstance = new Date(today);
      startDateInstance.setDate(today.getDate() - 59);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last90days':
      startDateInstance = new Date(today);
      startDateInstance.setDate(today.getDate() - 89);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'monthToDate':
      startDateInstance = new Date(currentYear, currentMonth, 1);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'lastMonth':
      startDateInstance = new Date(currentYear, currentMonth - 1, 1);
      endDateInstance = new Date(currentYear, currentMonth, 0); // Last day of previous month
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last2months':
      startDateInstance = new Date(currentYear, currentMonth - 2, 1); // Start of 2 months ago
      // endDate is today
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last3months':
      startDateInstance = new Date(currentYear, currentMonth - 3, 1);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last6months':
      startDateInstance = new Date(currentYear, currentMonth - 6, 1);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last12months':
      startDateInstance = new Date(currentYear, currentMonth - 11, 1); // Start of the month, 11 months ago (to include current partial month makes 12)
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'yearToDate':
      startDateInstance = new Date(currentYear, 0, 1);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'lastQuarter':
      // Calculate the last quarter
      let lastQuarterStartMonth: number;
      let lastQuarterEndMonth: number;
      const currentQuarter = Math.floor(currentMonth / 3);
      
      if (currentQuarter === 0) { // If current quarter is Q1, last quarter is Q4 of previous year
        lastQuarterStartMonth = 9; // October
        lastQuarterEndMonth = 11; // December
        startDateInstance = new Date(currentYear - 1, lastQuarterStartMonth, 1);
        endDateInstance = new Date(currentYear - 1, lastQuarterEndMonth + 1, 0); // Last day of December
      } else {
        lastQuarterStartMonth = (currentQuarter - 1) * 3;
        lastQuarterEndMonth = lastQuarterStartMonth + 2;
        startDateInstance = new Date(currentYear, lastQuarterStartMonth, 1);
        endDateInstance = new Date(currentYear, lastQuarterEndMonth + 1, 0); // Last day of the quarter
      }
      
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    default:
      console.warn(`[Flexible KPIs API] Unknown timeRange: ${timeRange}`);
      return { startDate: undefined, endDate: undefined };
  }
}

// Define a type for the top brands query result
interface TopBrandRow {
  brand: string;
  total_value: number;
}

// Function to get top brands by KPI
async function getTopBrands(
  client: RedshiftClient,
  kpi: string,
  count: number,
  dateRange: { startDate?: string; endDate?: string; timeRange?: string }
): Promise<string[]> {
  try {
    console.log(`[AI Assistant API] Getting top ${count} brands by ${kpi}`);
    
    // Convert timeRange to startDate and endDate if needed
    let startDate = dateRange.startDate || '';
    let endDate = dateRange.endDate || '';
    
    if (dateRange.timeRange) {
      const dates = convertTimeRangeToDates(dateRange.timeRange);
      startDate = dates.startDate || '';
      endDate = dates.endDate || '';
    }
    
    // Build query to get top brands by KPI
    const query = `
      SELECT 
        k.brand,
        SUM(k.kpi_value) as total_value
      FROM dwh_ai.ai_reporting_ds_kpis k
      JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
      WHERE k.kpi_name = $1
        AND k.date >= $2
        AND k.date <= $3
        AND b.stage = 'Selling Actively'
      GROUP BY k.brand
      ORDER BY total_value DESC
      LIMIT $4;
    `;
    
    const result = await client.query<TopBrandRow>(query, [kpi, startDate, endDate, count]);
    
    // Extract brand names from the result
    const topBrands = result.rows.map(row => row.brand);
    console.log(`[AI Assistant API] Top ${count} brands by ${kpi}:`, topBrands);
    
    return topBrands;
  } catch (error) {
    console.error(`[AI Assistant API] Error getting top brands by ${kpi}:`, error);
    return [];
  }
}

// Function to query Claude AI for insights
async function getClaudeInsights(
  userMessage: string, 
  contextInfo: string, 
  extractedInfo: {
    dateRange: { startDate?: string; endDate?: string; timeRange?: string },
    kpis: string[],
    brands: string[],
    grouping: { 
      groupByTime?: 'day' | 'week' | 'month' | 'quarter' | 'year';
      groupByDimension?: 'brand' | 'brandGroup';
    }
  },
  kpiData: FlexibleKpiResponse,
  currency: string = 'CAD'
): Promise<string> {
  try {
    if (!process.env.ANTHROPIC_API_KEY) {
      throw new Error('ANTHROPIC_API_KEY is not defined in environment variables');
    }

    const claudeModel = process.env.CLAUDE_MODEL || 'claude-3-7-sonnet-20250219';
    console.log(`[AI Assistant API] Using Claude model for insights: ${claudeModel}`);

    // Prepare data summary for Claude
    let dataSummary = '';
    if (kpiData) {
      dataSummary = `
# Data Summary
- Date Range: ${extractedInfo.dateRange.startDate || ''} to ${extractedInfo.dateRange.endDate || ''} ${extractedInfo.dateRange.timeRange ? `(${extractedInfo.dateRange.timeRange})` : ''}
- KPIs Analyzed: ${extractedInfo.kpis.join(', ')}
- Brands: ${extractedInfo.brands.length > 0 ? extractedInfo.brands.join(', ') : 'All brands'}
- Grouping: ${extractedInfo.grouping.groupByTime || 'day'} ${extractedInfo.grouping.groupByDimension ? `by ${extractedInfo.grouping.groupByDimension}` : ''}
- Currency: ${currency}
      `;
    }

    // Prepare the system prompt
    const systemPrompt = `
You are an AI business intelligence assistant for NOLK, an e-commerce company. 
Your role is to analyze business data and provide insights in a clear, professional manner.
Always respond with accurate, data-driven insights based on the provided information.
When discussing financial metrics, be precise and highlight important trends or anomalies.
Keep responses concise and focused on the user's question.

NUMBER FORMATTING GUIDELINES:
- Format all currency values with the appropriate currency symbol (${currency === 'CAD' ? 'C$' : '$'}) and no decimal places
  Example: ${currency === 'CAD' ? 'C$1,234,567' : '$1,234,567'}
- Format percentages with the % symbol and one decimal place
  Example: 12.3%
- Format large numbers (non-currency, non-percentage) with commas as thousand separators
  Example: 1,234,567
- Always include the currency symbol (${currency === 'CAD' ? 'C$' : '$'}) when mentioning revenue, costs, margins, or any monetary values
- Always include the % symbol when mentioning percentage metrics
- Be consistent with number formatting throughout your response

IMPORTANT: Format your responses using Markdown for better readability:
- Use # for main headings (e.g., # Key Insights)
- Use ## for subheadings (e.g., ## Top Performing Brands)
- Use bullet points with - for lists
- Use **bold** for emphasis on important metrics or brand names
- Include a clear title at the top using # that summarizes the analysis
- Group related insights under appropriate headings
- End with a question about what additional information the user might want

FORMATTING GUIDELINES:
- Be extremely concise with NO unnecessary empty lines
- DO NOT add empty lines between sections, use headings instead
- DO NOT add empty lines between paragraphs
- DO NOT add empty lines between bullet points
- DO NOT add extra spaces at the beginning or end of lines
- Keep all text compact with absolutely minimal spacing
- Use headings (# and ##) to separate sections instead of empty lines
- For lists, put each bullet point immediately after the previous one with no empty lines
- Maintain an extremely compact layout with no wasted space

Here is important context about the business data:
${contextInfo}

${dataSummary}
`;

    // Call the Anthropic API
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.ANTHROPIC_API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: claudeModel,
        system: systemPrompt,
        messages: [
          {
            role: 'user',
            content: userMessage
          }
        ],
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Claude API error: ${JSON.stringify(errorData)}`);
    }

    const claudeResponse = await response.json();
    return claudeResponse.content[0].text;
  } catch (error) {
    console.error('[AI Assistant API] Error querying Claude AI for insights:', error);
    return `I'm sorry, I encountered an error while processing your request. ${error instanceof Error ? error.message : 'Please try again later.'}`;
  }
}

// Main handler
export async function POST(request: NextRequest) {
  console.log('[AI Assistant API] Received POST request');
  
  // Get token for authentication
  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });

  if (!token) {
    console.error('[AI Assistant API] Unauthorized: No token found.');
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Parse request body
    const requestData: AIAssistantRequest = await request.json();
    const userMessage = requestData.message;
    const isInitialLoad = requestData.isInitialLoad || false;
    
    console.log(`[AI Assistant API] Processing user message: "${userMessage}", isInitialLoad: ${isInitialLoad}`);

    // Get available brands from Redshift
    const redshiftPool = getRedshiftPool();
    if (!redshiftPool) {
      throw new Error('Redshift connection pool is not available');
    }

    const client = await redshiftPool.connect();
    console.log('[AI Assistant API] Connected to Redshift');

    try {
      // Generate context information
      const contextInfo = await generateContextInformation(client, token);
      console.log('[AI Assistant API] Generated context information');
      
      // If this is the initial load, just return the context information
      if (isInitialLoad) {
        return NextResponse.json({
          message: "I'm ready to help you analyze your business data. You can ask me questions like 'Show me the gross revenue for the last 30 days' or 'Compare net revenue across brands for the last quarter'.",
          contextInfo: contextInfo
        });
      }
      
      // Step 1: Analyze the user's query with Claude AI
      const queryAnalysis = await analyzeUserQuery(userMessage, contextInfo);
      console.log('[AI Assistant API] Query analysis result:', queryAnalysis);
      
      // Fetch available brands for query processing
      const brandsQuery = `
        SELECT name
        FROM dwh_ai.ai_reporting_brands
        WHERE stage = 'Selling Actively'
        ORDER BY name;
      `;
      
      const brandsResult = await client.query(brandsQuery);
      const availableBrands = brandsResult.rows.map((row: { name: string }) => row.name);
      
      console.log(`[AI Assistant API] Fetched ${availableBrands.length} available brands`);
      
      // Step 2: Handle special cases like "top brands"
      let brands = queryAnalysis.brands;
      
      // If the user is asking for top brands, we need to query for them first
      if (queryAnalysis.needsTopBrands && queryAnalysis.kpis.length > 0) {
        const topBrandsCount = queryAnalysis.topBrandsCount || 5; // Default to top 5 if not specified
        const primaryKpi = queryAnalysis.kpis[0]; // Use the first KPI for determining top brands
        
        // Get top brands by the primary KPI
        const topBrands = await getTopBrands(client, primaryKpi, topBrandsCount, queryAnalysis.dateRange);
        
        if (topBrands.length > 0) {
          brands = topBrands;
          console.log(`[AI Assistant API] Using top ${topBrands.length} brands by ${primaryKpi}:`, brands);
        } else {
          // If we couldn't get top brands, fall back to extracting brands from the query
          brands = extractBrands(userMessage, availableBrands);
        }
      } else if (brands.length === 0) {
        // If no brands were specified in the query, extract them from the query
        brands = extractBrands(userMessage, availableBrands);
      }
      
      // Ensure we have KPIs to query
      let kpis = queryAnalysis.kpis;
      if (kpis.length === 0) {
        kpis = extractKPIs(userMessage);
      }
      
      // Build query parameters for the flexible-kpis API
      const queryParams = new URLSearchParams();
      
      // Add date range parameters
      if (queryAnalysis.dateRange.startDate) {
        queryParams.append('startDate', queryAnalysis.dateRange.startDate);
      }
      if (queryAnalysis.dateRange.endDate) {
        queryParams.append('endDate', queryAnalysis.dateRange.endDate);
      }
      if (queryAnalysis.dateRange.timeRange) {
        queryParams.append('timeRange', queryAnalysis.dateRange.timeRange);
      }
      
      // Add KPI parameters
      kpis.forEach(kpi => {
        queryParams.append('kpis', kpi);
      });
      
      // Add brand parameters
      brands.forEach(brand => {
        queryParams.append('brands', brand);
      });
      
      // Add grouping parameters - adapt to week or month for large date ranges to avoid performance issues
      let groupByTime = queryAnalysis.grouping.groupByTime;
      
      // If no grouping is specified or if the date range is large, adapt the grouping
      if (!groupByTime) {
        // Default grouping based on date range
        if (isLargeDateRange(queryAnalysis.dateRange)) {
          // For large date ranges (more than a year), use month grouping
          groupByTime = 'month';
          console.log('[AI Assistant API] Setting default grouping to month for large date range (more than a year)');
        } else if (isMediumDateRange(queryAnalysis.dateRange)) {
          // For medium date ranges (more than three months), use week grouping
          groupByTime = 'week';
          console.log('[AI Assistant API] Setting default grouping to week for medium date range (more than three months)');
        } else {
          // For small date ranges, use day grouping
          groupByTime = 'day';
          console.log('[AI Assistant API] Setting default grouping to day for small date range');
        }
      } else {
        // User specified a grouping, but we might need to adapt it for performance
        if (groupByTime === 'day') {
          if (isLargeDateRange(queryAnalysis.dateRange)) {
            // For large date ranges (more than a year), override to month
            groupByTime = 'month';
            console.log('[AI Assistant API] Adapting grouping from day to month for large date range (more than a year)');
          } else if (isMediumDateRange(queryAnalysis.dateRange)) {
            // For medium date ranges (more than three months), override to week
            groupByTime = 'week';
            console.log('[AI Assistant API] Adapting grouping from day to week for medium date range (more than three months)');
          }
        }
      }
      
      queryParams.append('groupByTime', groupByTime);
      if (queryAnalysis.grouping.groupByDimension) {
        queryParams.append('groupByDimension', queryAnalysis.grouping.groupByDimension);
      }
      
      // Add currency parameter (default to CAD)
      queryParams.append('currency', 'CAD');
      
      console.log(`[AI Assistant API] Built query parameters: ${queryParams.toString()}`);

      // Call the flexible-kpis API
      const apiUrl = `${request.nextUrl.origin}/api/dashboard/flexible-kpis?${queryParams.toString()}`;
      console.log(`[AI Assistant API] Calling flexible-kpis API: ${apiUrl}`);
      
      const apiResponse = await fetch(apiUrl, {
        headers: {
          'Cookie': request.headers.get('cookie') || '',
        },
      });
      
      if (!apiResponse.ok) {
        const errorData = await apiResponse.json();
        throw new Error(`Failed to fetch KPI data: ${errorData.error || errorData.details || apiResponse.statusText}`);
      }
      
      const kpiData = await apiResponse.json();
      console.log('[AI Assistant API] Received KPI data from flexible-kpis API');

      // Determine chart type based on the query and data
      const chartType = determineChartType(
        userMessage, 
        kpis, 
        !!queryAnalysis.grouping.groupByDimension
      );
      
      console.log(`[AI Assistant API] Determined chart type: ${chartType}`);

      // Format data for charts
      const charts = formatChartData(kpiData, kpis, queryAnalysis.grouping.groupByDimension);
      console.log(`[AI Assistant API] Formatted ${charts.length} charts`);

      // Step 3: Get insights from Claude AI
      const extractedInfo = { 
        dateRange: queryAnalysis.dateRange, 
        kpis, 
        brands, 
        grouping: queryAnalysis.grouping 
      };
      
      const claudeResponse = await getClaudeInsights(userMessage, contextInfo, extractedInfo, kpiData);
      console.log(`[AI Assistant API] Received insights from Claude AI`);

      // Return the response
      return NextResponse.json({
        message: claudeResponse,
        charts: charts,
        contextInfo: contextInfo
      } as AIAssistantResponse);
    } finally {
      client.release();
      console.log('[AI Assistant API] Released Redshift client');
    }
  } catch (error) {
    console.error('[AI Assistant API] Error:', error);
    
    let errorMessage = 'An unexpected error occurred.';
    let statusCode = 500;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      if (errorMessage.includes('Parameter validation failed')) {
        statusCode = 400;
      }
    }
    
    return NextResponse.json({
      error: errorMessage,
      message: "I'm sorry, I couldn't process your request. " + errorMessage
    } as AIAssistantResponse, { status: statusCode });
  }
}
