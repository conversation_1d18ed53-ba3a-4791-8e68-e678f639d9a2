import { NextRequest, NextResponse } from 'next/server';
import { getAllBrands, getBudgetsByBrand, importBudgetsFromCsv, updateBudget } from '@/lib/api/budget-service';

// This endpoint doesn't require authentication
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const brand = searchParams.get('brand') || undefined;
    
    // If import=true is passed, import the budget data from CSV
    if (searchParams.get('import') === 'true') {
      const result = await importBudgetsFromCsv();
      if (!result.success) {
        return NextResponse.json({ error: result.message }, { status: 500 });
      }
    }
    
    // If brands=true is passed, return the list of brands
    if (searchParams.get('brands') === 'true') {
      const brands = await getAllBrands();
      return NextResponse.json({ brands });
    }
    
    // Otherwise, return the budget data
    const budgets = await getBudgetsByBrand(brand);
    return NextResponse.json({ budgets });
  } catch (error: unknown) {
    console.error('Error in budget API:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while fetching budget data';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { brand, month, account, budget_value, updated_by } = body;

    // Validate required fields
    if (!brand || !month || !account || budget_value === undefined || budget_value === null) {
      return NextResponse.json(
        { error: 'Brand, month, account, and budget_value are required' },
        { status: 400 }
      );
    }

    // Validate budget_value is a number
    const numericValue = typeof budget_value === 'string' ? parseFloat(budget_value) : budget_value;
    if (typeof numericValue !== 'number' || isNaN(numericValue)) {
      return NextResponse.json(
        { error: 'Budget value must be a valid number' },
        { status: 400 }
      );
    }

    // Update the budget
    const result = await updateBudget(brand, month, account, numericValue, updated_by);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        data: { brand, month, account, budget_value: numericValue }
      });
    } else {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      );
    }
  } catch (error: unknown) {
    console.error('Error in budget PUT API:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while updating budget data';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
