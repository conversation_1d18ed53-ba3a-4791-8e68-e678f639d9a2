import { NextRequest, NextResponse } from 'next/server';

import { getDb } from '@/lib/api/db';
import { getRedshiftPool } from '@/lib/api/redshift';
import { getToken } from 'next-auth/jwt';

export async function GET(request: NextRequest) {
  try {
    const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });

    if (!token) {
      console.log('[DEBUG BRANDS API] No token found - unauthorized');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('[DEBUG BRANDS API] Full token contents:', {
      sub: token.sub,
      email: token.email,
      name: token.name,
      isImpersonating: token.isImpersonating,
      isSuperAdmin: token.isSuperAdmin,
      roles: token.roles,
      permissions: token.permissions,
      groups: token.groups,
      brands: token.brands,
      originalUser: token.originalUser
    });

    const { isImpersonating, isSuperAdmin, brands: rawBrands } = token as {
      isImpersonating?: boolean;
      isSuperAdmin?: boolean;
      brands?: unknown; // Use unknown for robust parsing
    };

    console.log('[IMPERSONATION DEBUG] Brand Selector API Token details:', {
      isImpersonating,
      isSuperAdmin,
      rawBrands,
      tokenSub: token.sub,
      tokenEmail: token.email,
      originalUser: token.originalUser
    });

    let processedBrandIds: string[] | undefined = undefined;

    if (Array.isArray(rawBrands)) {
      const mappedBrands = rawBrands
        .map(id => {
          if (typeof id === 'number') {
            return String(id);
          }
          if (typeof id === 'string') {
            return id.trim();
          }
          // Handle other potential types or invalid entries by returning null
          // so they can be filtered out.
          return null;
        })
        .filter((id): id is string => id !== null && id !== ''); // Filter out nulls and empty strings

      if (mappedBrands.length > 0) {
        processedBrandIds = mappedBrands;
      }
    }
    // If rawBrands was not an array, or resulted in an empty filteredBrands,
    // processedBrandIds will be undefined. The logic below handles this.

    // Check if Redshift pool is available
    const redshiftPool = getRedshiftPool();
    if (!redshiftPool) {
      console.error('API Error fetching brands: Redshift connection pool is not available.');
      return NextResponse.json({ error: 'Failed to fetch brands.', details: 'Redshift connection pool is not available.' }, { status: 500 });
    }
    
    let client;
    try {
      client = await redshiftPool.connect();
    } catch (connectionError) {
      console.error('API Error fetching brands: Failed to connect to Redshift.', connectionError);
      return NextResponse.json({ error: 'Failed to fetch brands.', details: 'Failed to connect to the database.' }, { status: 500 });
    }
    
    try {
      let query: string;
      const queryParams: string[] = [];

      // For all users, we only want to return brands that exist in the SQL database
      // AND are accessible to the current user based on their permissions.
      
      console.log('[IMPERSONATION DEBUG] Brand Selector filtering logic:', {
        isImpersonating,
        isSuperAdmin,
        processedBrandIds,
        restrictedAccess: (isImpersonating || !isSuperAdmin),
        hasNoBrands: (!processedBrandIds || processedBrandIds.length === 0)
      });

      // Case 1: If user has no brand IDs in their token (and is not a SuperAdmin or is impersonating),
      // return no brands (403 Forbidden)
      if ((isImpersonating || !isSuperAdmin) && (!processedBrandIds || processedBrandIds.length === 0)) {
        console.warn(`[IMPERSONATION DEBUG] Brand Selector: ${isImpersonating ? 'Impersonating user' : 'Non-SuperAdmin'} has no brands in token. Denying access.`);
        return NextResponse.json(
          {
            error: "No brands accessible",
            message: "You do not have permission to view any brands. Please contact your administrator if you believe this is an error."
          },
          { status: 403 }
        );
      }
      
      // Case 2: SuperAdmin, not impersonating, and no specific brands in token
      // Only return brands from the database (no need to filter by brand_id)
      if (isSuperAdmin && !isImpersonating && (!processedBrandIds || processedBrandIds.length === 0)) {
        query = `
          SELECT brand_id, name, "group", stage
          FROM dwh_ai.ai_reporting_brands
          ORDER BY name ASC;
        `;
        console.log('[IMPERSONATION DEBUG] Brand Selector: SuperAdmin, not impersonating, no specific brands in token. Fetching all brands from database.');
      }
      // Case 3: User has specific brand IDs in token (or is SuperAdmin with specific brands)
      // Filter to only include brands that are both in the database AND in the user's allowed brands
      else {
        console.log('[IMPERSONATION DEBUG] Brand Selector: User has specific brand IDs in token, filtering by those brands');
        // Map local brand IDs to brand names using the local database
        let allowedBrandNames: string[] = [];
        
        if (processedBrandIds && processedBrandIds.length > 0) {
          try {
            const localDb = await getDb();
            const brandNameQuery = `SELECT name FROM Brands WHERE id IN (${processedBrandIds.map(() => '?').join(',')})`;
            const brandNameResults = await localDb.all<{ name: string }[]>(brandNameQuery, processedBrandIds);
            allowedBrandNames = brandNameResults.map(row => row.name);
            console.log(`[API Brands] Mapped ${processedBrandIds.length} brand IDs to names:`, allowedBrandNames);
          } catch (localDbError) {
            console.error('[API Brands] Error querying local database for brand names:', localDbError);
            return NextResponse.json({ error: 'Failed to resolve brand permissions' }, { status: 500 });
          }
        }

        if (allowedBrandNames.length === 0) {
          console.warn("[API Brands] No brand names found for user's brand IDs. Returning empty array.");
          return NextResponse.json([]);
        }

        // Filter Redshift brands by name instead of brand_id
        const whereConditions: string[] = [];
        let currentParamIndex = 1;

        const brandNamePlaceholders = allowedBrandNames.map(() => `$${currentParamIndex++}`).join(',');
        whereConditions.push(`name IN (${brandNamePlaceholders})`);
        queryParams.push(...allowedBrandNames);
        console.log(`[API Brands] Filtering by ${allowedBrandNames.length} brand names from local database.`);

        query = `
          SELECT brand_id, name, "group", stage
          FROM dwh_ai.ai_reporting_brands
          WHERE ${whereConditions.join(' AND ')}
          ORDER BY name ASC;
        `;
      }
      
      console.log('Executing Redshift query:', query, 'with params:', JSON.stringify(queryParams));
      const result = await client.query(query, queryParams);
      
      return NextResponse.json(result.rows);
    } catch (queryError) {
      console.error('API Error fetching brands: Error executing Redshift query:', queryError);
      // It's important not to re-throw here if we want the outer catch to handle the response
      // Or, construct a specific response here.
      // For now, let the outer catch handle it but with more specific logging done.
      // We'll let the generic error handler below return the 500.
      // To make it more specific, we could:
      // return NextResponse.json({ error: 'Failed to fetch brands.', details: 'Error during database query execution.' }, { status: 500 });
      // However, the current structure re-throws and lets the outer catch handle it.
      // Let's make the outer catch more informative based on the error type.
      throw queryError; // Re-throw to be caught by the outer try-catch
    } finally {
      if (client) {
        client.release();
      }
    }
  } catch (error: unknown) {
    console.error(`API Error fetching brands:`, error);
    let errorMessage = 'An unexpected error occurred while fetching brands.';
    let errorDetails = '';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = 'Failed to fetch brands.'; // More generic for client
      errorDetails = error.message; // Specific details for logging/internal use
      if (error.message.includes('Redshift connection pool is not available')) {
         errorMessage = 'Database connection pool is unavailable.';
      } else if (error.message.includes('Failed to connect to Redshift')) {
         errorMessage = 'Could not connect to the database.';
      } else if (error.message.includes('query')) { // Generic check for query related errors
         errorMessage = 'Error executing database query.';
      }
    }
    // Log the detailed error for server-side debugging
    console.error(`Error details for fetching brands: ${errorDetails}`, error);
    
    return NextResponse.json({ error: errorMessage, details: errorDetails }, { status: statusCode });
  }
}
