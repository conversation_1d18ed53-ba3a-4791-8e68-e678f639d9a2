import { NextRequest, NextResponse } from 'next/server';

import { getRedshiftPool } from '@/lib/api/redshift';
import { getToken } from 'next-auth/jwt';

export async function GET(request: NextRequest) {
  try {
    const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { isImpersonating, isSuperAdmin, brands: tokenBrands } = token as { isImpersonating?: boolean, isSuperAdmin?: boolean, brands?: unknown };

    // Process brand IDs from token
    let processedBrandIds: (string | number)[] = [];
    if (Array.isArray(tokenBrands)) {
      processedBrandIds = tokenBrands
        .map(id => {
          if (typeof id === 'number' || typeof id === 'string') {
            return id;
          }
          return null;
        })
        .filter((id): id is string | number => id !== null);
    }

    // Check if Redshift pool is available
    const redshiftPool = getRedshiftPool();
    if (!redshiftPool) {
      throw new Error('Redshift connection pool is not available');
    }
    
    const client = await redshiftPool.connect();
    
    try {
      // First, check if we need to join with brands table to get brand_id
      let query = `
        SELECT
          k.country_name,
          SUM(k.kpi_value) as total_country_sales
        FROM dwh_ai.ai_reporting_ds_kpis k
        JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
      `;
      const queryParams: (string | number)[] = ['Net Revenue']; // Add 'Net Revenue' as the first parameter
      const whereClauses: string[] = [
        "k.country_name IS NOT NULL",
        `k.kpi_name = $${queryParams.length}` // Reference the parameter index
      ];

      // For both impersonating and non-SuperAdmin users, we need to filter by brands
      if (isImpersonating || !isSuperAdmin) {
        // Check if the user has any brands assigned (directly or via groups)
        if (processedBrandIds.length === 0) {
          console.warn(`${isImpersonating ? 'Impersonating' : 'Non-SuperAdmin'} user with no brands assigned. Returning empty countries list.`);
          return NextResponse.json([]);
        }
        
        // Use IN clause with individual parameters
        const placeholders = processedBrandIds.map((_, i) => `$${queryParams.length + i + 1}`).join(',');
        whereClauses.push(`b.brand_id IN (${placeholders})`);
        // Add each brand ID as a separate parameter
        processedBrandIds.forEach(id => queryParams.push(id));
      }
      // SuperAdmins (not impersonating) get all countries, so no additional filtering needed

      if (whereClauses.length > 0) {
        query += ` WHERE ${whereClauses.join(' AND ')}`;
      }
      
      query += `
        GROUP BY k.country_name
        ORDER BY k.country_name ASC;
      `;
      
      const result = await client.query(query, queryParams);
      
      const countries = result.rows.map(row => ({
        name: row.country_name,
        hasSales: row.total_country_sales > 0
      }));
      
      return NextResponse.json(countries);
    } catch (error) {
      console.error('Error executing Redshift query:', error);
      throw error;
    } finally {
      client.release();
    }
  } catch (error: unknown) {
    console.error(`API Error fetching countries:`, error);
    let errorMessage = 'An unexpected error occurred.';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }
    
    return NextResponse.json({ error: 'Failed to fetch countries.', details: errorMessage }, { status: statusCode });
  }
}
