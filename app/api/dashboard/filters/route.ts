import { NextRequest, NextResponse } from 'next/server';

import { getDb } from '@/lib/api/db';
import { getRedshiftPool } from '@/lib/api/redshift';
import { getToken } from 'next-auth/jwt';

export async function GET(request: NextRequest) {
  try {
    const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { isImpersonating, isSuperAdmin, brands: rawBrands } = token as {
      isImpersonating?: boolean;
      isSuperAdmin?: boolean;
      brands?: unknown; // Use unknown for robust parsing
    };

    let processedBrandIds: string[] | undefined = undefined;

    if (Array.isArray(rawBrands)) {
      const mappedBrands = rawBrands
        .map(id => {
          if (typeof id === 'number') {
            return String(id);
          }
          if (typeof id === 'string') {
            return id.trim();
          }
          // Handle other potential types or invalid entries by returning null
          // so they can be filtered out.
          return null;
        })
        .filter((id): id is string => id !== null && id !== ''); // Filter out nulls and empty strings

      if (mappedBrands.length > 0) {
        processedBrandIds = mappedBrands;
      }
    }
    
    // Check if Redshift pool is available
    const redshiftPool = getRedshiftPool();
    if (!redshiftPool) {
      console.error('API Error fetching filters: Redshift connection pool is not available.');
      return NextResponse.json({ error: 'Failed to fetch filters.', details: 'Redshift connection pool is not available.' }, { status: 500 });
    }

    // Get Redshift client
    let client;
    try {
      client = await redshiftPool.connect();
    } catch (connectionError) {
      console.error('API Error fetching filters: Failed to connect to Redshift.', connectionError);
      return NextResponse.json({ error: 'Failed to fetch filters.', details: 'Failed to connect to the database.' }, { status: 500 });
    }
    
    try {
      let query: string;
      const queryParams: string[] = [];

      // For all users, we only want to return brands that exist in the SQL database
      // AND are accessible to the current user based on their permissions.
      
      // Case 1: If user has no brand IDs in their token (and is not a SuperAdmin or is impersonating),
      // return no brands (403 Forbidden)
      if ((isImpersonating || !isSuperAdmin) && (!processedBrandIds || processedBrandIds.length === 0)) {
        console.warn(`[API Filters] ${isImpersonating ? 'Impersonating user' : 'Non-SuperAdmin'} has no brands in token. Denying access.`);
        return NextResponse.json(
          {
            error: "No brands accessible",
            message: "You do not have permission to view any brands. Please contact your administrator if you believe this is an error."
          },
          { status: 403 }
        );
      }
      
      // Case 2: SuperAdmin, not impersonating, and no specific brands in token
      // Only return brands from the database (no need to filter by brand_id)
      if (isSuperAdmin && !isImpersonating && (!processedBrandIds || processedBrandIds.length === 0)) {
        query = `
          SELECT brand_id, name, "group", stage
          FROM dwh_ai.ai_reporting_brands
          ORDER BY name ASC;
        `;
        console.log('[API Filters] SuperAdmin, not impersonating, no specific brands in token. Fetching all brands from database.');
      }
      // Case 3: User has specific brand IDs in token (or is SuperAdmin with specific brands)
      // Filter to only include brands that are both in the database AND in the user's allowed brands
      else {
        // Map local brand IDs to brand names using the local database
        let allowedBrandNames: string[] = [];
        
        if (processedBrandIds && processedBrandIds.length > 0) {
          try {
            const localDb = await getDb();
            const brandNameQuery = `SELECT name FROM Brands WHERE id IN (${processedBrandIds.map(() => '?').join(',')})`;
            const brandNameResults = await localDb.all<{ name: string }[]>(brandNameQuery, processedBrandIds);
            allowedBrandNames = brandNameResults.map(row => row.name);
            console.log(`[API Filters] Mapped ${processedBrandIds.length} brand IDs to names:`, allowedBrandNames);
          } catch (localDbError) {
            console.error('[API Filters] Error querying local database for brand names:', localDbError);
            return NextResponse.json({ error: 'Failed to resolve brand permissions' }, { status: 500 });
          }
        }

        if (allowedBrandNames.length === 0) {
          console.warn("[API Filters] No brand names found for user's brand IDs. Returning empty array.");
          return NextResponse.json({ brands: [], brandGroups: [] });
        }

        // Filter Redshift brands by name instead of brand_id
        const whereConditions: string[] = [];
        let currentParamIndex = 1;

        const brandNamePlaceholders = allowedBrandNames.map(() => `$${currentParamIndex++}`).join(',');
        whereConditions.push(`name IN (${brandNamePlaceholders})`);
        queryParams.push(...allowedBrandNames);
        console.log(`[API Filters] Filtering by ${allowedBrandNames.length} brand names from local database.`);

        query = `
          SELECT brand_id, name, "group", stage
          FROM dwh_ai.ai_reporting_brands
          WHERE ${whereConditions.join(' AND ')}
          ORDER BY name ASC;
        `;
      }
      
      console.log('Executing Redshift query:', query, 'with params:', JSON.stringify(queryParams));
      const brandsResult = await client.query(query, queryParams);
      
      // Format brands data
      const brands = [
        { name: 'All Brands', group: 'All' }, // Always include "All Brands" option
        ...brandsResult.rows.map((row: { name: string; group?: string }) => ({
          name: row.name,
          group: row.group || 'Uncategorized',
        }))
      ];
      
      // Extract unique brand groups
      const brandGroups = [...new Set(['All', ...brands.map(brand => brand.group)])].filter(Boolean);
      
      // Construct filter data
      const filterData = {
        brands,
        brandGroups,
      };

      return NextResponse.json(filterData);
    } catch (queryError) {
      console.error('API Error fetching filters: Error executing Redshift query:', queryError);
      throw queryError; // Re-throw to be caught by the outer try-catch
    } finally {
      if (client) {
        client.release();
      }
    }
  } catch (error: unknown) {
    console.error(`API Error fetching filters:`, error);
    let errorMessage = 'An unexpected error occurred while fetching filters.';
    let errorDetails = '';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = 'Failed to fetch filters.'; // More generic for client
      errorDetails = error.message; // Specific details for logging/internal use
      if (error.message.includes('Redshift connection pool is not available')) {
         errorMessage = 'Database connection pool is unavailable.';
      } else if (error.message.includes('Failed to connect to Redshift')) {
         errorMessage = 'Could not connect to the database.';
      } else if (error.message.includes('query')) { // Generic check for query related errors
         errorMessage = 'Error executing database query.';
      }
    }
    // Log the detailed error for server-side debugging
    console.error(`Error details for fetching filters: ${errorDetails}`, error);
    
    return NextResponse.json({ error: errorMessage, details: errorDetails }, { status: statusCode });
  }
}
