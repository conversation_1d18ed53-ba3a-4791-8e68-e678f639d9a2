import { NextRequest, NextResponse } from 'next/server';

// Import the redshift connection
import { getRedshiftPool } from '@/lib/api/redshift';
import { getToken } from 'next-auth/jwt';

// --- Types ---

// Query Parameter Types
type ValidGroupByTime = 'day' | 'week' | 'month' | 'quarter' | 'year';
type ValidGroupByDimension = 'brand' | 'brandGroup';
type ValidCurrency = 'CAD' | 'USD';

type FlexibleKpiQueryParams = {
  startDate?: string; // YYYY-MM-DD
  endDate?: string;   // YYYY-MM-DD
  currency?: ValidCurrency;
  brands?: string | string[];
  brandGroups?: string | string[];
  kpis?: string | string[];
  groupByTime?: ValidGroupByTime;
  groupByDimension?: ValidGroupByDimension;
  salesChannels?: string | string[];
  countryNames?: string | string[];
};

// Raw Data Structure from DB
type RawDbData = {
  date: string; // Truncated date
  kpi_name: string;
  value: number;
  dimension?: string; // Populated if groupByDimension is used (e.g., brand name)
};

// Intermediate Processing Structure
type TimeSeriesDataPoint = { date: string; value: number | null };
type KpiSummary = {
  value: number | null;
};

// Structure to hold processed base KPI data before calculating derived KPIs
// Nested structure: [dimension?][kpiName][date] = value
type ProcessedBaseKpiMap = {
  [dimensionValue: string]: { // Key is dimension value (e.g., brand name) or a default key if no dimension
    [kpiName: string]: {
      timeSeriesMap: { [date: string]: number | null }; // Allow nulls from calculation
      total: number | null; // Allow nulls from calculation
    };
  };
};

// Final Response Data Structure
type KpiResultData = {
  summary: KpiSummary;
  timeSeries: TimeSeriesDataPoint[];
};

// Response type when NOT grouping by dimension
type SimpleKpiResponse = {
  [kpiName: string]: KpiResultData;
};

// Response type WHEN grouping by dimension
type GroupedKpiResponse = {
  [kpiName: string]: {
    [dimensionValue: string]: KpiResultData;
  };
};

// Union type for the final response
type FlexibleKpiResponse = SimpleKpiResponse | GroupedKpiResponse;

// Response type for error cases
interface ErrorResponse {
  error: string;
  details?: string;
}

// --- Constants ---
const ALL_AVAILABLE_KPIS: Set<string> = new Set([
  'Gross Revenue', 'Net Revenue', 'Gross Margin', 'Adspend', 'Contribution Margin',
  '% Gross Margin', '% Contribution Margin', '% Adspend', 'Landed Cost', 'Fulfillment Cost',
  'Transaction Cost', 'Discount', 'Refund', '% Landed Cost', '% Fulfillment Cost',
  '% Transaction Cost', '% Discount', '% Refund',
  'ACOS', 'TACOS', 'TCAC', 'Website Traffic', 'Conversion Rate', 'Organic Traffic', 'Paid Traffic',
]);

const KPI_DEPENDENCIES: Map<string, string[]> = new Map([
  ['Contribution Margin', ['Gross Margin', 'Adspend']],
  ['% Gross Margin', ['Gross Margin', 'Net Revenue']],
  ['% Contribution Margin', ['Gross Margin', 'Adspend', 'Net Revenue']],
  ['% Adspend', ['Adspend', 'Net Revenue']],
  ['% Landed Cost', ['Landed Cost', 'Net Revenue']],
  ['% Fulfillment Cost', ['Fulfillment Cost', 'Net Revenue']],
  ['% Transaction Cost', ['Transaction Cost', 'Net Revenue']],
  ['% Discount', ['Discount', 'Gross Revenue']],
  ['% Refund', ['Refund', 'Gross Revenue']],
]);

// --- Helper Functions ---

// Helper function to convert timeRange to startDate and endDate
function convertTimeRangeToDates(timeRange: string | undefined): { startDate?: string; endDate?: string } {
  if (!timeRange) return { startDate: undefined, endDate: undefined };

  const today = new Date();
  let startDateInstance: Date;
  let endDateInstance: Date = new Date(today); // endDate is often today

  // Helper to format date as YYYY-MM-DD
  const formatDate = (date: Date): string => {
    // Adjust for timezone to ensure date isn't skewed
    const offset = date.getTimezoneOffset();
    const adjustedDate = new Date(date.getTime() - (offset*60*1000));
    return adjustedDate.toISOString().split('T')[0];
  };
  
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth(); // 0-11

  switch (timeRange) {
    case 'last7days':
      startDateInstance = new Date(today);
      startDateInstance.setDate(today.getDate() - 6); // Inclusive of today, so 6 days back + today = 7 days
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last14days':
      startDateInstance = new Date(today);
      startDateInstance.setDate(today.getDate() - 13);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last30days':
      startDateInstance = new Date(today);
      startDateInstance.setDate(today.getDate() - 29);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last60days':
      startDateInstance = new Date(today);
      startDateInstance.setDate(today.getDate() - 59);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last90days':
      startDateInstance = new Date(today);
      startDateInstance.setDate(today.getDate() - 89);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'monthToDate':
      startDateInstance = new Date(currentYear, currentMonth, 1);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'lastMonth':
      startDateInstance = new Date(currentYear, currentMonth - 1, 1);
      endDateInstance = new Date(currentYear, currentMonth, 0); // Last day of previous month
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last2months':
      startDateInstance = new Date(currentYear, currentMonth - 2, 1); // Start of 2 months ago
      // endDate is today
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last3months':
      startDateInstance = new Date(currentYear, currentMonth - 3, 1);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last6months':
      startDateInstance = new Date(currentYear, currentMonth - 6, 1);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'last12months':
      startDateInstance = new Date(currentYear, currentMonth - 11, 1); // Start of the month, 11 months ago (to include current partial month makes 12)
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    case 'yearToDate':
      startDateInstance = new Date(currentYear, 0, 1);
      return { startDate: formatDate(startDateInstance), endDate: formatDate(endDateInstance) };
    default:
      console.warn(`[Flexible KPIs API] Unknown timeRange: ${timeRange}`);
      return { startDate: undefined, endDate: undefined };
  }
}


function sanitizeArrayParameter(input: string | string[] | undefined): string[] {
  if (!input) {
    return [];
  }
  
  let flatStringArray: string[];
  if (Array.isArray(input)) {
    // If input is an array, iterate through its elements.
    // If an element is a string, split it by comma.
    // This handles cases like ["brand1,brand2", "brand3"] or [["brand1", "brand2"], "brand3"] (though the latter is less likely from URL params)
    flatStringArray = input.flatMap(item =>
      typeof item === 'string' ? item.split(',') : [] // if item is not string (e.g. nested array), ignore or handle as needed
    );
  } else { // input is string
    flatStringArray = input.split(',');
  }
  
  // Basic sanitization: Allow letters, numbers, spaces, hyphens, underscores, ampersands, plus signs
  return flatStringArray.map(item => item.trim().replace(/[^a-zA-Z0-9\s\-_&+]/g, '')).filter(Boolean);
}

function resolveRequiredBaseKpis(requestedKpis: string[]): Set<string> {
  // We need to fetch the base KPIs needed for calculations
  const baseKpisToFetch = new Set<string>();
  
  // Create normalized maps for case-insensitive lookups
  const normalizedKpiMap = new Map<string, string>();
  ALL_AVAILABLE_KPIS.forEach(kpi => {
    normalizedKpiMap.set(kpi.toLowerCase().trim(), kpi);
  });
  
  const normalizedDependencyMap = new Map<string, string[]>();
  KPI_DEPENDENCIES.forEach((deps, kpi) => {
    normalizedDependencyMap.set(kpi.toLowerCase().trim(), deps);
  });
  
  requestedKpis.forEach(kpi => {
    // For non-percentage KPIs, add them directly to the fetch list
    if (ALL_AVAILABLE_KPIS.has(kpi) && !kpi.startsWith('%')) {
      baseKpisToFetch.add(kpi);
    }
    
    const normalizedKpi = kpi.toLowerCase().trim();
    
    // Check for dependencies with exact match
    if (normalizedDependencyMap.has(normalizedKpi)) {
      normalizedDependencyMap.get(normalizedKpi)!.forEach(dep => {
        // Make sure we add the correctly cased version
        baseKpisToFetch.add(dep);
      });
    } 
    // Check for dependencies with % prefix handling
    else if (normalizedKpi.startsWith('%') && normalizedDependencyMap.has(normalizedKpi)) {
      normalizedDependencyMap.get(normalizedKpi)!.forEach(dep => {
        baseKpisToFetch.add(dep);
      });
    }
    // Check for dependencies with % prefix in map
    else if (normalizedDependencyMap.has(`% ${normalizedKpi}`)) {
      normalizedDependencyMap.get(`% ${normalizedKpi}`)!.forEach(dep => {
        baseKpisToFetch.add(dep);
      });
    }
    // Check if it's a base KPI with exact match
    else if (normalizedKpiMap.has(normalizedKpi) && !normalizedKpi.startsWith('%')) {
      // Add the correctly cased version from our map
      baseKpisToFetch.add(normalizedKpiMap.get(normalizedKpi)!);
    }
    // Check if it's a base KPI with % prefix handling
    else if (normalizedKpi.startsWith('%') && normalizedKpiMap.has(normalizedKpi.substring(1).trim())) {
      baseKpisToFetch.add(normalizedKpiMap.get(normalizedKpi.substring(1).trim())!);
    }
    // Check if it's a base KPI with % prefix in map
    else if (normalizedKpiMap.has(`% ${normalizedKpi}`)) {
      baseKpisToFetch.add(normalizedKpiMap.get(`% ${normalizedKpi}`)!);
    }
  });
  
  // Add all the base KPIs needed for the requested KPIs
  return baseKpisToFetch;
}

function validateParameters(query: FlexibleKpiQueryParams): {
  startDate?: string;
  endDate?: string;
  currency: ValidCurrency;
  brands: string[];
  brandGroups: string[];
  requestedKpis: string[]; // Sanitized and validated against ALL_AVAILABLE_KPIS
  groupByTime: ValidGroupByTime;
  groupByDimension?: ValidGroupByDimension;
  salesChannels: string[]; // Sales channels
  countryNames: string[]; // Country names
} {
  const errors: string[] = [];
  let validatedStartDate: string | undefined = undefined;
  let validatedEndDate: string | undefined = undefined;

  // Validate dates
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (query.startDate) {
    if (typeof query.startDate !== 'string' || !dateRegex.test(query.startDate)) {
        errors.push("Invalid format for startDate. Expected YYYY-MM-DD.");
    } else {
        validatedStartDate = query.startDate;
    }
  }
   if (query.endDate) {
    if (typeof query.endDate !== 'string' || !dateRegex.test(query.endDate)) {
        errors.push("Invalid format for endDate. Expected YYYY-MM-DD.");
    } else {
        validatedEndDate = query.endDate;
    }
  }
  if (validatedStartDate && validatedEndDate && validatedStartDate > validatedEndDate) {
      errors.push("startDate cannot be after endDate.");
  }

  // Validate currency
  let currency: ValidCurrency = 'CAD';
  if (query.currency) {
      if (query.currency !== 'CAD' && query.currency !== 'USD') {
          errors.push("Invalid currency. Expected 'CAD' or 'USD'.");
      } else {
          currency = query.currency;
      }
  }

  // Sanitize arrays
  const brands = sanitizeArrayParameter(query.brands);
  const brandGroups = sanitizeArrayParameter(query.brandGroups);
  const salesChannels = sanitizeArrayParameter(query.salesChannels);
  const countryNames = sanitizeArrayParameter(query.countryNames);

  // Sanitize and validate KPIs
  let requestedKpis: string[] = [];
  const rawKpis = query.kpis;
  if (!rawKpis || (Array.isArray(rawKpis) && rawKpis.length === 0) || (typeof rawKpis === 'string' && rawKpis.trim() === '')) {
      requestedKpis = Array.from(ALL_AVAILABLE_KPIS); // Default to all
  } else {
      const sanitizedKpis = sanitizeArrayParameter(rawKpis);
      const unknownKpis: string[] = [];
      
      // Create a normalized map of available KPIs for case-insensitive comparison
      // but ensure we distinguish between regular KPIs and percentage KPIs
      const normalizedKpiMap = new Map<string, string>();
      ALL_AVAILABLE_KPIS.forEach(kpi => {
          // Store with the % prefix preserved in the key to distinguish percentage KPIs
          const normalizedKey = kpi.toLowerCase().trim();
          normalizedKpiMap.set(normalizedKey, kpi);
      });
      
      sanitizedKpis.forEach(kpi => {
          const normalizedKpi = kpi.toLowerCase().trim();
          
          // Direct match first
          if (normalizedKpiMap.has(normalizedKpi)) {
              // Use the correctly cased version from our map
              requestedKpis.push(normalizedKpiMap.get(normalizedKpi)!);
          } 
          // Check if user entered a KPI without the % prefix but meant the percentage version
          else if (normalizedKpi.startsWith('%') && normalizedKpiMap.has(normalizedKpi)) {
              requestedKpis.push(normalizedKpiMap.get(normalizedKpi)!);
          }
          // Check if user entered a KPI name that should have a % prefix
          else if (normalizedKpiMap.has(`% ${normalizedKpi}`)) {
              requestedKpis.push(normalizedKpiMap.get(`% ${normalizedKpi}`)!);
          }
          // If still not found, try without the % prefix for flexibility
          else if (normalizedKpi.startsWith('%') && normalizedKpiMap.has(normalizedKpi.substring(1).trim())) {
              requestedKpis.push(normalizedKpiMap.get(normalizedKpi.substring(1).trim())!);
          }
          else {
              unknownKpis.push(kpi);
          }
      });
      if (unknownKpis.length > 0) {
          errors.push(`Unknown KPI(s) requested: ${unknownKpis.join(', ')}.`);
      }
       if (requestedKpis.length === 0 && unknownKpis.length > 0) {
           // Avoid proceeding if only unknown KPIs were requested
           errors.push("No valid KPIs were requested.");
       } else if (requestedKpis.length === 0) {
           // If input was provided but sanitized to empty (e.g., just ',') default to all
           requestedKpis = Array.from(ALL_AVAILABLE_KPIS);
       }
  }

  // Validate groupByTime
  let groupByTime: ValidGroupByTime = 'day';
  const validGroupByTimes: ValidGroupByTime[] = ['day', 'week', 'month', 'quarter', 'year'];
  if (query.groupByTime) {
      if (!validGroupByTimes.includes(query.groupByTime as ValidGroupByTime)) {
          errors.push(`Invalid groupByTime. Expected one of: ${validGroupByTimes.join(', ')}.`);
      } else {
          groupByTime = query.groupByTime as ValidGroupByTime;
      }
  }

  // Validate groupByDimension
  let groupByDimension: ValidGroupByDimension | undefined = undefined;
  const validGroupByDimensions: ValidGroupByDimension[] = ['brand', 'brandGroup'];
  if (query.groupByDimension) {
      if (!validGroupByDimensions.includes(query.groupByDimension as ValidGroupByDimension)) {
          errors.push(`Invalid groupByDimension. Expected one of: ${validGroupByDimensions.join(', ')}.`);
      } else {
          groupByDimension = query.groupByDimension as ValidGroupByDimension;
      }
  }

  // Throw if errors
  if (errors.length > 0) {
      // Consider creating a custom error class for validation errors
      throw new Error(`Parameter validation failed: ${errors.join('; ')}`);
  }

  return {
      startDate: validatedStartDate,
      endDate: validatedEndDate,
      currency,
      brands,
      brandGroups,
      requestedKpis: Array.from(new Set(requestedKpis)), // Ensure unique KPIs
      groupByTime,
      groupByDimension,
      salesChannels,
      countryNames
  };
}


async function buildSqlQuery(
  params: ReturnType<typeof validateParameters>,
  baseKpisToFetch: Set<string>,
  tokenInfo: { isImpersonating?: boolean, isSuperAdmin?: boolean, tokenBrands?: number[] }
): Promise<{ query: string; queryParams: (string | number | number[])[] }> {
  let sql = "";
  const queryParams: (string | number | number[])[] = [];
  let paramIndex = 1;
  const conditions: string[] = [];
  const selectList: string[] = [];
  const groupByList: string[] = [];

  // -- SELECT Clause --
  selectList.push(`DATE_TRUNC('${params.groupByTime}', k.date)::DATE::VARCHAR AS date`);
  groupByList.push("1");

  selectList.push("k.kpi_name");
  groupByList.push("2");

  if (params.groupByDimension) {
    if (params.groupByDimension === 'brand') {
      selectList.push("k.brand AS dimension");
      groupByList.push("3");
    } else if (params.groupByDimension === 'brandGroup') {
      selectList.push("b.group AS dimension");
      groupByList.push("3");
    }
  }

  const valueExpression = params.currency === 'USD'
    ? `k.kpi_value / (CASE WHEN k.raw_kpi_unit = 'CAD' THEN COALESCE(ex.exchange_rate, 1) ELSE 1 END)`
    : `k.kpi_value`;
  selectList.push(`SUM(${valueExpression})::FLOAT8 AS value`);

  // -- JOIN Clause --
  let joins = "FROM dwh_ai.ai_reporting_ds_kpis k";
  joins += " LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name";

  if (params.currency === 'USD') {
    joins += `
      LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
           ON ex.from_currency = 'USD'
          AND ex.to_currency = 'CAD'
          AND ex.snapshot_date::DATE = k.date::DATE`;
  }

  // -- WHERE Clause --
  // Note: The `b.stage = 'Selling Actively'` filter is now applied conditionally later,
  // after checking for user-specific brand assignments from the token.

  if (params.startDate) {
    conditions.push(`k.date >= $${paramIndex++}`);
    queryParams.push(params.startDate);
  }
  if (params.endDate) {
    conditions.push(`k.date <= $${paramIndex++}`);
    queryParams.push(params.endDate);
  }

  // --- Brand Filtering Logic based on Token ---
  // --- New Brand Filtering Logic ---
  const urlBrandNames = params.brands; // These are brand NAMES from URL
  const tokenBrandIdsFromAuth = tokenInfo.tokenBrands; // These are brand IDs from token
  const isSuperAdminCtx = tokenInfo.isSuperAdmin ?? false;
  const isImpersonatingCtx = tokenInfo.isImpersonating ?? false;

  // Use same logic pattern as working brand selector: (isImpersonating || !isSuperAdmin)
  const shouldUseRestrictedAccess = isImpersonatingCtx || !isSuperAdminCtx;

  console.log('[IMPERSONATION DEBUG] Brand filtering logic:', {
    urlBrandNames,
    tokenBrandIdsFromAuth,
    isSuperAdminCtx,
    isImpersonatingCtx,
    shouldUseRestrictedAccess
  });

  if (!shouldUseRestrictedAccess) {
      // Super Admin, not impersonating: Filter by URL brands if provided, otherwise wide open (respects 'Selling Actively')
      console.log('[IMPERSONATION DEBUG] Using privileged viewer path (SuperAdmin, not impersonating)');
      if (urlBrandNames.length > 0) {
          const brandNamePlaceholders = urlBrandNames.map(() => `$${paramIndex++}`).join(',');
          conditions.push(`k.brand IN (${brandNamePlaceholders})`); // k.brand is brand_name
          queryParams.push(...urlBrandNames);
          console.log('[IMPERSONATION DEBUG] Added URL brand name filter:', urlBrandNames);
      }
  } else {
      // Regular user OR any Impersonating user: Access is governed by token brands, potentially intersected with URL brands.
      console.log('[IMPERSONATION DEBUG] Using restricted access path (Regular user OR Impersonating)');
      if (!tokenBrandIdsFromAuth || tokenBrandIdsFromAuth.length === 0) {
          // No brands in token, or tokenBrands is undefined. User sees no data.
          console.warn(`[IMPERSONATION DEBUG] ${isImpersonatingCtx ? 'Impersonating user' : 'Regular user'} has no token brands or tokenBrands array is empty. Query will return no data for brands.`);
          conditions.push("1=0");
      } else {
          // User has brands in their token - convert token brand IDs to brand names for proper filtering
          console.log('[IMPERSONATION DEBUG] User has token brands:', tokenBrandIdsFromAuth);
          
          // Import local database connection
          const { getDb } = await import('@/lib/api/db');
          const localDb = await getDb();
          
          // Convert token brand IDs to brand names
          const brandNameQuery = `SELECT name FROM Brands WHERE id IN (${tokenBrandIdsFromAuth.map(() => '?').join(',')})`;
          const brandNameResults = await localDb.all<{ name: string }[]>(brandNameQuery, tokenBrandIdsFromAuth);
          const allowedBrandNames = brandNameResults.map(row => row.name);
          
          console.log('[IMPERSONATION DEBUG] Token brand IDs mapped to names:', {
            tokenBrandIds: tokenBrandIdsFromAuth,
            allowedBrandNames
          });

          if (urlBrandNames.length > 0) {
              // Filter by intersection of URL brands and token-allowed brands
              console.log('[IMPERSONATION DEBUG] Applying intersection of URL brands and token-allowed brands');
              const finalBrandNames = urlBrandNames.filter(name => allowedBrandNames.includes(name));
              console.log('[IMPERSONATION DEBUG] Final brand names after intersection:', finalBrandNames);
              
              if (finalBrandNames.length === 0) {
                  console.log('[IMPERSONATION DEBUG] No valid brands after intersection - denying access');
                  conditions.push("1=0");
              } else {
                  const brandNamePlaceholders = finalBrandNames.map(() => `$${paramIndex++}`).join(',');
                  conditions.push(`k.brand IN (${brandNamePlaceholders})`);
                  queryParams.push(...finalBrandNames);
                  console.log('[IMPERSONATION DEBUG] Added intersected brand name filter:', finalBrandNames);
              }
          } else {
              // No URL brands specified, filter by all token-allowed brands
              console.log('[IMPERSONATION DEBUG] No URL brands specified - filtering by all token-allowed brands');
              if (allowedBrandNames.length === 0) {
                  console.log('[IMPERSONATION DEBUG] No allowed brand names found - denying access');
                  conditions.push("1=0");
              } else {
                  const brandNamePlaceholders = allowedBrandNames.map(() => `$${paramIndex++}`).join(',');
                  conditions.push(`k.brand IN (${brandNamePlaceholders})`);
                  queryParams.push(...allowedBrandNames);
                  console.log('[IMPERSONATION DEBUG] Added all token-allowed brand names filter:', allowedBrandNames);
              }
          }
      }
  }

  // Conditionally add the 'Selling Actively' stage filter.
  // This logic is preserved and interacts with the new brand filtering.
  let applyStageFilter = true;
  if ((isImpersonatingCtx || !isSuperAdminCtx) && (tokenBrandIdsFromAuth && tokenBrandIdsFromAuth.length > 0)) {
      // If user is impersonating OR not a super admin, AND they have token brands assigned,
      // then we do NOT filter by 'Selling Actively'. They see all stages for their assigned brands.
      applyStageFilter = false;
  }

  if (applyStageFilter) {
      // Apply stage filter if:
      // 1. User is SuperAdmin and not impersonating (isPrivilegedViewer = true).
      // 2. User is (Impersonating or !SuperAdmin) BUT has NO token brands.
      //    (In this case, `1=0` might already be applied from brand logic. If not, stage filter applies to a wider set before other filters).
      conditions.push(`b.stage = 'Selling Actively'`);
  }
  // --- End of New Brand Filtering Logic ---

  if (params.brandGroups.length > 0) {
    const groupPlaceholders = params.brandGroups.map(() => `$${paramIndex++}`).join(',');
    conditions.push(`b.group IN (${groupPlaceholders})`);
    queryParams.push(...params.brandGroups);
  }
  
  // Filter by sales channels if provided
  if (params.salesChannels.length > 0) {
    // Use LOWER for case-insensitive matching
    const channelConditions = params.salesChannels.map(() => `LOWER(k.sales_channel_type) = LOWER($${paramIndex++})`).join(' OR ');
    conditions.push(`(${channelConditions})`);
    queryParams.push(...params.salesChannels);
    console.log('Adding sales channel filter:', `(${channelConditions})`);
    console.log('Sales channel values:', params.salesChannels);
  }
  
  // Filter by countries if provided
  if (params.countryNames.length > 0) {
    // Use LOWER for case-insensitive matching
    const countryConditions = params.countryNames.map(() => `LOWER(k.country_name) = LOWER($${paramIndex++})`).join(' OR ');
    conditions.push(`(${countryConditions})`);
    queryParams.push(...params.countryNames);
    console.log('Adding country filter:', `(${countryConditions})`);
    console.log('Country values:', params.countryNames);
  }

  if (baseKpisToFetch.size > 0) {
     const baseKpisArray = Array.from(baseKpisToFetch);
     const kpiPlaceholders = baseKpisArray.map((_, i) => `$${paramIndex + i}`).join(',');
     conditions.push(`k.kpi_name IN (${kpiPlaceholders})`);
     baseKpisArray.forEach(kpi => queryParams.push(kpi));
     paramIndex += baseKpisArray.length;
  } else {
     // This case should ideally be prevented by validation ensuring requestedKpis is never empty after processing
     console.warn("Warning: No base KPIs resolved for query. This might indicate an issue.");
     conditions.push("1=0"); // Ensure no results if something went wrong
  }

  // -- Assemble Query --
  sql = `
    SELECT
      ${selectList.join(',\n      ')}
    ${joins}
    WHERE ${conditions.length > 0 ? conditions.join(' AND ') : '1=1'}
    GROUP BY ${groupByList.join(', ')}
    ORDER BY ${groupByList.join(', ')} ASC;
  `;

  return { query: sql, queryParams: queryParams };
}

function calculatePercent(numerator: number | null | undefined, denominator: number | null | undefined): number | null {
    if (denominator != null && denominator !== 0 && numerator != null) {
        // Return the decimal value (e.g., 0.25 for 25%)
        // This will be formatted as a percentage by the UI
        return numerator / denominator;
    } else {
        return null;
    }
}

function processAndCalculateKpis(
    dbRows: RawDbData[],
    requestedKpis: string[],
    groupByDimension?: ValidGroupByDimension
): { processedData: ProcessedBaseKpiMap; allDates: Set<string> } {
    const processedData: ProcessedBaseKpiMap = {};
    const allDates = new Set<string>();
    const allDimensions = new Set<string>();

    // 1. Populate base KPIs and collect all dates/dimensions
    dbRows.forEach(row => {
        const dimensionKey = groupByDimension ? (row.dimension ?? 'Unknown Dimension') : 'all';
        allDimensions.add(dimensionKey);
        allDates.add(row.date);

        if (!processedData[dimensionKey]) {
            processedData[dimensionKey] = {};
        }
        if (!processedData[dimensionKey][row.kpi_name]) {
          processedData[dimensionKey][row.kpi_name] = { timeSeriesMap: {}, total: 0 }; // Initialize total as number for summing
        }

        // Ensure value is treated as number before adding
        const valueToAdd = typeof row.value === 'number' ? row.value : 0;

        processedData[dimensionKey][row.kpi_name].timeSeriesMap[row.date] = valueToAdd;
        // Safely add to total, initializing if necessary
        processedData[dimensionKey][row.kpi_name].total = (processedData[dimensionKey][row.kpi_name].total ?? 0) + valueToAdd;
    });

    const sortedDates = Array.from(allDates).sort();

    // 2. Calculate derived KPIs
    allDimensions.forEach(dimKey => {
        // First, ensure all requested KPIs have a structure, even if they weren't in the DB results
        requestedKpis.forEach(kpiName => {
            if (!processedData[dimKey]) {
                processedData[dimKey] = {};
            }
            if (!processedData[dimKey][kpiName]) {
                processedData[dimKey][kpiName] = { timeSeriesMap: {}, total: null };
            }
        });

        // Helper functions scoped to current dimension
        const getBaseValue = (baseKpi: string, date: string): number | null => {
            return processedData[dimKey]?.[baseKpi]?.timeSeriesMap?.[date] ?? null;
        }
        const getBaseTotal = (baseKpi: string): number | null => {
            // Base totals were summed as numbers, return null if the base KPI doesn't exist for this dimension
            return processedData[dimKey]?.[baseKpi]?.total ?? null;
        }

        // Calculate percentage KPIs
        requestedKpis.forEach(kpiName => {
            // Skip if this KPI was directly fetched from the database and already has values
            // AND it's not a percentage KPI (which we need to calculate)
            if (!kpiName.startsWith('%') && 
                processedData[dimKey][kpiName]?.total !== null && 
                Object.keys(processedData[dimKey][kpiName]?.timeSeriesMap || {}).length > 0) {
                return;
            }

            // Calculate time series for each KPI
            sortedDates.forEach(date => {
                let calculatedValue: number | null = null;
                
                // Add specific calculation logic
                if (kpiName === 'Contribution Margin') {
                    const gm = getBaseValue('Gross Margin', date);
                    const ad = getBaseValue('Adspend', date);
                    calculatedValue = (gm === null || ad === null) ? null : gm - ad;
                } else if (kpiName === '% Gross Margin') {
                    calculatedValue = calculatePercent(getBaseValue('Gross Margin', date), getBaseValue('Net Revenue', date));
                } else if (kpiName === '% Contribution Margin') {
                    const gm = getBaseValue('Gross Margin', date);
                    const ad = getBaseValue('Adspend', date);
                    const cm = (gm === null || ad === null) ? null : gm - ad;
                    calculatedValue = calculatePercent(cm, getBaseValue('Net Revenue', date));
                } else if (kpiName === '% Adspend') {
                    calculatedValue = calculatePercent(getBaseValue('Adspend', date), getBaseValue('Net Revenue', date));
                } else if (kpiName === '% Landed Cost') {
                    calculatedValue = calculatePercent(getBaseValue('Landed Cost', date), getBaseValue('Net Revenue', date));
                } else if (kpiName === '% Fulfillment Cost') {
                    calculatedValue = calculatePercent(getBaseValue('Fulfillment Cost', date), getBaseValue('Net Revenue', date));
                } else if (kpiName === '% Transaction Cost') {
                    calculatedValue = calculatePercent(getBaseValue('Transaction Cost', date), getBaseValue('Net Revenue', date));
                } else if (kpiName === '% Discount') {
                    calculatedValue = calculatePercent(getBaseValue('Discount', date), getBaseValue('Gross Revenue', date));
                } else if (kpiName === '% Refund') {
                    calculatedValue = calculatePercent(getBaseValue('Refund', date), getBaseValue('Gross Revenue', date));
                }

                if (calculatedValue !== null) {
                    processedData[dimKey][kpiName].timeSeriesMap[date] = calculatedValue;
                }
            });

            // Calculate summary/total
            let calculatedTotal: number | null = null;
            if (kpiName === 'Contribution Margin') {
                const gmT = getBaseTotal('Gross Margin');
                const adT = getBaseTotal('Adspend');
                calculatedTotal = (gmT === null || adT === null) ? null : gmT - adT;
            } else if (kpiName === '% Gross Margin') {
                calculatedTotal = calculatePercent(getBaseTotal('Gross Margin'), getBaseTotal('Net Revenue'));
            } else if (kpiName === '% Contribution Margin') {
                const gmT = getBaseTotal('Gross Margin');
                const adT = getBaseTotal('Adspend');
                const cmTotal = (gmT === null || adT === null) ? null : gmT - adT;
                calculatedTotal = calculatePercent(cmTotal, getBaseTotal('Net Revenue'));
            } else if (kpiName === '% Adspend') {
                calculatedTotal = calculatePercent(getBaseTotal('Adspend'), getBaseTotal('Net Revenue'));
            } else if (kpiName === '% Landed Cost') {
                calculatedTotal = calculatePercent(getBaseTotal('Landed Cost'), getBaseTotal('Net Revenue'));
            } else if (kpiName === '% Fulfillment Cost') {
                calculatedTotal = calculatePercent(getBaseTotal('Fulfillment Cost'), getBaseTotal('Net Revenue'));
            } else if (kpiName === '% Transaction Cost') {
                calculatedTotal = calculatePercent(getBaseTotal('Transaction Cost'), getBaseTotal('Net Revenue'));
            } else if (kpiName === '% Discount') {
                calculatedTotal = calculatePercent(getBaseTotal('Discount'), getBaseTotal('Gross Revenue'));
            } else if (kpiName === '% Refund') {
                calculatedTotal = calculatePercent(getBaseTotal('Refund'), getBaseTotal('Gross Revenue'));
            }

            if (calculatedTotal !== null) {
                processedData[dimKey][kpiName].total = calculatedTotal;
            }
        });
    });

    return { processedData, allDates };
}


function formatResponseData(
    processedDataMap: ProcessedBaseKpiMap,
    requestedKpis: string[],
    allDatesSet: Set<string>,
    groupByDimension?: ValidGroupByDimension
): FlexibleKpiResponse {
  let finalResponse: FlexibleKpiResponse = {};
  const sortedDates = Array.from(allDatesSet).sort();

  if (groupByDimension) {
    // --- Format for Grouped Response ---
    const groupedResponse: GroupedKpiResponse = {};
    requestedKpis.forEach(kpiName => {
      groupedResponse[kpiName] = {};
      Object.keys(processedDataMap).forEach(dimensionKey => {
        // Only include dimension if it actually has data for this KPI
        const kpiDataForDim = processedDataMap[dimensionKey]?.[kpiName];
        if (kpiDataForDim) {
            const timeSeries: TimeSeriesDataPoint[] = sortedDates.map(date => ({
                date: date,
                // Use null if date doesn't exist for this kpi/dimension combo
                value: kpiDataForDim.timeSeriesMap?.[date] ?? null
            }));
            // Use null for summary if total is missing/null
            const summary: KpiSummary = {
                value: kpiDataForDim.total ?? null,
            };
            groupedResponse[kpiName][dimensionKey] = { summary, timeSeries };
        }
      });
    });
    finalResponse = groupedResponse;
  } else {
    // --- Format for Simple Response ---
    const simpleResponse: SimpleKpiResponse = {};
    const dimensionKey = 'all'; // The single key used when no dimension
    if (processedDataMap[dimensionKey]) {
        requestedKpis.forEach(kpiName => {
            const kpiData = processedDataMap[dimensionKey]?.[kpiName];
            // Only include KPI if it has data
            if (kpiData) {
                const timeSeries: TimeSeriesDataPoint[] = sortedDates.map(date => ({
                    date: date,
                    value: kpiData.timeSeriesMap?.[date] ?? null
                }));
                const summary: KpiSummary = {
                    value: kpiData.total ?? null,
                };
                simpleResponse[kpiName] = { summary, timeSeries };
            }
        });
    }
    finalResponse = simpleResponse;
  }

  return finalResponse;
}

// --- Main Handler ---
export async function GET(request: NextRequest) {
  console.log('[Flexible KPIs API] Received GET request. Url:', request.nextUrl.toString());
  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });

  if (!token) {
    console.error('[Flexible KPIs API] Unauthorized: No token found.');
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  console.log('[Flexible KPIs API] Token retrieved.');

  const {
    isImpersonating,
    isSuperAdmin,
    brands: tokenBrandsArrayRaw
  } = token as { isImpersonating?: boolean, isSuperAdmin?: boolean, brands?: number[] };
  console.log('[IMPERSONATION DEBUG] Flexible KPIs API Token details:', {
    isImpersonating,
    isSuperAdmin,
    tokenBrandsArrayRaw,
    tokenSub: token.sub,
    tokenEmail: token.email,
    originalUser: token.originalUser
  });

  // Ensure tokenBrandsArray is number[] or undefined
  const tokenBrandsArray: number[] | undefined = Array.isArray(tokenBrandsArrayRaw)
    ? tokenBrandsArrayRaw.filter(id => typeof id === 'number')
    : undefined;
  
  const searchParams = request.nextUrl.searchParams;
  console.log('[Flexible KPIs API] Raw Search Params:', searchParams.toString());
  
  // Convert searchParams to a regular object
  const params: Record<string, string | string[]> = {};
  for (const [key, value] of searchParams.entries()) {
    if (key === 'kpis' || key === 'brands' || key === 'brandGroups' || key === 'salesChannels' || key === 'countryNames') {
      // Handle array parameters
      if (params[key]) {
        if (Array.isArray(params[key])) {
          (params[key] as string[]).push(value);
        } else {
          params[key] = [params[key] as string, value];
        }
      } else {
        params[key] = [value];
      }
    } else {
      // Handle scalar parameters
      params[key] = value;
    }
  }
  
  // Handle special case: if there's a 'brand' param (singular from brand-deep-dive),
  // add it to the 'brands' array param to ensure it's included in brand filtering
  if (searchParams.has('brand') && !params['brand']) {
    const brandValue = searchParams.get('brand');
    if (brandValue) {
      if (!params['brands']) {
        params['brands'] = [brandValue];
      } else if (Array.isArray(params['brands']) && !params['brands'].includes(brandValue)) {
        // Add the singular 'brand' value to the 'brands' array if not already there
        params['brands'].push(brandValue);
      }
      // Don't need to handle the case where params['brands'] is a string, 
      // as our loop above would have already made it an array
    }
  }
  
  console.log('[Flexible KPIs API] Parsed query parameters object:', params);
  
  // Cast to a type that can include timeRange initially
  const processedParams: FlexibleKpiQueryParams & { timeRange?: string } =
    params as unknown as (FlexibleKpiQueryParams & { timeRange?: string });

  // If timeRange is provided, convert it to startDate and endDate
  // This will override any startDate/endDate from the original query if timeRange is present
  if (processedParams.timeRange && typeof processedParams.timeRange === 'string') {
    console.log(`[Flexible KPIs API] Processing timeRange: ${processedParams.timeRange}`);
    const { startDate: rangeStartDate, endDate: rangeEndDate } = convertTimeRangeToDates(processedParams.timeRange);
    if (rangeStartDate && rangeEndDate) {
      processedParams.startDate = rangeStartDate;
      processedParams.endDate = rangeEndDate;
      console.log(`[Flexible KPIs API] Converted timeRange to startDate: ${processedParams.startDate}, endDate: ${processedParams.endDate}`);
    }
    // Remove timeRange so it's not passed to validateParameters as an unknown param
    delete processedParams.timeRange;
  }

  const queryParams: FlexibleKpiQueryParams = processedParams; // Now it's the correct type

  try {
    console.log('[Flexible KPIs API] Validating parameters...');
    const validatedParams = validateParameters(queryParams);
    console.log('[Flexible KPIs API] Validated parameters (after timeRange processing):', JSON.stringify(validatedParams, null, 2));

    const kpiAmountToPercentPairs: Record<string, string> = {
      'Gross Margin': '% Gross Margin',
      'Contribution Margin': '% Contribution Margin',
      'Adspend': '% Adspend',
      'Landed Cost': '% Landed Cost',
      'Fulfillment Cost': '% Fulfillment Cost',
      'Transaction Cost': '% Transaction Cost',
      'Discount': '% Discount',
      'Refund': '% Refund'
    };

    const currentRequestedKpis = new Set(validatedParams.requestedKpis);
    for (const amountKpi of validatedParams.requestedKpis) {
      const percentKpiName = kpiAmountToPercentPairs[amountKpi];
      if (percentKpiName && ALL_AVAILABLE_KPIS.has(percentKpiName) && !currentRequestedKpis.has(percentKpiName)) {
        currentRequestedKpis.add(percentKpiName);
      }
    }
    validatedParams.requestedKpis = Array.from(currentRequestedKpis);
    console.log('[Flexible KPIs API] Requested KPIs after auto-adding percentages:', JSON.stringify(validatedParams.requestedKpis, null, 2));
    
    console.log('[Flexible KPIs API] Resolving base KPIs to fetch...');
    const baseKpisToFetch = resolveRequiredBaseKpis(validatedParams.requestedKpis);
    console.log('[Flexible KPIs API] Base KPIs to fetch from DB:', JSON.stringify(Array.from(baseKpisToFetch), null, 2));

    console.log(`[Flexible KPIs API] Executing Flexible KPI query (Currency: ${validatedParams.currency}, GroupByTime: ${validatedParams.groupByTime}, GroupByDim: ${validatedParams.groupByDimension ?? 'None'})`);
    
    console.log('[Flexible KPIs API] Building SQL query...');
    const { query, queryParams: sqlParams } = await buildSqlQuery(
      validatedParams,
      baseKpisToFetch,
      { isImpersonating, isSuperAdmin, tokenBrands: tokenBrandsArray }
    );
    console.log('[Flexible KPIs API] Built SQL query:', query);
    console.log('[Flexible KPIs API] SQL query parameters:', JSON.stringify(sqlParams, null, 2));
    
    const redshiftPool = getRedshiftPool();
    if (!redshiftPool) {
      console.error('[Flexible KPIs API] Redshift connection pool is not available at query time.');
      throw new Error('Redshift connection pool is not available');
    }
    console.log('[Flexible KPIs API] Redshift pool is available. Attempting to connect client...');
    
    const client = await redshiftPool.connect();
    console.log('[Flexible KPIs API] Redshift client connected.');
    let dbRows: RawDbData[] = [];
    
    try {
      const queryStartTime = Date.now();
      console.log('[Flexible KPIs API] Executing Redshift query...');
      console.log(`[Flexible KPIs API] Query performance tracking - brands filter: ${validatedParams.brands.length === 0 ? 'ALL BRANDS (no filter)' : `${validatedParams.brands.length} specific brands`}`);
      
      const result = await client.query<RawDbData>(query, sqlParams);
      const queryDuration = Date.now() - queryStartTime;
      
      dbRows = result.rows;
      console.log(`[Flexible KPIs API] Query completed in ${queryDuration}ms, returned ${dbRows.length} rows.`);
      
      if (queryDuration > 5000) {
        console.warn(`[Flexible KPIs API] SLOW QUERY WARNING: Query took ${queryDuration}ms (over 5 seconds)`);
      }
      
      if (dbRows.length < 10) { // Log first few rows if the set is small
          console.log('[Flexible KPIs API] First few rows:', JSON.stringify(dbRows.slice(0,10), null, 2));
      }
    } catch (dbError) {
      console.error('[Flexible KPIs API] Error executing Redshift query:', dbError);
      // Log detailed error information if available
      if (dbError instanceof Error) {
        console.error('[Flexible KPIs API] DB Error Name:', dbError.name);
        console.error('[Flexible KPIs API] DB Error Message:', dbError.message);
        console.error('[Flexible KPIs API] DB Error Stack:', dbError.stack);
      }
      throw dbError; // Re-throw to be caught by the main handler
    } finally {
      console.log('[Flexible KPIs API] Releasing Redshift client.');
      client.release();
      console.log('[Flexible KPIs API] Redshift client released.');
    }
    
    console.log('[Flexible KPIs API] Processing and calculating KPIs from DB rows...');
    const { processedData, allDates } = processAndCalculateKpis(
      dbRows,
      validatedParams.requestedKpis,
      validatedParams.groupByDimension
    );
    console.log('[Flexible KPIs API] Finished processing and calculating KPIs.');

    console.log('[Flexible KPIs API] Formatting response data...');
    const finalResponseData = formatResponseData(
      processedData,
      validatedParams.requestedKpis,
      allDates,
      validatedParams.groupByDimension
    );
    console.log('[Flexible KPIs API] Finished formatting response data. Sending response.');

    return NextResponse.json(finalResponseData);
  } catch (error: unknown) {
    console.error(`[Flexible KPIs API] Overall API Error:`, error);
    let errorMessage = 'An unexpected error occurred.';
    let statusCode = 500;

    if (error instanceof Error) {
      console.error('[Flexible KPIs API] Error Name:', error.name);
      console.error('[Flexible KPIs API] Error Message:', error.message);
      console.error('[Flexible KPIs API] Error Stack:', error.stack);
      if (error.message.startsWith('Parameter validation failed:')) {
        statusCode = 400;
        errorMessage = error.message;
      } else {
        errorMessage = error.message;
      }
    } else {
      console.error('[Flexible KPIs API] Unknown error type:', typeof error, JSON.stringify(error, null, 2));
    }

    const errorResponse: ErrorResponse = {
      error: statusCode === 400 ? 'Invalid request parameters.' : 'Failed to fetch flexible KPI data.',
      details: errorMessage,
    };
    console.log(`[Flexible KPIs API] Sending error response (Status ${statusCode}):`, JSON.stringify(errorResponse, null, 2));
    return NextResponse.json(errorResponse, { status: statusCode });
  }
}

// No longer needed - removed mock data generation function
