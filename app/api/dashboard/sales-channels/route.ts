import { NextRequest, NextResponse } from 'next/server';

import { getRedshiftPool } from '@/lib/api/redshift';
import { getToken } from 'next-auth/jwt';

export async function GET(request: NextRequest) {
  try {
    const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { isImpersonating, isSuperAdmin, brands: tokenBrands } = token as { isImpersonating?: boolean, isSuperAdmin?: boolean, brands?: unknown };

    // Process brand IDs from token
    let processedBrandIds: (string | number)[] = [];
    if (Array.isArray(tokenBrands)) {
      processedBrandIds = tokenBrands
        .map(id => {
          if (typeof id === 'number' || typeof id === 'string') {
            return id;
          }
          return null;
        })
        .filter((id): id is string | number => id !== null);
    }

    // Check if Redshift pool is available
    const redshiftPool = getRedshiftPool();
    if (!redshiftPool) {
      throw new Error('Redshift connection pool is not available');
    }
    
    const client = await redshiftPool.connect();
    
    try {
      // First, check if we need to join with brands table to get brand_id
      let query = `
        SELECT DISTINCT k.sales_channel_type
        FROM dwh_ai.ai_reporting_ds_kpis k
        JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
      `;
      const queryParams: (string | number)[] = [];
      const whereClauses: string[] = ["k.sales_channel_type IS NOT NULL"];

      // For both impersonating and non-SuperAdmin users, we need to filter by brands
      if (isImpersonating || !isSuperAdmin) {
        // Check if the user has any brands assigned (directly or via groups)
        if (processedBrandIds.length === 0) {
          console.warn(`${isImpersonating ? 'Impersonating' : 'Non-SuperAdmin'} user with no brands assigned. Returning empty sales channels list.`);
          return NextResponse.json([]);
        }
        
        // Use IN clause with individual parameters
        const placeholders = processedBrandIds.map((_, i) => `$${queryParams.length + i + 1}`).join(',');
        whereClauses.push(`b.brand_id IN (${placeholders})`);
        // Add each brand ID as a separate parameter
        processedBrandIds.forEach(id => queryParams.push(id));
      }
      // SuperAdmins (not impersonating) get all sales channels, so no additional filtering needed

      if (whereClauses.length > 0) {
        query += ` WHERE ${whereClauses.join(' AND ')}`;
      }
      
      query += ` ORDER BY k.sales_channel_type ASC;`;
      
      const result = await client.query(query, queryParams);
      
      const salesChannels = result.rows.map(row => row.sales_channel_type);
      
      return NextResponse.json(salesChannels);
    } catch (error) {
      console.error('Error executing Redshift query:', error);
      throw error;
    } finally {
      client.release();
    }
  } catch (error: unknown) {
    console.error(`API Error fetching sales channels:`, error);
    let errorMessage = 'An unexpected error occurred.';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }
    
    return NextResponse.json({ error: 'Failed to fetch sales channels.', details: errorMessage }, { status: statusCode });
  }
}
