import { NextRequest, NextResponse } from 'next/server';
import {
  deleteSavedView,
  getSavedViewById,
  updateSavedView
} from '@/lib/api/saved-views-service';

import { authOptions } from '@/lib/auth-options';
import { getServerSession } from 'next-auth';

/**
 * GET /api/dashboard/saved-views/:id
 * Retrieves a specific saved view by ID
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const { id } = params;
    
    // Get the current user session
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    if (!id || isNaN(Number(id))) {
      return NextResponse.json({ error: 'Invalid view ID' }, { status: 400 });
    }

    // Get the saved view
    const view = await getSavedViewById(Number(id), Number(session.user.id));
    
    if (!view) {
      return NextResponse.json(
        { error: 'Saved view not found' }, 
        { status: 404 }
      );
    }
    
    return NextResponse.json(view);
  } catch (error) {
    console.error('Error retrieving saved view:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve saved view' }, 
      { status: 500 }
    );
  }
}

/**
 * PUT /api/dashboard/saved-views/:id
 * Updates an existing saved view
 */
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const { id } = params;
    
    // Get the current user session
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    if (!id || isNaN(Number(id))) {
      return NextResponse.json({ error: 'Invalid view ID' }, { status: 400 });
    }

    // Get the request body
    const body = await request.json();
    
    // Validate that at least one field to update is provided
    if (!body.name && !body.filter_data) {
      return NextResponse.json(
        { error: 'At least one field to update is required' }, 
        { status: 400 }
      );
    }

    // Prepare the update data
    const updateData: { name?: string; filter_data?: string } = {};
    
    if (body.name) {
      updateData.name = body.name;
    }
    
    if (body.filter_data) {
      updateData.filter_data = typeof body.filter_data === 'string' 
        ? body.filter_data 
        : JSON.stringify(body.filter_data);
    }

    // Update the saved view
    const updatedView = await updateSavedView(
      Number(id),
      Number(session.user.id),
      updateData
    );
    
    return NextResponse.json(updatedView);
  } catch (error) {
    console.error('Error updating saved view:', error);
    
    // Handle specific error for view not found
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json({ error: error.message }, { status: 404 });
    }
    
    // Handle specific error for duplicate view names
    if (error instanceof Error && error.message.includes('already exists')) {
      return NextResponse.json({ error: error.message }, { status: 409 });
    }
    
    return NextResponse.json(
      { error: 'Failed to update saved view' }, 
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/dashboard/saved-views/:id
 * Deletes a saved view
 */
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const { id } = params;
    
    // Get the current user session
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    if (!id || isNaN(Number(id))) {
      return NextResponse.json({ error: 'Invalid view ID' }, { status: 400 });
    }

    // Delete the saved view
    const success = await deleteSavedView(Number(id), Number(session.user.id));
    
    if (success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { error: 'Failed to delete saved view' }, 
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error deleting saved view:', error);
    
    // Handle specific error for view not found
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json({ error: error.message }, { status: 404 });
    }
    
    return NextResponse.json(
      { error: 'Failed to delete saved view' }, 
      { status: 500 }
    );
  }
}
