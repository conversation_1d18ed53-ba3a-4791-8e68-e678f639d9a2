import { NextRequest, NextResponse } from 'next/server';
import {
  createSavedView,
  getSavedViews
} from '@/lib/api/saved-views-service';

import { authOptions } from '@/lib/auth-options';
import { getServerSession } from 'next-auth';

/**
 * GET /api/dashboard/saved-views
 * Retrieves all saved views for the current user and specified page type
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the page type from the query parameters
    const searchParams = request.nextUrl.searchParams;
    const pageType = searchParams.get('pageType');
    
    if (!pageType) {
      return NextResponse.json({ error: 'Page type is required' }, { status: 400 });
    }

    // Get the saved views for the user and page type
    const views = await getSavedViews(Number(session.user.id), pageType);
    
    return NextResponse.json(views);
  } catch (error) {
    console.error('Error retrieving saved views:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve saved views' }, 
      { status: 500 }
    );
  }
}

/**
 * POST /api/dashboard/saved-views
 * Creates a new saved view
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.page_type || !body.filter_data) {
      return NextResponse.json(
        { error: 'Name, page type, and filter data are required' }, 
        { status: 400 }
      );
    }

    // Create the saved view
    const savedView = await createSavedView({
      user_id: Number(session.user.id),
      name: body.name,
      page_type: body.page_type,
      filter_data: typeof body.filter_data === 'string' 
        ? body.filter_data 
        : JSON.stringify(body.filter_data)
    });
    
    return NextResponse.json(savedView, { status: 201 });
  } catch (error) {
    console.error('Error creating saved view:', error);
    
    // Handle specific error for duplicate view names
    if (error instanceof Error && error.message.includes('already exists')) {
      return NextResponse.json({ error: error.message }, { status: 409 });
    }
    
    return NextResponse.json(
      { error: 'Failed to create saved view' }, 
      { status: 500 }
    );
  }
}

// Note: PUT and DELETE handlers have been moved to the [id]/route.ts file
