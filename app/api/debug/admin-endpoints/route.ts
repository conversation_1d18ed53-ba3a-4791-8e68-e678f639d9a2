import { NextResponse } from 'next/server';
import { authOptions } from '@/lib/auth-options';
import { getServerSession } from 'next-auth';
import { hasAdminAccess } from '@/lib/api/admin-auth';

export async function GET() {
  try {
    console.log('[DEBUG ADMIN ENDPOINTS] Starting admin endpoints diagnostic');
    
    // Get the session
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({
        error: 'No session found',
        authenticated: false
      }, { status: 401 });
    }

    // Test admin access function
    const adminAccess = hasAdminAccess(session);
    
    // Simulate the exact checks that admin endpoints perform
    const endpointTests = {
      groups: {
        endpoint: '/api/admin/groups',
        hasAccess: adminAccess,
        sessionCheck: !!session,
        adminAccessCheck: hasAdminAccess(session),
        userRoles: session.user?.roles || [],
        expectedRoles: ['Admin', 'Super Admin'],
        hasAdminRole: session.user?.roles?.includes('Admin') || false,
        hasSuperAdminRole: session.user?.roles?.includes('Super Admin') || false,
        wouldReturn401: !hasAdminAccess(session)
      },
      permissions: {
        endpoint: '/api/admin/permissions',
        hasAccess: adminAccess,
        sessionCheck: !!session,
        adminAccessCheck: hasAdminAccess(session),
        userRoles: session.user?.roles || [],
        expectedRoles: ['Admin', 'Super Admin'],
        hasAdminRole: session.user?.roles?.includes('Admin') || false,
        hasSuperAdminRole: session.user?.roles?.includes('Super Admin') || false,
        wouldReturn401: !hasAdminAccess(session)
      },
      brands: {
        endpoint: '/api/admin/brands',
        hasAccess: adminAccess,
        sessionCheck: !!session,
        adminAccessCheck: hasAdminAccess(session),
        userRoles: session.user?.roles || [],
        expectedRoles: ['Admin', 'Super Admin'],
        hasAdminRole: session.user?.roles?.includes('Admin') || false,
        hasSuperAdminRole: session.user?.roles?.includes('Super Admin') || false,
        wouldReturn401: !hasAdminAccess(session)
      }
    };

    // Additional diagnostic info
    const diagnosticInfo = {
      sessionUser: {
        id: session.user?.id,
        email: session.user?.email,
        name: session.user?.name,
        isSuperAdmin: session.user?.isSuperAdmin,
        roles: session.user?.roles || [],
        permissions: session.user?.permissions || [],
        isImpersonating: session.user?.isImpersonating || false
      },
      adminAuthChecks: {
        hasAdminAccess: hasAdminAccess(session),
        sessionExists: !!session,
        userExists: !!session?.user,
        rolesArray: session?.user?.roles || [],
        hasAdminInRoles: session?.user?.roles?.includes('Admin') || false,
        hasSuperAdminInRoles: session?.user?.roles?.includes('Super Admin') || false,
        adminAuthFunction: hasAdminAccess(session)
      },
      expectedForSuperAdmin: {
        email: '<EMAIL>',
        roles: ['Admin', 'Super Admin'],
        shouldHaveAccess: true
      }
    };

    console.log('[DEBUG ADMIN ENDPOINTS] Diagnostic completed:', {
      email: session.user?.email,
      roles: session.user?.roles,
      adminAccess,
      allEndpointsWouldFail: endpointTests.groups.wouldReturn401
    });

    return NextResponse.json({
      endpointTests,
      diagnosticInfo,
      summary: {
        authenticated: true,
        hasAdminAccess: adminAccess,
        allEndpointsWouldReturn401: !adminAccess,
        userEmail: session.user?.email,
        userRoles: session.user?.roles || [],
        expectedEmail: '<EMAIL>',
        isExpectedSuperAdmin: session.user?.email === '<EMAIL>'
      }
    });
    
  } catch (error) {
    console.error('[DEBUG ADMIN ENDPOINTS] Error during diagnostic:', error);
    return NextResponse.json({
      error: 'Failed to test admin endpoints',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}