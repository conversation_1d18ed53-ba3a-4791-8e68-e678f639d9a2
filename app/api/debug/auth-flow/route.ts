import { NextRequest, NextResponse } from 'next/server';

import { authOptions } from '@/lib/auth-options';
import { getServerSession } from 'next-auth';
import { getToken } from 'next-auth/jwt';

export async function GET(request: NextRequest) {
  console.log('[AUTH FLOW DEBUG] Starting comprehensive auth flow diagnostic');
  
  try {
    // Test 1: Check if session exists
    console.log('[AUTH FLOW DEBUG] Test 1: Checking server session...');
    const session = await getServerSession(authOptions);
    console.log('[AUTH FLOW DEBUG] Session result:', !!session);
    
    // Test 2: Check JWT token from request
    console.log('[AUTH FLOW DEBUG] Test 2: Checking JWT token from request...');
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET
    });
    console.log('[AUTH FLOW DEBUG] Token result:', !!token);
    
    // Test 3: Check cookies
    console.log('[AUTH FLOW DEBUG] Test 3: Examining cookies...');
    const cookies = request.cookies;
    const cookieNames: string[] = [];
    for (const [name] of cookies) {
      cookieNames.push(name);
    }
    console.log('[AUTH FLOW DEBUG] Available cookies:', cookieNames);
    
    const sessionToken = cookies.get('next-auth.session-token');
    const sessionTokenSecure = cookies.get('__Secure-next-auth.session-token');
    const callbackUrl = cookies.get('next-auth.callback-url');
    const csrfToken = cookies.get('next-auth.csrf-token');
    
    console.log('[AUTH FLOW DEBUG] NextAuth cookies:', {
      sessionToken: !!sessionToken,
      sessionTokenSecure: !!sessionTokenSecure,
      callbackUrl: !!callbackUrl,
      csrfToken: !!csrfToken
    });
    
    // Test 4: Environment check
    console.log('[AUTH FLOW DEBUG] Test 4: Environment configuration...');
    const envCheck = {
      nextauthUrl: process.env.NEXTAUTH_URL,
      nextauthSecret: !!process.env.NEXTAUTH_SECRET,
      googleClientId: !!process.env.GOOGLE_CLIENT_ID,
      googleClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,
      nodeEnv: process.env.NODE_ENV
    };
    console.log('[AUTH FLOW DEBUG] Environment check:', envCheck);
    
    // Test 5: Manual JWT decode attempt if token exists
    let manualTokenDecode = null;
    if (sessionToken || sessionTokenSecure) {
      console.log('[AUTH FLOW DEBUG] Test 5: Manual JWT decode attempt...');
      try {
        const tokenValue = sessionToken?.value || sessionTokenSecure?.value;
        if (tokenValue) {
          const { decode } = await import('next-auth/jwt');
          manualTokenDecode = await decode({ 
            secret: process.env.NEXTAUTH_SECRET!, 
            token: tokenValue 
          });
          console.log('[AUTH FLOW DEBUG] Manual decode successful:', !!manualTokenDecode);
        }
      } catch (decodeError) {
        console.error('[AUTH FLOW DEBUG] Manual decode failed:', decodeError);
        manualTokenDecode = { error: decodeError instanceof Error ? decodeError.message : 'Unknown error' };
      }
    }
    
    return NextResponse.json({
      success: true,
      tests: {
        serverSession: {
          exists: !!session,
          userEmail: session?.user?.email,
          userId: session?.user?.id,
          roles: session?.user?.roles,
          permissions: session?.user?.permissions?.length || 0
        },
        jwtToken: {
          exists: !!token,
          userEmail: token?.email,
          userId: token?.sub,
          roles: token?.roles,
          permissions: token?.permissions?.length || 0
        },
        cookies: {
          available: cookieNames,
          sessionToken: !!sessionToken,
          sessionTokenSecure: !!sessionTokenSecure,
          callbackUrl: !!callbackUrl,
          csrfToken: !!csrfToken
        },
        environment: envCheck,
        manualDecode: manualTokenDecode
      },
      headers: {
        userAgent: request.headers.get('user-agent'),
        host: request.headers.get('host'),
        origin: request.headers.get('origin'),
        referer: request.headers.get('referer')
      }
    });
    
  } catch (error) {
    console.error('[AUTH FLOW DEBUG] Error during auth flow diagnostic:', error);
    
    const errorDetails = {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    };
    
    return NextResponse.json({
      success: false,
      error: errorDetails,
      isJwtError: errorDetails.name.includes('JWT') || errorDetails.name.includes('JWE') || errorDetails.message.includes('JWT'),
      isCryptographicError: errorDetails.message.includes('Encryption') || errorDetails.message.includes('decrypt')
    }, { 
      status: 500
    });
  }
}
