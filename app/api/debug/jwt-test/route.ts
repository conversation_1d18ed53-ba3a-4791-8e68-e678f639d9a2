import { decode, encode } from 'next-auth/jwt';

import { NextResponse } from 'next/server';

export async function GET() {
  console.log('[JWT TEST] Starting JWT diagnostic test');
  
  try {
    const secret = process.env.NEXTAUTH_SECRET;
    
    if (!secret) {
      return NextResponse.json({
        error: 'NEXTAUTH_SECRET not found',
        success: false
      });
    }
    
    console.log('[JWT TEST] Secret available:', !!secret);
    console.log('[JWT TEST] Secret length:', secret.length);
    console.log('[JWT TEST] Secret format check:', secret.startsWith('=') ? 'base64-like' : 'plain');
    
    // Test 1: Simple token encoding/decoding
    const simpleToken = {
      sub: 'test-user-123',
      email: '<EMAIL>',
      name: 'Test User',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 60 * 60 // 1 hour
    };
    
    console.log('[JWT TEST] Testing simple token encoding...');
    const encodedSimple = await encode({ secret, token: simpleToken });
    console.log('[JWT TEST] Simple token encoded successfully:', !!encodedSimple);
    
    console.log('[JWT TEST] Testing simple token decoding...');
    const decodedSimple = await decode({ secret, token: encodedSimple });
    console.log('[JWT TEST] Simple token decoded successfully:', !!decodedSimple);
    
    // Test 2: Complex token with user data (similar to actual auth)
    const complexToken = {
      sub: 'test-user-123',
      email: '<EMAIL>',
      name: 'Test User',
      roles: ['Admin', 'Super Admin'],
      permissions: ['view_users', 'manage_users', 'view_roles', 'manage_roles', 'view_permissions', 'manage_permissions'],
      groups: [1, 2, 3],
      brands: [1, 2, 3, 4, 5],
      isSuperAdmin: true,
      isImpersonating: false,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 60 * 60
    };
    
    console.log('[JWT TEST] Testing complex token encoding...');
    const encodedComplex = await encode({ secret, token: complexToken });
    console.log('[JWT TEST] Complex token encoded successfully:', !!encodedComplex);
    
    console.log('[JWT TEST] Testing complex token decoding...');
    const decodedComplex = await decode({ secret, token: encodedComplex });
    console.log('[JWT TEST] Complex token decoded successfully:', !!decodedComplex);
    
    // Test 3: Very large token (stress test)
    const largeToken = {
      ...complexToken,
      permissions: Array.from({ length: 50 }, (_, i) => `permission_${i}`),
      groups: Array.from({ length: 20 }, (_, i) => i + 1),
      brands: Array.from({ length: 100 }, (_, i) => i + 1),
      metadata: {
        largeData: 'x'.repeat(1000),
        moreData: Array.from({ length: 100 }, (_, i) => ({ id: i, value: `data_${i}` }))
      }
    };
    
    console.log('[JWT TEST] Testing large token encoding...');
    const encodedLarge = await encode({ secret, token: largeToken });
    console.log('[JWT TEST] Large token encoded successfully:', !!encodedLarge);
    
    console.log('[JWT TEST] Testing large token decoding...');
    const decodedLarge = await decode({ secret, token: encodedLarge });
    console.log('[JWT TEST] Large token decoded successfully:', !!decodedLarge);
    
    return NextResponse.json({
      success: true,
      tests: {
        secretAvailable: !!secret,
        secretLength: secret.length,
        simpleToken: {
          encoded: !!encodedSimple,
          decoded: !!decodedSimple,
          matches: decodedSimple?.sub === simpleToken.sub
        },
        complexToken: {
          encoded: !!encodedComplex,
          decoded: !!decodedComplex,
          matches: decodedComplex?.sub === complexToken.sub,
          rolesMatch: JSON.stringify(decodedComplex?.roles) === JSON.stringify(complexToken.roles)
        },
        largeToken: {
          encoded: !!encodedLarge,
          decoded: !!decodedLarge,
          matches: decodedLarge?.sub === largeToken.sub,
          tokenSize: encodedLarge?.length || 0
        }
      }
    });
    
  } catch (error) {
    console.error('[JWT TEST] Error during JWT test:', error);
    
    const errorDetails = {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    };
    
    console.error('[JWT TEST] Error details:', errorDetails);
    
    return NextResponse.json({
      success: false,
      error: errorDetails,
      isJwtError: errorDetails.name.includes('JWT') || errorDetails.name.includes('JWE'),
      isCryptographicError: errorDetails.message.includes('Encryption') || errorDetails.message.includes('decrypt')
    }, { status: 500 });
  }
}