import { hasAdminAccess, hasSuperAdminAccess } from '@/lib/api/admin-auth';

import { NextResponse } from 'next/server';
import { authOptions } from '@/lib/auth-options';
import { getServerSession } from 'next-auth';

export async function GET() {
  try {
    console.log('[DEBUG SESSION API] Starting session diagnostic');
    
    // Get the session
    const session = await getServerSession(authOptions);
    
    if (!session) {
      console.log('[DEBUG SESSION API] No session found');
      return NextResponse.json({
        error: 'No session found',
        authenticated: false
      }, { status: 401 });
    }

    console.log('[DEBUG SESSION API] Session found, analyzing...');

    // Check admin access
    const adminAccess = hasAdminAccess(session);
    const superAdminAccess = hasSuperAdminAccess(session);

    // Prepare diagnostic data
    const diagnosticData = {
      authenticated: true,
      sessionExists: !!session,
      userExists: !!session.user,
      
      // Basic user info
      userId: session.user?.id,
      userEmail: session.user?.email,
      userName: session.user?.name,
      
      // Super admin flags
      isSuperAdmin: session.user?.isSuperAdmin,
      expectedSuperAdmin: session.user?.email === '<EMAIL>',
      
      // Roles and permissions
      roles: session.user?.roles || [],
      permissions: session.user?.permissions || [],
      expectedRoles: ['Admin', 'Super Admin'],
      expectedPermissions: [
        'view_groups', 'manage_groups',
        'view_permissions', 'manage_permissions', 
        'view_brands', 'manage_brands',
        'view_users', 'manage_users',
        'view_roles', 'manage_roles'
      ],
      
      // Access checks
      hasAdminAccess: adminAccess,
      hasSuperAdminAccess: superAdminAccess,
      
      // Groups and brands
      groups: session.user?.groups || [],
      brands: session.user?.brands || [],
      
      // Impersonation status
      isImpersonating: session.user?.isImpersonating || false,
      originalUser: session.user?.originalUser || null,
      
      // Session-level properties
      sessionIsImpersonating: session.isImpersonating || false,
      sessionOriginalUserId: session.originalUserId || null,
      
      // Validation checks
      validationChecks: {
        hasExpectedEmail: session.user?.email === '<EMAIL>',
        hasAdminRole: session.user?.roles?.includes('Admin') || false,
        hasSuperAdminRole: session.user?.roles?.includes('Super Admin') || false,
        hasViewGroupsPermission: session.user?.permissions?.includes('view_groups') || false,
        hasManageGroupsPermission: session.user?.permissions?.includes('manage_groups') || false,
        hasViewPermissionsPermission: session.user?.permissions?.includes('view_permissions') || false,
        hasManagePermissionsPermission: session.user?.permissions?.includes('manage_permissions') || false,
        hasViewBrandsPermission: session.user?.permissions?.includes('view_brands') || false,
        hasManageBrandsPermission: session.user?.permissions?.includes('manage_brands') || false,
      }
    };

    console.log('[DEBUG SESSION API] Diagnostic data prepared:', {
      email: diagnosticData.userEmail,
      roles: diagnosticData.roles,
      permissions: diagnosticData.permissions?.length,
      adminAccess: diagnosticData.hasAdminAccess,
      superAdminAccess: diagnosticData.hasSuperAdminAccess
    });

    return NextResponse.json(diagnosticData);
    
  } catch (error) {
    console.error('[DEBUG SESSION API] Error during session diagnostic:', error);
    return NextResponse.json({
      error: 'Failed to analyze session',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}