import { NextRequest, NextResponse } from "next/server";

import { getCampaignDetails } from "@/lib/api/campaign-detail-service";

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ campaignId: string }> }
) {
  try {
    const params = await context.params;
    const { campaignId } = params;

    if (!campaignId) {
      return NextResponse.json(
        { error: "Campaign ID is required" },
        { status: 400 }
      );
    }

    // Get campaign details from the service
    const campaignDetails = await getCampaignDetails(campaignId);

    // Return the campaign details
    return NextResponse.json(campaignDetails);
  } catch (error) {
    console.error("Error fetching campaign details:", error);
    return NextResponse.json(
      { error: "Failed to fetch campaign details" },
      { status: 500 }
    );
  }
}
