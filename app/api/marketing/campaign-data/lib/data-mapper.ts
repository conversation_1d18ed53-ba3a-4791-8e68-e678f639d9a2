// data-mapper.ts
import { CampaignDataPoint, CampaignDetailData, RedshiftRow } from './types';

/**
 * Maps a Redshift row to a CampaignDataPoint
 */
export function mapToCampaignDataPoint(row: RedshiftRow): CampaignDataPoint {
  return {
    date: row.date || '',
    campaign_name: row.campaign_name ? String(row.campaign_name) : undefined,
    campaign_brand_id: row.campaign_brand_id ? Number(row.campaign_brand_id) : undefined,
    brand_name: row.brand_name ? String(row.brand_name) : undefined,
    sales_channel_type: row.sales_channel_type ? String(row.sales_channel_type) : undefined,
    totalSpend: parseFloat(String(row.totalspend ?? 0)),
    FacebookSpend: parseFloat(String(row.facebookspend ?? 0)),
    InstagramSpend: parseFloat(String(row.instagramspend ?? 0)),
    GoogleSpend: parseFloat(String(row.googlespend ?? 0)),
    TikTokSpend: parseFloat(String(row.tiktokspend ?? 0)),
    AmazonSpend: parseFloat(String(row.amazonspend ?? 0)),
    AwarenessSpend: parseFloat(String(row.awarenessspend ?? 0)),
    ConversionSpend: parseFloat(String(row.conversionspend ?? 0)),
    RetargetingSpend: parseFloat(String(row.retargetingspend ?? 0)),
    SeasonalSpend: parseFloat(String(row.seasonalspend ?? 0)),
    totalImpressions: row.totalimpressions ? parseFloat(String(row.totalimpressions)) : undefined,
    totalClicks: row.totalclicks ? parseFloat(String(row.totalclicks)) : undefined,
    totalConversions: row.totalconversions ? parseFloat(String(row.totalconversions)) : undefined,
    totalConversionValue: row.totalconversionvalue ? parseFloat(String(row.totalconversionvalue)) : undefined,
    roas: row.roas ? parseFloat(String(row.roas)) : undefined,
    cpa: row.cpa ? parseFloat(String(row.cpa)) : undefined,
  };
}

/**
 * Maps a Redshift row to a CampaignDetailData (for single campaign details)
 */
export function mapToCampaignDetailData(row: RedshiftRow): CampaignDetailData {
  return {
    id: String(row.campaign_name),
    name: String(row.campaign_name),
    brand_name: row.brand_name ? String(row.brand_name) : undefined,
    campaign_brand_id: row.campaign_brand_id ? Number(row.campaign_brand_id) : undefined,
    totalSpend: parseFloat(String(row.totalspend ?? 0)),
    FacebookSpend: parseFloat(String(row.facebookspend ?? 0)),
    InstagramSpend: parseFloat(String(row.instagramspend ?? 0)),
    GoogleSpend: parseFloat(String(row.googlespend ?? 0)),
    TikTokSpend: parseFloat(String(row.tiktokspend ?? 0)),
    AmazonSpend: parseFloat(String(row.amazonspend ?? 0)),
    AwarenessSpend: parseFloat(String(row.awarenessspend ?? 0)),
    ConversionSpend: parseFloat(String(row.conversionspend ?? 0)),
    RetargetingSpend: parseFloat(String(row.retargetingspend ?? 0)),
    SeasonalSpend: parseFloat(String(row.seasonalspend ?? 0)),
    totalImpressions: row.totalimpressions ? parseFloat(String(row.totalimpressions)) : undefined,
    totalClicks: row.totalclicks ? parseFloat(String(row.totalclicks)) : undefined,
    totalConversions: row.totalconversions ? parseFloat(String(row.totalconversions)) : undefined,
    totalConversionValue: row.totalconversionvalue ? parseFloat(String(row.totalconversionvalue)) : undefined,
    roas: row.roas ? parseFloat(String(row.roas)) : undefined,
    cpa: row.cpa ? parseFloat(String(row.cpa)) : undefined,
    startDate: row.min_date,
    endDate: row.max_date,
    channels: row.platform_array,
  };
}
