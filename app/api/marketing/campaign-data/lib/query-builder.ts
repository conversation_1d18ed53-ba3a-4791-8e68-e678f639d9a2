import { SqlQueryResult, TokenInfo, ValidatedParams } from './types';

import { getSortByToSqlIdentifierMap } from './utils';

/**
 * Builds the SQL query based on validated parameters
 * Optimized for Redshift (PostgreSQL 8.0.2)
 */
export async function buildSqlQuery(
  params: ValidatedParams,
  tokenInfo: TokenInfo
): Promise<SqlQueryResult> {
  const queryParams: (string | number | number[] | boolean)[] = [];
  let paramIndex = 1;
  const conditions: string[] = [];
  let selectList: string[] = [];
  let groupByClause = "";
  let orderByClause = "";

  // Build joins for the query - using exact match like the working query
  let joins = "FROM dwh_ai.ai_reporting_ds_marketing_advertisings m";
  joins += " LEFT JOIN dwh_ai.ai_reporting_brands b ON m.brand = b.name";
  
  // Exchange rate join - exactly like the working query
  const targetCurrency = params.currency === 'USD' ? 'USD' : 'CAD';
  joins += ` LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex 
    ON ex.from_currency = m.currency
    AND ex.to_currency = '${targetCurrency}'
    AND ex.snapshot_date::DATE = m.date::DATE`;

  // Simple expressions like the working query
  const costExpression = `m.total_cost * NVL(ex.exchange_rate, 1)`;
  const salesExpression = `m.total_sales * NVL(ex.exchange_rate, 1)`;

  // Platform detection expressions - simplified
  const platformExpressions = {
    facebook: `CASE 
      WHEN m.advertising_platform ILIKE 'Facebook%' THEN 1
      WHEN m.advertising_platform = 'Meta' 
        AND (m.campaign_name ILIKE '%facebook%' 
          OR m.campaign_name ILIKE '%_fb_%'
          OR m.advertising_group_name ILIKE '%facebook%'
          OR m.advertising_group_name ILIKE '%_fb_%') THEN 1
      ELSE 0 
    END`,
    instagram: `CASE 
      WHEN m.advertising_platform ILIKE 'Instagram%' THEN 1
      WHEN m.advertising_platform = 'Meta' 
        AND (m.campaign_name ILIKE '%instagram%' 
          OR m.campaign_name ILIKE '%_ig_%'
          OR m.advertising_group_name ILIKE '%instagram%'
          OR m.advertising_group_name ILIKE '%_ig_%') THEN 1
      ELSE 0 
    END`,
    google: `CASE 
      WHEN m.advertising_platform ILIKE 'Google%' THEN 1
      WHEN m.advertising_platform ILIKE '%Google Ads%' THEN 1
      ELSE 0 
    END`,
    tiktok: `CASE 
      WHEN m.advertising_platform ILIKE 'TikTok%' THEN 1
      WHEN m.advertising_platform ILIKE 'Tik Tok%' THEN 1
      ELSE 0 
    END`,
    amazon: `CASE 
      WHEN m.advertising_platform ILIKE 'Amazon%' THEN 1
      WHEN m.advertising_platform = 'Amazon Advertising' THEN 1
      ELSE 0 
    END`
  };

  // Campaign type expressions
  const campaignTypeExpressions = {
    awareness: `CASE WHEN m.campaign_type ILIKE 'Awareness%' THEN 1 ELSE 0 END`,
    conversion: `CASE WHEN m.campaign_type ILIKE 'Conversion%' THEN 1 ELSE 0 END`,
    retargeting: `CASE WHEN m.campaign_type ILIKE 'Retargeting%' THEN 1 ELSE 0 END`,
    seasonal: `CASE WHEN m.campaign_type ILIKE 'Seasonal%' THEN 1 ELSE 0 END`
  };

  if (params.campaignId) {
    // --- Single Campaign Detail Query ---
    selectList = [
      `m.campaign_name AS "campaign_name"`,
      `m.brand AS "brand_name"`,
      `b.brand_id AS "campaign_brand_id"`,
      `MIN(m.date)::DATE::VARCHAR AS "min_date"`,
      `MAX(m.date)::DATE::VARCHAR AS "max_date"`,
      `LISTAGG(DISTINCT m.advertising_platform, ', ') WITHIN GROUP (ORDER BY m.advertising_platform) AS "platform_array"`,
      `SUM(${costExpression}) AS totalspend`,
      `SUM(${platformExpressions.facebook} * ${costExpression}) AS facebookspend`,
      `SUM(${platformExpressions.instagram} * ${costExpression}) AS instagramspend`,
      `SUM(${platformExpressions.google} * ${costExpression}) AS googlespend`,
      `SUM(${platformExpressions.tiktok} * ${costExpression}) AS tiktokspend`,
      `SUM(${platformExpressions.amazon} * ${costExpression}) AS amazonspend`,
      `SUM(${campaignTypeExpressions.awareness} * ${costExpression}) AS awarenessspend`,
      `SUM(${campaignTypeExpressions.conversion} * ${costExpression}) AS conversionspend`,
      `SUM(${campaignTypeExpressions.retargeting} * ${costExpression}) AS retargetingspend`,
      `SUM(${campaignTypeExpressions.seasonal} * ${costExpression}) AS seasonalspend`,
      `SUM(m.total_impressions) AS totalimpressions`,
      `SUM(m.total_clicks) AS totalclicks`,
      `SUM(m.number_of_sales_orders) AS totalconversions`,
      `SUM(${salesExpression}) AS totalconversionvalue`,
      `CASE 
        WHEN SUM(${costExpression}) > 0 
        THEN SUM(${salesExpression}) / SUM(${costExpression})
        ELSE NULL 
      END AS roas`,
      `CASE 
        WHEN SUM(m.number_of_sales_orders) > 0 
        THEN SUM(${costExpression}) / SUM(m.number_of_sales_orders)
        ELSE NULL 
      END AS cpa`
    ];
    
    conditions.push(`m.campaign_name = $${paramIndex++}`);
    queryParams.push(params.campaignId);

    // Date range filters
    if (params.startDate) {
      conditions.push(`m.date >= $${paramIndex++}::DATE`);
      queryParams.push(params.startDate);
    }
    if (params.endDate) {
      conditions.push(`m.date <= $${paramIndex++}::DATE`);
      queryParams.push(params.endDate);
    }
    
    groupByClause = `GROUP BY m.campaign_name, m.brand, b.brand_id`;
    orderByClause = `ORDER BY "campaign_name"`;

  } else {
    // --- Aggregated Campaign List Query ---
    selectList = [
      `DATE_TRUNC('${params.groupByTime}', m.date)::DATE::VARCHAR AS "date"`,
      `m.campaign_name AS "campaign_name"`,
      `b.brand_id AS "campaign_brand_id"`,
      `m.brand AS "brand_name"`,
      `m.sales_channel_type AS "sales_channel_type"`,
      `SUM(${costExpression}) AS totalspend`,
      `SUM(${platformExpressions.facebook} * ${costExpression}) AS facebookspend`,
      `SUM(${platformExpressions.instagram} * ${costExpression}) AS instagramspend`,
      `SUM(${platformExpressions.google} * ${costExpression}) AS googlespend`,
      `SUM(${platformExpressions.tiktok} * ${costExpression}) AS tiktokspend`,
      `SUM(${platformExpressions.amazon} * ${costExpression}) AS amazonspend`,
      `SUM(${campaignTypeExpressions.awareness} * ${costExpression}) AS awarenessspend`,
      `SUM(${campaignTypeExpressions.conversion} * ${costExpression}) AS conversionspend`,
      `SUM(${campaignTypeExpressions.retargeting} * ${costExpression}) AS retargetingspend`,
      `SUM(${campaignTypeExpressions.seasonal} * ${costExpression}) AS seasonalspend`,
      `SUM(m.total_impressions) AS totalimpressions`,
      `SUM(m.total_clicks) AS totalclicks`,
      `SUM(m.number_of_sales_orders) AS totalconversions`,
      `SUM(${salesExpression}) AS totalconversionvalue`,
      `CASE 
        WHEN SUM(${costExpression}) > 0 
        THEN SUM(${salesExpression}) / SUM(${costExpression})
        ELSE NULL 
      END AS roas`,
      `CASE 
        WHEN SUM(m.number_of_sales_orders) > 0 
        THEN SUM(${costExpression}) / SUM(m.number_of_sales_orders)
        ELSE NULL 
      END AS cpa`
    ];

    // Date filters - exactly like working query with BETWEEN
    if (params.startDate && params.endDate) {
      conditions.push(`m.date BETWEEN $${paramIndex++}::DATE AND $${paramIndex++}::DATE`);
      queryParams.push(params.startDate, params.endDate);
    } else {
      if (params.startDate) {
        conditions.push(`m.date >= $${paramIndex++}::DATE`);
        queryParams.push(params.startDate);
      }
      if (params.endDate) {
        conditions.push(`m.date <= $${paramIndex++}::DATE`);
        queryParams.push(params.endDate);
      }
    }
    
    // Brand Filtering Logic - simplified to match working query
    const urlBrandNames = params.brands;
    const tokenBrandIdsFromAuth = tokenInfo.tokenBrands;
    const isSuperAdminCtx = tokenInfo.isSuperAdmin ?? false;
    const isImpersonatingCtx = tokenInfo.isImpersonating ?? false;
    // Use same logic pattern as working brand selector: (isImpersonating || !isSuperAdmin)
    const shouldUseRestrictedAccess = isImpersonatingCtx || !isSuperAdminCtx;

    console.log('[IMPERSONATION DEBUG] Marketing Campaign brand filtering logic:', {
      urlBrandNames,
      tokenBrandIdsFromAuth,
      isSuperAdminCtx,
      isImpersonatingCtx,
      shouldUseRestrictedAccess
    });

    if (!shouldUseRestrictedAccess) {
      // Super admin without impersonation - use exact brand names
      console.log('[IMPERSONATION DEBUG] Marketing: Using privileged viewer path (SuperAdmin, not impersonating)');
      if (urlBrandNames.length > 0) {
        const brandNamePlaceholders = urlBrandNames.map(() => `$${paramIndex++}`).join(',');
        conditions.push(`m.brand IN (${brandNamePlaceholders})`);
        queryParams.push(...urlBrandNames);
        console.log('[IMPERSONATION DEBUG] Marketing: Added URL brand name filter:', urlBrandNames);
      }
    } else {
      // Regular user or impersonating admin - restricted by token brands
      console.log('[IMPERSONATION DEBUG] Marketing: Using restricted access path (Regular user OR Impersonating)');
      if (!tokenBrandIdsFromAuth || tokenBrandIdsFromAuth.length === 0) {
        console.warn('[IMPERSONATION DEBUG] Marketing: No token brands, blocking access');
        conditions.push("1=0"); // No access
      } else {
        console.log('[IMPERSONATION DEBUG] Marketing: User has token brands:', tokenBrandIdsFromAuth);
        
        // Convert token brand IDs to brand names for proper filtering
        const { getDb } = await import('@/lib/api/db');
        const localDb = await getDb();
        
        const brandNameQuery = `SELECT name FROM Brands WHERE id IN (${tokenBrandIdsFromAuth.map(() => '?').join(',')})`;
        const brandNameResults = await localDb.all<{ name: string }[]>(brandNameQuery, tokenBrandIdsFromAuth);
        const allowedBrandNames = brandNameResults.map(row => row.name);
        
        console.log('[IMPERSONATION DEBUG] Marketing: Token brand IDs mapped to names:', {
          tokenBrandIds: tokenBrandIdsFromAuth,
          allowedBrandNames
        });

        if (urlBrandNames.length > 0) {
          // Filter by intersection of URL brands and token-allowed brands
          console.log('[IMPERSONATION DEBUG] Marketing: Applying intersection of URL brands and token-allowed brands');
          const finalBrandNames = urlBrandNames.filter(name => allowedBrandNames.includes(name));
          console.log('[IMPERSONATION DEBUG] Marketing: Final brand names after intersection:', finalBrandNames);
          
          if (finalBrandNames.length === 0) {
            console.log('[IMPERSONATION DEBUG] Marketing: No valid brands after intersection - denying access');
            conditions.push("1=0");
          } else {
            const brandNamePlaceholders = finalBrandNames.map(() => `$${paramIndex++}`).join(',');
            conditions.push(`m.brand IN (${brandNamePlaceholders})`);
            queryParams.push(...finalBrandNames);
            console.log('[IMPERSONATION DEBUG] Marketing: Added intersected brand name filter:', finalBrandNames);
          }
        } else {
          // No URL brands specified, filter by all token-allowed brands
          console.log('[IMPERSONATION DEBUG] Marketing: No URL brands, filtering by all token-allowed brands');
          if (allowedBrandNames.length === 0) {
            console.log('[IMPERSONATION DEBUG] Marketing: No allowed brand names found - denying access');
            conditions.push("1=0");
          } else {
            const brandNamePlaceholders = allowedBrandNames.map(() => `$${paramIndex++}`).join(',');
            conditions.push(`m.brand IN (${brandNamePlaceholders})`);
            queryParams.push(...allowedBrandNames);
            console.log('[IMPERSONATION DEBUG] Marketing: Added all token-allowed brand names filter:', allowedBrandNames);
          }
        }
      }
    }
    
    // Stage filter - use corrected logic pattern
    const applyStageFilter = !shouldUseRestrictedAccess || (!tokenBrandIdsFromAuth || tokenBrandIdsFromAuth.length === 0);
    if (applyStageFilter) {
      conditions.push(`b.stage = 'Selling Actively'`);
    }

    // Sales channel filter - use exact match when possible
    if (params.salesChannels.length > 0) {
      const channelPlaceholders = params.salesChannels.map(() => `$${paramIndex++}`).join(',');
      conditions.push(`m.sales_channel_type IN (${channelPlaceholders})`);
      queryParams.push(...params.salesChannels);
    }

    // Country filter - use exact match when possible
    if (params.countryNames.length > 0) {
      const countryPlaceholders = params.countryNames.map(() => `$${paramIndex++}`).join(',');
      conditions.push(`m.country_name IN (${countryPlaceholders})`);
      queryParams.push(...params.countryNames);
    }
    
    // Currency filter - only filter when not using exchange rates
    // Don't add currency filters as the exchange rate join handles conversion

    // Sorting
    const sortByToSqlIdentifierMap = getSortByToSqlIdentifierMap(costExpression);
    const sqlSortByMapped = sortByToSqlIdentifierMap[params.sortBy] || '"date"';
    const sqlSortOrder = params.sortOrder.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';
    
    groupByClause = `GROUP BY DATE_TRUNC('${params.groupByTime}', m.date), m.campaign_name, b.brand_id, m.brand, m.sales_channel_type`;
    orderByClause = `ORDER BY ${sqlSortByMapped} ${sqlSortOrder}`;
    
    // Add secondary sort for stability
    if (sqlSortByMapped !== '"date"') {
      orderByClause += `, "date" DESC`;
    }
    orderByClause += `, "campaign_name"`;
  }

  // Data quality filters - minimal like working query
  conditions.push(`m.campaign_name IS NOT NULL`);

  const sql = `
    SELECT
      ${selectList.join(',\n      ')}
    ${joins}
    WHERE ${conditions.length > 0 ? conditions.join('\n      AND ') : '1=1'}
    ${groupByClause}
    ${orderByClause}
  `;

  return { query: sql, queryParams };
}