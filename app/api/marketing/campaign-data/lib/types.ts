// --- Types ---

export interface CampaignDataPoint {
  date: string; // Used for time-series data
  campaign_name?: string;
  campaign_brand_id?: number;
  brand_name?: string;
  sales_channel_type?: string;
  totalSpend: number;
  FacebookSpend?: number;
  InstagramSpend?: number;
  GoogleSpend?: number;
  TikTokSpend?: number;
  AmazonSpend?: number;
  AwarenessSpend?: number;
  ConversionSpend?: number;
  RetargetingSpend?: number;
  SeasonalSpend?: number;
  totalImpressions?: number;
  totalClicks?: number;
  totalConversions?: number;
  totalConversionValue?: number;
  roas?: number;
  cpa?: number;
  [key: string]: number | string | undefined;
}

// For single campaign details
export interface CampaignDetailData {
  id: string; // campaign_name
  name: string; // campaign_name
  brand_name?: string;
  campaign_brand_id?: number;
  totalSpend: number;
  FacebookSpend?: number;
  InstagramSpend?: number;
  GoogleSpend?: number;
  TikTokSpend?: number;
  AmazonSpend?: number;
  AwarenessSpend?: number;
  ConversionSpend?: number;
  RetargetingSpend?: number;
  SeasonalSpend?: number;
  totalImpressions?: number;
  totalClicks?: number;
  totalConversions?: number;
  totalConversionValue?: number;
  roas?: number;
  cpa?: number;
  startDate?: string;
  endDate?: string;
  channels?: string[];
  // Fields like status, budget, targetAudience, notes are not in the current table
}

// Represents a row from Redshift, potentially for aggregated or detailed data
export interface RedshiftRow {
  // Common fields for aggregated list
  date?: string; // Only for aggregated time-series
  campaign_name: string | null;
  campaign_brand_id: number | null;
  brand_name: string | null;
  sales_channel_type: string | null; // For aggregated list, less relevant for single campaign summary

  // Aggregated metrics (can be for a single campaign summary too)
  totalspend: number | null;
  facebookspend: number | null;
  instagramspend: number | null;
  googlespend: number | null;
  tiktokspend: number | null;
  amazonspend: number | null;
  awarenessspend: number | null;
  conversionspend: number | null;
  retargetingspend: number | null;
  seasonalspend: number | null;
  totalimpressions: number | null;
  totalclicks: number | null;
  totalconversions: number | null;
  totalconversionvalue: number | null;
  roas: number | null;
  cpa: number | null;

  // Fields for single campaign detail
  min_date?: string; // startDate
  max_date?: string; // endDate
  platform_array?: string[]; // channels
}

export type ValidGroupByTime = 'day' | 'week' | 'month' | 'quarter' | 'year' | 'none'; // 'none' for single campaign
export type ValidCurrency = 'CAD' | 'USD';

export type CampaignQueryParams = {
  startDate?: string; // YYYY-MM-DD
  endDate?: string;   // YYYY-MM-DD
  currency?: ValidCurrency;
  brands?: string | string[];
  groupByTime?: ValidGroupByTime;
  salesChannels?: string | string[];
  countryNames?: string | string[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  campaignId?: string; // For fetching a single campaign's details
  campaign_name?: string; // For backward compatibility
};

export interface ErrorResponse {
  error: string;
  details?: string;
}

export interface TokenInfo {
  isImpersonating?: boolean;
  isSuperAdmin?: boolean; 
  tokenBrands?: number[];
}

export interface ValidatedParams {
  startDate?: string;
  endDate?: string;
  currency: ValidCurrency;
  brands: string[];
  groupByTime: ValidGroupByTime;
  salesChannels: string[];
  countryNames: string[];
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  campaignId?: string;
}

export interface SqlQueryResult {
  query: string;
  queryParams: (string | number | number[] | boolean)[];
}
