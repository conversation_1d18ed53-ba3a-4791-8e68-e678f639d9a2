import { CampaignQueryParams, ValidCurrency, ValidGroupByTime, ValidatedParams } from './types';

// --- Helper Functions ---

/**
 * Sanitizes array parameters from query string
 */
export function sanitizeArrayParameter(input: string | string[] | undefined): string[] {
  if (!input) {
    return [];
  }
  const arr = Array.isArray(input) ? input : input.split(',');
  return arr.map(item => item.trim().replace(/[^a-zA-Z0-9\s\-_&+.%]/g, '')).filter(Boolean);
}

// Define valid fields for sorting
export const validSortByFields = [
  "date", "campaign_name", "campaign_brand_id", "brand_name", "sales_channel_type",
  "totalSpend", "FacebookSpend", "InstagramSpend", "GoogleSpend", "TikTokSpend", "AmazonSpend",
  "AwarenessSpend", "ConversionSpend", "RetargetingSpend", "SeasonalSpend",
  "totalImpressions", "totalClicks", "totalConversions", "totalConversionValue",
  "roas", "cpa"
];

/**
 * Validates and sanitizes query parameters
 */
export function validateParameters(query: CampaignQueryParams): ValidatedParams {
  const errors: string[] = [];
  let validatedStartDate: string | undefined = undefined;
  let validatedEndDate: string | undefined = undefined;
  let validatedCampaignId: string | undefined = undefined;

  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (query.startDate) {
    if (typeof query.startDate !== 'string' || !dateRegex.test(query.startDate)) {
      errors.push("Invalid format for startDate. Expected YYYY-MM-DD.");
    } else {
      validatedStartDate = query.startDate;
    }
  }
  if (query.endDate) {
    if (typeof query.endDate !== 'string' || !dateRegex.test(query.endDate)) {
      errors.push("Invalid format for endDate. Expected YYYY-MM-DD.");
    } else {
      validatedEndDate = query.endDate;
    }
  }
  if (validatedStartDate && validatedEndDate && validatedStartDate > validatedEndDate) {
    errors.push("startDate cannot be after endDate.");
  }

  let currency: ValidCurrency = 'CAD'; // Default currency
  if (query.currency) {
    if (query.currency !== 'CAD' && query.currency !== 'USD') {
      errors.push("Invalid currency. Expected 'CAD' or 'USD'.");
    } else {
      currency = query.currency;
    }
  }

  const brands = sanitizeArrayParameter(query.brands);
  const salesChannels = sanitizeArrayParameter(query.salesChannels);
  const countryNames = sanitizeArrayParameter(query.countryNames);

  let groupByTime: ValidGroupByTime = query.campaignId ? 'none' : 'day'; // Default groupByTime
  const validGroupByTimes: ValidGroupByTime[] = ['day', 'week', 'month', 'quarter', 'year', 'none'];
  if (query.groupByTime) {
    if (!validGroupByTimes.includes(query.groupByTime as ValidGroupByTime)) {
      errors.push(`Invalid groupByTime. Expected one of: ${validGroupByTimes.join(', ')}.`);
    } else {
      groupByTime = query.groupByTime as ValidGroupByTime;
    }
  }
  // If campaignId is present, groupByTime should be 'none' as we fetch a summary.
  if (query.campaignId) {
    groupByTime = 'none';
  }

  let sortBy: string = 'date'; // Default sortBy
  if (query.campaignId) {
    sortBy = 'campaign_name'; // Not really sorting for a single item, but provide a default
  } else if (query.sortBy) {
    if (validSortByFields.includes(query.sortBy)) {
      sortBy = query.sortBy;
    } else {
      sortBy = 'date';
    }
  }

  let sortOrder: 'asc' | 'desc' = 'desc'; // Default sortOrder
  if (query.campaignId) {
    sortOrder = 'asc'; // Not really sorting for a single item
  } else if (query.sortOrder) {
    if (query.sortOrder === 'asc' || query.sortOrder === 'desc') {
      sortOrder = query.sortOrder;
    } else {
      sortOrder = 'desc';
    }
  }

  // Check for campaignId parameter (used for single campaign details)
  if (query.campaignId && typeof query.campaignId === 'string') {
    validatedCampaignId = query.campaignId.trim();
    if (!validatedCampaignId) {
      errors.push("campaignId cannot be empty.");
    }
    // Potentially add more validation for campaignId format if necessary
  }
  // For backward compatibility, also check for campaign_name parameter
  else if (query.campaign_name && typeof query.campaign_name === 'string') {
    validatedCampaignId = query.campaign_name.trim();
    if (!validatedCampaignId) {
      errors.push("campaign_name cannot be empty.");
    }
  }

  if (errors.length > 0) {
    throw new Error(`Parameter validation failed: ${errors.join('; ')}`);
  }

  return {
    startDate: validatedStartDate,
    endDate: validatedEndDate,
    currency,
    brands,
    groupByTime,
    salesChannels,
    countryNames,
    sortBy,
    sortOrder,
    campaignId: validatedCampaignId,
  };
}

/**
 * Maps API sortBy keys to SQL identifiers (quoted aliases from SELECT)
 */
export function getSortByToSqlIdentifierMap(costExpression: string): Record<string, string> {
  return {
    "date": `"date"`, // For aggregated list
    "campaign_name": `"campaign_name"`,
    "campaign_brand_id": `"campaign_brand_id"`,
    "brand_name": `"brand_name"`,
    "sales_channel_type": `"sales_channel_type"`, // For aggregated list
    "totalSpend": `"totalSpend"`,
    "FacebookSpend": `"FacebookSpend"`,
    "InstagramSpend": `"InstagramSpend"`,
    "GoogleSpend": `"GoogleSpend"`,
    "TikTokSpend": `"TikTokSpend"`,
    "AmazonSpend": `"AmazonSpend"`,
    "AwarenessSpend": `"AwarenessSpend"`,
    "ConversionSpend": `"ConversionSpend"`,
    "RetargetingSpend": `"RetargetingSpend"`,
    "SeasonalSpend": `"SeasonalSpend"`,
    "totalImpressions": `"totalImpressions"`,
    "totalClicks": `"totalClicks"`,
    "totalConversions": `"totalConversions"`,
    "totalConversionValue": `"totalConversionValue"`,
    "roas": `"roas"`,
    "cpa": `"cpa"`,
    "startDate": `"min_date"`, // For single campaign
    "endDate": `"max_date"`,   // For single campaign
    // Special calculated fields
    "totalClicks/totalImpressions": `SUM(m.total_clicks) / NULLIF(SUM(m.total_impressions), 0)`, // CTR
    "totalConversionValue/totalSpend": `SUM(m.total_sales) / NULLIF(SUM(${costExpression}), 0)`, // ROAS
    "totalSpend/totalConversions": `SUM(${costExpression}) / NULLIF(SUM(m.number_of_sales_orders), 0)`, // CPA
  };
}
