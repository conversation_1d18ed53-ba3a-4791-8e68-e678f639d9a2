import {
  CampaignQueryParams,
  ErrorResponse,
  RedshiftRow,
  TokenInfo,
  buildSqlQuery,
  mapToCampaignDataPoint,
  mapToCampaignDetailData,
  validateParameters
} from './lib';
import { NextRequest, NextResponse } from 'next/server';

import { PoolClient } from 'pg';
import { getRedshiftPool } from '@/lib/api/redshift';
import { getToken } from 'next-auth/jwt';

// --- Main Handler ---
export async function GET(request: NextRequest) {
  console.log('[DEBUG] CAMPAIGN-DATA ROUTE HANDLER ENTERED');
  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });
  console.log('[DEBUG] Token from getToken in API route:', JSON.stringify(token, null, 2));
  console.log('[DEBUG] NEXTAUTH_SECRET availability in API route:', !!process.env.NEXTAUTH_SECRET ? "Available" : "NOT AVAILABLE");

  if (!token) {
    return NextResponse.json({ error: 'Unauthorized' } as ErrorResponse, { status: 401 });
  }

  const {
    isImpersonating,
    isSuperAdmin,
    brands: tokenBrandsArrayRaw,
  } = token as { isImpersonating?: boolean; isSuperAdmin?: boolean; brands?: unknown };
  console.log('[IMPERSONATION DEBUG] Marketing Campaign API Token details:', {
    isImpersonating,
    isSuperAdmin,
    tokenBrandsArrayRaw,
    tokenSub: token.sub,
    tokenEmail: token.email,
    originalUser: token.originalUser
  });

  const tokenBrandsArray: number[] | undefined = Array.isArray(tokenBrandsArrayRaw)
    ? tokenBrandsArrayRaw.filter(id => typeof id === 'number')
    : undefined;

  const searchParams = request.nextUrl.searchParams;
  const params: Record<string, string | string[]> = {};
  for (const [key, value] of searchParams.entries()) {
    if (['brands', 'salesChannels', 'countryNames'].includes(key)) {
      if (params[key]) {
        if (Array.isArray(params[key])) {
          (params[key] as string[]).push(value);
        } else {
          params[key] = [params[key] as string, value];
        }
      } else {
        params[key] = value.split(','); // Also handle comma-separated strings for array params
      }
    } else {
      params[key] = value;
    }
  }
  
  const queryParamsFromUrl = params as unknown as CampaignQueryParams;

  let client: PoolClient | null = null;
  try {
    const validatedParams = validateParameters(queryParamsFromUrl);
    const tokenInfoForQuery: TokenInfo = { 
      isImpersonating, 
      isSuperAdmin, 
      tokenBrands: tokenBrandsArray 
    };
    
    const { query, queryParams: sqlParams } = await buildSqlQuery(
        validatedParams,
        tokenInfoForQuery
    );

    const redshiftPool = getRedshiftPool();
    if (!redshiftPool) {
      throw new Error('Redshift connection pool is not available');
    }
    
    client = await redshiftPool.connect();

    const result = await client.query<RedshiftRow>(query, sqlParams);

    if (validatedParams.campaignId) {
      if (result.rows.length === 0) {
        return NextResponse.json({ 
          error: 'Campaign not found', 
          details: `Campaign with ID ${validatedParams.campaignId} not found or no data available.` 
        } as ErrorResponse, { status: 404 });
      }
      
      const campaignDetail = mapToCampaignDetailData(result.rows[0]);
      return NextResponse.json(campaignDetail);
    } else {
      const campaignDataPoints = result.rows.map(mapToCampaignDataPoint);
      return NextResponse.json(campaignDataPoints);
    }
    
  } catch (error) {
    console.error('Error fetching campaign data:', error);
    return NextResponse.json({ 
      error: 'Error fetching campaign data', 
      details: error instanceof Error ? error.message : String(error) 
    } as ErrorResponse, { status: 500 });
  } finally {
    if (client !== null) {
      client.release();
    }
  }
}
