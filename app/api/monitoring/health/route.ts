import { NextRequest, NextResponse } from 'next/server';
import { getRedshiftPool, testRedshiftConnection, getRedshiftPoolStats } from '@/lib/api/redshift';
import { getDb } from '@/lib/api/db';
import { performanceMonitor } from '@/lib/api/performance-monitor';
import { errorHandler } from '@/lib/api/error-handler';
import { authOptions } from '@/lib/auth-options';
import { getServerSession } from 'next-auth';

interface HealthCheck {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime?: number;
  details?: string;
  lastChecked: number;
}

interface SystemHealth {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: number;
  uptime: number;
  version: string;
  environment: string;
  checks: HealthCheck[];
  performance: {
    memory: NodeJS.MemoryUsage;
    cpu?: NodeJS.CpuUsage;
    queries: ReturnType<typeof performanceMonitor.getQueryStats>;
    apis: ReturnType<typeof performanceMonitor.getAPIStats>;
    errors: ReturnType<typeof errorHandler.getErrorStats>;
  };
  database: {
    sqlite: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      responseTime?: number;
      details?: string;
    };
    redshift: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      responseTime?: number;
      poolStats?: ReturnType<typeof getRedshiftPoolStats>;
      details?: string;
    };
  };
}

/**
 * Health check endpoint for monitoring system status
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now();
  
  try {
    // Check if user has monitoring access (admin or super admin)
    const session = await getServerSession(authOptions);
    if (!session || !session.user.roles?.some(role => ['Admin', 'Super Admin'].includes(role))) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Admin access required for health monitoring' },
        { status: 401 }
      );
    }

    const checks: HealthCheck[] = [];
    
    // Check SQLite database
    const sqliteCheck = await checkSQLiteHealth();
    checks.push(sqliteCheck);
    
    // Check Redshift database
    const redshiftCheck = await checkRedshiftHealth();
    checks.push(redshiftCheck);
    
    // Check external services (if any)
    const externalChecks = await checkExternalServices();
    checks.push(...externalChecks);
    
    // Determine overall health
    const overallHealth = determineOverallHealth(checks);
    
    // Get performance metrics
    const performanceMetrics = {
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      queries: performanceMonitor.getQueryStats(),
      apis: performanceMonitor.getAPIStats(),
      errors: errorHandler.getErrorStats()
    };
    
    // Get database-specific information
    const databaseInfo = {
      sqlite: {
        status: sqliteCheck.status,
        responseTime: sqliteCheck.responseTime,
        details: sqliteCheck.details
      },
      redshift: {
        status: redshiftCheck.status,
        responseTime: redshiftCheck.responseTime,
        poolStats: getRedshiftPoolStats(),
        details: redshiftCheck.details
      }
    };
    
    const healthResponse: SystemHealth = {
      overall: overallHealth,
      timestamp: Date.now(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'unknown',
      checks,
      performance: performanceMetrics,
      database: databaseInfo
    };
    
    // Record this health check as a performance metric
    const totalTime = Date.now() - startTime;
    performanceMonitor.recordAPI('/api/monitoring/health', 'GET', totalTime, 200, session.user.id);
    
    return NextResponse.json(healthResponse, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Health-Check-Time': totalTime.toString(),
        'X-Overall-Status': overallHealth
      }
    });
    
  } catch (error) {
    console.error('[Health Check] Error performing health check:', error);
    
    const errorResponse = {
      overall: 'unhealthy' as const,
      timestamp: Date.now(),
      error: 'Health check failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    };
    
    return NextResponse.json(errorResponse, { status: 500 });
  }
}

/**
 * Check SQLite database health
 */
async function checkSQLiteHealth(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    const db = await getDb();
    
    // Simple query to test database connectivity
    await db.get('SELECT 1 as test');
    
    const responseTime = Date.now() - startTime;
    
    return {
      service: 'SQLite Database',
      status: responseTime < 100 ? 'healthy' : 'degraded',
      responseTime,
      details: responseTime < 100 ? 'Database responding normally' : 'Database responding slowly',
      lastChecked: Date.now()
    };
    
  } catch (error) {
    return {
      service: 'SQLite Database',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      details: error instanceof Error ? error.message : 'Database connection failed',
      lastChecked: Date.now()
    };
  }
}

/**
 * Check Redshift database health
 */
async function checkRedshiftHealth(): Promise<HealthCheck> {
  const startTime = Date.now();
  
  try {
    const isConnected = await testRedshiftConnection();
    const responseTime = Date.now() - startTime;
    
    if (isConnected) {
      return {
        service: 'Redshift Database',
        status: responseTime < 500 ? 'healthy' : 'degraded',
        responseTime,
        details: responseTime < 500 ? 'Redshift responding normally' : 'Redshift responding slowly',
        lastChecked: Date.now()
      };
    } else {
      return {
        service: 'Redshift Database',
        status: 'unhealthy',
        responseTime,
        details: 'Redshift connection failed or not configured',
        lastChecked: Date.now()
      };
    }
    
  } catch (error) {
    return {
      service: 'Redshift Database',
      status: 'unhealthy',
      responseTime: Date.now() - startTime,
      details: error instanceof Error ? error.message : 'Redshift connection error',
      lastChecked: Date.now()
    };
  }
}

/**
 * Check external services health
 */
async function checkExternalServices(): Promise<HealthCheck[]> {
  const checks: HealthCheck[] = [];
  
  // Check NextAuth service
  try {
    const authCheck: HealthCheck = {
      service: 'NextAuth',
      status: process.env.NEXTAUTH_SECRET ? 'healthy' : 'degraded',
      details: process.env.NEXTAUTH_SECRET ? 'NextAuth configured' : 'NextAuth secret missing',
      lastChecked: Date.now()
    };
    checks.push(authCheck);
  } catch (error) {
    checks.push({
      service: 'NextAuth',
      status: 'unhealthy',
      details: 'NextAuth configuration error',
      lastChecked: Date.now()
    });
  }
  
  // Check Google OAuth
  try {
    const googleCheck: HealthCheck = {
      service: 'Google OAuth',
      status: (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) ? 'healthy' : 'degraded',
      details: (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) 
        ? 'Google OAuth configured' 
        : 'Google OAuth credentials missing',
      lastChecked: Date.now()
    };
    checks.push(googleCheck);
  } catch (error) {
    checks.push({
      service: 'Google OAuth',
      status: 'unhealthy',
      details: 'Google OAuth configuration error',
      lastChecked: Date.now()
    });
  }
  
  return checks;
}

/**
 * Determine overall system health based on individual checks
 */
function determineOverallHealth(checks: HealthCheck[]): 'healthy' | 'degraded' | 'unhealthy' {
  const unhealthyCount = checks.filter(check => check.status === 'unhealthy').length;
  const degradedCount = checks.filter(check => check.status === 'degraded').length;
  
  if (unhealthyCount > 0) {
    return 'unhealthy';
  }
  
  if (degradedCount > 0) {
    return 'degraded';
  }
  
  return 'healthy';
}

/**
 * Lightweight health check endpoint (for load balancers)
 */
export async function HEAD(): Promise<NextResponse> {
  try {
    // Quick check - just verify the service is running
    const db = await getDb();
    await db.get('SELECT 1');
    
    return new NextResponse(null, { 
      status: 200,
      headers: {
        'X-Health': 'ok',
        'Cache-Control': 'no-cache'
      }
    });
  } catch (error) {
    return new NextResponse(null, { 
      status: 503,
      headers: {
        'X-Health': 'error',
        'Cache-Control': 'no-cache'
      }
    });
  }
}
