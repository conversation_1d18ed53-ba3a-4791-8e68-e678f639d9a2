import { NextResponse } from 'next/server';

// Define a type for the mock data rows
type MockDataRow = {
  date: string;
  kpi_name: string;
  value: number;
  raw_kpi_unit?: string;
  brand?: string;
  sales_channel_type?: string;
  country_name?: string;
};

type TestResults = {
  mockData: {
    success: boolean;
    error: string | null;
    data: MockDataRow[];
  };
};

// Helper function to generate mock KPI data (copied from config.ts)
function mockKpiData(): { rows: MockDataRow[] } {
  const kpis = [
    'Gross Revenue',
    'Net Revenue',
    'Gross Margin',
    'Adspend',
    'Contribution Margin',
    '% Gross Margin',
    '% Contribution Margin',
    '% Adspend',
    'Landed Cost',
    'Fulfillment Cost',
    'Transaction Cost',
    'Discount',
    'Refund'
  ];
  
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 90);
  
  const rows: MockDataRow[] = [];
  
  // Generate 5 days of data for each KPI (limited for testing)
  for (let i = 0; i < 5; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];
    
    kpis.forEach(kpi => {
      // Generate random values based on the KPI
      let value: number;
      
      switch (kpi) {
        case 'Gross Revenue':
          value = Math.random() * 100000 + 50000;
          break;
        case 'Net Revenue':
          value = Math.random() * 80000 + 40000;
          break;
        case 'Gross Margin':
          value = Math.random() * 40000 + 20000;
          break;
        case 'Adspend':
          value = Math.random() * 10000 + 5000;
          break;
        case 'Contribution Margin':
          value = Math.random() * 30000 + 15000;
          break;
        case '% Gross Margin':
          value = Math.random() * 0.4 + 0.3;
          break;
        case '% Contribution Margin':
          value = Math.random() * 0.3 + 0.2;
          break;
        case '% Adspend':
          value = Math.random() * 0.15 + 0.05;
          break;
        case 'Landed Cost':
          value = Math.random() * 30000 + 15000;
          break;
        case 'Fulfillment Cost':
          value = Math.random() * 10000 + 5000;
          break;
        case 'Transaction Cost':
          value = Math.random() * 5000 + 2500;
          break;
        case 'Discount':
          value = Math.random() * 8000 + 2000;
          break;
        case 'Refund':
          value = Math.random() * 5000 + 1000;
          break;
        default:
          value = Math.random() * 10000;
      }
      
      rows.push({
        date: dateStr,
        kpi_name: kpi,
        value: Math.round(value * 100) / 100,
        raw_kpi_unit: 'CAD',
        brand: 'Sample Brand',
        sales_channel_type: 'Online',
        country_name: 'Canada'
      });
    });
  }
  
  return { rows };
}

export async function GET() {
  const results: TestResults = {
    mockData: {
      success: false,
      error: null,
      data: []
    }
  };

  try {
    console.log('Generating mock data...');
    const mockData = mockKpiData();
    results.mockData.success = true;
    results.mockData.data = mockData.rows;
  } catch (error) {
    console.error('Mock data generation failed:', error);
    results.mockData.error = error instanceof Error ? error.message : 'Unknown error';
  }

  return NextResponse.json(results);
}
