import { NextResponse } from 'next/server';
import { getRedshiftPool } from '@/lib/api/config';

// Define a type for the mock data rows
type MockDataRow = {
  date: string;
  kpi_name: string;
  value: number;
  raw_kpi_unit?: string;
  brand?: string;
  sales_channel_type?: string;
  country_name?: string;
};

type TestResults = {
  mockRedshift: {
    connectionTest: boolean;
    queryTest: boolean;
    error: string | null;
    data: MockDataRow[];
  };
};

export async function GET() {
  const results: TestResults = {
    mockRedshift: {
      connectionTest: false,
      queryTest: false,
      error: null,
      data: []
    }
  };

  // Test mock Redshift implementation
  try {
    console.log('Testing mock Redshift connection...');
    const pool = getRedshiftPool();
    if (!pool) {
      results.mockRedshift.error = 'Redshift pool not available';
      return NextResponse.json(results);
    }
    const client = await pool.connect();
    results.mockRedshift.connectionTest = true;
    
    console.log('Testing mock Redshift query...');
    try {
      const result = await client.query<MockDataRow>('SELECT * FROM ai_reporting_ds_kpis LIMIT 5', []);
      results.mockRedshift.queryTest = true;
      results.mockRedshift.data = result.rows as MockDataRow[];
    } catch (error) {
      console.error('Mock Redshift query test failed:', error);
      results.mockRedshift.error = error instanceof Error ? error.message : 'Unknown error';
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Mock Redshift connection test failed:', error);
    results.mockRedshift.error = error instanceof Error ? error.message : 'Unknown error';
  }

  return NextResponse.json(results);
}
