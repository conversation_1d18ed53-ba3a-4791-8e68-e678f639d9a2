import { getRedshiftPool, testRedshiftConnection } from '@/lib/api/redshift';

import { NextResponse } from 'next/server';

export async function GET() {
  console.log('[API /test-redshift-connection] Received GET request.');
  try {
    console.log('[API /test-redshift-connection] Checking environment variables...');
    const envVars = {
      REDSHIFT_HOST: process.env.REDSHIFT_HOST ? 'Set' : 'Not set',
      REDSHIFT_PORT: process.env.REDSHIFT_PORT ? 'Set' : 'Not set',
      REDSHIFT_DATABASE: process.env.REDSHIFT_DATABASE ? 'Set' : 'Not set',
      REDSHIFT_USER: process.env.REDSHIFT_USER ? 'Set' : 'Not set',
      REDSHIFT_PASSWORD: process.env.REDSHIFT_PASSWORD ? 'Set (presence check only)' : 'Not set', // Avoid logging actual password
    };
    console.log('[API /test-redshift-connection] Environment variables status:', envVars);

    console.log('[API /test-redshift-connection] Checking if Redshift pool was created...');
    const redshiftPool = getRedshiftPool();
    const poolCreated = redshiftPool !== null;
    console.log('[API /test-redshift-connection] Pool created status:', poolCreated);

    console.log('[API /test-redshift-connection] Testing Redshift connection...');
    const connectionSuccessful = await testRedshiftConnection();
    console.log('[API /test-redshift-connection] Connection successful status:', connectionSuccessful);

    const responsePayload = {
      environmentVariables: envVars,
      poolCreated,
      connectionSuccessful,
      message: connectionSuccessful
        ? 'Successfully connected to Redshift'
        : 'Failed to connect to Redshift'
    };
    console.log('[API /test-redshift-connection] Sending response:', responsePayload);
    return NextResponse.json(responsePayload);
  } catch (error) {
    console.error('[API /test-redshift-connection] Error in GET handler:', error);
    if (error instanceof Error) {
      console.error('[API /test-redshift-connection] Error name:', error.name);
      console.error('[API /test-redshift-connection] Error message:', error.message);
      console.error('[API /test-redshift-connection] Error stack:', error.stack);
    }
    return NextResponse.json(
      {
        error: 'Failed to test Redshift connection',
        details: error instanceof Error ? error.message : 'Unknown error',
        environmentVariablesChecked: 'See server logs for status', // Avoid exposing env status directly in error response
        poolCreatedChecked: 'See server logs for status',
      },
      { status: 500 }
    );
  }
}
