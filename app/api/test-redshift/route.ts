import { NextResponse } from 'next/server';
import { PoolClient } from 'pg';
import { getRedshiftPool } from '@/lib/api/redshift';

export async function GET() {
  const redshiftPool = getRedshiftPool();
  if (!redshiftPool) {
    return NextResponse.json({ error: 'Redshift pool is not available.' }, { status: 500 });
  }

  let client: PoolClient | undefined;
  try {
    client = await redshiftPool.connect();
    const query = `
      SELECT DISTINCT country_name
      FROM dwh_ai.ai_reporting_ds_kpis
      WHERE country_name IN (
          'Åland Islands',
          'Côte d''Ivoire',
          'Réunion',
          'Curaçao',
          'Saint Barthélemy'
      ) OR country_name LIKE '?land Islands';
    `;
    console.log('Executing query:', query);
    const { rows } = await client.query(query);
    return NextResponse.json({ data: rows });
  } catch (error) {
    console.error('Error executing Redshift query:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error during Redshift query';
    return NextResponse.json({ error: 'Failed to query Redshift.', details: errorMessage }, { status: 500 });
  } finally {
    if (client) {
      client.release();
    }
  }
}
