import SignInComponent from '@/components/auth/SignInComponent';
import { Suspense } from 'react';
import { getSafeServerSession } from '@/lib/auth-helpers';
import { redirect } from 'next/navigation';

// Force dynamic rendering to prevent static generation issues
export const dynamic = 'force-dynamic';

export default async function SignInPage() {
  const session = await getSafeServerSession();
  
  if (session) {
    redirect('/dashboard');
  }
  
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <SignInComponent />
    </Suspense>
  );
}
