"use client"

import { BrandDeepDiveProvider, useBrandDeepDive } from '@/lib/contexts/brand-deep-dive-context'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  Ta<PERSON>Trigger,
} from "@/components/ui/tabs"
import { mainNavItems, mainSecondaryNavItems } from "@/components/main/MainNavConfig"

import { AppLayout } from "@/components/shared/AppLayout"
import { BrandDeepDiveCharts } from "@/components/brand-deep-dive/brand-deep-dive-charts"
import { BrandDeepDiveKpiCards } from "@/components/brand-deep-dive/brand-deep-dive-kpi-cards"
import { BrandDeepDiveMarketingCampaigns } from "@/components/brand-deep-dive/brand-deep-dive-marketing-campaigns"
import { BrandDeepDiveMarketingKpiCards } from "@/components/brand-deep-dive/brand-deep-dive-marketing-kpi-cards"
import { EnhancedDashboardFilters } from "@/components/enhanced-dashboard-filters"
import { FilterProvider } from "@/lib/contexts/filter-context"

// Wrapper component that provides the BrandDeepDiveProvider context
export default function BrandDeepDiveClient() {
  return (
    <BrandDeepDiveProvider>
      <BrandDeepDiveContent />
    </BrandDeepDiveProvider>
  );
}

// Main content component that uses the context
function BrandDeepDiveContent() {
  const { state } = useBrandDeepDive();

  return (
    <AppLayout
      navItems={mainNavItems}
      secondaryNavItems={mainSecondaryNavItems}
      pageTitle="Brand Deep Dive"
      pageDescription="Detailed analysis of brand performance metrics"
      showPageHeader={true}
    >
      <FilterProvider>
        {/* Global Filters (including brand filter) */}
        <EnhancedDashboardFilters
          showBrandFilter={true}
          contextType="brandDeepDive"
          pageType="brand-deep-dive"
        />
        
        {/* Main Content Tabs */}
        {!state.selectedBrand ? (
          <Card className="w-full">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="text-4xl text-muted-foreground mb-2">📊</div>
              <h2 className="text-xl font-semibold mb-2">No Brand Selected</h2>
              <p className="text-muted-foreground text-center max-w-md">
                Please select a brand from the dropdown above to view detailed performance metrics and analysis.
              </p>
            </CardContent>
          </Card>
        ) : (
          <Tabs defaultValue="financial" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="financial">Financial</TabsTrigger>
              <TabsTrigger value="marketing">Marketing</TabsTrigger>
            </TabsList>
            
            {/* Financial Tab Content */}
            <TabsContent value="financial" className="space-y-4 pt-4">
              <BrandDeepDiveKpiCards />
              
              <Card>
                <CardHeader>
                  <CardTitle>Financial Performance Trends</CardTitle>
                  <CardDescription>
                    Key financial metrics over time for {state.selectedBrand}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <BrandDeepDiveCharts />
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Marketing Tab Content */}
            <TabsContent value="marketing" className="space-y-4 pt-4">
              <BrandDeepDiveMarketingKpiCards />
              
             
            </TabsContent>
          </Tabs>
        )}
      </FilterProvider>
    </AppLayout>
  )
}