"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { mainNavItems, mainSecondaryNavItems } from "@/components/main/MainNavConfig"
import { useCallback, useEffect, useState } from "react";

import { AppLayout } from "@/components/shared/AppLayout"
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface BudgetRecord {
  brand: string;
  month: string;
  account: string;
  budget_value: number;
}

interface EditingCell {
  month: string;
  account: string;
  value: string;
  originalValue: number;
}

interface PendingUpdate {
  month: string;
  account: string;
  value: number;
  originalValue: number;
}

export default function BudgetClient() {
  const [budgets, setBudgets] = useState<BudgetRecord[]>([]);
  const [brands, setBrands] = useState<string[]>([]);
  const [selectedBrand, setSelectedBrand] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingCell, setEditingCell] = useState<EditingCell | null>(null);
  const [pendingUpdates, setPendingUpdates] = useState<Map<string, PendingUpdate>>(new Map());
  const [savingCells, setSavingCells] = useState<Set<string>>(new Set());

  // Fetch brands on initial load
  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const response = await fetch("/api/budget?brands=true");
        if (!response.ok) {
          throw new Error("Failed to fetch brands");
        }
        const data = await response.json();
        setBrands(data.brands || []);
      } catch (err: unknown) {
        const errorMessage = err instanceof Error ? err.message : "An error occurred while fetching brands";
        setError(errorMessage);
        console.error("Error fetching brands:", err);
      }
    };

    fetchBrands();
  }, []);

  // Fetch budgets when selectedBrand changes
  useEffect(() => {
    const fetchBudgets = async () => {
      setLoading(true);
      setError(null);
      try {
        const url = selectedBrand && selectedBrand !== "all"
          ? `/api/budget?brand=${encodeURIComponent(selectedBrand)}`
          : "/api/budget";
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error("Failed to fetch budget data");
        }
        const data = await response.json();
        setBudgets(data.budgets || []);
        // Clear pending updates when switching brands
        setPendingUpdates(new Map());
        setEditingCell(null);
      } catch (err: unknown) {
        const errorMessage = err instanceof Error ? err.message : "An error occurred while fetching budget data";
        setError(errorMessage);
        console.error("Error fetching budgets:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchBudgets();
  }, [selectedBrand]);

  // Import budget data from CSV
  const importBudgets = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/budget?import=true");
      if (!response.ok) {
        throw new Error("Failed to import budget data");
      }
      // Refresh the data after import
      const brandResponse = await fetch("/api/budget?brands=true");
      if (brandResponse.ok) {
        const data = await brandResponse.json();
        setBrands(data.brands || []);
      }
      // Fetch budgets for the selected brand
      const url = selectedBrand && selectedBrand !== "all"
        ? `/api/budget?brand=${encodeURIComponent(selectedBrand)}`
        : "/api/budget";
      const budgetResponse = await fetch(url);
      if (budgetResponse.ok) {
        const data = await budgetResponse.json();
        setBudgets(data.budgets || []);
      }
      // Clear pending updates after import
      setPendingUpdates(new Map());
      setEditingCell(null);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "An error occurred while importing budget data";
      setError(errorMessage);
      console.error("Error importing budgets:", err);
    } finally {
      setLoading(false);
    }
  };

  // Handle cell click to start editing
  const handleCellClick = useCallback((month: string, account: string, currentValue: number) => {
    // Don't allow editing if we're in "All Brands" mode
    if (!selectedBrand || selectedBrand === "all") {
      return;
    }

    const cellKey = `${month}-${account}`;
    const pendingUpdate = pendingUpdates.get(cellKey);
    const displayValue = pendingUpdate ? pendingUpdate.value : currentValue;
    
    setEditingCell({
      month,
      account,
      value: displayValue.toString(),
      originalValue: currentValue
    });
  }, [selectedBrand, pendingUpdates]);

  // Handle input change while editing
  const handleInputChange = useCallback((value: string) => {
    if (editingCell) {
      setEditingCell({ ...editingCell, value });
    }
  }, [editingCell]);

  // Handle save (Enter key or blur)
  const handleSave = useCallback(async () => {
    if (!editingCell || !selectedBrand || selectedBrand === "all") {
      return;
    }

    const numericValue = parseFloat(editingCell.value);
    if (isNaN(numericValue)) {
      setError("Please enter a valid number");
      return;
    }

    // If value hasn't changed, just exit editing mode
    if (numericValue === editingCell.originalValue) {
      setEditingCell(null);
      return;
    }

    const cellKey = `${editingCell.month}-${editingCell.account}`;
    setSavingCells(prev => new Set(prev).add(cellKey));

    try {
      const response = await fetch("/api/budget", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          brand: selectedBrand,
          month: editingCell.month,
          account: editingCell.account,
          budget_value: numericValue,
          updated_by: "user" // In a real app, this would be the current user
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update budget");
      }

      // Update local state optimistically
      setBudgets(prev => prev.map(budget => 
        budget.month === editingCell.month && 
        budget.account === editingCell.account && 
        budget.brand === selectedBrand
          ? { ...budget, budget_value: numericValue }
          : budget
      ));

      // Remove from pending updates
      setPendingUpdates(prev => {
        const newMap = new Map(prev);
        newMap.delete(cellKey);
        return newMap;
      });

      setEditingCell(null);
      setError(null);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Failed to save budget";
      setError(errorMessage);
      
      // Add to pending updates for retry
      setPendingUpdates(prev => new Map(prev).set(cellKey, {
        month: editingCell.month,
        account: editingCell.account,
        value: numericValue,
        originalValue: editingCell.originalValue
      }));
    } finally {
      setSavingCells(prev => {
        const newSet = new Set(prev);
        newSet.delete(cellKey);
        return newSet;
      });
    }
  }, [editingCell, selectedBrand]);

  // Handle cancel (Escape key)
  const handleCancel = useCallback(() => {
    setEditingCell(null);
  }, []);

  // Handle key events
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSave();
    } else if (e.key === "Escape") {
      e.preventDefault();
      handleCancel();
    }
  }, [handleSave, handleCancel]);

  // Group budgets by month and account for display
  const groupedBudgets = budgets.reduce((acc, budget) => {
    const month = budget.month;
    if (!acc[month]) {
      acc[month] = {};
    }
    acc[month][budget.account] = budget.budget_value;
    return acc;
  }, {} as Record<string, Record<string, number>>);

  // Get unique accounts across all months
  const allAccounts = Array.from(
    new Set(budgets.map((budget) => budget.account))
  ).sort();

  // Sort months in chronological order
  const sortedMonths = Object.keys(groupedBudgets).sort((a, b) => {
    // Extract month and year from format like "Jan-25"
    const [aMonth, aYear] = a.split("-");
    const [bMonth, bYear] = b.split("-");
    
    // Compare years first
    if (aYear !== bYear) {
      return parseInt(aYear) - parseInt(bYear);
    }
    
    // If years are the same, compare months
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    return months.indexOf(aMonth) - months.indexOf(bMonth);
  });

  // Format currency values
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(value);
  };

  // Get display value for a cell (considering pending updates)
  const getCellDisplayValue = (month: string, account: string) => {
    const cellKey = `${month}-${account}`;
    const pendingUpdate = pendingUpdates.get(cellKey);
    const originalValue = groupedBudgets[month][account];
    
    if (pendingUpdate) {
      return pendingUpdate.value;
    }
    return originalValue;
  };

  // Check if cell has pending changes
  const hasPendingChanges = (month: string, account: string) => {
    const cellKey = `${month}-${account}`;
    return pendingUpdates.has(cellKey);
  };

  // Check if cell is currently saving
  const isCellSaving = (month: string, account: string) => {
    const cellKey = `${month}-${account}`;
    return savingCells.has(cellKey);
  };

  const canEdit = selectedBrand && selectedBrand !== "all";

  return (
    <AppLayout
      navItems={mainNavItems}
      secondaryNavItems={mainSecondaryNavItems}
      pageTitle="Budget Data"
      pageDescription="Manage and view budget allocations"
      showPageHeader={true}
    >
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Budget Data</CardTitle>
          <div className="flex space-x-4">
            <Select
              value={selectedBrand || ""}
              onValueChange={(value) => setSelectedBrand(value || undefined)}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select Brand" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Brands</SelectItem>
                {brands.map((brand) => (
                  <SelectItem key={brand} value={brand}>
                    {brand}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button onClick={importBudgets} disabled={loading}>
              {loading ? "Loading..." : "Import from CSV"}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {!canEdit && selectedBrand && (
            <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
              Select a specific brand to enable editing. Aggregated data for &ldquo;All Brands&rdquo; is read-only.
            </div>
          )}

          {canEdit && pendingUpdates.size > 0 && (
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
              You have {pendingUpdates.size} unsaved change(s). Changes are saved automatically when you finish editing a cell.
            </div>
          )}

          {loading ? (
            <div className="text-center py-8">Loading budget data...</div>
          ) : budgets.length === 0 ? (
            <div className="text-center py-8">
              No budget data available. Please import data from CSV.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Month</TableHead>
                    {allAccounts.map((account) => (
                      <TableHead key={account}>{account}</TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedMonths.map((month) => (
                    <TableRow key={month}>
                      <TableCell className="font-medium">{month}</TableCell>
                      {allAccounts.map((account) => {
                        const cellKey = `${month}-${account}`;
                        const isEditing = editingCell?.month === month && editingCell?.account === account;
                        const hasPending = hasPendingChanges(month, account);
                        const isSaving = isCellSaving(month, account);
                        const originalValue = groupedBudgets[month][account];
                        const displayValue = getCellDisplayValue(month, account);

                        return (
                          <TableCell 
                            key={cellKey}
                            className={`
                              ${canEdit ? 'cursor-pointer hover:bg-gray-50' : ''}
                              ${hasPending ? 'bg-yellow-50 border-yellow-200' : ''}
                              ${isSaving ? 'bg-blue-50' : ''}
                              ${isEditing ? 'p-1' : ''}
                            `}
                            onClick={() => !isEditing && originalValue !== undefined && handleCellClick(month, account, originalValue)}
                          >
                            {originalValue !== undefined ? (
                              isEditing ? (
                                <Input
                                  type="number"
                                  value={editingCell.value}
                                  onChange={(e) => handleInputChange(e.target.value)}
                                  onBlur={handleSave}
                                  onKeyDown={handleKeyDown}
                                  className="w-full h-8 text-sm"
                                  autoFocus
                                  step="0.01"
                                />
                              ) : (
                                <div className="flex items-center justify-between">
                                  <span className={hasPending ? 'font-semibold' : ''}>
                                    {formatCurrency(displayValue)}
                                  </span>
                                  {isSaving && (
                                    <span className="text-xs text-blue-600 ml-2">Saving...</span>
                                  )}
                                  {hasPending && !isSaving && (
                                    <span className="text-xs text-yellow-600 ml-2">Modified</span>
                                  )}
                                </div>
                              )
                            ) : (
                              "-"
                            )}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </AppLayout>
  );
}