import { mainNavItems, mainSecondaryNavItems } from "@/components/main/MainNavConfig"

import { AppLayout } from "@/components/shared/AppLayout"
import { EcommerceKpiCards } from "@/components/ecommerce-kpi-cards"
import { EnhancedDashboardFilters } from "@/components/enhanced-dashboard-filters"
import { FilterProvider } from "@/lib/contexts/filter-context"
import { MasterKpiComparisonChart } from "@/components/master-kpi-comparison-chart"
import React from "react"
import { authOptions } from "@/lib/auth-options";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";

export default async function DashboardPage() {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    redirect("/auth/signin");
  }

  return (
    <AppLayout
      navItems={mainNavItems}
      secondaryNavItems={mainSecondaryNavItems}
      pageTitle="Dashboard"
      pageDescription="Overview of your business performance"
      showPageHeader={true}
    >
      <FilterProvider>
        {/* Dashboard Filters */}
        <EnhancedDashboardFilters pageType="dashboard" />
        
        {/* Master KPI Comparison Chart */}
        <MasterKpiComparisonChart />
        
        {/* KPI Cards */}
        <EcommerceKpiCards />
      </FilterProvider>
    </AppLayout>
  )
}
