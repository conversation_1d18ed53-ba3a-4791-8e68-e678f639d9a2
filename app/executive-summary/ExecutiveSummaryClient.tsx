'use client';

import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Grid3X3, Presentation } from 'lucide-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { mainNavItems, mainSecondaryNavItems } from "@/components/main/MainNavConfig"
import { useCurrencyPreference, useFilterData, useKpiData, useTimePeriod } from '@/components/executive-summary';
import { useRouter, useSearchParams } from 'next/navigation';

// Import components directly
import { AppLayout } from "@/components/shared/AppLayout"
import { Button } from "@/components/ui/button";
import KpiCardsGrid from '@/components/executive-summary/KpiCardsGrid';
import type { KpiSummaryData } from '@/components/executive-summary/types';
import PdfExport from '@/components/executive-summary/PdfExport';
import { PeriodType } from '@/components/executive-summary/types';
// Import slide components
import { ScrollableSlideView } from '@/components/executive-summary/slides/components/ScrollableSlideView';
import { SimpleBrandSelector } from '@/components/executive-summary/SimpleBrandSelector';
// Executive Summary specific imports
// Import utilities and hooks directly
import { prepareKpiSummaryData } from '@/components/executive-summary/dataPrep';

export default function ExecutiveSummaryClient() {
  // URL and navigation hooks
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // All hooks must be called at the top level
  const { currency, setCurrency } = useCurrencyPreference();
  const {
    filterData,
    loadingFilters,
    filterError,
    selectedBrand,
    setSelectedBrand,
  } = useFilterData();

  const {
    selectedPeriodType,
    selectedPeriod, // This should be non-null after initialization
    availablePeriods,
    changePeriodType,
    selectPeriod,
  } = useTimePeriod();

  const {
    kpiData,
    ytdData,
    lastYearData,
    budgetData,
    weekTrend,
    monthTrend,
    yearTrend,
    loadingData,
    apiError,
    fetchKpiData, // Retry function
    formatPeriodForBudget,
  } = useKpiData(currency, selectedBrand?.name, selectedPeriod);

  // View state management
  const currentView = searchParams.get('view') || 'slides';

  // PDF Export state
  const [showPdfExport, setShowPdfExport] = useState(false);
  const [pdfStatus, setPdfStatus] = useState('');

  // URL update function - removed searchParams dependency to prevent re-render loop
  const updateURL = useCallback((view: string, slide?: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set('view', view);
    
    if (view === 'slides' && slide !== undefined) {
      params.set('slide', (slide + 1).toString());
    } else {
      params.delete('slide');
    }
    
    router.push(`?${params.toString()}`, { scroll: false });
  }, [router]);

  // View switching functions
  const switchToSlideView = useCallback((slideIndex: number = 0) => {
    updateURL('slides', slideIndex);
  }, [updateURL]);

  const switchToGridView = useCallback(() => {
    updateURL('grid');
  }, [updateURL]);

  // Data Preparation for KpiTable
  const kpiSummaryData: KpiSummaryData[] = useMemo(() => {
    if (kpiData && ytdData && !loadingData) {
      // Format the selected period for budget data lookup
      const formattedPeriod = formatPeriodForBudget(selectedPeriod);
      
      const summaryData = prepareKpiSummaryData(
        kpiData, 
        ytdData, 
        lastYearData, 
        budgetData, 
        formattedPeriod
      );
      return summaryData;
    }
    return [];
  }, [kpiData, ytdData, lastYearData, budgetData, selectedPeriod, loadingData, formatPeriodForBudget]);

  // Set default currency to CAD if not set
  useEffect(() => {
    if (!currency) {
      setCurrency('CAD');
    }
  }, [currency, setCurrency]);

  // Log the selectedBrand?.name being passed to useKpiData
  console.log(`[Executive Summary Debug - Page] selectedBrand?.name for useKpiData: ${selectedBrand?.name}`);

  const handleExportPdf = () => {
    if (!selectedPeriod) {
      setPdfStatus('Please select a period before exporting.');
      setTimeout(() => setPdfStatus(''), 3000);
      return;
    }
    setShowPdfExport(true);
    setPdfStatus('Generating PDF...');
  };

  const handlePdfSuccess = () => {
    setShowPdfExport(false);
    setPdfStatus('PDF generated successfully!');
    // Consider using a toaster notification here
    setTimeout(() => setPdfStatus(''), 3000);
  };

  const handlePdfError = (error: Error) => {
    setShowPdfExport(false);
    setPdfStatus(`Error generating PDF: ${error.message}`);
    // Consider using a toaster notification here
    setTimeout(() => setPdfStatus(''), 5000);
  };

  const brandNameToDisplay = useMemo(() => selectedBrand?.name || "All Brands", [selectedBrand?.name]);

  const handleCurrencyChange = useCallback((newCurrency: string) => {
    setCurrency(newCurrency as 'CAD' | 'USD');
  }, [setCurrency]);

  // Helper function to map string to PeriodType - memoized to prevent re-creation
  const mapStringToPeriodType = useCallback((type: string): PeriodType => {
    switch (type) {
      case 'month':
        return PeriodType.MONTH;
      case 'quarter':
        return PeriodType.QUARTER;
      case 'year':
        return PeriodType.YEAR;
      default:
        return PeriodType.MONTH;
    }
  }, []);

  // Helper function to map PeriodType to string for comparison - memoized to prevent re-creation
  const mapPeriodTypeToString = useCallback((type: PeriodType): string => {
    switch (type) {
      case PeriodType.MONTH:
        return 'month';
      case PeriodType.QUARTER:
        return 'quarter';
      case PeriodType.YEAR:
        return 'year';
      default:
        return 'month';
    }
  }, []);

  // Create a unique identifier for each period to ensure proper selection - memoized to prevent re-creation
  const createPeriodId = useCallback((period: { type: PeriodType; year: number; value: number }) => {
    return `${period.type}-${period.year}-${period.value}`;
  }, []);

  // Get the current selected period ID - memoized to prevent re-calculation
  const selectedPeriodId = useMemo(() => selectedPeriod ? createPeriodId(selectedPeriod) : '', [selectedPeriod, createPeriodId]);

  // Memoized period selection handler to prevent re-renders
  const handlePeriodChange = useCallback((value: string) => {
    console.log(`[Executive Summary Debug - Page] Period selection changed to: ${value}`);
    const period = availablePeriods.find(p => createPeriodId(p) === value);
    if (period) {
      console.log(`[Executive Summary Debug - Page] Found matching period:`, period);
      selectPeriod(period);
    } else {
      console.log(`[Executive Summary Debug - Page] No matching period found for value: ${value}`);
      console.log(`[Executive Summary Debug - Page] Available periods:`, availablePeriods.map(p => ({ id: createPeriodId(p), label: p.label })));
    }
  }, [availablePeriods, createPeriodId, selectPeriod]);

  // Memoized view change handler to prevent re-renders
  const handleViewChange = useCallback((value: string | undefined) => {
    if (value === 'slides') {
      switchToSlideView(0);
    } else if (value === 'grid') {
      switchToGridView();
    }
  }, [switchToSlideView, switchToGridView]);

  // Memoized slide change handler to prevent re-renders
  const handleSlideChange = useCallback((slideIndex: number) => {
    // Update URL when slide changes
    updateURL('slides', slideIndex);
  }, [updateURL]);

  return (
    <AppLayout
      navItems={mainNavItems}
      secondaryNavItems={mainSecondaryNavItems}
      pageTitle="Executive Summary"
      pageDescription="Comprehensive business performance overview"
      showPageHeader={false}
    >
      {/* Modern Header Section */}
      <div className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 rounded-lg border p-6 mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-slate-900 dark:text-slate-100">
              Executive Summary
            </h1>
            <p className="text-slate-600 dark:text-slate-400 mt-1">
              Performance highlights for {brandNameToDisplay}
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {/* View Toggle */}
            <ToggleGroup
              type="single"
              value={currentView}
              onValueChange={handleViewChange}
              className="bg-white dark:bg-slate-800 border rounded-lg"
            >
              <ToggleGroupItem value="slides" aria-label="Slide View" className="gap-2">
                <Presentation className="h-4 w-4" />
                Slides
              </ToggleGroupItem>
              <ToggleGroupItem value="grid" aria-label="Grid View" className="gap-2">
                <Grid3X3 className="h-4 w-4" />
                Grid
              </ToggleGroupItem>
            </ToggleGroup>

            <Button
              onClick={handleExportPdf}
              variant="default"
              size="sm"
              disabled={showPdfExport || loadingData}
              className="bg-primary hover:bg-primary/90"
            >
              {showPdfExport ? "Generating..." : "Export PDF"}
            </Button>
          </div>
        </div>

        {/* Compact Controls Row - Show for both grid and slides view */}
        {!loadingFilters && !filterError && selectedPeriod && (
          <div className="bg-white dark:bg-slate-800 rounded-lg border p-4">
            <div className="flex flex-wrap items-center gap-6">
              {/* Brand Selector */}
              {filterData && (
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">Brand:</span>
                  <SimpleBrandSelector
                    brands={filterData.brands}
                    selectedBrand={selectedBrand}
                    onBrandSelect={setSelectedBrand}
                    isLoading={loadingFilters}
                    error={filterError}
                  />
                </div>
              )}

              {/* Period Type Selector */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-slate-700 dark:text-slate-300">Period:</span>
                <div className="flex bg-slate-100 dark:bg-slate-700 rounded-lg p-1">
                  {['month', 'quarter', 'year'].map((type) => (
                    <button
                      key={type}
                      onClick={() => changePeriodType(mapStringToPeriodType(type))}
                      className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                        mapPeriodTypeToString(selectedPeriodType) === type
                          ? 'bg-white dark:bg-slate-600 text-slate-900 dark:text-slate-100 shadow-sm'
                          : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100'
                      }`}
                    >
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              {/* Period Selector */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  {selectedPeriodType === PeriodType.MONTH ? 'Month:' :
                   selectedPeriodType === PeriodType.QUARTER ? 'Quarter:' : 'Year:'}
                </span>
                <Select
                  value={selectedPeriodId}
                  onValueChange={handlePeriodChange}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    {availablePeriods.map((period, index) => {
                      const periodId = createPeriodId(period);
                      return (
                        <SelectItem key={`${periodId}-${index}`} value={periodId}>
                          {period.label}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              {/* Currency Selector */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-slate-700 dark:text-slate-300">Currency:</span>
                <Select value={currency} onValueChange={handleCurrencyChange}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CAD">CAD</SelectItem>
                    <SelectItem value="USD">USD</SelectItem>
                  </SelectContent>
                </Select>
              </div>

            </div>
          </div>
        )}
      </div>

      {/* Status Messages */}
      {pdfStatus && (
        <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <p className="text-sm text-blue-700 dark:text-blue-300">{pdfStatus}</p>
        </div>
      )}

      {loadingFilters && (
        <div className="flex justify-center items-center p-12 bg-slate-50 dark:bg-slate-900 rounded-lg border">
          <div className="flex items-center gap-3">
            <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
            <p className="text-slate-600 dark:text-slate-400">Loading configuration...</p>
          </div>
        </div>
      )}
      
      {filterError && (
        <Card className="border-destructive bg-red-50 dark:bg-red-900/20 mb-6">
          <CardHeader>
            <CardTitle className="text-destructive flex items-center gap-2">
              <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">!</span>
              </div>
              Configuration Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700 dark:text-red-300">{filterError.message}</p>
          </CardContent>
        </Card>
      )}

      {!loadingFilters && !filterError && selectedPeriod && (
        <>
          {loadingData && (
            <div className="flex justify-center items-center p-12 bg-slate-50 dark:bg-slate-900 rounded-lg border">
              <div className="flex items-center gap-3">
                <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                <p className="text-slate-600 dark:text-slate-400">Loading report data...</p>
              </div>
            </div>
          )}
          
          {apiError && !loadingData && (
            <Card className="border-destructive bg-red-50 dark:bg-red-900/20 mb-6">
              <CardHeader>
                <CardTitle className="text-destructive flex items-center gap-2">
                  <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">!</span>
                  </div>
                  Data Loading Error
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-red-700 dark:text-red-300 mb-3">{apiError.message}</p>
                <Button onClick={() => fetchKpiData()} variant="outline" size="sm">
                  Retry
                </Button>
              </CardContent>
            </Card>
          )}

          {!loadingData && !apiError && kpiData && (
            <>
              {currentView === 'slides' ? (
                <ScrollableSlideView
                  kpiData={kpiSummaryData}
                  currency={currency}
                  period={selectedPeriod}
                  rawKpiData={kpiData}
                  onSlideChange={handleSlideChange}
                />
              ) : (
                <KpiCardsGrid
                  kpiSummaryData={kpiSummaryData}
                  currency={currency}
                  period={selectedPeriod}
                  kpiData={kpiData}
                  isLoading={loadingData}
                  error={apiError}
                  onRetry={() => fetchKpiData()}
                />
              )}
            </>
          )}

          {showPdfExport && selectedPeriod && (
            <PdfExport
              selectedBrand={brandNameToDisplay}
              selectedPeriod={selectedPeriod}
              currency={currency}
              kpiSummaryData={kpiSummaryData}
              weekTrend={weekTrend}
              monthTrend={monthTrend}
              yearTrend={yearTrend}
              onSuccess={handlePdfSuccess}
              onError={handlePdfError}
            />
          )}
        </>
      )}
    </AppLayout>
  );
}
