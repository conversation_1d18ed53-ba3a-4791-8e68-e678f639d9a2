import ExecutiveSummaryClient from "@/app/executive-summary/ExecutiveSummaryClient";
import { authOptions } from "@/lib/auth-options";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";

export default async function ExecutiveSummaryPage() {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    redirect("/auth/signin");
  }

  return <ExecutiveSummaryClient />;
}
