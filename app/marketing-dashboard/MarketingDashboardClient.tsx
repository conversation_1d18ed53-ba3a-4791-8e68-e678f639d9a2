"use client";

// Import from the new src/lib location
import { FilterProvider, useFilters } from "@/lib/contexts/filter-context";

import { mainNavItems, mainSecondaryNavItems } from "@/components/main/MainNavConfig"

import { AppLayout } from "@/components/shared/AppLayout"
import { CampaignDataTable } from "@/components/campaign-data-table"
import { EnhancedDashboardFilters } from "@/components/enhanced-dashboard-filters"
import { OverallKPICards } from "@/components/marketing-kpi-cards"

import { SpendBreakdownChart } from "@/components/spend-breakdown-chart"

// Create a wrapper component that uses the FilterProvider
function MarketingDashboardContent() {
  const { filters, setFilters } = useFilters();

  return (
    <>
      {/* Dashboard Filters */}
      <EnhancedDashboardFilters
        showCountryFilter={false}
        showCurrencyFilter={true}
        showGroupByFilter={true}
        pageType="marketing-dashboard"
      />

      {/* KPI Summary Section */}
      <OverallKPICards />

      {/* Spend Breakdown Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Spend Breakdown</h3>
        <SpendBreakdownChart />
      </div>

      {/* Main Data Table Section */}
      <CampaignDataTable />
    </>
  );
}

// Main component that provides the context
export default function MarketingDashboardClient() {
  return (
    <AppLayout
      navItems={mainNavItems}
      secondaryNavItems={mainSecondaryNavItems}
      pageTitle="Marketing Dashboard"
      pageDescription="Track and analyze your marketing performance"
      showPageHeader={true}
    >
      <FilterProvider>
        <MarketingDashboardContent />
      </FilterProvider>
    </AppLayout>
  )
}