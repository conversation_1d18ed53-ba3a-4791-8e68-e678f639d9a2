import CampaignDetailsClient from "./CampaignDetailsClient";
import { authOptions } from "@/lib/auth-options";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { formatCampaignNameShort } from "@/lib/chart-utils";
import type { Metadata } from "next";

interface CampaignDetailsPageProps {
  params: Promise<{
    campaignId: string;
  }>;
}

export async function generateMetadata({ params }: CampaignDetailsPageProps): Promise<Metadata> {
  try {
    const { campaignId } = await params;

    // Decode the campaign ID (which is the URL-encoded campaign name)
    const decodedCampaignName = decodeURIComponent(campaignId);

    // Format the campaign name for the title
    const formattedName = formatCampaignNameShort(decodedCampaignName, 40);

    return {
      title: `${formattedName} - Campaign Details | NOLK`,
      description: `View detailed analytics and performance metrics for ${formattedName} campaign.`,
    };
  } catch (error) {
    // Fallback if there's an error
    return {
      title: "Campaign Details | NOLK",
      description: "View detailed campaign analytics and performance metrics.",
    };
  }
}

export default async function CampaignDetailsPage({ params }: CampaignDetailsPageProps) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/auth/signin");
  }

  return <CampaignDetailsClient params={params} />;
}
