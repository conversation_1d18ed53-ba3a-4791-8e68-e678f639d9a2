import MarketingDashboardClient from "@/app/marketing-dashboard/MarketingDashboardClient";
import { authOptions } from "@/lib/auth-options";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";

export default async function MarketingDashboardPage() {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    redirect("/auth/signin");
  }

  return <MarketingDashboardClient />;
}
