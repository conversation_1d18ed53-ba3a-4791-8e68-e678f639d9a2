'use client';

import { redirect } from 'next/navigation';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

export default function TestAuthPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Try immediate redirect if not authenticated
  if (status !== 'loading' && !session) {
    console.log('[TestAuth] Immediate redirect attempt...');
    redirect('/auth/signin?callbackUrl=' + encodeURIComponent('/test-auth'));
  }

  useEffect(() => {
    console.log('[TestAuth] Session status:', status);
    console.log('[TestAuth] Session data:', session);
    
    if (status === 'loading') {
      console.log('[TestAuth] Still loading...');
      return;
    }
    
    if (!session) {
      console.log('[TestAuth] No session in useEffect, attempting redirect...');
      
      // Try multiple redirect methods
      const redirectUrl = '/auth/signin?callbackUrl=' + encodeURIComponent('/test-auth');
      
      // Method 1: Next.js router
      console.log('[TestAuth] Trying router.push...');
      router.push(redirectUrl);
      
      // Method 2: router.replace
      setTimeout(() => {
        console.log('[TestAuth] Trying router.replace...');
        router.replace(redirectUrl);
      }, 500);
      
      // Method 3: window.location as fallback
      setTimeout(() => {
        console.log('[TestAuth] Trying window.location.href...');
        window.location.href = redirectUrl;
      }, 1000);
      
      // Method 4: window.location.replace as last resort
      setTimeout(() => {
        console.log('[TestAuth] Trying window.location.replace...');
        window.location.replace(redirectUrl);
      }, 1500);
      
    } else {
      console.log('[TestAuth] Session found:', session.user?.email);
    }
  }, [session, status, router]);

  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  if (!session) {
    return (
      <div>
        <div>Redirecting to login...</div>
        <div>If redirect doesn&apos;t work, <a href="/auth/signin?callbackUrl=%2Ftest-auth">click here</a></div>
        <div style={{ marginTop: '20px' }}>
          <button onClick={() => window.location.href = '/auth/signin?callbackUrl=%2Ftest-auth'}>
            Force redirect with button
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1>Test Auth Page</h1>
      <p>You are logged in as: {session.user?.email}</p>
      <p>Session status: {status}</p>
    </div>
  );
}
