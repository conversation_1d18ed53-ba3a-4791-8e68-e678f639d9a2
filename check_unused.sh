#!/bin/bash

# Check if we're in a Next.js project
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found. Please run this script from your Next.js project root."
    exit 1
fi

# Install unimported globally if not already installed
if ! command -v unimported &> /dev/null; then
    echo "Installing 'unimported' tool..."
    npm install -g unimported
fi

echo "Scanning for unused files in your Next.js project..."
echo "=================================================="

# Create a temporary config file for unimported
cat > .unimportedrc.json << EOF
{
  "entry": [
    "pages/**/*",
    "app/**/*",
    "src/pages/**/*",
    "src/app/**/*"
  ],
  "extensions": [".js", ".jsx", ".ts", ".tsx"],
  "ignorePatterns": [
    "**/node_modules/**",
    "**/.next/**",
    "**/dist/**",
    "**/build/**",
    "**/*.test.*",
    "**/*.spec.*",
    "**/*.stories.*",
    "**/test/**",
    "**/tests/**",
    "**/__tests__/**",
    "**/coverage/**",
    "**/.git/**"
  ],
  "ignoreUnresolved": [],
  "ignoreUnimported": [
    "next.config.js",
    "next.config.mjs",
    "tailwind.config.js",
    "postcss.config.js",
    "jest.config.js",
    "prettier.config.js",
    ".eslintrc.js",
    "next-env.d.ts"
  ],
  "ignoreUnused": []
}
EOF

# Run unimported and save to a file
echo "Running analysis..."
unimported > unused_files_raw.txt 2>&1

# Check if unimported ran successfully
if [ $? -ne 0 ]; then
    echo "Error running unimported. Output:"
    cat unused_files_raw.txt
    rm -f .unimportedrc.json unused_files_raw.txt
    exit 1
fi

# Extract and display unused files
echo ""
echo "UNUSED FILES FOUND:"
echo "==================="
grep -E "^\s*-" unused_files_raw.txt | sed 's/^[[:space:]]*- //' | sort | tee unused_files_list.txt

# Count the results
count=$(grep -E "^\s*-" unused_files_raw.txt | wc -l | tr -d ' ')

echo ""
echo "==================="
echo "Total unused files: $count"
echo ""
echo "Results saved to: unused_files_list.txt"

# Clean up
rm -f .unimportedrc.json unused_files_raw.txt

echo ""
echo "To view the list again, run: cat unused_files_list.txt"
