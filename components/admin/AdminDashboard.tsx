'use client';

import {
  Act<PERSON>,
  <PERSON>Right,
  BarChart3,
  Building2,
  Clock,
  Database,
  Settings,
  Shield,
  TrendingUp,
  <PERSON>r<PERSON>heck,
  UserPlus,
  Users
} from "lucide-react";
import {
  Card,
  CardContent,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { useEffect, useState } from 'react';

import { Badge } from "@/components/ui/badge";
import Link from 'next/link';
import { Skeleton } from "@/components/ui/skeleton";

interface DashboardStats {
  userCount: number;
  brandCount: number;
  roleCount: number;
  groupCount: number;
  permissionCount: number;
  latestUserAdditions: { id: number; name: string; email: string; createdAt: string }[];
  recentUserLogins: { id: number; name: string; email: string; lastLoginAt: string }[];
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch('/api/admin/dashboard-stats');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setStats(data);
      } catch (e) {
        if (e instanceof Error) {
          setError(e.message || 'Failed to fetch dashboard stats');
        } else {
          setError('An unknown error occurred');
        }
        console.error(e);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const metricCards = [
    {
      title: "Total Users",
      value: stats?.userCount,
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      description: "Active system users"
    },
    {
      title: "Total Brands",
      value: stats?.brandCount,
      icon: Building2,
      color: "text-green-600",
      bgColor: "bg-green-50",
      description: "Managed brands"
    },
    {
      title: "Total Roles",
      value: stats?.roleCount,
      icon: Shield,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      description: "Permission roles"
    },
    {
      title: "Total Groups",
      value: stats?.groupCount,
      icon: UserCheck,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      description: "User groups"
    },
    {
      title: "Total Permissions",
      value: stats?.permissionCount,
      icon: Settings,
      color: "text-red-600",
      bgColor: "bg-red-50",
      description: "System permissions"
    }
  ];

  const managementSections = [
    {
      title: "User Management",
      description: "Manage users, roles, and access permissions",
      href: "/admin/users",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      stats: `${stats?.userCount || 0} users`
    },
    {
      title: "Role Management",
      description: "Configure user roles and permission sets",
      href: "/admin/roles",
      icon: Shield,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      stats: `${stats?.roleCount || 0} roles`
    },
    {
      title: "Permission Management",
      description: "Define and manage application permissions",
      href: "/admin/permissions",
      icon: Settings,
      color: "text-red-600",
      bgColor: "bg-red-50",
      stats: `${stats?.permissionCount || 0} permissions`
    },
    {
      title: "Group Management",
      description: "Organize users into logical groups",
      href: "/admin/groups",
      icon: UserCheck,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      stats: `${stats?.groupCount || 0} groups`
    },
    {
      title: "Brand Management",
      description: "Manage brand information and settings",
      href: "/admin/brands",
      icon: Building2,
      color: "text-green-600",
      bgColor: "bg-green-50",
      stats: `${stats?.brandCount || 0} brands`
    },
    {
      title: "Database Structure",
      description: "View and manage database schema",
      href: "/admin/db-structure",
      icon: Database,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50",
      stats: "Schema info"
    }
  ];

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
              Admin Dashboard
            </h1>
            <p className="mt-2 text-lg text-gray-600 dark:text-gray-300">
              Manage users, roles, and system settings
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="flex items-center space-x-1">
              <Activity className="h-3 w-3" />
              <span>System Active</span>
            </Badge>
          </div>
        </div>
      </div>

      {error && (
        <div className="rounded-lg bg-red-50 border border-red-200 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading dashboard data</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Key Metrics */}
      <section>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Key Metrics
          </h2>
          <Badge variant="outline">Live Data</Badge>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6">
          {metricCards.map((metric, index) => {
            const IconComponent = metric.icon;
            return (
              <Card key={index} className="relative overflow-hidden border-0 shadow-sm hover:shadow-md transition-shadow duration-200">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className={`p-2 rounded-lg ${metric.bgColor}`}>
                      <IconComponent className={`h-5 w-5 ${metric.color}`} />
                    </div>
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  </div>
                  <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {metric.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  {loading ? (
                    <Skeleton data-testid="skeleton" className="h-8 w-16" />
                  ) : (
                    <div className="text-3xl font-bold text-gray-900 dark:text-white">
                      {metric.value ?? 'N/A'}
                    </div>
                  )}
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {metric.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </section>

      {/* Recent Activities */}
      <section>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
          <Clock className="h-5 w-5 mr-2" />
          Recent Activity
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="border-0 shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold flex items-center">
                <UserPlus className="h-5 w-5 mr-2 text-green-600" />
                Latest User Additions
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-3">
                      <Skeleton data-testid="skeleton" className="h-10 w-10 rounded-full" />
                      <div className="space-y-1 flex-1">
                        <Skeleton data-testid="skeleton" className="h-4 w-3/4" />
                        <Skeleton data-testid="skeleton" className="h-3 w-1/2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {stats?.latestUserAdditions && stats.latestUserAdditions.length > 0 ? (
                    stats.latestUserAdditions.slice(0, 5).map(user => (
                      <div key={user.id} className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                            <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {user.name}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            {user.email}
                          </p>
                          <p className="text-xs text-gray-400 dark:text-gray-500">
                            {new Date(user.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <UserPlus className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-sm text-gray-500 dark:text-gray-400">No recent user additions</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold flex items-center">
                <Activity className="h-5 w-5 mr-2 text-blue-600" />
                Recent Login Activities
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-3">
                      <Skeleton data-testid="skeleton" className="h-10 w-10 rounded-full" />
                      <div className="space-y-1 flex-1">
                        <Skeleton data-testid="skeleton" className="h-4 w-3/4" />
                        <Skeleton data-testid="skeleton" className="h-3 w-1/2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {stats?.recentUserLogins && stats.recentUserLogins.length > 0 ? (
                    stats.recentUserLogins.slice(0, 5).map(login => (
                      <div key={login.id} className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                            <Activity className="h-5 w-5 text-green-600 dark:text-green-400" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {login.name}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            {login.email}
                          </p>
                          <p className="text-xs text-gray-400 dark:text-gray-500">
                            {new Date(login.lastLoginAt).toLocaleString()}
                          </p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-sm text-gray-500 dark:text-gray-400">No recent login activities</p>
                      <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                        Login tracking will appear here once implemented
                      </p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Management Sections */}
      <section>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
          <Settings className="h-5 w-5 mr-2" />
          Management Sections
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {managementSections.map((section, index) => {
            const IconComponent = section.icon;
            return (
              <Link key={index} href={section.href}>
                <Card className="border-0 shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer group">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div className={`p-3 rounded-lg ${section.bgColor} group-hover:scale-110 transition-transform duration-200`}>
                        <IconComponent className={`h-6 w-6 ${section.color}`} />
                      </div>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1 transition-all duration-200" />
                    </div>
                    <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors duration-200">
                      {section.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {section.description}
                    </p>
                    <Badge variant="secondary" className="text-xs">
                      {section.stats}
                    </Badge>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>
      </section>
    </div>
  );
}
