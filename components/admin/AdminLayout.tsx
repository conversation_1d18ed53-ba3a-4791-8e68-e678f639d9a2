'use client';

import { adminNavItems, adminSecondaryNavItems } from './AdminNavConfig';

import { AppLayout } from '@/components/shared/AppLayout';
import React from 'react';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  return (
    <AppLayout
      navItems={adminNavItems}
      secondaryNavItems={adminSecondaryNavItems}
      logoText="Admin"
      logoHref="/admin"
    >
      {children}
    </AppLayout>
  );
};

export default AdminLayout;
