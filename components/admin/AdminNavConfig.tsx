"use client"

import {
  IconArchive,
  IconArrowLeft,
  IconBrandApple,
  IconDashboard,
  IconDatabase,
  IconHome,
  IconKey,
  IconSettings,
  IconShield,
  IconUsers,
  IconUsersGroup,
} from "@tabler/icons-react"

import type { NavItem } from "@/components/shared/AppLayout"

export const adminNavItems: NavItem[] = [
  {
    title: "Back to Dashboard",
    url: "/dashboard",
    icon: IconArrowLeft,
  },
  {
    title: "Dashboard",
    url: "/admin",
    icon: IconDashboard,
  },
  {
    title: "Users",
    url: "/admin/users",
    icon: IconUsers,
  },
  {
    title: "Roles",
    url: "/admin/roles",
    icon: IconShield,
  },
  {
    title: "Permissions",
    url: "/admin/permissions",
    icon: IconKey,
  },
  {
    title: "Groups",
    url: "/admin/groups",
    icon: IconUsersGroup,
  },
  {
    title: "Brands",
    url: "/admin/brands",
    icon: IconBrandApple,
  },
  {
    title: "DB Structure",
    url: "/admin/db-structure",
    icon: IconDatabase,
  },
  {
    title: "Backups",
    url: "/admin/backups",
    icon: IconArchive,
  },
]

export const adminSecondaryNavItems: NavItem[] = [
  {
    title: "Main Dashboard",
    url: "/dashboard",
    icon: IconHome,
  },
  {
    title: "Settings",
    url: "/admin/settings",
    icon: IconSettings,
  },
]
