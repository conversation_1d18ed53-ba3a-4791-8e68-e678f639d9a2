'use client';

import BrandForm, { BrandFormData, BrandFormRef } from './brands/BrandForm';
import BrandTable, { Brand } from './brands/BrandTable';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import AdminConfirmationDialog from './ui/AdminConfirmationDialog';
import AdminModalBase from './ui/AdminModalBase';
import { Button } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';

export default function BrandsComponent() {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchValue, setSearchValue] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });

  const [showBrandModal, setShowBrandModal] = useState(false);
  const [editingBrand, setEditingBrand] = useState<Brand | null>(null);
  const [isFormLoading, setIsFormLoading] = useState(false);
  const brandFormRef = useRef<BrandFormRef>(null);

  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletingBrand, setDeletingBrand] = useState<Brand | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchBrands = useCallback(async (search?: string, page?: number) => {
    setIsLoading(true);
    setError(null);
    try {
      const searchParams = new URLSearchParams();
      if (search) searchParams.append('search', search);
      if (page) searchParams.append('page', page.toString());
      searchParams.append('limit', pagination.limit.toString());

      const response = await fetch(`/api/admin/brands?${searchParams.toString()}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch brands: ${response.statusText}`);
      }
      const responseData = await response.json();
      
      // Handle both old format (direct array) and new format (with pagination)
      if (Array.isArray(responseData)) {
        // Old format - direct array
        const adaptedData: Brand[] = responseData.map((brand) => ({
          ...brand,
          id: brand.brand_id,
        }));
        setBrands(adaptedData);
      } else {
        // New format - with pagination
        const adaptedData: Brand[] = responseData.data.map((brand: Omit<Brand, 'id'>) => ({
          ...brand,
          id: brand.brand_id,
        }));
        setBrands(adaptedData);
        setPagination(responseData.pagination);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      toast.error('Failed to load brands.');
    } finally {
      setIsLoading(false);
    }
  }, [pagination.limit]);

  useEffect(() => {
    fetchBrands();
  }, [fetchBrands]);

  const handleOpenCreateModal = () => {
    setEditingBrand(null);
    setShowBrandModal(true);
  };

  const handleOpenEditModal = (brand: Brand) => {
    setEditingBrand(brand);
    setShowBrandModal(true);
  };

  const handleCloseBrandModal = () => {
    setShowBrandModal(false);
    setEditingBrand(null);
  };

  const handleSaveBrand = async (formData: BrandFormData) => {
    setIsFormLoading(true);
    const url = editingBrand?.brand_id ? `/api/admin/brands/${editingBrand.brand_id}` : '/api/admin/brands';
    const method = editingBrand?.brand_id ? 'PUT' : 'POST';

    try {
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: formData.name,
          code: formData.code,
          description: formData.description,
          abbreviation: formData.abbreviation,
          logo_image_url: formData.logo_image_url,
          status: formData.status,
          stage: formData.stage,
          group: formData.group,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to ${editingBrand?.brand_id ? 'update' : 'create'} brand`);
      }

      toast.success(`Brand ${editingBrand?.brand_id ? 'updated' : 'created'} successfully!`);
      fetchBrands(); // Refresh list
      handleCloseBrandModal();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : `An unknown error occurred while ${editingBrand?.brand_id ? 'updating' : 'creating'} brand.`);
    } finally {
      setIsFormLoading(false);
    }
  };
  
  const triggerBrandFormSubmit = () => {
    brandFormRef.current?.submit();
  }

  const handleOpenDeleteDialog = (brand: Brand) => {
    setDeletingBrand(brand);
    setShowDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setShowDeleteDialog(false);
    setDeletingBrand(null);
  };

  const handleConfirmDelete = async () => {
    if (!deletingBrand) return;
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/admin/brands/${deletingBrand.brand_id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete brand');
      }

      toast.success('Brand deleted successfully!');
      fetchBrands(); // Refresh list
      handleCloseDeleteDialog();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'An unknown error occurred while deleting brand.');
    } finally {
      setIsDeleting(false);
    }
  };
  
  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);
    fetchBrands(value, 1); // Reset to page 1 when searching
  }, [fetchBrands]);

  const renderContent = () => {
    if (isLoading && !brands.length) { // Show skeleton only on initial load
      return (
        <div className="space-y-2">
          <Skeleton className="h-8 w-1/4" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      );
    }
    if (error) {
      return <p className="text-red-500">Error loading brands: {error}</p>;
    }
    return (
      <BrandTable
        brands={brands}
        onEdit={handleOpenEditModal}
        onDelete={handleOpenDeleteDialog}
        enableSearch={true}
        searchValue={searchValue}
        onSearchChange={handleSearchChange}
        searchPlaceholder="Search brands..."
      />
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Brand Management</h1>
          <h2 className="text-sm sm:text-lg text-muted-foreground mt-1">Manage brands and their information</h2>
        </div>
        <Button onClick={handleOpenCreateModal}>
          <PlusCircle className="mr-2 h-4 w-4" /> Create Brand
        </Button>
      </div>

      {renderContent()}

      {showBrandModal && (
        <AdminModalBase
          isOpen={showBrandModal}
          onClose={handleCloseBrandModal}
          title={editingBrand ? 'Edit Brand' : 'Create New Brand'}
          onSave={triggerBrandFormSubmit}
          isLoading={isFormLoading}
          size="2xl"
        >
          <BrandForm
            ref={brandFormRef}
            brand={editingBrand}
            onSave={handleSaveBrand}
            isLoading={isFormLoading}
          />
        </AdminModalBase>
      )}

      {/* Delete Confirmation Dialog */}
      {deletingBrand && (
        <AdminConfirmationDialog
          isOpen={showDeleteDialog}
          onClose={handleCloseDeleteDialog}
          onConfirm={handleConfirmDelete}
          title="Delete Brand"
          description={<>Are you sure you want to delete the brand <strong>{deletingBrand.name}</strong>? This action cannot be undone.</>}
          confirmButtonText="Delete"
          confirmButtonVariant="destructive"
          isLoading={isDeleting}
        />
      )}
    </div>
  );
}
