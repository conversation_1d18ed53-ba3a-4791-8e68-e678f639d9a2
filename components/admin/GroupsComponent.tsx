'use client';

import GroupForm, { GroupFormRef } from './groups/GroupForm';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import AdminConfirmationDialog from './ui/AdminConfirmationDialog';
import AdminModalBase from './ui/AdminModalBase';
import { Button } from '@/components/ui/button';
import GroupTable from './groups/GroupTable';
import { PlusCircle } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from "sonner";

interface Group {
  id: string;
  name: string;
  description: string | null;
  user_ids?: string[];
  brand_ids?: string[];
}

interface User {
  id: string;
  name: string;
  email: string;
}

interface Brand {
  id: string;
  name: string;
  slug: string;
}

export default function GroupsComponent() {
  const [groups, setGroups] = useState<Group[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });

  const [showGroupModal, setShowGroupModal] = useState(false);
  const [editingGroup, setEditingGroup] = useState<Group | null>(null);
  const [isFormLoading, setIsFormLoading] = useState(false);
  const groupFormRef = useRef<GroupFormRef>(null);

  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletingGroup, setDeletingGroup] = useState<Group | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchData = useCallback(async (search?: string, page?: number) => {
    setIsLoading(true);
    setError(null);
    try {
      const searchParams = new URLSearchParams();
      if (search) searchParams.append('search', search);
      if (page) searchParams.append('page', page.toString());
      searchParams.append('limit', pagination.limit.toString());

      const [groupsRes, usersRes, brandsRes] = await Promise.all([
        fetch(`/api/admin/groups?${searchParams.toString()}`),
        fetch('/api/admin/users'),
        fetch('/api/admin/brands'),
      ]);

      if (!groupsRes.ok || !usersRes.ok || !brandsRes.ok) {
        throw new Error('Failed to fetch data');
      }

      const groupsData = await groupsRes.json();
      const usersData = await usersRes.json();
      const brandsData = await brandsRes.json();

      // Handle both old format (direct array) and new format (with pagination)
      let groupsList = [];
      if (Array.isArray(groupsData)) {
        // Old format - direct array
        groupsList = groupsData;
      } else {
        // New format - with pagination
        groupsList = groupsData.data;
        setPagination(groupsData.pagination);
      }

      const usersList = Array.isArray(usersData) ? usersData : [];
      const brandsList = Array.isArray(brandsData) ? brandsData.map(brand => ({
        id: brand.brand_id || brand.id,
        name: brand.name,
        slug: brand.code || brand.slug || brand.name?.toLowerCase().replace(/\s+/g, '-')
      })) : [];

      setGroups(groupsList);
      setUsers(usersList);
      setBrands(brandsList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      toast.error('Failed to load groups.');
    } finally {
      setIsLoading(false);
    }
  }, [pagination.limit]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleOpenCreateModal = () => {
    setEditingGroup(null);
    setShowGroupModal(true);
  };

  const handleOpenEditModal = (group: Group) => {
    setEditingGroup(group);
    setShowGroupModal(true);
  };

  const handleCloseGroupModal = () => {
    setShowGroupModal(false);
    setEditingGroup(null);
  };

  const handleSaveGroup = async (groupData: Omit<Group, 'id'> & { id?: string; user_ids: string[]; brand_ids: string[] }) => {
    setIsFormLoading(true);
    const url = groupData.id ? `/api/admin/groups/${groupData.id}` : '/api/admin/groups';
    const method = groupData.id ? 'PUT' : 'POST';

    try {
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(groupData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${groupData.id ? 'update' : 'create'} group`);
      }

      toast.success(`Group ${groupData.id ? 'updated' : 'created'} successfully!`);
      fetchData(); // Refresh list
      handleCloseGroupModal();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : `An unknown error occurred while ${groupData.id ? 'updating' : 'creating'} group.`);
    } finally {
      setIsFormLoading(false);
    }
  };

  const triggerGroupFormSubmit = () => {
    groupFormRef.current?.submit();
  };

  const handleOpenDeleteDialog = (group: Group) => {
    setDeletingGroup(group);
    setShowDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setShowDeleteDialog(false);
    setDeletingGroup(null);
  };

  const handleConfirmDelete = async () => {
    if (!deletingGroup) return;
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/admin/groups/${deletingGroup.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete group');
      }

      toast.success('Group deleted successfully!');
      fetchData(); // Refresh list
      handleCloseDeleteDialog();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'An unknown error occurred while deleting group.');
    } finally {
      setIsDeleting(false);
    }
  };

  const renderContent = () => {
    if (isLoading && !groups.length) { // Show skeleton only on initial load
      return (
        <div className="space-y-2">
          <Skeleton className="h-8 w-1/4" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      );
    }
    if (error) {
      return <p className="text-red-500">Error loading groups: {error}</p>;
    }
    return (
      <GroupTable
        groups={groups}
        onEdit={handleOpenEditModal}
        onDelete={handleOpenDeleteDialog}
        loading={isLoading}
        error={error}
      />
    );
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Group Management</h1>
          <h2 className="text-sm sm:text-lg text-muted-foreground mt-1">Manage user groups and their information</h2>
        </div>
        <Button onClick={handleOpenCreateModal}>
          <PlusCircle className="mr-2 h-4 w-4" /> Create Group
        </Button>
      </div>

      {renderContent()}

      {showGroupModal && (
        <AdminModalBase
          isOpen={showGroupModal}
          onClose={handleCloseGroupModal}
          title={editingGroup ? 'Edit Group' : 'Create New Group'}
          onSave={triggerGroupFormSubmit}
          isLoading={isFormLoading}
          size="2xl"
        >
          <GroupForm
            ref={groupFormRef}
            group={editingGroup}
            users={users}
            brands={brands}
            onSave={handleSaveGroup}
            onCancel={handleCloseGroupModal}
          />
        </AdminModalBase>
      )}

      {/* Delete Confirmation Dialog */}
      {deletingGroup && (
        <AdminConfirmationDialog
          isOpen={showDeleteDialog}
          onClose={handleCloseDeleteDialog}
          onConfirm={handleConfirmDelete}
          title="Delete Group"
          description={<>Are you sure you want to delete the group <strong>{deletingGroup.name}</strong>? This action cannot be undone.</>}
          confirmButtonText="Delete"
          confirmButtonVariant="destructive"
          isLoading={isDeleting}
        />
      )}
    </div>
  );
}
