'use client';

import PermissionForm, { PermissionFormData, PermissionFormRef } from './permissions/PermissionForm';
import PermissionTable, { Permission } from './permissions/PermissionTable';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Toaster, toast } from 'sonner';

import AdminConfirmationDialog from './ui/AdminConfirmationDialog';
import AdminModalBase from './ui/AdminModalBase';
import { Button } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';

export default function PermissionsComponent() {
  const permissionFormRef = useRef<PermissionFormRef>(null);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);

  const [isDeletingModalOpen, setIsDeletingModalOpen] = useState(false);
  const [permissionToDelete, setPermissionToDelete] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false); // For form submission and delete confirmation

  const fetchPermissions = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/admin/permissions');
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to fetch permissions' }));
        throw new Error(errorData.message || 'Failed to fetch permissions');
      }
      const data = await response.json();
      setPermissions(data);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(message);
      toast.error(`Error fetching permissions: ${message}`);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchPermissions();
  }, [fetchPermissions]);

  const handleCreatePermission = () => {
    setEditingPermission(null);
    setIsFormModalOpen(true);
  };

  const handleEditPermission = (permission: Permission) => {
    setEditingPermission(permission);
    setIsFormModalOpen(true);
  };

  const handleDeletePermission = (permissionId: string) => {
    setPermissionToDelete(permissionId);
    setIsDeletingModalOpen(true);
  };

  const confirmDeletePermission = async () => {
    if (!permissionToDelete) return;
    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/admin/permissions/${permissionToDelete}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to delete permission' }));
        throw new Error(errorData.message || 'Failed to delete permission');
      }
      toast.success('Permission deleted successfully!');
      setPermissions(prev => prev.filter(p => p.id !== permissionToDelete));
      setIsDeletingModalOpen(false);
      setPermissionToDelete(null);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'An unknown error occurred';
      toast.error(`Error deleting permission: ${message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFormSave = async (data: PermissionFormData) => { // Renamed from handleFormSubmit to match AdminModalBase prop
    setIsSubmitting(true);
    const url = editingPermission ? `/api/admin/permissions/${editingPermission.id}` : '/api/admin/permissions';
    const method = editingPermission ? 'PUT' : 'POST';

    try {
      const response = await fetch(url, {
        method: method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: `Failed to ${editingPermission ? 'update' : 'create'} permission` }));
        throw new Error(errorData.message || `Failed to ${editingPermission ? 'update' : 'create'} permission`);
      }

      const savedPermission = await response.json();
      toast.success(`Permission ${editingPermission ? 'updated' : 'created'} successfully!`);
      
      if (editingPermission) {
        setPermissions(prev => prev.map(p => p.id === savedPermission.id ? savedPermission : p));
      } else {
        setPermissions(prev => [savedPermission, ...prev]);
      }
      setIsFormModalOpen(false);
      setEditingPermission(null);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'An unknown error occurred';
      toast.error(`Error: ${message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <Toaster richColors position="top-right" />
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Permission Management</h1>
          <h2 className="text-sm sm:text-lg text-muted-foreground mt-1">Manage application permissions</h2>
        </div>
        <Button onClick={handleCreatePermission}>
          <PlusCircle className="mr-2 h-4 w-4" /> Create Permission
        </Button>
      </div>

      <PermissionTable
        permissions={permissions}
        onEdit={handleEditPermission}
        onDelete={handleDeletePermission}
        loading={loading}
        error={error}
      />

      {/* Create/Edit Permission Modal using AdminModalBase */}
      <AdminModalBase
        title={editingPermission ? 'Edit Permission' : 'Create New Permission'}
        isOpen={isFormModalOpen}
        onClose={() => {
          setIsFormModalOpen(false);
          setEditingPermission(null);
        }}
        onSave={() => {
          // Trigger the form submission via the ref
          if (permissionFormRef.current) {
            permissionFormRef.current.submit();
          }
        }}
      >
        <PermissionForm
          ref={permissionFormRef}
          initialData={editingPermission}
          onSubmit={handleFormSave} // This function is called by the form itself after validation
          isSubmitting={isSubmitting}
        />
      </AdminModalBase>

      {/* Delete Confirmation Modal */}
      {permissionToDelete && (
        <AdminConfirmationDialog
          isOpen={isDeletingModalOpen}
          onClose={() => {
            setIsDeletingModalOpen(false);
            setPermissionToDelete(null);
          }}
          onConfirm={confirmDeletePermission}
          title="Delete Permission"
          description={<>Are you sure you want to delete the permission <strong>{permissions.find(p => p.id === permissionToDelete)?.action} on {permissions.find(p => p.id === permissionToDelete)?.resource}</strong>? This action cannot be undone.</>}
          confirmButtonText="Delete"
          confirmButtonVariant="destructive"
          isLoading={isSubmitting}
        />
      )}
    </div>
  );
}
