'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import RoleForm, { RoleFormData, RoleFormRef } from './roles/RoleForm';
import RoleTable, { Role } from './roles/RoleTable';

import AdminConfirmationDialog from './ui/AdminConfirmationDialog';
import AdminModalBase from './ui/AdminModalBase';
import { Button } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';

export interface Permission {
  id: string;
  name: string;
  description?: string;
}

export default function RolesComponent() {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchValue, setSearchValue] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });

  const [showRoleModal, setShowRoleModal] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [isFormLoading, setIsFormLoading] = useState(false);
  const roleFormRef = useRef<RoleFormRef>(null);

  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletingRole, setDeletingRole] = useState<Role | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchRoles = useCallback(async (search?: string, page?: number) => {
    setIsLoading(true);
    setError(null);
    try {
      const searchParams = new URLSearchParams();
      if (search) searchParams.append('search', search);
      if (page) searchParams.append('page', page.toString());
      searchParams.append('limit', pagination.limit.toString());

      const response = await fetch(`/api/admin/roles?${searchParams.toString()}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch roles: ${response.statusText}`);
      }
      const responseData = await response.json();
      
      // Handle both old format (direct array) and new format (with pagination)
      if (Array.isArray(responseData)) {
        // Old format - direct array
        setRoles(responseData);
      } else {
        // New format - with pagination
        setRoles(responseData.data);
        setPagination(responseData.pagination);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      toast.error('Failed to load roles.');
    } finally {
      setIsLoading(false);
    }
  }, [pagination.limit]);

  const fetchPermissions = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/permissions');
      if (!response.ok) {
        throw new Error(`Failed to fetch permissions: ${response.statusText}`);
      }
      const data = await response.json();
      setPermissions(data);
    } catch (err) {
      toast.error('Failed to load permissions.');
      console.error("Failed to fetch permissions:", err);
    }
  }, []);

  useEffect(() => {
    fetchRoles();
    fetchPermissions();
  }, [fetchRoles, fetchPermissions]);

  const handleOpenCreateModal = () => {
    setEditingRole(null);
    setShowRoleModal(true);
  };

  const handleOpenEditModal = (role: Role) => {
    setEditingRole(role);
    setShowRoleModal(true);
  };

  const handleCloseRoleModal = () => {
    setShowRoleModal(false);
    setEditingRole(null);
  };

  const handleSaveRole = async (formData: RoleFormData) => {
    setIsFormLoading(true);
    const url = editingRole?.id ? `/api/admin/roles/${editingRole.id}` : '/api/admin/roles';
    const method = editingRole?.id ? 'PUT' : 'POST';

    try {
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
          permission_ids: formData.permission_ids
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to ${editingRole?.id ? 'update' : 'create'} role`);
      }

      toast.success(`Role ${editingRole?.id ? 'updated' : 'created'} successfully!`);
      fetchRoles(); // Refresh list
      handleCloseRoleModal();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : `An unknown error occurred while ${editingRole?.id ? 'updating' : 'creating'} role.`);
    } finally {
      setIsFormLoading(false);
    }
  };
  
  const triggerRoleFormSubmit = () => {
    roleFormRef.current?.submit();
  }

  const handleOpenDeleteDialog = (role: Role) => {
    setDeletingRole(role);
    setShowDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setShowDeleteDialog(false);
    setDeletingRole(null);
  };

  const handleConfirmDelete = async () => {
    if (!deletingRole) return;
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/admin/roles/${deletingRole.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete role');
      }

      toast.success('Role deleted successfully!');
      fetchRoles(); // Refresh list
      handleCloseDeleteDialog();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'An unknown error occurred while deleting role.');
    } finally {
      setIsDeleting(false);
    }
  };
  
  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);
    fetchRoles(value, 1); // Reset to page 1 when searching
  }, [fetchRoles]);

  const renderContent = () => {
    if (isLoading && !roles.length) { // Show skeleton only on initial load
      return (
        <div className="space-y-2">
          <Skeleton className="h-8 w-1/4" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      );
    }
    if (error) {
      return <p className="text-red-500">Error loading roles: {error}</p>;
    }
    return (
      <RoleTable
        roles={roles}
        onEdit={handleOpenEditModal}
        onDelete={handleOpenDeleteDialog}
        enableSearch={true}
        searchValue={searchValue}
        onSearchChange={handleSearchChange}
        searchPlaceholder="Search roles..."
      />
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Role Management</h1>
          <h2 className="text-sm sm:text-lg text-muted-foreground mt-1">Manage user roles and permissions</h2>
        </div>
        <Button onClick={handleOpenCreateModal}>
          <PlusCircle className="mr-2 h-4 w-4" /> Create Role
        </Button>
      </div>

      {renderContent()}

      {showRoleModal && (
        <AdminModalBase
          isOpen={showRoleModal}
          onClose={handleCloseRoleModal}
          title={editingRole ? 'Edit Role' : 'Create New Role'}
          onSave={triggerRoleFormSubmit}
          isLoading={isFormLoading}
          size="2xl" // Or any other appropriate size
        >
          <RoleForm
            ref={roleFormRef}
            role={editingRole}
            permissions={permissions}
            onSave={handleSaveRole}
            isLoading={isFormLoading}
          />
        </AdminModalBase>
      )}

      {/* Delete Confirmation Dialog */}
      {deletingRole && (
        <AdminConfirmationDialog
          isOpen={showDeleteDialog}
          onClose={handleCloseDeleteDialog}
          onConfirm={handleConfirmDelete}
          title="Delete Role"
          description={<>Are you sure you want to delete the role <strong>{deletingRole.name}</strong>? This action cannot be undone.</>}
          confirmButtonText="Delete"
          confirmButtonVariant="destructive"
          isLoading={isDeleting}
        />
      )}
    </div>
  );
}
