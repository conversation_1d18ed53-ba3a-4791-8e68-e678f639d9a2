'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Toaster, toast } from 'sonner';
import UserForm, { UserFormHandles } from './users/UserForm';

import AdminConfirmationDialog from './ui/AdminConfirmationDialog';
import AdminModalBase from './ui/AdminModalBase';
import { Button } from '@/components/ui/button';
import UserTable from './users/UserTable';

// Types for UserForm props
interface Group {
  id: string;
  name: string;
}

interface Role {
  id: string;
  name: string;
}

// Define a type for the user data
interface UserAssociation {
  id: string;
  name: string;
}
interface UserPermissionAssociation {
    id: string;
    action: string;
    resource: string;
    description?: string;
}

export interface User {
  id: string;
  name: string | null;
  email: string | null;
  password?: string;
  groups?: UserAssociation[];
  permissions?: UserPermissionAssociation[];
  roles?: UserAssociation[];
  groupIds?: string[];
  permissionIds?: string[]; // Retained for User type consistency, though UserForm might not use it directly for submission
  roleIds?: string[];
}

export default function UsersComponent() {
  const [users, setUsers] = useState<User[]>([]);
  const [tableLoading, setTableLoading] = useState(true); // Renamed to avoid clash
  const [error, setError] = useState<string | null>(null);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const userFormRef = useRef<UserFormHandles>(null);

  const [allGroups, setAllGroups] = useState<Group[]>([]);
  const [allRoles, setAllRoles] = useState<Role[]>([]);
  const [isFetchingAssignments, setIsFetchingAssignments] = useState(false);
  const [modalIsSaving, setModalIsSaving] = useState(false);

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingUserId, setDeletingUserId] = useState<string | null>(null);
  const [isDeletingUserProcess, setIsDeletingUserProcess] = useState(false);

  const fetchUsers = async () => {
    setTableLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/admin/users');
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: `Failed to fetch users: ${response.statusText}` }));
        throw new Error(errorData.message || `Failed to fetch users: ${response.statusText}`);
      }
      const data = await response.json();
      setUsers(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred while fetching users';
      setError(errorMessage);
      toast.error(errorMessage);
      setUsers([]);
    } finally {
      setTableLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchAssignments = async () => {
    setIsFetchingAssignments(true);
    try {
      const [groupsResponse, rolesResponse] = await Promise.all([
        fetch('/api/admin/groups'),
        fetch('/api/admin/roles'),
      ]);

      if (!groupsResponse.ok) {
        const errorData = await groupsResponse.json().catch(() => ({ message: 'Failed to fetch groups' }));
        throw new Error(errorData.message || `HTTP error ${groupsResponse.status} while fetching groups`);
      }
      if (!rolesResponse.ok) {
        const errorData = await rolesResponse.json().catch(() => ({ message: 'Failed to fetch roles' }));
        throw new Error(errorData.message || `HTTP error ${rolesResponse.status} while fetching roles`);
      }

      const groupsData = await groupsResponse.json();
      const rolesData = await rolesResponse.json();

      setAllGroups(groupsData.data || groupsData);
      setAllRoles(rolesData.data || rolesData);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred while fetching assignment data.';
      toast.error(errorMessage);
      setAllGroups([]);
      setAllRoles([]);
    } finally {
      setIsFetchingAssignments(false);
    }
  };

  const openModalWithAssignments = async (user: User | null) => {
    setEditingUser(user);
    // Fetch assignments only if they are not already loaded or if we want to refresh them each time
    // For simplicity, fetching them each time the modal opens for now.
    await fetchAssignments();
    setIsModalOpen(true);
  };

  const handleCreateUserClick = () => {
    openModalWithAssignments(null);
  };

  const handleEditUserClick = (user: User) => {
    openModalWithAssignments(user);
  };
  
  const closeModal = () => {
    setIsModalOpen(false);
    setEditingUser(null);
    // Optionally clear assignments if they should be fresh each time
    // setAllGroups([]);
    // setAllRoles([]);
  };

  const handleSaveUser = async () => {
    if (!userFormRef.current) {
      toast.error("User form is not available.");
      return;
    }
    setModalIsSaving(true);
    try {
      const success = await userFormRef.current.submit();
      if (success) {
        // handleSaveSuccess will be called from UserForm, which then calls fetchUsers and closeModal
      }
    } catch (error) {
      // This catch might not be necessary if UserForm's submit handles its own errors
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred during save operation.';
      toast.error(`Failed to save user: ${errorMessage}`);
    } finally {
      setModalIsSaving(false); // Ensure loading state is reset
    }
  };

  const handleSaveSuccess = () => {
    fetchUsers();
    closeModal();
  };

  const handleDeleteUserClick = (userId: string) => {
    setDeletingUserId(userId);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteDialogClose = () => {
    setIsDeleteDialogOpen(false);
    setDeletingUserId(null);
  };

  const confirmDeleteUser = async () => {
    if (!deletingUserId) return;
    setIsDeletingUserProcess(true);
    const userName = users.find(u => u.id === deletingUserId)?.name || 'User';
    try {
      const response = await fetch(`/api/admin/users/${deletingUserId}`, {
        method: 'DELETE',
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.message || `Failed to delete user ${userName}`);
      }
      toast.success(`User "${userName}" (ID: ${deletingUserId}) deleted successfully!`);
      fetchUsers();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      toast.error(`Failed to delete user ${userName}: ${errorMessage}`);
    } finally {
      handleDeleteDialogClose();
      setIsDeletingUserProcess(false);
    }
  };

  return (
    <div>
      <Toaster richColors position="top-right" />
      {/* Page Title */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">User Management</h1>
          <h2 className="text-sm sm:text-lg text-muted-foreground mt-1">Manage users and their details</h2>
        </div>
        <Button onClick={handleCreateUserClick}>Create User</Button>
      </div>

      {/* Users List Section */}
      <section>
        <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-4">Users List</h2>
        {tableLoading && <p>Loading users...</p>}
        {error && !tableLoading && <p className="text-red-500">Error: {error}</p>}
        {!tableLoading && !error && (
          <UserTable
            users={users}
            onEdit={handleEditUserClick}
            onDelete={handleDeleteUserClick}
          />
        )}
      </section>

      <AdminModalBase
        isOpen={isModalOpen}
        onClose={closeModal}
        title={editingUser ? 'Edit User' : 'Create New User'}
        onSave={handleSaveUser}
        isLoading={modalIsSaving || isFetchingAssignments} // Modal shows loading if saving or fetching assignments
        saveButtonText={editingUser ? 'Save Changes' : 'Create User'}
        size="xl" // Using xl for better tab layout
      >
        <UserForm
          ref={userFormRef}
          userToEdit={editingUser}
          onSaveSuccess={handleSaveSuccess}
          allGroups={allGroups}
          allRoles={allRoles}
          isFetchingAssignments={isFetchingAssignments}
        />
      </AdminModalBase>

      {isDeleteDialogOpen && deletingUserId && (
        <AdminConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onClose={handleDeleteDialogClose}
          onConfirm={confirmDeleteUser}
          title="Delete User"
          description={<>Are you sure you want to delete the user <strong>{users.find(u => u.id === deletingUserId)?.name || 'this user'}</strong>? This action cannot be undone.</>}
          confirmButtonText="Delete"
          confirmButtonVariant="destructive"
          isLoading={isDeletingUserProcess}
        />
      )}
    </div>
  );
}
