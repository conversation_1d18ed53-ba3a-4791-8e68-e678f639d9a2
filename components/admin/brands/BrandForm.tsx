import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { Brand } from './BrandTable';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

export interface BrandFormData {
  name: string;
  code: string;
  description: string;
  abbreviation: string;
  logo_image_url: string;
  status: string;
  stage: string;
  group: string;
}

interface BrandFormProps {
  brand?: Brand | null;
  onSave: (formData: BrandFormData) => void;
  isLoading?: boolean;
}

export interface BrandFormRef {
  submit: () => void;
}

const BrandForm = forwardRef<BrandFormRef, BrandFormProps>(({ brand, onSave, isLoading }, ref) => {
  const [name, setName] = useState('');
  const [code, setCode] = useState('');
  const [description, setDescription] = useState('');
  const [abbreviation, setAbbreviation] = useState('');
  const [logoImageUrl, setLogoImageUrl] = useState('');
  const [status, setStatus] = useState('');
  const [stage, setStage] = useState('');
  const [group, setGroup] = useState('');
  const [activeTab, setActiveTab] = useState("basic");

  useEffect(() => {
    if (brand) {
      setName(brand.name || '');
      setCode(brand.code || '');
      setDescription(brand.description || '');
      setAbbreviation(brand.abbreviation || '');
      setLogoImageUrl(brand.logo_image_url || '');
      setStatus(brand.status || '');
      setStage(brand.stage || '');
      setGroup(brand.group || '');
    } else {
      setName('');
      setCode('');
      setDescription('');
      setAbbreviation('');
      setLogoImageUrl('');
      setStatus('');
      setStage('');
      setGroup('');
    }
  }, [brand]);

  useImperativeHandle(ref, () => ({
    submit: () => {
      // Basic validation
      if (!name.trim()) {
        alert("Brand Name is required.");
        setActiveTab("basic");
        return;
      }
      if (!code.trim()) {
        alert("Brand Code is required.");
        setActiveTab("basic");
        return;
      }
      
      onSave({
        name: name.trim(),
        code: code.trim(),
        description: description.trim(),
        abbreviation: abbreviation.trim(),
        logo_image_url: logoImageUrl.trim(),
        status: status.trim(),
        stage: stage.trim(),
        group: group.trim(),
      });
    }
  }));

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="basic">Basic Information</TabsTrigger>
        <TabsTrigger value="details">Additional Details</TabsTrigger>
      </TabsList>
      
      <TabsContent value="basic">
        <div className="space-y-4 py-4">
          <div>
            <Label htmlFor="brandName">Brand Name *</Label>
            <Input
              id="brandName"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              className="mt-1"
              disabled={isLoading}
              placeholder="Enter brand name"
            />
          </div>
          
          <div>
            <Label htmlFor="brandCode">Brand Code *</Label>
            <Input
              id="brandCode"
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              required
              className="mt-1"
              disabled={isLoading}
              placeholder="Enter brand code"
            />
          </div>
          
          <div>
            <Label htmlFor="brandAbbreviation">Abbreviation</Label>
            <Input
              id="brandAbbreviation"
              type="text"
              value={abbreviation}
              onChange={(e) => setAbbreviation(e.target.value)}
              className="mt-1"
              disabled={isLoading}
              placeholder="Enter brand abbreviation"
            />
          </div>
          
          <div>
            <Label htmlFor="brandDescription">Description</Label>
            <Textarea
              id="brandDescription"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="mt-1"
              disabled={isLoading}
              placeholder="Enter brand description"
              rows={3}
            />
          </div>
        </div>
      </TabsContent>
      
      <TabsContent value="details">
        <div className="space-y-4 py-4">
          <div>
            <Label htmlFor="brandStatus">Status</Label>
            <Select value={status} onValueChange={setStatus} disabled={isLoading}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Owned">Owned</SelectItem>
                <SelectItem value="Co-op">Co-op</SelectItem>
                <SelectItem value="Departed">Departed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="brandStage">Stage</Label>
            <Select value={stage} onValueChange={setStage} disabled={isLoading}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select stage" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Selling Actively">Selling Actively</SelectItem>
                <SelectItem value="Discontinued">Discontinued</SelectItem>
                <SelectItem value="Offloading">Offloading</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="brandGroup">Group</Label>
            <Input
              id="brandGroup"
              type="text"
              value={group}
              onChange={(e) => setGroup(e.target.value)}
              className="mt-1"
              disabled={isLoading}
              placeholder="Enter brand group"
            />
          </div>
          
          <div>
            <Label htmlFor="brandLogoUrl">Logo Image URL</Label>
            <Input
              id="brandLogoUrl"
              type="url"
              value={logoImageUrl}
              onChange={(e) => setLogoImageUrl(e.target.value)}
              className="mt-1"
              disabled={isLoading}
              placeholder="https://example.com/logo.png"
            />
          </div>
        </div>
      </TabsContent>
    </Tabs>
  );
});

BrandForm.displayName = 'BrandForm';
export default BrandForm;
