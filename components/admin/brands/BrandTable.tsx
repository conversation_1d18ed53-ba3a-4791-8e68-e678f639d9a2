import { Edit, Trash2 } from 'lucide-react';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/data-table";
import React from 'react';
import { Skeleton } from "@/components/ui/skeleton";

export interface Brand {
  id: string;
  brand_id: string;
  code: string | null;
  name: string | null;
  description: string | null;
  abbreviation: string | null;
  logo_image_url: string | null;
  status: string | null;
  stage: string | null;
  group: string | null;
}

interface BrandTableProps {
  brands: Brand[];
  onEdit: (brand: Brand) => void;
  onDelete: (brand: Brand) => void;
  loading?: boolean;
  error?: string | null;
  enableSearch?: boolean;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  searchPlaceholder?: string;
}

const BrandTable: React.FC<BrandTableProps> = ({ 
  brands, 
  onEdit, 
  onDelete, 
  loading, 
  error,
  enableSearch,
  searchValue,
  onSearchChange,
  searchPlaceholder
}) => {

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    );
  }

  if (error) {
    return <p className="text-red-500">Error loading brands: {error}</p>;
  }

  if (!loading && !error && (!brands || brands.length === 0)) {
    return (
      <div className="rounded-md border p-4 text-center">
        No brands found.
      </div>
    );
  }

  const columns: ColumnDef<Brand>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => <span className="font-medium">{row.original.name || 'N/A'}</span>,
    },
    {
      accessorKey: "code",
      header: "Code",
      cell: ({ row }) => row.original.code || 'N/A',
    },
    {
      accessorKey: "abbreviation",
      header: "Abbreviation",
      cell: ({ row }) => row.original.abbreviation || 'N/A',
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          row.original.status === 'active' 
            ? 'bg-green-100 text-green-800' 
            : 'bg-gray-100 text-gray-800'
        }`}>
          {row.original.status || 'N/A'}
        </span>
      ),
    },
    {
      accessorKey: "stage",
      header: "Stage",
      cell: ({ row }) => row.original.stage || 'N/A',
    },
    {
      accessorKey: "group",
      header: "Group",
      cell: ({ row }) => row.original.group || 'N/A',
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => {
        const description = row.original.description;
        return (
          <div className="max-w-xs truncate" title={description || undefined}>
            {description || 'N/A'}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => (
        <div className="text-right space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(row.original)}
          >
            <Edit className="mr-2 h-4 w-4" /> Edit
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => onDelete(row.original)}
            className="text-white hover:text-white"
          >
            <Trash2 className="mr-2 h-4 w-4 text-white" /> Delete
          </Button>
        </div>
      ),
      enableSorting: false,
    },
  ];

  return (
    <DataTable<Brand, unknown> 
      columns={columns} 
      data={brands}
      enableSearch={enableSearch}
      searchValue={searchValue}
      onSearchChange={onSearchChange}
      searchPlaceholder={searchPlaceholder}
    />
  );
};

export default BrandTable;
