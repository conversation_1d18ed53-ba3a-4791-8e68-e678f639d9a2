'use client';

import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';

interface Group {
  id: string;
  name: string;
  description: string | null;
  user_ids?: string[];
  brand_ids?: string[];
}

interface User {
  id: string;
  name: string;
  email: string;
}

interface Brand {
  id: string;
  name: string;
  slug: string;
}

interface GroupFormProps {
  group?: Group | null;
  users: User[];
  brands: Brand[];
  onSave: (groupData: Omit<Group, 'id'> & { id?: string; user_ids: string[]; brand_ids: string[] }) => Promise<void>;
  onCancel: () => void;
}

export interface GroupFormRef {
  submit: () => Promise<void>;
}

const GroupForm = forwardRef<GroupFormRef, GroupFormProps>(
  ({ group, users, brands, onSave }, ref) => {
    const [formData, setFormData] = useState({
      name: '',
      description: '',
      user_ids: [] as string[],
      brand_ids: [] as string[],
    });
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
      if (group) {
        setFormData({
          name: group.name || '',
          description: group.description || '',
          user_ids: group.user_ids || [],
          brand_ids: group.brand_ids || [],
        });
      } else {
        setFormData({
          name: '',
          description: '',
          user_ids: [],
          brand_ids: [],
        });
      }
    }, [group]);

    const handleSubmit = async () => {
      if (isSubmitting) return;

      if (!formData.name.trim()) {
        toast.error('Group name is required');
        return;
      }

      setIsSubmitting(true);
      try {
        const submitData = {
          ...formData,
          ...(group?.id && { id: group.id }),
        };
        await onSave(submitData);
      } catch (error) {
        console.error('Error submitting form:', error);
      } finally {
        setIsSubmitting(false);
      }
    };

    useImperativeHandle(ref, () => ({
      submit: handleSubmit,
    }));

    const handleUserToggle = (userId: string, checked: boolean) => {
      setFormData(prev => ({
        ...prev,
        user_ids: checked
          ? [...prev.user_ids, userId]
          : prev.user_ids.filter(id => id !== userId),
      }));
    };

    const handleBrandToggle = (brandId: string, checked: boolean) => {
      setFormData(prev => ({
        ...prev,
        brand_ids: checked
          ? [...prev.brand_ids, brandId]
          : prev.brand_ids.filter(id => id !== brandId),
      }));
    };

    return (
      <div className="space-y-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">Group Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter group name"
              disabled={isSubmitting}
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Enter group description"
              disabled={isSubmitting}
              rows={3}
            />
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <Label className="text-base font-medium">Users</Label>
            <div className="mt-2 space-y-2 max-h-40 overflow-y-auto border rounded-md p-3">
              {users.length === 0 ? (
                <p className="text-sm text-muted-foreground">No users available</p>
              ) : (
                users.map((user) => (
                  <div key={user.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`user-${user.id}`}
                      checked={formData.user_ids.includes(user.id)}
                      onCheckedChange={(checked) => handleUserToggle(user.id, checked as boolean)}
                      disabled={isSubmitting}
                    />
                    <Label
                      htmlFor={`user-${user.id}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {user.name} ({user.email})
                    </Label>
                  </div>
                ))
              )}
            </div>
          </div>

          <div>
            <Label className="text-base font-medium">Brands</Label>
            <div className="mt-2 space-y-2 max-h-40 overflow-y-auto border rounded-md p-3">
              {brands.length === 0 ? (
                <p className="text-sm text-muted-foreground">No brands available</p>
              ) : (
                brands.map((brand) => (
                  <div key={brand.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`brand-${brand.id}`}
                      checked={formData.brand_ids.includes(brand.id)}
                      onCheckedChange={(checked) => handleBrandToggle(brand.id, checked as boolean)}
                      disabled={isSubmitting}
                    />
                    <Label
                      htmlFor={`brand-${brand.id}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {brand.name}
                    </Label>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }
);

GroupForm.displayName = 'GroupForm';

export default GroupForm;
