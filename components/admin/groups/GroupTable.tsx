import { Edit, Trash2 } from 'lucide-react';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/data-table";
import React from 'react';
import { Skeleton } from "@/components/ui/skeleton";

export interface Group {
  id: string;
  name: string;
  description: string | null;
}

interface GroupTableProps {
  groups: Group[];
  onEdit: (group: Group) => void;
  onDelete: (group: Group) => void;
  loading?: boolean;
  error?: string | null;
}

const GroupTable: React.FC<GroupTableProps> = ({ groups, onEdit, onDelete, loading, error }) => {

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    );
  }

  if (error) {
    return <p className="text-red-500">Error loading groups: {error}</p>;
  }

  if (!loading && !error && (!groups || groups.length === 0)) {
    return (
      <div className="rounded-md border p-4 text-center">
        No groups found.
      </div>
    );
  }

  const columns: ColumnDef<Group>[] = [
    {
      accessorKey: "id",
      header: "ID",
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => <span className="font-medium">{row.original.name}</span>,
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => {
        const description = row.original.description;
        return (
          <div className="max-w-xs truncate" title={description || undefined}>
            {description || 'N/A'}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => (
        <div className="text-right space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(row.original)}
          >
            <Edit className="mr-2 h-4 w-4" /> Edit
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => onDelete(row.original)}
            className="text-white hover:text-white"
          >
            <Trash2 className="mr-2 h-4 w-4 text-white" /> Delete
          </Button>
        </div>
      ),
      enableSorting: false,
    },
  ];

  return <DataTable<Group, unknown> columns={columns} data={groups} />;
};

export default GroupTable;
