import React, { forwardRef, useEffect, useImperative<PERSON><PERSON><PERSON>, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"; // Added Tabs

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Permission } from './PermissionTable'; // Re-use the interface
import { Textarea } from '../../../components/ui/textarea';
import { X } from 'lucide-react';
import { toast } from 'sonner';

// Omit 'id' for creation, make 'description' and 'allowed_sales_channels' explicitly possibly undefined for the form
export interface PermissionFormData {
  action: string;
  resource: string;
  description?: string | null;
  allowed_sales_channels?: string[];
}

interface PermissionFormProps {
  initialData?: Permission | null;
  onSubmit: (data: PermissionFormData) => Promise<void>; // This will be called by the parent via ref
  isSubmitting?: boolean;
  // onCancel is removed as AdminModalBase handles closing
}

export interface PermissionFormRef {
  submit: () => void;
}

const PermissionForm = forwardRef<PermissionFormRef, PermissionFormProps>(({ initialData, onSubmit, isSubmitting }, ref) => {
  const formRef = useRef<HTMLFormElement>(null);
  const [formData, setFormData] = useState<PermissionFormData>({
    action: '',
    resource: '',
    description: '',
    allowed_sales_channels: [],
  });
  const [currentChannelInput, setCurrentChannelInput] = useState('');

  useEffect(() => {
    if (initialData) {
      setFormData({
        action: initialData.action,
        resource: initialData.resource,
        description: initialData.description || '',
        allowed_sales_channels: initialData.allowed_sales_channels || [],
      });
    } else {
      // Reset form for creation
      setFormData({
        action: '',
        resource: '',
        description: '',
        allowed_sales_channels: [],
      });
    }
  }, [initialData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleChannelInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentChannelInput(e.target.value);
  };

  const handleAddChannel = () => {
    if (currentChannelInput.trim() && !formData.allowed_sales_channels?.includes(currentChannelInput.trim())) {
      setFormData(prev => ({
        ...prev,
        allowed_sales_channels: [...(prev.allowed_sales_channels || []), currentChannelInput.trim()],
      }));
      setCurrentChannelInput('');
    } else if (formData.allowed_sales_channels?.includes(currentChannelInput.trim())) {
      toast.error('Sales channel already added.');
    }
  };

  const handleRemoveChannel = (channelToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      allowed_sales_channels: prev.allowed_sales_channels?.filter(channel => channel !== channelToRemove),
    }));
  };

  const triggerSubmit = async () => {
    if (!formData.action || !formData.resource) {
      toast.error('Action and Resource are required fields.');
      return;
    }
    const dataToSubmit: PermissionFormData = {
      ...formData,
      description: formData.description?.trim() || null,
      allowed_sales_channels: formData.allowed_sales_channels && formData.allowed_sales_channels.length > 0 ? formData.allowed_sales_channels : undefined,
    };
    await onSubmit(dataToSubmit);
  };

  useImperativeHandle(ref, () => ({
    submit: triggerSubmit,
  }));

  // Internal handleSubmit for the form element, though it might not be explicitly used if triggered by ref
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    triggerSubmit();
  };

  return (
    <form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
      <Tabs defaultValue="definition" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="definition">Definition</TabsTrigger>
          <TabsTrigger value="scope">Scope</TabsTrigger>
        </TabsList>
        <TabsContent value="definition" className="pt-6">
          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <Label htmlFor="action">Action</Label>
                <Input
                  id="action"
                  name="action"
                  value={formData.action}
                  onChange={handleChange}
                  placeholder="e.g., read, create, update, delete"
                  required
                  disabled={isSubmitting}
                />
              </div>
              <div>
                <Label htmlFor="resource">Resource (Subject)</Label>
                <Input
                  id="resource"
                  name="resource"
                  value={formData.resource}
                  onChange={handleChange}
                  placeholder="e.g., products, orders, users"
                  required
                  disabled={isSubmitting}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description || ''}
                onChange={handleChange}
                placeholder="Optional: A brief description of what this permission allows"
                disabled={isSubmitting}
              />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="scope" className="pt-6">
          <div className="space-y-6">
            <div>
              <Label htmlFor="allowed_sales_channels">Allowed Sales Channels (Optional)</Label>
              <div className="flex items-center gap-2 mb-2">
                <Input
                  id="allowed_sales_channels_input"
                  name="allowed_sales_channels_input"
                  value={currentChannelInput}
                  onChange={handleChannelInputChange}
                  placeholder="Enter sales channel and press Add"
                  disabled={isSubmitting}
                  onKeyDown={(e) => { if (e.key === 'Enter') { e.preventDefault(); handleAddChannel();}}}
                />
                <Button type="button" onClick={handleAddChannel} disabled={isSubmitting || !currentChannelInput.trim()}>
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.allowed_sales_channels?.map(channel => (
                  <Badge key={channel} variant="secondary" className="flex items-center gap-1">
                    {channel}
                    <button
                      type="button"
                      onClick={() => handleRemoveChannel(channel)}
                      className="ml-1 rounded-full hover:bg-destructive/20 p-0.5"
                      disabled={isSubmitting}
                    >
                      <X className="h-3 w-3" />
                      <span className="sr-only">Remove {channel}</span>
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
      {/* Buttons are removed as AdminModalBase will provide them */}
    </form>
  );
});

PermissionForm.displayName = 'PermissionForm'; // for better debugging

export default PermissionForm;
