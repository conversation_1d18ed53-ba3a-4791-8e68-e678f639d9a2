import { Pen<PERSON><PERSON>, Trash2 } from 'lucide-react';

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/data-table";
import React from 'react';
import { Skeleton } from "@/components/ui/skeleton";

export interface Permission {
  id: string;
  action: string;
  resource: string;
  description: string | null;
  allowed_sales_channels: string[] | null;
}

interface PermissionTableProps {
  permissions: Permission[];
  onEdit: (permission: Permission) => void;
  onDelete: (permissionId: string) => void;
  loading?: boolean; // Changed from isLoading to loading for consistency
  error?: string | null; // Added error prop
}

const PermissionTable: React.FC<PermissionTableProps> = ({ permissions, onEdit, onDelete, loading, error }) => {

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    );
  }

  if (error) {
    return <p className="text-red-500">Error loading permissions: {error}</p>;
  }

  if (!loading && !error && (!permissions || permissions.length === 0)) {
    return <p>No permissions found.</p>;
  }

  const columns: ColumnDef<Permission>[] = [
    {
      accessorKey: "id",
      header: "ID",
    },
    {
      accessorKey: "action",
      header: "Name",
    },
    {
      accessorKey: "resource",
      header: "Resource",
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => row.original.description || '-',
    },
    {
      accessorKey: "allowed_sales_channels",
      header: "Allowed Sales Channels",
      cell: ({ row }) =>
        row.original.allowed_sales_channels && row.original.allowed_sales_channels.length > 0
          ? row.original.allowed_sales_channels.map(channel => (
              <Badge key={channel} variant="outline" className="mr-1 mb-1">{channel}</Badge>
            ))
          : '-',
    },
    {
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => (
        <div className="text-right space-x-2">
          <Button
            variant="ghost"
            size="icon" // Keep as icon if preferred
            onClick={() => onEdit(row.original)}
            className="mr-2"
          >
            <Pencil className="h-4 w-4" />
            <span className="sr-only">Edit</span>
          </Button>
          <Button
            variant="ghost"
            size="icon" // Keep as icon
            onClick={() => onDelete(row.original.id)}
          >
            <Trash2 className="h-4 w-4 text-red-500" />
            <span className="sr-only">Delete</span>
          </Button>
        </div>
      ),
      enableSorting: false,
    },
  ];

  return <DataTable<Permission, unknown> columns={columns} data={permissions} />;
};

export default PermissionTable;
