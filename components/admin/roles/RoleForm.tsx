import React, { forwardRef, useEffect, useImperative<PERSON><PERSON><PERSON>, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Permission } from '@/components/admin/RolesComponent';
import { Role } from './RoleTable';

export interface RoleFormData {
  name: string;
  description: string;
  permission_ids: string[];
}

interface RoleFormProps {
  role?: Role | null;
  permissions: Permission[];
  onSave: (formData: Omit<Role, 'id' | 'permission_ids'> & { id?: string; permission_ids: string[] }) => void;
  isLoading?: boolean;
}

export interface RoleFormRef {
  submit: () => void;
}

const RoleForm = forwardRef<RoleFormRef, RoleFormProps>(({ role, permissions, onSave, isLoading }, ref) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("basic");

  useEffect(() => {
    if (role) {
      setName(role.name);
      setDescription(role.description);
      setSelectedPermissions(role.permission_ids || []);
    } else {
      setName('');
      setDescription('');
      setSelectedPermissions([]);
    }
  }, [role]);

  useImperativeHandle(ref, () => ({
    submit: () => {
      // Basic validation, can be expanded
      if (!name.trim()) {
        // Potentially set an error state or use a toast notification
        alert("Role Name is required."); // Replace with better UX
        setActiveTab("basic"); // Switch to tab with error
        return;
      }
      onSave({
        id: role?.id,
        name,
        description,
        permission_ids: selectedPermissions,
      });
    }
  }));

  const handlePermissionChange = (permissionId: string) => {
    setSelectedPermissions((prev) =>
      prev.includes(permissionId)
        ? prev.filter((id) => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="basic">Basic Information</TabsTrigger>
        <TabsTrigger value="permissions">Permission Assignments</TabsTrigger>
      </TabsList>
      <TabsContent value="basic">
        <div className="space-y-4 py-4">
          <div>
            <Label htmlFor="roleName">Role Name</Label>
            <Input
              id="roleName"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              className="mt-1"
              disabled={isLoading}
            />
          </div>
          <div>
            <Label htmlFor="roleDescription">Description</Label>
            <Input
              id="roleDescription"
              type="text"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="mt-1"
              disabled={isLoading}
            />
          </div>
        </div>
      </TabsContent>
      <TabsContent value="permissions">
        <div className="space-y-4 py-4">
          <div>
            <Label>Permissions</Label>
            <div className="mt-2 space-y-2 p-4 border rounded-md max-h-60 overflow-y-auto">
              {permissions.length === 0 && <p className="text-sm text-muted-foreground">No permissions available.</p>}
              {permissions.map((permission) => (
                <div key={permission.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`perm-${permission.id}`}
                    checked={selectedPermissions.includes(permission.id)}
                    onCheckedChange={() => handlePermissionChange(permission.id)}
                    disabled={isLoading}
                  />
                  <Label htmlFor={`perm-${permission.id}`} className="font-normal">
                    {permission.name}
                  </Label>
                </div>
              ))}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Select the permissions this role should have.
            </p>
          </div>
        </div>
      </TabsContent>
    </Tabs>
  );
});

RoleForm.displayName = 'RoleForm';
export default RoleForm;
