import { Edit, Trash2 } from 'lucide-react';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/data-table";
import React from 'react';
import { Skeleton } from "@/components/ui/skeleton";

export interface Role {
  id: string;
  name: string;
  description: string;
  permission_ids: string[];
}

interface RoleTableProps {
  roles: Role[];
  onEdit: (role: Role) => void;
  onDelete: (role: Role) => void;
  loading?: boolean;
  error?: string | null;
  enableSearch?: boolean;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  searchPlaceholder?: string;
}

const RoleTable: React.FC<RoleTableProps> = ({ 
  roles, 
  onEdit, 
  onDelete, 
  loading, 
  error,
  enableSearch,
  searchValue,
  onSearchChange,
  searchPlaceholder
}) => {

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    );
  }

  if (error) {
    return <p className="text-red-500">Error loading roles: {error}</p>;
  }

  if (!loading && !error && (!roles || roles.length === 0)) {
    return (
      <div className="rounded-md border p-4 text-center">
        No roles found.
      </div>
    );
  }

  const columns: ColumnDef<Role>[] = [
    {
      accessorKey: "id",
      header: "ID",
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => <span className="font-medium">{row.original.name}</span>,
    },
    {
      accessorKey: "description",
      header: "Description",
    },
    // Note: Displaying permission_ids directly might not be user-friendly.
    // Consider mapping them to names or showing a count if needed in a real app.
    // For this refactor, we'll keep it simple or omit if not essential for the table view.
    // {
    //   accessorKey: "permission_ids",
    //   header: "Permission IDs",
    //   cell: ({ row }) => row.original.permission_ids.join(', ') || '-',
    // },
    {
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => (
        <div className="text-right space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(row.original)}
          >
            <Edit className="mr-2 h-4 w-4" /> Edit
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => onDelete(row.original)}
            className="text-white hover:text-white"
          >
            <Trash2 className="mr-2 h-4 w-4 text-white" /> Delete
          </Button>
        </div>
      ),
      enableSorting: false,
    },
  ];

  return (
    <DataTable<Role, unknown> 
      columns={columns} 
      data={roles}
      enableSearch={enableSearch}
      searchValue={searchValue}
      onSearchChange={onSearchChange}
      searchPlaceholder={searchPlaceholder}
    />
  );
};

export default RoleTable;
