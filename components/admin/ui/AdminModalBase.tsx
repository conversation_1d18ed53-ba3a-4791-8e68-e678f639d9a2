import * as React from 'react';

import {
  Dialog,
  DialogClose,
  DialogContent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { Button } from '@/components/ui/button'; // Assuming path
import { cn } from '@/lib/utils'; // Assuming path

// Assuming path based on components.json and existing ui components

interface AdminModalBaseProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  onSave?: () => Promise<void> | void;
  saveButtonText?: string;
  cancelButtonText?: string;
  isLoading?: boolean;
  footer?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | 'full'; // Extended DialogContent sizes
}

const AdminModalBase: React.FC<AdminModalBaseProps> = ({
  isOpen,
  onClose,
  title,
  children,
  onSave,
  saveB<PERSON>onText = 'Save',
  cancelButtonText = 'Cancel',
  isLoading = false,
  footer,
  size = 'lg',
}) => {
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  const dialogContentSizeClasses: Record<string, string> = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-3xl',
    xl: 'max-w-5xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    full: 'max-w-full h-full',
  };
  
  // Shadcn DialogContent already supports sm, md, lg, xl, 2xl, 3xl, 4xl, 5xl by default through class names like sm:max-w-xl
  // The 'full' size might need custom styling if not directly supported or if specific height/width behavior is needed.
  // For now, relying on shadcn's convention. If 'full' needs more, it can be adjusted.
  // The DialogContent component in shadcn/ui typically uses `sm:max-w-[size]` classes.
  // Let's adjust to use the standard shadcn size prop if available or map to classes.
  // The DialogContent component from `src/components/ui/dialog.tsx` might have its own way of handling sizes.
  // For now, I'll pass a className based on the size.

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className={cn("bg-background p-8 shadow-lg rounded-lg", dialogContentSizeClasses[size], "data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:zoom-in-50", "data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-50")} onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader className="pb-6">
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className="py-6 max-h-[calc(100vh-12rem)] overflow-y-auto">
          {children}
        </div>
        {footer ? (
          <DialogFooter className="pt-6">{footer}</DialogFooter>
        ) : onSave ? (
          <DialogFooter className="pt-6 sm:justify-start">
            <Button type="button" onClick={onSave} disabled={isLoading}>
              {isLoading ? 'Saving...' : saveButtonText}
            </Button>
            <DialogClose asChild>
              <Button type="button" variant="outline">
                {cancelButtonText}
              </Button>
            </DialogClose>
          </DialogFooter>
        ) : (
          // If no footer and no onSave, render a default close button or nothing
          // For now, rendering a close button if no onSave and no custom footer
          <DialogFooter className="pt-6 sm:justify-start">
            <DialogClose asChild>
              <Button type="button" variant="outline">
                {cancelButtonText}
              </Button>
            </DialogClose>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AdminModalBase;