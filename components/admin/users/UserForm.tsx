'use client';

import React, { forwardRef, useEffect, useImperative<PERSON>andle, useState } from 'react';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';

// Define the User type directly here to avoid import issues
interface BaseUser {
  id: string;
  name: string | null;
  email: string | null;
  password?: string;
  groups?: { id: string }[];
  permissions?: { id: string; action?: string; resource?: string }[]; // Retain for User type, but not used in form submission
  roles?: { id: string }[];
  groupIds?: string[];
  // permissionIds?: string[]; // Removed as per requirement for form submission
  roleIds?: string[];
}

// Define types for Groups, Permissions, and Roles
interface Group {
  id: string;
  name: string;
}

interface Permission {
  id: string;
  action: string;
  resource: string;
  description?: string;
}

interface Role {
  id: string;
  name: string;
  permissions?: Permission[]; // Roles might have associated permissions
}

// Extend BaseUser type to include associations
interface User extends BaseUser {
  groups?: Group[];
  permissions?: Permission[]; // Retain for User type, but not used in form submission
  roles?: Role[];
  groupIds?: string[];
  // permissionIds?: string[]; // Removed as per requirement for form submission
  roleIds?: string[];
}

interface UserFormProps {
  userToEdit: User | null;
  onSaveSuccess: () => void;
  allGroups: Group[];
  allRoles: Role[];
  isFetchingAssignments: boolean;
}

export interface UserFormHandles {
  submit: () => Promise<boolean>; // Returns true on success
}

const UserForm = forwardRef<UserFormHandles, UserFormProps>(
  ({ userToEdit, onSaveSuccess, allGroups, allRoles, isFetchingAssignments }, ref) => {
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [internalIsLoading, setInternalIsLoading] = useState(false);

    const [selectedGroupIds, setSelectedGroupIds] = useState<Set<string>>(new Set());
    const [selectedRoleIds, setSelectedRoleIds] = useState<Set<string>>(new Set());

    const isEditing = !!userToEdit;

    useEffect(() => {
      if (userToEdit) {
        setName(userToEdit.name || '');
        setEmail(userToEdit.email || '');
        setPassword(''); // Password should be explicitly set if changing

        let extractedGroupIds: string[] = [];
        if (userToEdit.groupIds && userToEdit.groupIds.length > 0) {
          extractedGroupIds = userToEdit.groupIds.filter(id => id != null).map(id => String(id));
        } else if (userToEdit.groups && userToEdit.groups.length > 0) {
          if (typeof userToEdit.groups[0] === 'string') {
            extractedGroupIds = userToEdit.groups.filter(g => typeof g === 'string').map(id => String(id));
          } else if (typeof userToEdit.groups[0] === 'object' && userToEdit.groups[0] !== null) {
            extractedGroupIds = userToEdit.groups
              .map((g: Group | string) => (typeof g === 'object' && g !== null && g.id != null ? String(g.id) : undefined))
              .filter(id => typeof id === 'string') as string[];
          }
        }

        let extractedRoleIds: string[] = [];
        if (userToEdit.roleIds && userToEdit.roleIds.length > 0) {
          extractedRoleIds = userToEdit.roleIds.filter(id => id != null).map(id => String(id));
        } else if (userToEdit.roles && userToEdit.roles.length > 0) {
          if (typeof userToEdit.roles[0] === 'string') {
            extractedRoleIds = userToEdit.roles.filter(r => typeof r === 'string').map(id => String(id));
          } else if (typeof userToEdit.roles[0] === 'object' && userToEdit.roles[0] !== null) {
            extractedRoleIds = userToEdit.roles
              .map((r: Role | string) => (typeof r === 'object' && r !== null && r.id != null ? String(r.id) : undefined))
              .filter(id => typeof id === 'string') as string[];
          }
        }
        
        setSelectedGroupIds(new Set(extractedGroupIds));
        setSelectedRoleIds(new Set(extractedRoleIds));
      } else {
        // Reset form for creating
        setName('');
        setEmail('');
        setPassword('');
        setSelectedGroupIds(new Set());
        setSelectedRoleIds(new Set());
      }
    }, [userToEdit]); // Removed allGroups, allRoles from dependency array as they are stable props from parent

    const handleInternalSave = async (): Promise<boolean> => {
      setInternalIsLoading(true);

      const userData: Partial<User> & { groupIds?: string[]; roleIds?: string[] } = {
        name,
        email,
        groupIds: Array.from(selectedGroupIds),
        roleIds: Array.from(selectedRoleIds),
      };

      if (password || !isEditing) {
        userData.password = password;
      }

      if (!isEditing && !password) {
        toast.error("Password is required for new users.");
        setInternalIsLoading(false);
        return false;
      }

      try {
        const url = isEditing ? `/api/admin/users/${userToEdit?.id}` : '/api/admin/users';
        const method = isEditing ? 'PUT' : 'POST';

        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(userData),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.message || `Failed to ${isEditing ? 'update' : 'create'} user`);
        }

        toast.success(`User ${isEditing ? 'updated' : 'created'} successfully!`);
        onSaveSuccess();
        setInternalIsLoading(false);
        return true;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        toast.error(errorMessage);
        setInternalIsLoading(false);
        return false;
      }
    };

    useImperativeHandle(ref, () => ({
      submit: handleInternalSave,
    }));

  return (
    <div className="space-y-6">
      {/* Basic Information Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Basic Information</h3>
        <div className="grid grid-cols-1 gap-4">
          <div>
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setName(e.target.value)}
              placeholder="Full Name"
              required
              disabled={internalIsLoading}
            />
          </div>
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
              disabled={internalIsLoading}
            />
          </div>
          <div>
            <Label htmlFor="password">{isEditing ? 'New Password (optional)' : 'Password'}</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPassword(e.target.value)}
              placeholder={isEditing ? 'Leave blank to keep current' : '********'}
              required={!isEditing}
              disabled={internalIsLoading}
            />
          </div>
        </div>
      </div>

      {/* Role Selection Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Role Assignment</h3>
        {isFetchingAssignments ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-32 w-full" />
          </div>
        ) : (
          <div className="space-y-3">
            <Label>Select User Role</Label>
            <div className="grid grid-cols-1 gap-2 max-h-48 overflow-y-auto border rounded-md p-3">
              {allRoles.map((role) => (
                <div key={role.id} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id={`role-${role.id}`}
                    name="userRole"
                    value={role.id}
                    checked={selectedRoleIds.has(role.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedRoleIds(new Set([role.id]));
                      }
                    }}
                    disabled={internalIsLoading}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <Label 
                    htmlFor={`role-${role.id}`}
                    className="text-sm font-normal cursor-pointer flex-1"
                  >
                    {role.name}
                  </Label>
                </div>
              ))}
              {allRoles.length === 0 && (
                <p className="text-sm text-muted-foreground">No roles available</p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Group Assignment Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Group Assignment</h3>
        {isFetchingAssignments ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-32 w-full" />
          </div>
        ) : (
          <div className="space-y-3">
            <Label>Select Groups (optional)</Label>
            <div className="grid grid-cols-1 gap-2 max-h-48 overflow-y-auto border rounded-md p-3">
              {allGroups.map((group) => (
                <div key={group.id} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={`group-${group.id}`}
                    checked={selectedGroupIds.has(group.id)}
                    onChange={(e) => {
                      const newSelectedIds = new Set(selectedGroupIds);
                      if (e.target.checked) {
                        newSelectedIds.add(group.id);
                      } else {
                        newSelectedIds.delete(group.id);
                      }
                      setSelectedGroupIds(newSelectedIds);
                    }}
                    disabled={internalIsLoading}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <Label 
                    htmlFor={`group-${group.id}`}
                    className="text-sm font-normal cursor-pointer flex-1"
                  >
                    {group.name}
                  </Label>
                </div>
              ))}
              {allGroups.length === 0 && (
                <p className="text-sm text-muted-foreground">No groups available</p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
);

UserForm.displayName = 'UserForm';

export default UserForm;
