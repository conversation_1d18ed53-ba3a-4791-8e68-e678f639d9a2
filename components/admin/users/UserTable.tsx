import { Button } from '@/components/ui/button';
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/data-table";
import Link from 'next/link';
import React from 'react';
import { Skeleton } from '@/components/ui/skeleton'; // Assuming parent might handle loading
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

interface UserAssociation {
  id: string;
  name: string;
}

interface User {
  id: string;
  name: string | null;
  email: string | null;
  roles?: UserAssociation[];
  groups?: UserAssociation[];
  // Add other relevant user fields if needed for display or logic
}

interface UserTableProps {
  users: User[];
  onEdit: (user: User) => void;
  onDelete: (userId: string) => void;
  loading?: boolean; // Optional loading prop
  error?: string | null; // Optional error prop
}

const UserTable: React.FC<UserTableProps> = ({ users, onEdit, onDelete, loading, error }) => {
  const { data: session, update: updateSession } = useSession();
  const router = useRouter();

  const isSuperAdmin = session?.user?.roles?.includes('Super Admin');

  const handleImpersonate = async (originalIdFromCell: unknown) => {
    let processedUserId: string | undefined;

    if (typeof originalIdFromCell === 'string') {
      processedUserId = originalIdFromCell;
    } else if (typeof originalIdFromCell === 'number') {
      processedUserId = String(originalIdFromCell);
    }
    // For other types (null, undefined, boolean, object, etc.), processedUserId remains undefined.

    // Validate: must be a non-empty string
    if (typeof processedUserId !== 'string' || processedUserId.trim() === '') {
      toast.error('Invalid User ID for impersonation. Please select a valid user.');
      console.error(
        'handleImpersonate: Invalid or empty User ID. Original ID from cell:',
        originalIdFromCell,
        'Processed ID:',
        processedUserId
      );
      return;
    }

    // At this point, processedUserId is a non-empty string.
    try {
      const response = await fetch('/api/admin/impersonation/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userIdToImpersonate: processedUserId }), // API expects 'userIdToImpersonate'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to start impersonation');
      }

      await updateSession({ impersonateTargetUserId: processedUserId });
      
      toast.success(`Successfully started impersonating user ${processedUserId}.`);
      router.push('/admin');

    } catch (error) {
      console.error('Impersonation error:', error);
      toast.error(error instanceof Error ? error.message : 'Could not start impersonation.');
    }
  };

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    );
  }

  if (error) {
    return <p className="text-red-500">Error loading users: {error}</p>;
  }
  
  if (!loading && !error && (!users || users.length === 0)) {
    return <p>No users found.</p>;
  }

  const columns: ColumnDef<User>[] = [
    {
      accessorKey: "id",
      header: "ID",
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => {
        const name = row.original.name ?? 'N/A';
        return (
          <button
            onClick={() => onEdit(row.original)}
            className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer text-left"
          >
            {name}
          </button>
        );
      },
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => {
        const email = row.original.email;
        if (!email) return 'N/A';
        return (
          <a
            href={`mailto:${email}`}
            className="text-blue-600 hover:text-blue-800 hover:underline"
          >
            {email}
          </a>
        );
      },
    },
    {
      accessorKey: "roles",
      header: "Roles",
      cell: ({ row }) => {
        const roles = row.original.roles;
        if (!roles || roles.length === 0) return 'N/A';
        return (
          <div className="flex flex-wrap gap-1">
            {roles.map((role, index) => (
              <span key={role.id}>
                <Link
                  href="/admin/roles"
                  className="text-blue-600 hover:text-blue-800 hover:underline"
                >
                  {role.name}
                </Link>
                {index < roles.length - 1 && ', '}
              </span>
            ))}
          </div>
        );
      },
    },
    {
      accessorKey: "groups",
      header: "Groups",
      cell: ({ row }) => {
        const groups = row.original.groups;
        if (!groups || groups.length === 0) return 'N/A';
        return (
          <div className="flex flex-wrap gap-1">
            {groups.map((group, index) => (
              <span key={group.id}>
                <Link
                  href="/admin/groups"
                  className="text-blue-600 hover:text-blue-800 hover:underline"
                >
                  {group.name}
                </Link>
                {index < groups.length - 1 && ', '}
              </span>
            ))}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => (
        <div className="text-right space-x-2">
          <Button variant="outline" size="sm" onClick={() => onEdit(row.original)}>
            Edit
          </Button>
          <Button 
            variant="destructive" 
            size="sm" 
            onClick={() => onDelete(row.original.id)}
            className="text-white hover:text-white"
          >
            Delete
          </Button>
          {isSuperAdmin && session?.user?.id !== row.original.id && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleImpersonate(row.original.id)}
            >
              Impersonate
            </Button>
          )}
        </div>
      ),
      enableSorting: false,
    },
  ];

  return <DataTable columns={columns} data={users} />;
};

export default UserTable;
