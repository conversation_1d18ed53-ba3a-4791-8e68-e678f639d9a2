"use client"

import * as React from "react"

import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON><PERSON>nt,
  <PERSON>bar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "./ui/sidebar"

import { IconInnerShadowTop } from "@tabler/icons-react"
import type { NavItem } from "@/components/shared/AppLayout"
import { NavMain } from "./nav-main"
import { NavSecondary } from "./nav-secondary"
import { NavUser } from "./nav-user"

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  navItems?: NavItem[];
  secondaryNavItems?: NavItem[];
  logoText?: string;
  logoHref?: string;
}

export function AppSidebar({ 
  navItems = [],
  secondaryNavItems = [],
  logoText = "NOLK",
  logoHref = "/dashboard",
  ...props 
}: AppSidebarProps) {
  return (
    <Sidebar collapsible="offcanvas" variant={props.variant} {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href={logoHref}>
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">{logoText}</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navItems} />
        {secondaryNavItems.length > 0 && (
          <NavSecondary items={secondaryNavItems} className="mt-auto" />
        )}
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}
