'use client';

import { useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';

import { useSession } from 'next-auth/react';

interface AuthWrapperProps {
  children: React.ReactNode;
}

// Pages that don't require authentication
const PUBLIC_ROUTES = [
  '/auth/signin',
  '/auth/signup',
  '/auth/error',
  '/auth/verify-request',
];

export function AuthWrapper({ children }: AuthWrapperProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    console.log('[AuthWrapper] Effect triggered:', { status, session: !!session, pathname, isRedirecting });
    
    // Don't do anything while loading or already redirecting
    if (status === 'loading' || isRedirecting) return;

    // Check if current route is public
    const isPublicRoute = PUBLIC_ROUTES.some(route => pathname.startsWith(route));
    console.log('[AuthWrapper] Route check:', { pathname, isPublicRoute });

    // If not authenticated and trying to access a protected route
    if (!session && !isPublicRoute) {
      console.log('[AuthWrapper] Redirecting to login from:', pathname);
      setIsRedirecting(true);
      
      // Use window.location.href for immediate redirect
      const callbackUrl = encodeURIComponent(pathname);
      const redirectUrl = `/auth/signin?callbackUrl=${callbackUrl}`;
      console.log('[AuthWrapper] Redirect URL:', redirectUrl);
      
      // Use window.location for immediate redirect
      window.location.href = redirectUrl;
      return;
    }

    // If authenticated and trying to access auth pages, redirect to dashboard
    if (session && isPublicRoute) {
      console.log('[AuthWrapper] Redirecting authenticated user to dashboard');
      setIsRedirecting(true);
      window.location.href = '/dashboard';
      return;
    }
  }, [session, status, pathname, router, isRedirecting]);

  // Show loading while checking authentication
  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="flex items-center gap-3">
          <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="text-slate-600 dark:text-slate-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Check if current route is public
  const isPublicRoute = PUBLIC_ROUTES.some(route => pathname.startsWith(route));

  // Show redirecting state if not authenticated and trying to access protected route
  if (!session && !isPublicRoute) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="flex items-center gap-3">
          <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="text-slate-600 dark:text-slate-400">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // Show redirecting state if authenticated and trying to access auth pages
  if (session && isPublicRoute) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="flex items-center gap-3">
          <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="text-slate-600 dark:text-slate-400">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
