"use client"

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ian<PERSON>,
  <PERSON>,
  Toolt<PERSON> as Re<PERSON>rtsTooltip,
  ResponsiveContainer,
  <PERSON>Axi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"
import { BarChart3, DollarSign, HelpCircle, Percent, TrendingUp } from "lucide-react"
import {
  CHART_COLORS,
  formatCurrency as formatCurrencyUtil,
  formatDate as formatDateUtil,
  formatPercent as formatPercentUtil,
  formatRawPercent,
} from "@/lib/chart-utils"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { GroupedKpiResponse, SimpleKpiResponse, fetchKpiData } from '@/lib/api/dashboard-client'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useEffect, useState } from "react"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { useBrandDeepDive } from "@/lib/contexts/brand-deep-dive-context"

// Define the display mode type
type DisplayMode = 'value' | 'percent';

// Define comprehensive KPI groups with only available metrics from the API
// Based on ALL_AVAILABLE_KPIS from the flexible-kpis API route
const KPI_GROUPS = {
  revenue: {
    title: "Revenue Analysis",
    description: "Complete revenue breakdown and performance metrics",
    icon: DollarSign,
    kpis: [
      'Gross Revenue',
      'Discount',
      'Refund',
      'Net Revenue'
    ],
    percentageKpis: [
      '% Discount',
      '% Refund'
    ],
    colors: [CHART_COLORS.purple, CHART_COLORS.red, CHART_COLORS.pink, CHART_COLORS.green],
    hasPercentageView: true
  },
  margins: {
    title: "Margin Performance", 
    description: "Profitability analysis across all margin types",
    icon: TrendingUp,
    kpis: [
      'Gross Margin',
      'Contribution Margin'
    ],
    percentageKpis: [
      '% Gross Margin',
      '% Contribution Margin'
    ],
    colors: [CHART_COLORS.purple, CHART_COLORS.green],
    hasPercentageView: true
  },
  costs: {
    title: "Cost Breakdown",
    description: "Detailed analysis of all cost components",
    icon: BarChart3,
    kpis: [
      'Landed Cost',
      'Fulfillment Cost', 
      'Transaction Cost',
      'Adspend'
    ],
    percentageKpis: [
      '% Landed Cost',
      '% Fulfillment Cost',
      '% Transaction Cost', 
      '% Adspend'
    ],
    colors: [CHART_COLORS.blue, CHART_COLORS.cyan, CHART_COLORS.indigo, CHART_COLORS.orange],
    hasPercentageView: true
  },
  advertising: {
    title: "Advertising Performance",
    description: "Complete advertising spend and efficiency metrics",
    icon: TrendingUp,
    kpis: [
      'Adspend',
      'ACOS',
      'TACOS'
    ],
    percentageKpis: [
      '% Adspend'
    ],
    colors: [CHART_COLORS.orange, CHART_COLORS.red, CHART_COLORS.yellow],
    hasPercentageView: true
  },
  traffic: {
    title: "Traffic & Conversion",
    description: "Website traffic and conversion performance metrics",
    icon: BarChart3,
    kpis: [
      'Website Traffic',
      'Organic Traffic',
      'Paid Traffic',
      'Conversion Rate'
    ],
    percentageKpis: [],
    colors: [CHART_COLORS.green, CHART_COLORS.blue, CHART_COLORS.purple, CHART_COLORS.orange],
    hasPercentageView: false
  }
} as const;

/**
 * Enhanced brand deep dive charts component with comprehensive KPI analysis
 * Uses the context provided by the parent BrandDeepDiveProvider
 */
export function BrandDeepDiveCharts() {
  const { state } = useBrandDeepDive();
  const [kpiData, setKpiData] = useState<SimpleKpiResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [displayModes, setDisplayModes] = useState<Record<string, DisplayMode>>({
    revenue: 'value',
    margins: 'value', 
    costs: 'value',
    advertising: 'value',
    traffic: 'value'
  });


  // Get all unique KPIs from all groups
  const getAllKpis = () => {
    const allKpis = new Set<string>();
    Object.values(KPI_GROUPS).forEach(group => {
      group.kpis.forEach(kpi => allKpis.add(kpi));
      group.percentageKpis.forEach(kpi => allKpis.add(kpi));
    });
    return Array.from(allKpis);
  };

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);

        // Use the smart groupBy from context instead of calculating locally
        const smartGroupBy = state.groupBy;

        // Convert brand deep dive context to query parameters
        const params = {
          startDate: state.startDate,
          endDate: state.endDate,
          currency: state.currency as 'CAD' | 'USD',
          groupByTime: smartGroupBy,
          brands: state.selectedBrand ? [state.selectedBrand] : [],
          salesChannels: state.salesChannels,
          countryNames: state.countryNames,
          kpis: getAllKpis()
        };

        console.log(`[Enhanced Charts] Using smart groupBy: ${smartGroupBy} for date range ${state.startDate} to ${state.endDate}`);

        const data = await fetchKpiData(params);
        
        // Handle grouped vs simple response
        if ('Gross Revenue' in data && typeof data['Gross Revenue'] === 'object' && !('summary' in data['Gross Revenue'])) {
          console.warn('Received grouped response, using first dimension');
          const firstDimension = Object.keys(data['Gross Revenue'])[0];
          if (firstDimension) {
            const simpleData: SimpleKpiResponse = {};
            Object.keys(data).forEach(kpiName => {
              simpleData[kpiName] = (data as GroupedKpiResponse)[kpiName][firstDimension];
            });
            setKpiData(simpleData);
          } else {
            throw new Error('No dimension data available in grouped response');
          }
        } else {
          setKpiData(data as SimpleKpiResponse);
        }
      } catch (err) {
        console.error("Failed to load chart data:", err);
        setError("Failed to load chart data");
      } finally {
        setLoading(false);
      }
    }

    if (state.selectedBrand) {
      fetchData();
    }
  }, [state]);

  // Process data for charts
  const processChartData = () => {
    if (!kpiData) return [];

    const firstKpi = Object.keys(kpiData)[0];
    if (!firstKpi || !kpiData[firstKpi]?.timeSeries) return [];

    const dateMap: Record<string, Record<string, string | number | null>> = {};

    // Initialize with dates
    kpiData[firstKpi].timeSeries.forEach((point) => {
      dateMap[point.date] = { date: point.date };
    });

    // Add values for each KPI
    Object.entries(kpiData).forEach(([kpiName, data]) => {
      data.timeSeries.forEach((point) => {
        if (dateMap[point.date]) {
          dateMap[point.date][kpiName] = point.value;
        }
      });
    });

    return Object.values(dateMap).sort((a, b) => {
      const dateA = a.date as string;
      const dateB = b.date as string;
      return new Date(dateA).getTime() - new Date(dateB).getTime();
    });
  };

  const chartData = processChartData();

  // Formatting functions
  const formatCurrency = (value: number | null) => formatCurrencyUtil(value, state.currency);
  const formatPercent = formatPercentUtil;
  const rechartsDateFormatter = (value: string) => formatDateUtil(value);
  const rechartsLabelFormatter = (label: string) => formatDateUtil(label);

  // Custom tooltip formatter
  const customTooltipFormatter = (value: unknown, name: string) => {
    if (typeof value !== 'number') return 'N/A';
    
    // ACOS and TACOS are already in percentage format from the database
    if (name.includes("ACOS") || name.includes("TACOS")) {
      return formatRawPercent(value);
    }
    
    // Other percentage KPIs that need conversion from decimal
    if (name.startsWith("% ") || name === "Conversion Rate") {
      return formatPercent(value);
    }
    
    if (name.includes("Traffic")) {
      return new Intl.NumberFormat("en-CA").format(value);
    }
    
    return formatCurrency(value);
  };

  const toggleDisplayMode = (groupKey: string) => {
    setDisplayModes(prev => ({
      ...prev,
      [groupKey]: prev[groupKey] === 'value' ? 'percent' : 'value'
    }));
  };

  const renderChart = (groupKey: string, group: typeof KPI_GROUPS[keyof typeof KPI_GROUPS]) => {
    const displayMode = displayModes[groupKey];
    const kpisToShow = displayMode === 'percent' && group.hasPercentageView 
      ? group.percentageKpis 
      : group.kpis;

    // Filter out KPIs that don't have data
    const availableKpis = kpisToShow.filter(kpi => kpiData && kpiData[kpi]);

    if (availableKpis.length === 0) {
      return (
        <Card key={groupKey} className="w-full">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <group.icon className="h-5 w-5 text-muted-foreground" />
              <span>{group.title}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-center text-muted-foreground">
              No data available for {group.title.toLowerCase()} metrics.
            </div>
          </CardContent>
        </Card>
      );
    }

    // Get the smart groupBy from context for display info
    const smartGroupBy = state.groupBy;
    const groupByDisplayText = smartGroupBy === 'day' ? 'Daily' : smartGroupBy === 'week' ? 'Weekly' : 'Monthly';

    return (
      <Card key={groupKey} className="w-full">
        <CardHeader className="pb-4">
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <group.icon className="h-5 w-5 text-muted-foreground" />
                  <CardTitle className="text-lg font-semibold">{group.title}</CardTitle>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary" className="text-xs font-medium">
                    {availableKpis.length} KPIs
                  </Badge>
                  <Badge variant="outline" className="text-xs font-medium">
                    {groupByDisplayText}
                  </Badge>
                </div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                        <HelpCircle className="h-4 w-4" />
                        <span className="sr-only">KPI Definitions</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="max-w-xs">
                        <p className="font-medium mb-2">{group.title}</p>
                        <p className="text-sm">{group.description}</p>
                        <div className="mt-2 text-xs">
                          <strong>Available KPIs:</strong> {availableKpis.join(', ')}
                        </div>
                        <div className="mt-1 text-xs">
                          <strong>Grouping:</strong> {groupByDisplayText} (auto-selected based on date range)
                        </div>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              {group.hasPercentageView && group.percentageKpis.length > 0 && (
                <div className="flex items-center space-x-2">
                  <Button 
                    variant={displayMode === 'value' ? "default" : "outline"} 
                    size="sm"
                    onClick={() => toggleDisplayMode(groupKey)}
                    className="h-8"
                  >
                    <DollarSign className="h-3 w-3 mr-1" />
                    Values
                  </Button>
                  <Button 
                    variant={displayMode === 'percent' ? "default" : "outline"} 
                    size="sm"
                    onClick={() => toggleDisplayMode(groupKey)}
                    className="h-8"
                  >
                    <Percent className="h-3 w-3 mr-1" />
                    Percentages
                  </Button>
                </div>
              )}
            </div>
            <p className="text-sm text-muted-foreground">{group.description}</p>
          </div>
        </CardHeader>
        <CardContent className="h-[600px] pt-0">
          {loading ? (
            <Skeleton className="h-full w-full" />
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 40, bottom: 100 }}
                barCategoryGap="15%"
              >
                <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={rechartsDateFormatter}
                  angle={-45}
                  textAnchor="end"
                  height={100}
                  fontSize={11}
                  interval={0}
                  tick={{ fontSize: 11 }}
                />
                <YAxis 
                  tickFormatter={(value) => {
                    if (displayMode === 'percent' && group.hasPercentageView) {
                      return new Intl.NumberFormat("en-CA", {
                        style: "percent",
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 1,
                      }).format(value);
                    }
                    
                    if (groupKey === 'traffic' && (typeof value === 'number')) {
                      return new Intl.NumberFormat("en-CA", {
                        notation: "compact",
                        compactDisplay: "short",
                      }).format(value);
                    }
                    
                    return new Intl.NumberFormat("en-CA", {
                      notation: "compact",
                      compactDisplay: "short",
                    }).format(value);
                  }}
                  fontSize={11}
                  width={60}
                />
                <RechartsTooltip 
                  formatter={customTooltipFormatter}
                  labelFormatter={rechartsLabelFormatter}
                  contentStyle={{
                    backgroundColor: '#ffffff',
                    border: '1px solid #e2e8f0',
                    borderRadius: '8px',
                    fontSize: '12px',
                    boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05)',
                    padding: '12px',
                    color: '#1e293b',
                    opacity: 1,
                    minWidth: '200px',
                  }}
                  labelStyle={{
                    color: '#1e293b',
                    fontWeight: '600',
                    marginBottom: '8px',
                    fontSize: '13px',
                  }}
                  itemStyle={{
                    color: '#1e293b',
                    padding: '4px 0',
                    fontSize: '12px',
                  }}
                  cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
                />
                <Legend 
                  wrapperStyle={{ 
                    fontSize: '12px',
                    paddingTop: '20px'
                  }}
                  iconType="rect"
                />
                {availableKpis.map((kpi, index) => (
                  <Bar
                    key={kpi}
                    dataKey={kpi}
                    fill={group.colors[index % group.colors.length]}
                    radius={[3, 3, 0, 0]}
                    maxBarSize={80}
                    stroke="rgba(255,255,255,0.1)"
                    strokeWidth={1}
                  />
                ))}
              </BarChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>
    );
  };

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="text-center text-red-500">{error}</div>
        </CardContent>
      </Card>
    );
  }

  if (!state.selectedBrand) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Please select a brand to view detailed performance charts.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Financial Performance Trends</h2>
          <p className="text-muted-foreground">
            Comprehensive KPI analysis with smart time grouping for {state.selectedBrand}
          </p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <BarChart3 className="h-4 w-4" />
          <span>Smart Analytics</span>
        </div>
      </div>

      <Tabs defaultValue="revenue" className="w-full">
        <TabsList className="grid grid-cols-5 w-full max-w-2xl">
          {Object.entries(KPI_GROUPS).map(([key, group]) => (
            <TabsTrigger key={key} value={key} className="text-xs font-medium">
              <group.icon className="h-3 w-3 mr-1" />
              {group.title.split(' ')[0]}
            </TabsTrigger>
          ))}
        </TabsList>
        
        {Object.entries(KPI_GROUPS).map(([key, group]) => (
          <TabsContent key={key} value={key} className="space-y-4 pt-6">
            {renderChart(key, group)}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
