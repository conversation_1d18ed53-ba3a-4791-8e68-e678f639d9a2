"use client"

import { FilterState, useFilters } from '@/lib/contexts/filter-context';
import React, { ReactNode, createContext, useContext, useEffect, useState } from 'react';

import { useBrandDeepDive } from '@/lib/contexts/brand-deep-dive-context';

// Define our own context type 
type BrandDeepDiveFilterContextType = ReturnType<typeof useFilters>;

// Create the context
const BrandDeepDiveFilterContext = createContext<BrandDeepDiveFilterContextType | undefined>(undefined);

// Custom hook to use the brand deep dive filter context
export const useBrandDeepDiveFilters = () => {
  const context = useContext(BrandDeepDiveFilterContext);
  if (context === undefined) {
    throw new Error('useBrandDeepDiveFilters must be used within a BrandDeepDiveFilterProvider');
  }
  return context;
};

// Provider component that converts BrandDeepDive context to Filters context
export const BrandDeepDiveFilterProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const brandDeepDive = useBrandDeepDive();
  const [filters, setFilters] = useState<FilterState>({
    startDate: brandDeepDive.state.startDate,
    endDate: brandDeepDive.state.endDate,
    currency: brandDeepDive.state.currency,
    groupBy: brandDeepDive.state.groupBy,
    brands: brandDeepDive.state.selectedBrand ? [brandDeepDive.state.selectedBrand] : [],
    brandGroups: [],
    salesChannels: brandDeepDive.state.salesChannels,
    countryNames: brandDeepDive.state.countryNames,
  });

  // Update filters when brand deep dive context changes
  useEffect(() => {
    setFilters({
      startDate: brandDeepDive.state.startDate,
      endDate: brandDeepDive.state.endDate,
      currency: brandDeepDive.state.currency,
      groupBy: brandDeepDive.state.groupBy,
      brands: brandDeepDive.state.selectedBrand ? [brandDeepDive.state.selectedBrand] : [],
      brandGroups: [],
      salesChannels: brandDeepDive.state.salesChannels,
      countryNames: brandDeepDive.state.countryNames,
    });
  }, [brandDeepDive.state]);

  // Helper function to convert filter state to query params
  const getQueryParams = (): string => {
    const params = new URLSearchParams();

    if (filters.startDate) {
      params.append('startDate', filters.startDate);
    }
    if (filters.endDate) {
      params.append('endDate', filters.endDate);
    }
    if (filters.currency) {
      params.append('currency', filters.currency);
    }
    if (filters.groupBy) {
      params.append('groupByTime', filters.groupBy);
    }
    if (filters.brands && filters.brands.length > 0) {
      params.append('brands', filters.brands.join(','));
    }
    if (filters.salesChannels && filters.salesChannels.length > 0) {
      params.append('salesChannels', filters.salesChannels.join(','));
    }
    if (filters.countryNames && filters.countryNames.length > 0) {
      params.append('countryNames', filters.countryNames.join(','));
    }

    return params.toString();
  };

  return (
    <BrandDeepDiveFilterContext.Provider
      value={{
        filters,
        setFilters, // This won't actually update the brand deep dive context
        availableBrands: brandDeepDive.availableBrands,
        availableBrandGroups: [],
        availableSalesChannels: brandDeepDive.availableSalesChannels,
        availableCountries: brandDeepDive.availableCountries,
        isLoading: brandDeepDive.isLoading,
        error: brandDeepDive.error,
        getQueryParams,
      }}
    >
      {children}
    </BrandDeepDiveFilterContext.Provider>
  );
};
