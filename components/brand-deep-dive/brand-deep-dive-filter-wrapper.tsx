"use client"

import { DashboardFilters } from '../../components/dashboard-filters';
import React from 'react';

interface BrandDeepDiveFilterWrapperProps {
  showBrandFilter?: boolean;
  showCountryFilter?: boolean;
  showCurrencyFilter?: boolean;
  showGroupByFilter?: boolean;
}

/**
 * A wrapper component for filters in the brand deep dive view
 * Uses the context provided by the parent BrandDeepDiveProvider
 */
export function BrandDeepDiveFilterWrapper({
  showBrandFilter = true,
  showCountryFilter = true,
  showCurrencyFilter = true,
  showGroupByFilter = true
}: BrandDeepDiveFilterWrapperProps) {
  return (
    <DashboardFilters
      showBrandFilter={showBrandFilter}
      showCountryFilter={showCountryFilter}
      showCurrencyFilter={showCurrencyFilter}
      showGroupByFilter={showGroupByFilter}
      pageType="brand-deep-dive"
      contextType="brandDeepDive"
    />
  );
}
