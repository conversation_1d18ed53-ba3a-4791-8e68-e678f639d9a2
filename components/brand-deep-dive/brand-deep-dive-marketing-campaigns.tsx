"use client"

import { MarketingCampaigns } from '../marketing-campaigns';
import React from 'react';
import { useBrandDeepDive } from '@/lib/contexts/brand-deep-dive-context';

/**
 * A wrapper component for marketing campaigns in the brand deep dive view
 * Uses the context provided by the parent BrandDeepDiveProvider
 */
export function BrandDeepDiveMarketingCampaigns() {
  const { state } = useBrandDeepDive();
  
  // Only render if a brand is selected
  if (!state.selectedBrand) {
    return null;
  }
  
  // Connect directly to the main BrandDeepDiveProvider context
  return <MarketingCampaigns contextType="brandDeepDive" />;
}
