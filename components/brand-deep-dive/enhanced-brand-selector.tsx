"use client"

import { BrandSelectorDialog } from './brand-selector-dialog';
import { Button } from '@/components/ui/button';
import { useBrandDeepDive } from '@/lib/contexts/brand-deep-dive-context';
import { useState } from 'react';

export function EnhancedBrandSelector() {
  const { state, setState, availableBrands, isLoading } = useBrandDeepDive();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [tempSelectedBrand, setTempSelectedBrand] = useState<string | null>(state.selectedBrand);
  const [brandSearch, setBrandSearch] = useState('');

  const handleApply = () => {
    setState(prev => ({
      ...prev,
      selectedBrand: tempSelectedBrand
    }));
    setDialogOpen(false);
    setBrandSearch('');
  };

  const handleDialogOpenChange = (open: boolean) => {
    if (!open) {
      // Reset temp selection when closing without applying
      setTempSelectedBrand(state.selectedBrand);
      setBrandSearch('');
    }
    setDialogOpen(open);
  };

  return (
    <>
      <Button
        variant="outline"
        onClick={() => setDialogOpen(true)}
        className="w-full justify-between"
      >
        {state.selectedBrand ? state.selectedBrand : "Select a brand..."}
        <span className="ml-2 text-muted-foreground">
          {availableBrands.length > 0 && `(${availableBrands.length} available)`}
        </span>
      </Button>

      <BrandSelectorDialog
        open={dialogOpen}
        onOpenChange={handleDialogOpenChange}
        availableBrands={availableBrands}
        selectedBrand={tempSelectedBrand}
        brandSearch={brandSearch}
        setBrandSearch={setBrandSearch}
        isLoading={isLoading}
        onBrandSelect={setTempSelectedBrand}
        onApply={handleApply}
      />
    </>
  );
}
