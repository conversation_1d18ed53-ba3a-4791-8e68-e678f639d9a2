"use client"

import * as React from "react"

import { Check, ChevronsUpDown } from "lucide-react"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useEffect, useState } from "react"

import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface BrandSelectorProps {
  onBrandSelect: (brand: string) => void
  selectedBrand: string | null
}

export function BrandSelector({ onBrandSelect, selectedBrand }: BrandSelectorProps) {
  const [open, setOpen] = useState(false)
  const [brands, setBrands] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchBrands() {
      try {
        setLoading(true)
        setError(null)
        
        const response = await fetch('/api/dashboard/brands')
        if (!response.ok) {
          throw new Error(`Failed to fetch brands: ${response.statusText}`)
        }
        
        // Define a type for the brand data
        type BrandData = {
          name: string
          group?: string
          brand_id: string
        }
        
        try {
          const responseData = await response.json()
          
          // Check if the response is an error object
          if (responseData.error) {
            console.warn(`Error in brands response: ${responseData.error}`)
            throw new Error(responseData.error)
          } else {
            // Successfully received brands data
            const brandsData = responseData as BrandData[]
            
            // If the currently selected brand is not in the available brands list,
            // reset the selection
            if (selectedBrand && !brandsData.some(brand => brand.name === selectedBrand)) {
              onBrandSelect('')
            }
            
            setBrands(brandsData.map(brand => brand.name))
          }
        } catch (parseError) {
          console.error('Error parsing brands response:', parseError)
          throw parseError
        }
      } catch (err) {
        console.error('Error fetching brands:', err)
        setError('Failed to load brands')
        // Set some default brands as fallback
        setBrands(['Brand A', 'Brand B', 'Brand C'])
      } finally {
        setLoading(false)
      }
    }

    fetchBrands()
  }, [selectedBrand, onBrandSelect])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {selectedBrand ? selectedBrand : "Select a brand..."}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <Command>
          <CommandInput placeholder="Search brands..." />
          <CommandList>
            <CommandEmpty>
              {loading ? "Loading brands..." : error ? error : "No brand found."}
            </CommandEmpty>
            <CommandGroup>
            {brands.map((brand) => (
              <CommandItem
                key={brand}
                value={brand}
                onSelect={() => {
                  onBrandSelect(brand)
                  setOpen(false)
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    selectedBrand === brand ? "opacity-100" : "opacity-0"
                  )}
                />
                {brand}
              </CommandItem>
            ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
