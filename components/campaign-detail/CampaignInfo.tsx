"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Building, Target, Users, Hash, FileText, Info } from "lucide-react";

import { CampaignDetails } from "@/lib/api/campaign-detail-service";
import { formatDate, formatCampaignName } from "@/lib/chart-utils";

interface CampaignInfoProps {
  campaign: CampaignDetails;
}

export function CampaignInfo({ campaign }: CampaignInfoProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Campaign Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="md:col-span-2 lg:col-span-3">
            <h3 className="text-sm font-medium text-muted-foreground">Campaign Name</h3>
            <div className="mt-1">
              <p className="font-medium break-words">{formatCampaign<PERSON><PERSON>(campaign.name)}</p>
              {campaign.name !== formatCampaignName(campaign.name) && (
                <p className="text-xs text-muted-foreground mt-1 break-all" title={campaign.name}>
                  Original: {campaign.name}
                </p>
              )}
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Brand</h3>
            <p className="mt-1">{campaign.brand_name || "N/A"}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
            <div className="mt-1">
              <Badge variant={campaign.status === "Active" ? "default" : "secondary"}>
                {campaign.status || "N/A"}
              </Badge>
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Start Date</h3>
            <p className="mt-1">{campaign.startDate ? formatDate(campaign.startDate) : "N/A"}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">End Date</h3>
            <p className="mt-1">{campaign.endDate ? formatDate(campaign.endDate) : "N/A"}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Target Audience</h3>
            <p className="mt-1">{campaign.targetAudience || "N/A"}</p>
          </div>
          <div className="md:col-span-2 lg:col-span-3">
            <h3 className="text-sm font-medium text-muted-foreground">Channels</h3>
            <div className="mt-1">
              {campaign.channels && campaign.channels.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {campaign.channels.map((channel, index) => (
                    <Badge key={index} variant="outline">
                      {channel}
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">N/A</p>
              )}
            </div>
          </div>
          <div className="md:col-span-2 lg:col-span-3">
            <h3 className="text-sm font-medium text-muted-foreground">Notes</h3>
            <p className="mt-1">{campaign.notes || "No notes available"}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
