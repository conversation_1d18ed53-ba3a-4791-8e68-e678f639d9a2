"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Toolt<PERSON>,
} from "recharts";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON> as PieChartIcon, BarChart3 } from "lucide-react";

import { CHART_COLORS } from "@/lib/chart-utils";
import { CampaignDetails } from "@/lib/api/campaign-detail-service";
import { formatCurrency } from "@/lib/chart-utils";

interface SpendBreakdownProps {
  campaign: CampaignDetails;
  currency: string;
}

export function SpendBreakdown({ campaign, currency }: SpendBreakdownProps) {
  // Helper function to get a consistent color from the CHART_COLORS palette
  const getColor = (index: number): string => {
    const colorKeys = Object.keys(CHART_COLORS);
    return CHART_COLORS[colorKeys[index % colorKeys.length] as keyof typeof CHART_COLORS];
  };

  // Prepare platform spend data for pie chart
  const getPlatformSpendData = () => {
    const data = [
      { name: 'Facebook', value: campaign.FacebookSpend || 0 },
      { name: 'Instagram', value: campaign.InstagramSpend || 0 },
      { name: 'Google', value: campaign.GoogleSpend || 0 },
      { name: 'TikTok', value: campaign.TikTokSpend || 0 },
      { name: 'Amazon', value: campaign.AmazonSpend || 0 },
    ].filter(item => item.value > 0);

    return data;
  };

  // Prepare campaign type spend data for pie chart
  const getCampaignTypeSpendData = () => {
    const data = [
      { name: 'Awareness', value: campaign.AwarenessSpend || 0 },
      { name: 'Conversion', value: campaign.ConversionSpend || 0 },
      { name: 'Retargeting', value: campaign.RetargetingSpend || 0 },
      { name: 'Seasonal', value: campaign.SeasonalSpend || 0 },
    ].filter(item => item.value > 0);

    return data;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Spend Breakdown</CardTitle>
        <CardDescription>Analysis of campaign spend by platform and type</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="platform">
          <TabsList className="mb-4">
            <TabsTrigger value="platform">By Platform</TabsTrigger>
            <TabsTrigger value="type">By Campaign Type</TabsTrigger>
          </TabsList>

          <TabsContent value="platform">
            <div className="grid md:grid-cols-2 gap-6 items-stretch">
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={getPlatformSpendData()}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius="80%"
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={({ name, percent }) =>
                        percent > 0.05 ? `${name}: ${(percent * 100).toFixed(0)}%` : ''
                      }
                    >
                      {getPlatformSpendData().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={getColor(index)} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: number) => formatCurrency(value, currency)} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-4">Platform Spending</h3>
                <div className="space-y-3">
                  {campaign.FacebookSpend && campaign.FacebookSpend > 0 && (
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Facebook</span>
                        <span>{formatCurrency(campaign.FacebookSpend, currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${(campaign.FacebookSpend / (campaign.totalSpend || 1)) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {campaign.InstagramSpend && campaign.InstagramSpend > 0 && (
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Instagram</span>
                        <span>{formatCurrency(campaign.InstagramSpend, currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-pink-600 h-2 rounded-full"
                          style={{ width: `${(campaign.InstagramSpend / (campaign.totalSpend || 1)) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {campaign.GoogleSpend && campaign.GoogleSpend > 0 && (
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Google</span>
                        <span>{formatCurrency(campaign.GoogleSpend, currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${(campaign.GoogleSpend / (campaign.totalSpend || 1)) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {campaign.TikTokSpend && campaign.TikTokSpend > 0 && (
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>TikTok</span>
                        <span>{formatCurrency(campaign.TikTokSpend, currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-red-600 h-2 rounded-full"
                          style={{ width: `${(campaign.TikTokSpend / (campaign.totalSpend || 1)) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {campaign.AmazonSpend && campaign.AmazonSpend > 0 && (
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Amazon</span>
                        <span>{formatCurrency(campaign.AmazonSpend, currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-yellow-600 h-2 rounded-full"
                          style={{ width: `${(campaign.AmazonSpend / (campaign.totalSpend || 1)) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="type">
            <div className="grid md:grid-cols-2 gap-6 items-stretch">
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={getCampaignTypeSpendData()}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius="80%"
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={({ name, percent }) =>
                        percent > 0.05 ? `${name}: ${(percent * 100).toFixed(0)}%` : ''
                      }
                    >
                      {getCampaignTypeSpendData().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={getColor(index + 5)} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: number) => formatCurrency(value, currency)} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-4">Campaign Type Spending</h3>
                <div className="space-y-3">
                  {campaign.AwarenessSpend && campaign.AwarenessSpend > 0 && (
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Awareness</span>
                        <span>{formatCurrency(campaign.AwarenessSpend, currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-indigo-600 h-2 rounded-full"
                          style={{ width: `${(campaign.AwarenessSpend / (campaign.totalSpend || 1)) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {campaign.ConversionSpend && campaign.ConversionSpend > 0 && (
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Conversion</span>
                        <span>{formatCurrency(campaign.ConversionSpend, currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-orange-600 h-2 rounded-full"
                          style={{ width: `${(campaign.ConversionSpend / (campaign.totalSpend || 1)) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {campaign.RetargetingSpend && campaign.RetargetingSpend > 0 && (
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Retargeting</span>
                        <span>{formatCurrency(campaign.RetargetingSpend, currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-cyan-600 h-2 rounded-full"
                          style={{ width: `${(campaign.RetargetingSpend / (campaign.totalSpend || 1)) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {campaign.SeasonalSpend && campaign.SeasonalSpend > 0 && (
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Seasonal</span>
                        <span>{formatCurrency(campaign.SeasonalSpend, currency)}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-purple-600 h-2 rounded-full"
                          style={{ width: `${(campaign.SeasonalSpend / (campaign.totalSpend || 1)) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
