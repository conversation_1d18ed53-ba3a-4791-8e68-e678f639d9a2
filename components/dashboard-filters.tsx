"use client"

import { Calendar, Search, X } from 'lucide-react';
import { Card, CardContent } from './ui/card';
import { CountryData, GroupingOption, useFilters } from '@/lib/contexts/filter-context';
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import React, { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Checkbox } from './ui/checkbox';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { SavedViewsMenu } from './saved-views-menu';
import { useBrandDeepDive } from '@/lib/contexts/brand-deep-dive-context';

interface DashboardFiltersProps {
  showBrandFilter?: boolean;
  showCountryFilter?: boolean;
  showCurrencyFilter?: boolean;
  showGroupByFilter?: boolean;
  pageType?: 'brand-deep-dive' | 'marketing-dashboard' | 'dashboard';
  contextType?: 'dashboard' | 'brandDeepDive'; // Specify which context to use
  // TODO: Consider a prop for specific date presets if they vary across dashboards
}

export function DashboardFilters({
  showBrandFilter = true,
  showCountryFilter = true,
  showCurrencyFilter = true,
  showGroupByFilter = true,
  pageType,
  contextType = 'dashboard',
}: DashboardFiltersProps) {
  // Always call hooks at the top level without any conditions
  // We'll declare both contexts and then choose which one to use
  const dashboardContext = useFilters();
  
  // For the brand deep dive context, use a ref to store whether we should use it
  const shouldUseBrandDeepDiveContext = contextType === 'brandDeepDive';
  
  // Create a safe wrapper for the brand deep dive context
  // This avoids the conditional hook call by always calling it,
  // but handling the potential error inside
  const useSafeBrandDeepDive = () => {
    try {
      return useBrandDeepDive();
    } catch {
      // Catching without assigning to a variable to avoid ESLint warnings
      return null;
    }
  };
  
  // Always call our safe wrapper
  const brandDeepDiveContextValue = useSafeBrandDeepDive();
  
  // Choose which context to use
  const {
    filters,
    setFilters,
    availableBrands,
    availableBrandGroups = [], // BrandDeepDive context doesn't have this
    availableSalesChannels,
    availableCountries,
    isLoading,
    error: contextError // Rename to avoid unused variable warning
  } = (shouldUseBrandDeepDiveContext && brandDeepDiveContextValue)
    ? {
        // Map brand deep dive context to match filter context structure
        filters: {
          startDate: brandDeepDiveContextValue.state.startDate,
          endDate: brandDeepDiveContextValue.state.endDate,
          currency: brandDeepDiveContextValue.state.currency,
          groupBy: brandDeepDiveContextValue.state.groupBy,
          brands: brandDeepDiveContextValue.state.selectedBrand ? [brandDeepDiveContextValue.state.selectedBrand] : [],
          brandGroups: [],
          salesChannels: brandDeepDiveContextValue.state.salesChannels,
          countryNames: brandDeepDiveContextValue.state.countryNames,
        },
        setFilters: (newState: React.SetStateAction<{
          startDate: string;
          endDate: string;
          currency: 'CAD' | 'USD';
          groupBy: GroupingOption;
          brands: string[];
          brandGroups: string[];
          salesChannels: string[];
          countryNames: string[];
        }>) => {
          // Handle both function-style updates and direct object updates
          if (typeof newState === 'function') {
            // Function style update
            const currentMappedState = {
              startDate: brandDeepDiveContextValue!.state.startDate,
              endDate: brandDeepDiveContextValue!.state.endDate,
              currency: brandDeepDiveContextValue!.state.currency,
              groupBy: brandDeepDiveContextValue!.state.groupBy,
              brands: brandDeepDiveContextValue!.state.selectedBrand ? [brandDeepDiveContextValue!.state.selectedBrand] : [],
              brandGroups: [],
              salesChannels: brandDeepDiveContextValue!.state.salesChannels,
              countryNames: brandDeepDiveContextValue!.state.countryNames,
            };
            
            const updatedState = newState(currentMappedState);
            
            brandDeepDiveContextValue!.setState((prev) => ({
              ...prev,
              startDate: updatedState.startDate,
              endDate: updatedState.endDate,
              currency: updatedState.currency,
              groupBy: updatedState.groupBy,
              selectedBrand: updatedState.brands?.[0] || null,
              salesChannels: updatedState.salesChannels,
              countryNames: updatedState.countryNames,
            }));
          } else {
            // Direct object update
            brandDeepDiveContextValue!.setState((prev) => ({
              ...prev,
              startDate: newState.startDate ?? prev.startDate,
              endDate: newState.endDate ?? prev.endDate,
              currency: newState.currency ?? prev.currency,
              groupBy: newState.groupBy ?? prev.groupBy,
              selectedBrand: newState.brands?.[0] ?? prev.selectedBrand,
              salesChannels: newState.salesChannels ?? prev.salesChannels,
              countryNames: newState.countryNames ?? prev.countryNames,
            }));
          }
        },
        availableBrands: brandDeepDiveContextValue.availableBrands,
        availableBrandGroups: [], // Brand deep dive doesn't use brand groups
        availableSalesChannels: brandDeepDiveContextValue.availableSalesChannels,
        availableCountries: brandDeepDiveContextValue.availableCountries,
        isLoading: brandDeepDiveContextValue.isLoading,
        error: brandDeepDiveContextValue.error
      }
    : dashboardContext;

  // Local state for date inputs
  const [startDate, setStartDate] = useState(filters.startDate);
  const [endDate, setEndDate] = useState(filters.endDate);
  
  // Local state for search inputs
  const [brandSearch, setBrandSearch] = useState('');
  const [countrySearch, setCountrySearch] = useState('');
  const [channelSearch, setChannelSearch] = useState('');
  
  // Local state for temporary selections in modals
  const [tempBrands, setTempBrands] = useState<string[]>(filters.brands);
  const [tempBrandGroups, setTempBrandGroups] = useState<string[]>(filters.brandGroups);
  const [tempCountries, setTempCountries] = useState<string[]>(filters.countryNames);
  const [tempSalesChannels, setTempSalesChannels] = useState<string[]>(filters.salesChannels);

  // Auto-apply date filter for all contexts
  React.useEffect(() => {
    // Prevent initial effect run from triggering unnecessary updates
    if (startDate === filters.startDate && endDate === filters.endDate) {
      return;
    }
    
    // Debounce the update to avoid rapid consecutive changes
    const timerId = setTimeout(() => {
      setFilters(prev => ({
        ...prev,
        startDate,
        endDate
      }));
    }, 200);
    
    return () => clearTimeout(timerId);
  }, [startDate, endDate, filters.startDate, filters.endDate, setFilters]);

  // Apply brand selections from modal
  const applyBrandSelections = () => {
    setFilters(prev => ({
      ...prev,
      brands: tempBrands,
      brandGroups: tempBrandGroups
    }));
  };

  // Apply country selections from modal
  const applyCountrySelections = () => {
    setFilters(prev => ({
      ...prev,
      countryNames: tempCountries
    }));
  };
  
  // Apply sales channel selections from modal
  const applySalesChannelSelections = () => {
    setFilters(prev => ({
      ...prev,
      salesChannels: tempSalesChannels
    }));
  };

  // --- Functions to handle direct application for BrandDeepDive context ---
  const handleBrandChangeInDialog = (brand: string, checked: boolean | string) => {
    const newTempBrands = checked ? [...tempBrands, brand] : tempBrands.filter(b => b !== brand);
    setTempBrands(newTempBrands);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, brands: newTempBrands, brandGroups: tempBrandGroups }));
    }
  };
  
  const handleBrandGroupChangeInDialog = (group: string, checked: boolean | string) => {
    const newTempBrandGroups = checked ? [...tempBrandGroups, group] : tempBrandGroups.filter(g => g !== group);
    setTempBrandGroups(newTempBrandGroups);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, brands: tempBrands, brandGroups: newTempBrandGroups }));
    }
  };
  
  const handleCountryChangeInDialog = (countryName: string, checked: boolean | string) => {
    const newTempCountries = checked ? [...tempCountries, countryName] : tempCountries.filter(c => c !== countryName);
    setTempCountries(newTempCountries);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, countryNames: newTempCountries }));
    }
  };
  
  const handleSalesChannelChangeInDialog = (channel: string, checked: boolean | string) => {
    const newTempSalesChannels = checked ? [...tempSalesChannels, channel] : tempSalesChannels.filter(c => c !== channel);
    setTempSalesChannels(newTempSalesChannels);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, salesChannels: newTempSalesChannels }));
    }
  };
  // --- End of direct application functions ---

  // Handle currency change
  const handleCurrencyChange = (currency: string) => {
    setFilters(prev => ({
      ...prev,
      currency: currency as 'CAD' | 'USD'
    }));
  };

  // Remove a brand from the filter
  const removeBrand = (brand: string) => {
    setFilters(prev => ({
      ...prev,
      brands: prev.brands.filter(b => b !== brand)
    }));
  };

  // Remove a brand group from the filter
  const removeBrandGroup = (group: string) => {
    setFilters(prev => ({
      ...prev,
      brandGroups: prev.brandGroups.filter(g => g !== group)
    }));
  };

  // Date preset options
  type DatePreset = {
    label: string;
    type: 'last_days' | 'month_to_date'; // Added 'month_to_date'
    days?: number; // Optional, only for 'last_days'
  };

  // Updated presets as per marketing dashboard spec (plus common ones)
  // "Custom Range" is implicitly handled by the date inputs themselves.
  const datePresets: DatePreset[] = [
    { label: 'Last 7 days', type: 'last_days', days: 7 },
    { label: 'Last 30 days', type: 'last_days', days: 30 },
    { label: 'Month to Date', type: 'month_to_date' },
    // { label: 'Year to date', type: 'year_to_date' }, // Removed for now, can be added back if needed globally
  ];

  // Apply date preset
  const applyDatePreset = (preset: DatePreset) => {
    const today = new Date();
    let newStartDate = new Date();
    const newEndDate = new Date(today); // End date is typically today for these presets

    if (preset.type === 'last_days' && preset.days !== undefined) {
      newStartDate.setDate(today.getDate() - preset.days);
    } else if (preset.type === 'month_to_date') {
      newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);
    }
    // else if (preset.type === 'year_to_date') {
    //   newStartDate = new Date(today.getFullYear(), 0, 1);
    // }
    
    // Format dates as YYYY-MM-DD
    const formattedStartDate = newStartDate.toISOString().split('T')[0];
    const formattedEndDate = newEndDate.toISOString().split('T')[0];
    
    setStartDate(formattedStartDate);
    setEndDate(formattedEndDate);
    
    setFilters(prev => ({
      ...prev,
      startDate: formattedStartDate,
      endDate: formattedEndDate
    }));
  };

  // Handle groupBy change
  const handleGroupByChange = (value: string) => {
    setFilters(prev => ({
      ...prev,
      groupBy: value as GroupingOption
    }));
  };

  // Reset all filters
  const resetFilters = () => {
    // Get dates for the last 90 days
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 90);

    // Format dates as YYYY-MM-DD
    const formattedStartDate = startDate.toISOString().split('T')[0];
    const formattedEndDate = endDate.toISOString().split('T')[0];

    setStartDate(formattedStartDate);
    setEndDate(formattedEndDate);
    setTempBrands([]);
    setTempBrandGroups([]);
    setTempCountries([]);
    setTempSalesChannels([]);

    // Reset filters in context (which will also update localStorage via the effect in FilterProvider)
    setFilters({
      startDate: formattedStartDate,
      endDate: formattedEndDate,
      currency: 'CAD',
      groupBy: 'month',
      brands: [],
      brandGroups: [],
      salesChannels: [],
      countryNames: []
    });
  };
  
  // Select/deselect all brands
  const selectAllBrands = (select: boolean) => {
    const newBrands = select ? availableBrands : [];
    setTempBrands(newBrands);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, brands: newBrands, brandGroups: tempBrandGroups }));
    }
  };
  
  // Select/deselect all brand groups
  const selectAllBrandGroups = (select: boolean) => {
    const newBrandGroups = select ? availableBrandGroups : [];
    setTempBrandGroups(newBrandGroups);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, brands: tempBrands, brandGroups: newBrandGroups }));
    }
  };
  
  // Select/deselect all countries
  const selectAllCountries = (select: boolean) => {
    const newCountries = select ? availableCountries.filter(c => c.hasSales).map(c => c.name) : [];
    setTempCountries(newCountries);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, countryNames: newCountries }));
    }
  };
  
  // Select/deselect all sales channels
  const selectAllSalesChannels = (select: boolean) => {
    const newSalesChannels = select ? availableSalesChannels : [];
    setTempSalesChannels(newSalesChannels);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, salesChannels: newSalesChannels }));
    }
  };
  
  // Filter brands by search term
  const filteredBrands = availableBrands.filter(brand => 
    brand.toLowerCase().includes(brandSearch.toLowerCase())
  );
  
  // Filter countries by search term
  const filteredCountries = availableCountries.filter(country =>
    country.name.toLowerCase().includes(countrySearch.toLowerCase())
  );
  
  // Filter sales channels by search term
  const filteredSalesChannels = availableSalesChannels.filter(channel => 
    channel.toLowerCase().includes(channelSearch.toLowerCase())
  );

  if (contextError) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="text-red-500">{contextError}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardContent className="p-3 sm:p-4">
        <div className="flex flex-col gap-3 sm:gap-4">
          {/* First Row: Date Range and Presets */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 sm:items-end">
            {/* Date Range Filter */}
            <div className="flex flex-col sm:flex-row gap-2 sm:items-end">
              <div className="grid grid-cols-2 sm:flex gap-2 sm:items-end">
                <div className="min-w-0">
                  <Label htmlFor="start-date" className="text-xs">Start</Label>
                  <Input
                    id="start-date"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="w-full sm:w-36 h-9 text-sm"
                  />
                </div>
                <div className="min-w-0">
                  <Label htmlFor="end-date" className="text-xs">End</Label>
                  <Input
                    id="end-date"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="w-full sm:w-36 h-9 text-sm"
                  />
                </div>
              </div>
              
              {/* Date Presets Dropdown */}
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9 w-full sm:w-auto">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span className="sm:hidden">Date Presets</span>
                    <span className="hidden sm:inline">Presets</span>
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[300px]">
                  <DialogHeader>
                    <DialogTitle>Date Presets</DialogTitle>
                  </DialogHeader>
                  <div className="grid gap-2 py-4">
                    {datePresets.map((preset) => (
                      <Button
                        key={preset.label}
                        variant="outline"
                        className="justify-start"
                        onClick={() => {
                          applyDatePreset(preset);
                          document.querySelector('[data-state="open"]')?.dispatchEvent(
                            new KeyboardEvent('keydown', { key: 'Escape' })
                          );
                        }}
                      >
                        {preset.label}
                      </Button>
                    ))}
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Currency and Group By */}
            <div className="flex gap-2 sm:items-end">
              {/* Currency Selector */}
              {showCurrencyFilter && (
                <div className="flex-1 sm:flex-none">
                  <Label htmlFor="currency" className="text-xs">Currency</Label>
                  <Select
                    value={filters.currency}
                    onValueChange={handleCurrencyChange}
                  >
                    <SelectTrigger id="currency" className="w-full sm:w-24 h-9">
                      <SelectValue placeholder="Currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CAD">CAD</SelectItem>
                      <SelectItem value="USD">USD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              
              {/* Group By Selector */}
              {showGroupByFilter && (
                <div className="flex-1 sm:flex-none">
                  <Label htmlFor="group-by" className="text-xs">Group By</Label>
                  <Select
                    value={filters.groupBy}
                    onValueChange={handleGroupByChange}
                  >
                    <SelectTrigger id="group-by" className="w-full sm:w-32 h-9">
                      <SelectValue placeholder="Group By" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="day">Day</SelectItem>
                      <SelectItem value="week">Week</SelectItem>
                      <SelectItem value="month">Month</SelectItem>
                      <SelectItem value="quarter">Quarter</SelectItem>
                      <SelectItem value="year">Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </div>

          {/* Second Row: Filter Buttons */}
          <div className="flex flex-wrap gap-2 items-center">
            {/* Brand Filter Dialog */}
            {showBrandFilter && (
              <Dialog onOpenChange={(open: boolean) => {
                if (open) {
                  setTempBrands(filters.brands);
                  setTempBrandGroups(filters.brandGroups);
                  setBrandSearch('');
                }
              }}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="h-9 text-sm">
                    Brands
                    {(filters.brands.length > 0 || filters.brandGroups.length > 0) && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {filters.brands.length + filters.brandGroups.length}
                      </Badge>
                    )}
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[500px]">
                  <DialogHeader>
                    <DialogTitle>Select Brands</DialogTitle>
                  </DialogHeader>
                  
                  <div className="relative mt-2">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search brands..."
                      className="pl-8"
                      value={brandSearch}
                      onChange={(e) => setBrandSearch(e.target.value)}
                    />
                  </div>
                  
                  <div className="flex justify-between mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => selectAllBrands(true)}
                    >
                      Select All Brands
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => selectAllBrands(false)}
                    >
                      Clear Brands
                    </Button>
                  </div>
                  
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Brand Groups</h4>
                    <div className="flex justify-between mb-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => selectAllBrandGroups(true)}
                      >
                        Select All
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => selectAllBrandGroups(false)}
                      >
                        Clear
                      </Button>
                    </div>
                    <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border rounded-md p-2">
                      {isLoading ? (
                        <div className="col-span-2 text-center py-2">Loading...</div>
                      ) : (
                        availableBrandGroups.map((group) => (
                          <div key={group} className="flex items-center space-x-2">
                            <Checkbox
                              id={`group-${group}`}
                              checked={tempBrandGroups.includes(group)}
                              onCheckedChange={(checked) => handleBrandGroupChangeInDialog(group, checked)}
                            />
                            <Label
                              htmlFor={`group-${group}`}
                              className="text-sm cursor-pointer"
                            >
                              {group}
                            </Label>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Individual Brands</h4>
                    <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto border rounded-md p-2">
                      {isLoading ? (
                        <div className="col-span-2 text-center py-2">Loading...</div>
                      ) : filteredBrands.length === 0 ? (
                        <div className="col-span-2 text-center py-2">No brands match your search</div>
                      ) : (
                        filteredBrands.map((brand) => (
                          <div key={brand} className="flex items-center space-x-2">
                            <Checkbox
                              id={`brand-${brand}`}
                              checked={tempBrands.includes(brand)}
                              onCheckedChange={(checked) => handleBrandChangeInDialog(brand, checked)}
                            />
                            <Label
                              htmlFor={`brand-${brand}`}
                              className="text-sm cursor-pointer"
                            >
                              {brand}
                            </Label>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                  
                  <DialogFooter className="mt-4">
                    <DialogClose asChild>
                      <Button variant="outline">Cancel</Button>
                    </DialogClose>
                  {/* Auto-apply all brand selections */}
                  <DialogClose asChild>
                    <Button onClick={applyBrandSelections}>Done</Button>
                  </DialogClose>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            )}

            {/* Country Filter Dialog */}
            {showCountryFilter && (
              <Dialog onOpenChange={(open: boolean) => {
                if (open) {
                  setTempCountries(filters.countryNames);
                  setCountrySearch('');
                }
              }}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="h-9">
                    Countries
                    {filters.countryNames.length > 0 && (
                      <Badge variant="secondary" className="ml-2">
                        {filters.countryNames.length}
                      </Badge>
                    )}
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[500px]">
                  <DialogHeader>
                    <DialogTitle>Select Countries</DialogTitle>
                  </DialogHeader>
                  
                  <div className="relative mt-2">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search countries..."
                      className="pl-8"
                      value={countrySearch}
                      onChange={(e) => setCountrySearch(e.target.value)}
                    />
                  </div>
                  
                  <div className="flex justify-between mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => selectAllCountries(true)}
                    >
                      Select All
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => selectAllCountries(false)}
                    >
                      Clear All
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 max-h-64 overflow-y-auto border rounded-md p-2 mt-4">
                    {isLoading ? (
                      <div className="col-span-2 text-center py-2">Loading...</div>
                    ) : filteredCountries.length === 0 ? (
                      <div className="col-span-2 text-center py-2">No countries match your search</div>
                    ) : (
                      filteredCountries.map((country: CountryData) => (
                        <div key={country.name} className="flex items-center space-x-2">
                          <Checkbox
                            id={`country-${country.name}`}
                            checked={tempCountries.includes(country.name)}
                            disabled={!country.hasSales}
                            onCheckedChange={(checked) => handleCountryChangeInDialog(country.name, checked)}
                          />
                          <Label
                            htmlFor={`country-${country.name}`}
                            className={`text-sm ${!country.hasSales ? 'text-gray-400 cursor-not-allowed' : 'cursor-pointer'}`}
                          >
                            {country.name}
                          </Label>
                        </div>
                      ))
                    )}
                  </div>
                  
                  <DialogFooter className="mt-4">
                    <DialogClose asChild>
                      <Button variant="outline">Cancel</Button>
                    </DialogClose>
                  {/* Auto-apply all country selections */}
                  <DialogClose asChild>
                    <Button onClick={applyCountrySelections}>Done</Button>
                  </DialogClose>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            )}

            {/* Sales Channel Filter Dialog */}
            <Dialog onOpenChange={(open: boolean) => {
              if (open) {
                setTempSalesChannels(filters.salesChannels);
                setChannelSearch('');
              }
            }}>
              <DialogTrigger asChild>
                <Button variant="outline" className="h-9">
                  Sales Channels
                  {filters.salesChannels.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {filters.salesChannels.length}
                    </Badge>
                  )}
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Select Sales Channels</DialogTitle>
                </DialogHeader>
                
                <div className="relative mt-2">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search channels..."
                    className="pl-8"
                    value={channelSearch}
                    onChange={(e) => setChannelSearch(e.target.value)}
                  />
                </div>
                
                <div className="flex justify-between mt-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => selectAllSalesChannels(true)}
                  >
                    Select All
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => selectAllSalesChannels(false)}
                  >
                    Clear All
                  </Button>
                </div>
                
                <div className="grid grid-cols-2 gap-2 max-h-64 overflow-y-auto border rounded-md p-2 mt-4">
                  {isLoading ? (
                    <div className="col-span-2 text-center py-2">Loading...</div>
                  ) : filteredSalesChannels.length === 0 ? (
                    <div className="col-span-2 text-center py-2">No channels match your search</div>
                  ) : (
                    filteredSalesChannels.map((channel) => (
                      <div key={channel} className="flex items-center space-x-2">
                        <Checkbox
                          id={`channel-${channel}`}
                          checked={tempSalesChannels.includes(channel)}
                          onCheckedChange={(checked) => handleSalesChannelChangeInDialog(channel, checked)}
                        />
                        <Label
                          htmlFor={`channel-${channel}`}
                          className="text-sm cursor-pointer"
                        >
                          {channel}
                        </Label>
                      </div>
                    ))
                  )}
                </div>
                
                <DialogFooter className="mt-4">
                  <DialogClose asChild>
                    <Button variant="outline">Cancel</Button>
                  </DialogClose>
                  {/* Auto-apply all sales channel selections */}
                  <DialogClose asChild>
                    <Button onClick={applySalesChannelSelections}>Done</Button>
                  </DialogClose>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Saved Views Menu - Only show if pageType is provided */}
            {pageType && (
              <SavedViewsMenu pageType={pageType} />
            )}

            {/* Reset Button */}
            <Button variant="outline" onClick={resetFilters} size="sm" className="h-9 ml-auto">
              Reset All
            </Button>
          </div>
          
          {/* Brand Filter Pills */}
          {(filters.brands.length > 0 || filters.brandGroups.length > 0) && (
            <div className="flex flex-wrap gap-2 mt-2">
              {filters.brands.map(brand => (
                <Badge key={brand} variant="secondary" className="flex items-center gap-1 px-3 py-1">
                  {brand}
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                    onClick={() => removeBrand(brand)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
              {filters.brandGroups.map(group => (
                <Badge key={group} variant="outline" className="flex items-center gap-1 px-3 py-1">
                  Group: {group}
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                    onClick={() => removeBrandGroup(group)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
