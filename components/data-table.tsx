"use client"

import * as React from "react"
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  closestCenter,
  useSensor,
  useSensors,
  type DragEndEvent,
  type UniqueIdentifier,
} from "@dnd-kit/core"
import { restrictToVerticalAxis } from "@dnd-kit/modifiers"
import {
  SortableContext,
  arrayMove,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import {
  IconArrowDown,
  IconArrowUp,
  // IconChevronDown, // Unused
  IconChevronLeft,
  IconChevronRight,
  IconChevronsLeft,
  IconChevronsRight,
  // IconGripVertical, // Unused
  IconLayoutColumns,
  // IconPlus, // Removed as "Add Section" is example-specific
} from "@tabler/icons-react"
import {
  ColumnDef,
  ColumnFiltersState,
  Row,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
// import { toast } from "sonner"; // Removed as it was used in example-specific forms
// import { z } from "zod"; // Schema is now generic

// import { useIsMobile } from "'hooks/use-mobile"' (see below for file content); // Not used after removing TableCellViewer
// import { Badge } from "'components/ui/badge"' (see below for file content); // Used by example columns, not by generic table
import { Button } from "./ui/button"
// import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "'components/ui/chart"' (see below for file content); // Example specific
// import { Checkbox } from "'components/ui/checkbox"' (see below for file content) // Unused
// import { Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerTitle, DrawerTrigger } from "'components/ui/drawer"' (see below for file content); // Example specific
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu"
import { Input } from "./ui/input"
// import { Label } from "'components/ui/label"' (see below for file content) // Unused
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select" // Corrected path
// import { Separator } from "'components/ui/separator"' (see below for file content); // Example specific
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./ui/table"
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "'components/ui/tabs"' (see below for file content); // Example specific UI shell

// Removed hardcoded schema:
// export const schema = z.object({ ... });

// Draggable Row related components
interface DraggableRowProps<TData extends { id: UniqueIdentifier }> {
  row: Row<TData>;
}

function DraggableRow<TData extends { id: UniqueIdentifier }>({ row }: DraggableRowProps<TData>) {
  const { transform, transition, setNodeRef, isDragging } = useSortable({
    id: row.original.id,
  });

  return (
    <TableRow
      data-state={row.getIsSelected() && "selected"}
      data-dragging={isDragging}
      ref={setNodeRef}
      className="relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80"
      style={{
        transform: CSS.Transform.toString(transform),
        transition: transition,
      }}
    >
      {row.getVisibleCells().map((cell) => (
        <TableCell key={cell.id}>
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </TableCell>
      ))}
    </TableRow>
  );
}

// DataTable component props
interface DataTableProps<TData extends { id: UniqueIdentifier }, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  enableRowSelection?: boolean;
  enableDnd?: boolean; // Prop to enable/disable drag and drop
  enableMultiSort?: boolean; // Prop to enable/disable multi-column sorting
  onRowOrderChange?: (orderedData: TData[]) => void; // Callback for when row order changes
  // Props for server-side sorting
  state?: { sorting?: SortingState };
  onSortingChange?: (sorting: SortingState) => void;
  manualSorting?: boolean;
  // Props for search functionality
  enableSearch?: boolean;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  searchPlaceholder?: string;
}

export function DataTable<TData extends { id: UniqueIdentifier }, TValue>({
  columns,
  data: initialData,
  enableRowSelection = false, // Default to false if not provided
  enableDnd = false, // Default to false
  enableMultiSort = true, // Default to true if not provided
  onRowOrderChange,
  state: propState,
  onSortingChange: propOnSortingChange,
  manualSorting: propManualSorting,
  enableSearch = false,
  searchValue,
  onSearchChange,
  searchPlaceholder = "Search...",
}: DataTableProps<TData, TValue>) {
  const [data, setData] = React.useState(() => initialData || []);
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [internalSorting, setInternalSorting] = React.useState<SortingState>([]);
  const [pagination, setPagination] = React.useState({
    pageIndex: 0,
    pageSize: 50,
  });

  // Update internal state if initialData prop changes
  React.useEffect(() => {
    setData(initialData || []);
  }, [initialData]);

  const sortableId = React.useId();
  const sensors = useSensors(
    useSensor(MouseSensor, {}),
    useSensor(TouchSensor, {}),
    useSensor(KeyboardSensor, {})
  );

  const dataIds = React.useMemo<UniqueIdentifier[]>(
    () => (Array.isArray(data) ? data.map(({ id }) => id) : []),
    [data]
  );

  const handleReactTableSortingChange = React.useCallback(
    (updater: SortingState | ((old: SortingState) => SortingState)) => {
      if (propOnSortingChange) {
        const currentSortingState = propManualSorting
          ? propState?.sorting ?? []
          : internalSorting;
        const newSortingState =
          typeof updater === "function"
            ? updater(currentSortingState)
            : updater;
        propOnSortingChange(newSortingState);
      } else {
        setInternalSorting(updater);
      }
    },
    [propOnSortingChange, propManualSorting, propState?.sorting, internalSorting, setInternalSorting]
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting: propState?.sorting ?? internalSorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination,
    },
    getRowId: (row) => row.id.toString(), // Assumes TData has an 'id' property
    enableRowSelection: enableRowSelection,
    enableMultiSort, // Pass the enableMultiSort prop to the table
    onRowSelectionChange: setRowSelection,
    onSortingChange: handleReactTableSortingChange,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    manualSorting: propManualSorting ?? false,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    meta: {
        // Example of how you might pass setData to columns for inline editing
        // updateData: (rowIndex: number, columnId: string, value: unknown) => {
        //   setData(old => old.map((row, index) => {
        //     if (index === rowIndex) {
        //       return { ...old[rowIndex]!, [columnId]: value };
        //     }
        //     return row;
        //   }));
        // }
      }
  });

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event;
    if (active && over && active.id !== over.id) {
      setData((currentData) => {
        const oldIndex = dataIds.indexOf(active.id);
        const newIndex = dataIds.indexOf(over.id);
        const orderedData = arrayMove(currentData, oldIndex, newIndex);
        if (onRowOrderChange) {
          onRowOrderChange(orderedData);
        }
        return orderedData;
      });
    }
  }

  const tableContent = (
    <Table>
      <TableHeader className="bg-muted sticky top-0 z-10">
        {table.getHeaderGroups().map((headerGroup) => (
          <TableRow key={headerGroup.id}>
            {headerGroup.headers.map((header) => {
              return (
                <TableHead key={header.id} colSpan={header.colSpan}>
                  {header.isPlaceholder ? null : (
                    <div
                      className={
                        header.column.getCanSort()
                          ? "flex cursor-pointer select-none items-center"
                          : ""
                      }
                      onClick={header.column.getToggleSortingHandler()}
                      title={
                        header.column.getCanSort()
                          ? header.column.getNextSortingOrder() === "asc"
                            ? "Sort ascending"
                            : header.column.getNextSortingOrder() === "desc"
                              ? "Sort descending"
                              : "Clear sort"
                          : undefined
                      }
                    >
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                      {{
                        asc: <IconArrowUp className="ml-2 size-4" />,
                        desc: <IconArrowDown className="ml-2 size-4" />,
                      }[header.column.getIsSorted() as string] ?? null}
                    </div>
                  )}
                </TableHead>
              );
            })}
          </TableRow>
        ))}
      </TableHeader>
      <TableBody>
        {table.getRowModel().rows?.length ? (
          enableDnd ? (
            <SortableContext
              items={dataIds}
              strategy={verticalListSortingStrategy}
            >
              {table.getRowModel().rows.map((row) => (
                // Cast row to Row<TData> if necessary, or ensure DraggableRow is correctly typed
                <DraggableRow key={row.id} row={row as Row<TData>} />
              ))}
            </SortableContext>
          ) : (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(
                      cell.column.columnDef.cell,
                      cell.getContext()
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))
          )
        ) : (
          <TableRow>
            <TableCell
              colSpan={columns.length}
              className="h-24 text-center"
            >
              No results.
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );


  return (
    <div className="w-full space-y-4">
      {/* Toolbar: Search and column visibility controls */}
      <div className="flex items-center justify-between space-x-2 py-4">
        {enableSearch && (
          <div className="flex items-center space-x-2">
            <Input
              placeholder={searchPlaceholder}
              value={searchValue || ""}
              onChange={(event) => onSearchChange?.(event.target.value)}
              className="max-w-sm"
            />
          </div>
        )}
        <div className="flex items-center space-x-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <IconLayoutColumns className="mr-2 size-4" /> Columns
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter(
                  (column) =>
                    typeof column.accessorFn !== "undefined" &&
                    column.getCanHide()
                )
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg border">
        {enableDnd ? (
            <DndContext
                collisionDetection={closestCenter}
                modifiers={[restrictToVerticalAxis]}
                onDragEnd={handleDragEnd}
                sensors={sensors}
                id={sortableId}
            >
                {tableContent}
            </DndContext>
        ) : (
            tableContent
        )}
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-2">
        <div className="text-muted-foreground flex-1 text-sm">
          {enableRowSelection ? (
            <>
              {table.getFilteredSelectedRowModel().rows.length} of{" "}
              {table.getFilteredRowModel().rows.length} row(s) selected.
            </>
          ) : (
            <>
              {table.getFilteredRowModel().rows.length} row(s).
            </>
          )}
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Rows per page</p>
            <Select
              value={`${table.getState().pagination.pageSize}`}
              onValueChange={(value: string) => { // Added string type for value
                table.setPageSize(Number(value));
              }}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue placeholder={table.getState().pagination.pageSize} />
              </SelectTrigger>
              <SelectContent side="top">
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <SelectItem key={pageSize} value={`${pageSize}`}>
                    {pageSize}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Page {table.getState().pagination.pageIndex + 1} of{" "}
            {table.getPageCount()}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to first page</span>
              <IconChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Go to previous page</span>
              <IconChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to next page</span>
              <IconChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Go to last page</span>
              <IconChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
