"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Cartes<PERSON><PERSON>, <PERSON>, Toolt<PERSON> as Recha<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { CHART_COLORS, getGradientDefinition } from "@/lib/chart-utils"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DollarSign, HelpCircle, Percent } from "lucide-react"
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { createCompactFormatter, createFormatters } from "./utils"

import { Button } from "@/components/ui/button"
import { ChartContainer } from "@/components/ui/chart"
import { ChartWithToggleProps } from "./types"
import { Skeleton } from "@/components/ui/skeleton"

export function AdspendChart({ chartData, loading, currency, displayMode, onDisplayModeChange }: ChartWithToggleProps) {
  const { rechartsDateFormatter, rechartsLabe<PERSON><PERSON><PERSON><PERSON>er, customTooltipFormatter } = createFormatters(currency);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center space-x-1">
          <CardTitle>Adspend Analysis</CardTitle>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                  <HelpCircle className="h-4 w-4" />
                  <span className="sr-only">KPI Definition</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">Total advertising spend across all channels (Meta, Google, Amazon, etc.).</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant={displayMode === 'value' ? "default" : "outline"} 
            size="icon"
            onClick={() => onDisplayModeChange('value')}
            title="Show Values"
          >
            <DollarSign className="h-4 w-4" />
          </Button>
          <Button 
            variant={displayMode === 'percent' ? "default" : "outline"} 
            size="icon"
            onClick={() => onDisplayModeChange('percent')}
            title="Show Percentages"
          >
            <Percent className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="h-[400px]">
        {loading ? (
          <Skeleton className="h-full w-full" />
        ) : (
          <ChartContainer config={{
            "Adspend": { label: "Adspend", color: CHART_COLORS.orange },
            "% Adspend": { label: "% Adspend", color: CHART_COLORS.yellow }
          }}>
            <AreaChart
              data={chartData}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <defs>
                <linearGradient {...getGradientDefinition("adspend", CHART_COLORS.orange)}>
                  <stop offset="5%" stopColor={CHART_COLORS.orange} stopOpacity={0.8} />
                  <stop offset="95%" stopColor={CHART_COLORS.orange} stopOpacity={0.1} />
                </linearGradient>
                <linearGradient {...getGradientDefinition("adspendPercent", CHART_COLORS.yellow)}>
                  <stop offset="5%" stopColor={CHART_COLORS.yellow} stopOpacity={0.8} />
                  <stop offset="95%" stopColor={CHART_COLORS.yellow} stopOpacity={0.1} />
                </linearGradient>
              </defs>
              <XAxis 
                dataKey="date" 
                tickFormatter={rechartsDateFormatter}
                minTickGap={30}
              />
              <YAxis 
                tickFormatter={createCompactFormatter(displayMode === 'percent')}
              />
              <CartesianGrid strokeDasharray="3 3" />
              <RechartsTooltip 
                formatter={customTooltipFormatter}
                labelFormatter={rechartsLabelFormatter}
              />
              <Legend />
              {displayMode === 'value' ? (
                <Area
                  type="monotone"
                  dataKey="Adspend"
                  stroke={CHART_COLORS.orange}
                  strokeWidth={2}
                  fillOpacity={1}
                  fill="url(#adspend)"
                />
              ) : (
                <Area
                  type="monotone"
                  dataKey="% Adspend"
                  stroke={CHART_COLORS.yellow}
                  strokeWidth={2}
                  fillOpacity={1}
                  fill="url(#adspendPercent)"
                />
              )}
            </AreaChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
