"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { DollarSign, HelpCircle, Percent } from "lucide-react"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { createCompactFormatter, createFormatters } from "./utils"

import { Button } from "@/components/ui/button"
import { CHART_COLORS } from "@/lib/chart-utils"
import { ChartContainer } from "@/components/ui/chart"
import { ChartWithToggleProps } from "./types"
import { Skeleton } from "@/components/ui/skeleton"

export function CostChart({ chartData, loading, currency, displayMode, onDisplayModeChange }: ChartWithToggleProps) {
  const { rechartsDateFormatter, rechartsLabelFormatter, customTool<PERSON>F<PERSON>atter } = createFormatters(currency);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center space-x-1">
          <CardTitle>Cost Analysis</CardTitle>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                  <HelpCircle className="h-4 w-4" />
                  <span className="sr-only">KPI Definition</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">Cost components including landed cost, fulfillment fees, and transaction fees.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant={displayMode === 'value' ? "default" : "outline"} 
            size="icon"
            onClick={() => onDisplayModeChange('value')}
            title="Show Values"
          >
            <DollarSign className="h-4 w-4" />
          </Button>
          <Button 
            variant={displayMode === 'percent' ? "default" : "outline"} 
            size="icon"
            onClick={() => onDisplayModeChange('percent')}
            title="Show Percentages"
          >
            <Percent className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="h-[400px]">
        {loading ? (
          <Skeleton className="h-full w-full" />
        ) : (
          <ChartContainer config={{
            "Landed Cost": { label: "Landed Cost", color: CHART_COLORS.blue },
            "Fulfillment Cost": { label: "Fulfillment Cost", color: CHART_COLORS.cyan },
            "Transaction Cost": { label: "Transaction Cost", color: CHART_COLORS.indigo },
            "% Landed Cost": { label: "% Landed Cost", color: CHART_COLORS.blue },
            "% Fulfillment Cost": { label: "% Fulfillment Cost", color: CHART_COLORS.cyan },
            "% Transaction Cost": { label: "% Transaction Cost", color: CHART_COLORS.indigo }
          }}>
            <BarChart
              data={chartData}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tickFormatter={rechartsDateFormatter}
                minTickGap={30}
              />
              <YAxis 
                tickFormatter={createCompactFormatter(displayMode === 'percent')}
              />
              <RechartsTooltip 
                formatter={customTooltipFormatter}
                labelFormatter={rechartsLabelFormatter}
              />
              <Legend />
              {displayMode === 'value' ? (
                <>
                  <Bar
                    dataKey="Landed Cost"
                    fill={CHART_COLORS.blue}
                  />
                  <Bar
                    dataKey="Fulfillment Cost"
                    fill={CHART_COLORS.cyan}
                  />
                  <Bar
                    dataKey="Transaction Cost"
                    fill={CHART_COLORS.indigo}
                  />
                </>
              ) : (
                <>
                  <Bar
                    dataKey="% Landed Cost"
                    fill={CHART_COLORS.blue}
                  />
                  <Bar
                    dataKey="% Fulfillment Cost"
                    fill={CHART_COLORS.cyan}
                  />
                  <Bar
                    dataKey="% Transaction Cost"
                    fill={CHART_COLORS.indigo}
                  />
                </>
              )}
            </BarChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
