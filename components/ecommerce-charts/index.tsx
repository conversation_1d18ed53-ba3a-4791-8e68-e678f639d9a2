"use client"

import { Card, CardContent } from "@/components/ui/card"
import { ChartFilters, DisplayMode } from "./types"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"

import { AdjustmentsChart } from "./adjustments-chart"
import { AdspendChart } from "./adspend-chart"
import { CostChart } from "./cost-chart"
import { MarginChart } from "./margin-chart"
import { RevenueChart } from "./revenue-chart"
import { useBrandDeepDive } from "@/lib/contexts/brand-deep-dive-context"
import { useChartData } from "./use-chart-data"
import { useFilters } from "@/lib/contexts/filter-context"
import { useState } from "react"

// Helper components to guarantee hooks are called in a consistent order
function DashboardCharts() {
  const dashboardContext = useFilters();
  return <ChartsInternal filters={dashboardContext.filters} getQueryParams={dashboardContext.getQueryParams} />;
}

function BrandDeepDiveCharts() {
  const brandDeepDiveContext = useBrandDeepDive();
  
  console.log('[DEBUG] BrandDeepDiveCharts - Context state:', brandDeepDiveContext.state);
  
  // Convert brand deep dive context to the filter format
  const filters: ChartFilters = {
    startDate: brandDeepDiveContext.state.startDate,
    endDate: brandDeepDiveContext.state.endDate,
    currency: brandDeepDiveContext.state.currency,
    groupBy: brandDeepDiveContext.state.groupBy,
    brands: brandDeepDiveContext.state.selectedBrand ? [brandDeepDiveContext.state.selectedBrand] : [],
    brandGroups: [],
    salesChannels: brandDeepDiveContext.state.salesChannels,
    countryNames: brandDeepDiveContext.state.countryNames,
  };
  
  console.log('[DEBUG] BrandDeepDiveCharts - Converted filters:', filters);
  
  // Simple getQueryParams function
  const getQueryParams = () => {
    const params = new URLSearchParams();
    if (filters.startDate) params.append('startDate', filters.startDate);
    if (filters.endDate) params.append('endDate', filters.endDate);
    if (filters.currency) params.append('currency', filters.currency);
    if (filters.groupBy) params.append('groupByTime', filters.groupBy);
    if (filters.brands.length > 0) params.append('brands', filters.brands.join(','));
    if (filters.salesChannels.length > 0) params.append('salesChannels', filters.salesChannels.join(','));
    if (filters.countryNames.length > 0) params.append('countryNames', filters.countryNames.join(','));
    const queryString = params.toString();
    console.log('[DEBUG] BrandDeepDiveCharts - Generated query params:', queryString);
    return queryString;
  };
  
  return <ChartsInternal filters={filters} getQueryParams={getQueryParams} />;
}

// Internal component that accepts filters and query params directly
function ChartsInternal({ 
  filters, 
  getQueryParams 
}: { 
  filters: ChartFilters; 
  getQueryParams: () => string; 
}) {
  const { chartData, loading, error } = useChartData(filters, getQueryParams);
  
  // Display mode states for each chart type
  const [marginDisplayMode, setMarginDisplayMode] = useState<DisplayMode>('value')
  const [costsDisplayMode, setCostsDisplayMode] = useState<DisplayMode>('value')
  const [adjustmentsDisplayMode, setAdjustmentsDisplayMode] = useState<DisplayMode>('value')
  const [adspendDisplayMode, setAdspendDisplayMode] = useState<DisplayMode>('value')

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">{error}</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Tabs defaultValue="revenue">
      <div className="flex items-center justify-between">
        <TabsList>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="margins">Margins</TabsTrigger>
          <TabsTrigger value="costs">Costs</TabsTrigger>
          <TabsTrigger value="adjustments">Adjustments</TabsTrigger>
          <TabsTrigger value="adspend">Adspend</TabsTrigger>
        </TabsList>
      </div>
      
      <TabsContent value="revenue" className="space-y-4">
        <RevenueChart 
          chartData={chartData}
          loading={loading}
          currency={filters.currency}
        />
      </TabsContent>
      
      <TabsContent value="margins" className="space-y-4">
        <MarginChart 
          chartData={chartData}
          loading={loading}
          currency={filters.currency}
          displayMode={marginDisplayMode}
          onDisplayModeChange={setMarginDisplayMode}
        />
      </TabsContent>
      
      <TabsContent value="costs" className="space-y-4">
        <CostChart 
          chartData={chartData}
          loading={loading}
          currency={filters.currency}
          displayMode={costsDisplayMode}
          onDisplayModeChange={setCostsDisplayMode}
        />
      </TabsContent>
      
      <TabsContent value="adjustments" className="space-y-4">
        <AdjustmentsChart 
          chartData={chartData}
          loading={loading}
          currency={filters.currency}
          displayMode={adjustmentsDisplayMode}
          onDisplayModeChange={setAdjustmentsDisplayMode}
        />
      </TabsContent>
      
      <TabsContent value="adspend" className="space-y-4">
        <AdspendChart 
          chartData={chartData}
          loading={loading}
          currency={filters.currency}
          displayMode={adspendDisplayMode}
          onDisplayModeChange={setAdspendDisplayMode}
        />
      </TabsContent>
    </Tabs>
  );
}

// Main export component with contextType prop
export function EcommerceCharts({ contextType = 'dashboard' }: { contextType?: 'dashboard' | 'brandDeepDive' }) {
  // Render the appropriate component based on context type
  if (contextType === 'brandDeepDive') {
    try {
      return <BrandDeepDiveCharts />;
    } catch (error) {
      console.warn('Brand deep dive context not available, falling back to dashboard context:', error);
      return <DashboardCharts />;
    }
  }
  
  return <DashboardCharts />;
}
