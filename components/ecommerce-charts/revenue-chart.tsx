"use client"

import { <PERSON>, <PERSON><PERSON>hart, <PERSON><PERSON>ian<PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as Re<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"
import { CHART_COLORS, getGradientDefinition } from "@/lib/chart-utils"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { MouseEvent, useState } from "react"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { createCompactFormatter, createFormatters } from "./utils"

import { Button } from "@/components/ui/button"
import { ChartContainer } from "@/components/ui/chart"
import { ChartContextMenu } from "@/components/ui/chart-context-menu"
import type { ChartContextMenuData } from "@/lib/chart-context-menu-utils" // Import type directly
import { ChartProps } from "./types"
import { HelpCircle } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"

export function RevenueChart({ chartData, loading, currency }: ChartProps) {
  const { rechartsDateFormatter, rechartsLabelFormatter, customTooltipFormatter } = createFormatters(currency);
  
  // State to track the currently active chart element for context menu
  const [activeElement, setActiveElement] = useState<{
    dataKey?: string
    index?: number
    payload?: Record<string, unknown>
  } | null>(null);

  // State for programmatic context menu control
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });

  // Extract KPI names from the chart data structure (still needed for ChartContextMenu logic if it uses it)
  // const kpiNames = ["Gross Revenue", "Net Revenue"]; // This prop is removed from ChartContextMenu

  // Convert chart data to the format expected by ChartContextMenu
  // The `index` property might not be needed if `activeElement.index` is reliable
  const contextMenuData: ChartContextMenuData[] = chartData.map((item) => ({
    ...item,
    // index, // Not strictly needed on each data item if activeElement provides it
  })) as ChartContextMenuData[];

  // Handle mouse enter on chart elements
  const handleMouseEnter = (data: Record<string, unknown> & { dataKey?: string; payload?: Record<string, unknown> }, index: number) => {
    console.log('🐛 [RevenueChart] Mouse enter:', { data, index, dataKey: data?.dataKey });
    if (data && data.dataKey) {
      setActiveElement({
        dataKey: data.dataKey,
        index,
        payload: data.payload,
      });
      console.log('🐛 [RevenueChart] Active element set:', {
        dataKey: data.dataKey,
        index,
        payload: data.payload,
      });
    }
  };

  // Handle mouse leave from chart elements
  const handleMouseLeave = () => {
    console.log('🐛 [RevenueChart] Mouse leave - clearing active element');
    setActiveElement(null);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center space-x-1">
          <CardTitle>Revenue Trends</CardTitle>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-6 w-6 p-0">
                  <HelpCircle className="h-4 w-4" />
                  <span className="sr-only">KPI Definition</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs">Total sales revenue before any deductions (discounts, refunds, taxes, shipping, etc.).</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent className="h-[400px]">
        {loading ? (
          <Skeleton className="h-full w-full" />
        ) : (
          <ChartContainer
            config={{
              "Gross Revenue": { label: "Gross Revenue", color: CHART_COLORS.purple },
              "Net Revenue": { label: "Net Revenue", color: CHART_COLORS.green }
            }}
          >
            <ChartContextMenu
              chartData={contextMenuData}
              activeElement={activeElement}
              currency={currency}
              isMenuOpen={isMenuOpen}
              onOpenChange={setIsMenuOpen}
              menuPosition={menuPosition}
              // chartType and kpiNames props are removed
            >
              <div
                onContextMenu={(e: MouseEvent) => {
                  console.log('🐛 [RevenueChart] Div onContextMenu event:', {
                    clientX: e.clientX,
                    clientY: e.clientY,
                    activeElement: activeElement, // Log current activeElement
                  });
                  e.preventDefault();
                  setMenuPosition({ x: e.clientX, y: e.clientY });
                  setIsMenuOpen(true);
                }}
              >
                <AreaChart
                  data={chartData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  onMouseEnter={handleMouseEnter} // Keep this for activeElement
                  onMouseLeave={handleMouseLeave} // Keep this for activeElement
                >
                  <defs>
                    <linearGradient {...getGradientDefinition("grossRevenue", CHART_COLORS.purple)}>
                    <stop offset="5%" stopColor={CHART_COLORS.purple} stopOpacity={0.8} />
                    <stop offset="95%" stopColor={CHART_COLORS.purple} stopOpacity={0.1} />
                  </linearGradient>
                  <linearGradient {...getGradientDefinition("netRevenue", CHART_COLORS.green)}>
                    <stop offset="5%" stopColor={CHART_COLORS.green} stopOpacity={0.8} />
                    <stop offset="95%" stopColor={CHART_COLORS.green} stopOpacity={0.1} />
                  </linearGradient>
                </defs>
                <XAxis
                  dataKey="date"
                  tickFormatter={rechartsDateFormatter}
                  minTickGap={30}
                />
                <YAxis
                  tickFormatter={createCompactFormatter(false)}
                />
                <CartesianGrid strokeDasharray="3 3" />
                <RechartsTooltip
                  formatter={customTooltipFormatter}
                  labelFormatter={rechartsLabelFormatter}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="Gross Revenue"
                  stroke={CHART_COLORS.purple}
                  strokeWidth={2}
                  fillOpacity={1}
                  fill="url(#grossRevenue)"
                  onMouseEnter={(data, index) => handleMouseEnter({ ...data, dataKey: "Gross Revenue" }, index)}
                />
                <Area
                  type="monotone"
                  dataKey="Net Revenue"
                  stroke={CHART_COLORS.green}
                  strokeWidth={2}
                  fillOpacity={1}
                  fill="url(#netRevenue)"
                  onMouseEnter={(data, index) => handleMouseEnter({ ...data, dataKey: "Net Revenue" }, index)}
                />
                </AreaChart>
              </div>
            </ChartContextMenu>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
