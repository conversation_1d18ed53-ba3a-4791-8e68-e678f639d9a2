export type DisplayMode = 'value' | 'percent';

export interface ChartFilters {
  startDate: string;
  endDate: string;
  currency: string;
  groupBy: string;
  brands: string[];
  brandGroups: string[];
  salesChannels: string[];
  countryNames: string[];
}

export interface ChartProps {
  chartData: Record<string, string | number | null>[];
  loading: boolean;
  currency: string;
}

export interface ChartWithToggleProps extends ChartProps {
  displayMode: DisplayMode;
  onDisplayModeChange: (mode: DisplayMode) => void;
}
