import { GroupedKpiResponse, SimpleKpiResponse, fetchKpiData } from '@/lib/api/dashboard-client'
import { useEffect, useState } from "react"

import { ChartFilters } from "./types"

export function useChartData(filters: ChartFilters, getQueryParams: () => string) {
  const [kpiData, setKpiData] = useState<SimpleKpiResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        setError(null)

        // Get the query string from getQueryParams
        const queryString = getQueryParams();
        console.log('[DEBUG] useChartData - Original query params:', queryString);
        
        // Parse it into a URLSearchParams object
        const searchParams = new URLSearchParams(queryString);
        
        // DIAGNOSTIC LOG: Check what groupByTime was passed from context
        const originalGroupByTime = searchParams.get('groupByTime');
        console.log('[DEBUG] useChartData - Original groupByTime from context:', originalGroupByTime);
        console.log('[DEBUG] useChartData - Date range:', searchParams.get('startDate'), 'to', searchParams.get('endDate'));
        
        // FIX: Use the smart grouping from context instead of hard-coding 'day'
        const contextGroupByTime = searchParams.get('groupByTime');
        if (contextGroupByTime) {
          console.log('[DEBUG] useChartData - Using smart groupByTime from context:', contextGroupByTime);
          // Keep the context groupByTime - don't override it
        } else {
          console.log('[DEBUG] useChartData - No groupByTime from context, defaulting to "day"');
          searchParams.set('groupByTime', 'day');
        }
        
        // Add the KPIs
        const kpis = [
          'Gross Revenue',
          'Net Revenue',
          'Discount',
          'Refund',
          'Gross Margin',
          '% Gross Margin',
          'Landed Cost',
          '% Landed Cost',
          'Fulfillment Cost',
          '% Fulfillment Cost',
          'Transaction Cost',
          '% Transaction Cost',
          'Adspend',
          '% Adspend',
          'Contribution Margin',
          '% Contribution Margin'
        ];
        
        // Clear any existing kpis and add the new ones
        searchParams.delete('kpis');
        kpis.forEach(kpi => {
          searchParams.append('kpis', kpi);
        });
        
        // Build properly typed query parameters for the API
        const params = {
          startDate: searchParams.get('startDate') || filters.startDate,
          endDate: searchParams.get('endDate') || filters.endDate,
          currency: (searchParams.get('currency') || filters.currency) as 'CAD' | 'USD',
          groupByTime: (searchParams.get('groupByTime') || 'day') as 'day' | 'week' | 'month',
          brands: searchParams.get('brands')?.split(',').filter(Boolean) || filters.brands,
          salesChannels: searchParams.get('salesChannels')?.split(',').filter(Boolean) || filters.salesChannels,
          countryNames: searchParams.get('countryNames')?.split(',').filter(Boolean) || filters.countryNames,
          kpis: kpis
        };
        
        console.log('[DEBUG] useChartData - Final API params:', params);

        // Pass the properly typed parameters to the API client
        const data = await fetchKpiData(params);
        
        // Check if the response is grouped by dimension
        if ('Gross Revenue' in data && typeof data['Gross Revenue'] === 'object' && !('summary' in data['Gross Revenue'])) {
          console.warn('Received grouped response, but this component only supports simple responses');
          // Handle the case where we get a grouped response - we'll just use the first dimension
          const firstDimension = Object.keys(data['Gross Revenue'])[0];
          if (firstDimension) {
            const simpleData: SimpleKpiResponse = {};
            Object.keys(data).forEach(kpiName => {
              simpleData[kpiName] = (data as GroupedKpiResponse)[kpiName][firstDimension];
            });
            setKpiData(simpleData);
          } else {
            throw new Error('No dimension data available in grouped response');
          }
        } else {
          // It's a simple response, we can use it directly
          setKpiData(data as SimpleKpiResponse);
        }
      } catch (err) {
        console.error("Failed to load chart data:", err)
        setError("Failed to load chart data")
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [filters, getQueryParams])

  // Process data for charts
  const processChartData = () => {
    if (!kpiData) return []

    // Get all dates from any KPI
    const firstKpi = Object.keys(kpiData)[0]
    if (!firstKpi || !kpiData[firstKpi]?.timeSeries) return []

    // Create a map of dates to data points
    const dateMap: Record<string, Record<string, string | number | null>> = {}

    // Initialize with dates
    kpiData[firstKpi].timeSeries.forEach((point) => {
      dateMap[point.date] = { date: point.date }
    })

    // Add values for each KPI
    Object.entries(kpiData).forEach(([kpiName, data]) => {
      data.timeSeries.forEach((point) => {
        if (dateMap[point.date]) {
          dateMap[point.date][kpiName] = point.value
        }
      })
    })

    // Convert to array and sort by date
    return Object.values(dateMap).sort((a, b) => {
      const dateA = a.date as string;
      const dateB = b.date as string;
      return new Date(dateA).getTime() - new Date(dateB).getTime();
    })
  }

  const chartData = processChartData()

  return {
    chartData,
    loading,
    error
  }
}
