import {
  formatCurrency as formatCurrencyUtil,
  formatDate as formatDateUtil,
  formatPercent as formatPercentUtil,
} from "@/lib/chart-utils"

export const createFormatters = (currency: string) => {
  const formatCurrency = (value: number | null) => formatCurrencyUtil(value, currency);
  const formatPercent = formatPercentUtil;
  const rechartsDateFormatter = (value: string) => formatDateUtil(value);
  const rechartsLabelFormatter = (label: string) => formatDateUtil(label);

  const customTooltipFormatter = (value: unknown, name: string) => {
    if (typeof value !== 'number') return 'N/A';
    
    if (name.startsWith("% ")) {
      return formatPercent(value)
    }
    return formatCurrency(value)
  }

  return {
    formatCurrency,
    formatPercent,
    rechartsDateFormatter,
    rechartsLabelFormatter,
    customTooltipFormatter,
  };
};

export const createCompactFormatter = (isPercent: boolean) => (value: number) => {
  if (isPercent) {
    return new Intl.NumberFormat("en-CA", {
      style: "percent",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  }
  
  return new Intl.NumberFormat("en-CA", {
    notation: "compact",
    compactDisplay: "short",
  }).format(value);
};
