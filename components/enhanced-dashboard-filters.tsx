"use client"

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from './ui/card';
import { ChevronDown, ChevronUp, Filter, RotateCcw } from 'lucide-react';
import { GroupingOption, useFilters } from '@/lib/contexts/filter-context';
import React, { useState } from 'react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';

import { BrandFilterDialog } from './filters/brand-filter-dialog';
import { Button } from './ui/button';
import { CountryFilterDialog } from './filters/country-filter-dialog';
// Import our new components
import { DateRangeFilter } from './filters/date-range-filter';
import { FilterBadges } from './filters/filter-badges';
import { FilterButtons } from './filters/filter-buttons';
import { FilterSummary } from './filters/filter-summary';
import { SalesChannelFilterDialog } from './filters/sales-channel-filter-dialog';
import { SavedViewsMenu } from './saved-views-menu';
import { Separator } from './ui/separator';
import { SingleBrandFilterDialog } from './filters/single-brand-filter-dialog';
import { cn } from '@/lib/utils';
import { useBrandDeepDive } from '@/lib/contexts/brand-deep-dive-context';

interface EnhancedDashboardFiltersProps {
  showBrandFilter?: boolean;
  showCountryFilter?: boolean;
  showCurrencyFilter?: boolean;
  showGroupByFilter?: boolean;
  pageType?: 'brand-deep-dive' | 'marketing-dashboard' | 'dashboard';
  contextType?: 'dashboard' | 'brandDeepDive';
  compact?: boolean;
}

export function EnhancedDashboardFilters({
  showBrandFilter = true,
  showCountryFilter = true,
  showCurrencyFilter = true,
  showGroupByFilter = true,
  pageType,
  contextType = 'dashboard',
  compact = false,
}: EnhancedDashboardFiltersProps) {
  // Context handling
  const dashboardContext = useFilters();
  
  const useSafeBrandDeepDive = () => {
    try {
      return useBrandDeepDive();
    } catch {
      return null;
    }
  };
  
  const brandDeepDiveContextValue = useSafeBrandDeepDive();
  const shouldUseBrandDeepDiveContext = contextType === 'brandDeepDive';
  
  const {
    filters,
    setFilters,
    availableBrands,
    availableBrandGroups = [],
    availableSalesChannels,
    availableCountries,
    isLoading,
    error: contextError
  } = (shouldUseBrandDeepDiveContext && brandDeepDiveContextValue)
    ? {
        filters: {
          startDate: brandDeepDiveContextValue.state.startDate,
          endDate: brandDeepDiveContextValue.state.endDate,
          currency: brandDeepDiveContextValue.state.currency,
          groupBy: brandDeepDiveContextValue.state.groupBy,
          brands: brandDeepDiveContextValue.state.selectedBrand ? [brandDeepDiveContextValue.state.selectedBrand] : [],
          brandGroups: [],
          salesChannels: brandDeepDiveContextValue.state.salesChannels,
          countryNames: brandDeepDiveContextValue.state.countryNames,
        },
        setFilters: (newState: React.SetStateAction<{
          startDate: string;
          endDate: string;
          currency: 'CAD' | 'USD';
          groupBy: GroupingOption;
          brands: string[];
          brandGroups: string[];
          salesChannels: string[];
          countryNames: string[];
        }>) => {
          if (typeof newState === 'function') {
            const currentMappedState = {
              startDate: brandDeepDiveContextValue!.state.startDate,
              endDate: brandDeepDiveContextValue!.state.endDate,
              currency: brandDeepDiveContextValue!.state.currency,
              groupBy: brandDeepDiveContextValue!.state.groupBy,
              brands: brandDeepDiveContextValue!.state.selectedBrand ? [brandDeepDiveContextValue!.state.selectedBrand] : [],
              brandGroups: [],
              salesChannels: brandDeepDiveContextValue!.state.salesChannels,
              countryNames: brandDeepDiveContextValue!.state.countryNames,
            };
            
            const updatedState = newState(currentMappedState);
            
            brandDeepDiveContextValue!.setState((prev) => ({
              ...prev,
              startDate: updatedState.startDate,
              endDate: updatedState.endDate,
              currency: updatedState.currency,
              groupBy: updatedState.groupBy,
              selectedBrand: updatedState.brands?.[0] || null,
              salesChannels: updatedState.salesChannels,
              countryNames: updatedState.countryNames,
            }));
          } else {
            brandDeepDiveContextValue!.setState((prev) => ({
              ...prev,
              startDate: newState.startDate ?? prev.startDate,
              endDate: newState.endDate ?? prev.endDate,
              currency: newState.currency ?? prev.currency,
              groupBy: newState.groupBy ?? prev.groupBy,
              selectedBrand: newState.brands?.[0] ?? prev.selectedBrand,
              salesChannels: newState.salesChannels ?? prev.salesChannels,
              countryNames: newState.countryNames ?? prev.countryNames,
            }));
          }
        },
        availableBrands: brandDeepDiveContextValue.availableBrands,
        availableBrandGroups: [],
        availableSalesChannels: brandDeepDiveContextValue.availableSalesChannels,
        availableCountries: brandDeepDiveContextValue.availableCountries,
        isLoading: brandDeepDiveContextValue.isLoading,
        error: brandDeepDiveContextValue.error
      }
    : dashboardContext;

  // Local state
  const [startDate, setStartDate] = useState(filters.startDate);
  const [endDate, setEndDate] = useState(filters.endDate);
  const [brandSearch, setBrandSearch] = useState('');
  const [countrySearch, setCountrySearch] = useState('');
  const [channelSearch, setChannelSearch] = useState('');
  const [tempBrands, setTempBrands] = useState<string[]>(filters.brands);
  const [tempBrandGroups, setTempBrandGroups] = useState<string[]>(filters.brandGroups);
  const [tempCountries, setTempCountries] = useState<string[]>(filters.countryNames);
  const [tempSalesChannels, setTempSalesChannels] = useState<string[]>(filters.salesChannels);
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Dialog states
  const [brandDialogOpen, setBrandDialogOpen] = useState(false);
  const [countryDialogOpen, setCountryDialogOpen] = useState(false);
  const [salesChannelDialogOpen, setSalesChannelDialogOpen] = useState(false);

  // Single brand selection state for brand deep dive
  const [tempSelectedBrand, setTempSelectedBrand] = useState<string | null>(
    shouldUseBrandDeepDiveContext ? (filters.brands[0] || null) : null
  );

  // Auto-apply date filter
  React.useEffect(() => {
    if (startDate === filters.startDate && endDate === filters.endDate) {
      return;
    }
    
    const timerId = setTimeout(() => {
      setFilters(prev => ({
        ...prev,
        startDate,
        endDate
      }));
    }, 200);
    
    return () => clearTimeout(timerId);
  }, [startDate, endDate, filters.startDate, filters.endDate, setFilters]);

  // Apply functions
  const applyBrandSelections = () => {
    setFilters(prev => ({
      ...prev,
      brands: tempBrands,
      brandGroups: tempBrandGroups
    }));
  };

  // Single brand selection apply function
  const applySingleBrandSelection = () => {
    setFilters(prev => ({
      ...prev,
      brands: tempSelectedBrand ? [tempSelectedBrand] : [],
      brandGroups: []
    }));
  };

  const applyCountrySelections = () => {
    setFilters(prev => ({
      ...prev,
      countryNames: tempCountries
    }));
  };
  
  const applySalesChannelSelections = () => {
    setFilters(prev => ({
      ...prev,
      salesChannels: tempSalesChannels
    }));
  };

  // Handle changes
  const handleBrandChangeInDialog = (brand: string, checked: boolean | string) => {
    const newTempBrands = checked ? [...tempBrands, brand] : tempBrands.filter(b => b !== brand);
    setTempBrands(newTempBrands);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, brands: newTempBrands, brandGroups: tempBrandGroups }));
    }
  };
  
  const handleBrandGroupChangeInDialog = (group: string, checked: boolean | string) => {
    const newTempBrandGroups = checked ? [...tempBrandGroups, group] : tempBrandGroups.filter(g => g !== group);
    setTempBrandGroups(newTempBrandGroups);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, brands: tempBrands, brandGroups: newTempBrandGroups }));
    }
  };
  
  const handleCountryChangeInDialog = (countryName: string, checked: boolean | string) => {
    const newTempCountries = checked ? [...tempCountries, countryName] : tempCountries.filter(c => c !== countryName);
    setTempCountries(newTempCountries);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, countryNames: newTempCountries }));
    }
  };
  
  const handleSalesChannelChangeInDialog = (channel: string, checked: boolean | string) => {
    const newTempSalesChannels = checked ? [...tempSalesChannels, channel] : tempSalesChannels.filter(c => c !== channel);
    setTempSalesChannels(newTempSalesChannels);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, salesChannels: newTempSalesChannels }));
    }
  };

  const handleCurrencyChange = (currency: string) => {
    setFilters(prev => ({
      ...prev,
      currency: currency as 'CAD' | 'USD'
    }));
  };

  const handleGroupByChange = (value: string) => {
    setFilters(prev => ({
      ...prev,
      groupBy: value as GroupingOption
    }));
  };

  const resetFilters = () => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 90);

    const formattedStartDate = startDate.toISOString().split('T')[0];
    const formattedEndDate = endDate.toISOString().split('T')[0];

    setStartDate(formattedStartDate);
    setEndDate(formattedEndDate);
    setTempBrands([]);
    setTempBrandGroups([]);
    setTempCountries([]);
    setTempSalesChannels([]);

    setFilters({
      startDate: formattedStartDate,
      endDate: formattedEndDate,
      currency: 'CAD',
      groupBy: 'month',
      brands: [],
      brandGroups: [],
      salesChannels: [],
      countryNames: []
    });
  };

  // Remove functions
  const removeBrand = (brand: string) => {
    setFilters(prev => ({
      ...prev,
      brands: prev.brands.filter(b => b !== brand)
    }));
  };

  const removeBrandGroup = (group: string) => {
    setFilters(prev => ({
      ...prev,
      brandGroups: prev.brandGroups.filter(g => g !== group)
    }));
  };

  const removeCountry = (country: string) => {
    setFilters(prev => ({
      ...prev,
      countryNames: prev.countryNames.filter(c => c !== country)
    }));
  };

  const removeSalesChannel = (channel: string) => {
    setFilters(prev => ({
      ...prev,
      salesChannels: prev.salesChannels.filter(c => c !== channel)
    }));
  };

  // Select all functions
  const selectAllBrands = (select: boolean) => {
    const newBrands = select ? availableBrands : [];
    setTempBrands(newBrands);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, brands: newBrands, brandGroups: tempBrandGroups }));
    }
  };
  
  const selectAllBrandGroups = (select: boolean) => {
    const newBrandGroups = select ? availableBrandGroups : [];
    setTempBrandGroups(newBrandGroups);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, brands: tempBrands, brandGroups: newBrandGroups }));
    }
  };
  
  const selectAllCountries = (select: boolean) => {
    const newCountries = select ? availableCountries.filter(c => c.hasSales).map(c => c.name) : [];
    setTempCountries(newCountries);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, countryNames: newCountries }));
    }
  };
  
  const selectAllSalesChannels = (select: boolean) => {
    const newSalesChannels = select ? availableSalesChannels : [];
    setTempSalesChannels(newSalesChannels);
    if (shouldUseBrandDeepDiveContext) {
      setFilters(prev => ({ ...prev, salesChannels: newSalesChannels }));
    }
  };

  // Count active filters
  const activeFilterCount = filters.brands.length + filters.brandGroups.length + 
    filters.countryNames.length + filters.salesChannels.length;

  if (contextError) {
    return (
      <Card className="border-destructive">
        <CardContent className="p-4">
          <div className="text-destructive">{contextError}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full shadow-lg border-border/50 bg-gradient-to-br from-background to-muted/20">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <Filter className="h-4 w-4 text-primary" />
            </div>
            <div>
              <CardTitle className="text-base font-semibold">Filters</CardTitle>
              {activeFilterCount > 0 && (
                <p className="text-xs text-muted-foreground mt-1">
                  {activeFilterCount} filter{activeFilterCount > 1 ? 's' : ''} active
                </p>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {pageType && <SavedViewsMenu pageType={pageType} />}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" onClick={resetFilters} className="text-xs">
                    <RotateCcw className="h-3 w-3 mr-1" />
                    Reset
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Reset all filters to default values</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="text-xs"
            >
              {isCollapsed ? <ChevronDown className="h-3 w-3" /> : <ChevronUp className="h-3 w-3" />}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      {/* Filter Summary - shown when collapsed */}
      {isCollapsed && (
        <CardContent className="px-6 pb-6">
          <FilterSummary
            startDate={filters.startDate}
            endDate={filters.endDate}
            currency={filters.currency}
            brands={filters.brands}
            brandGroups={filters.brandGroups}
            countries={filters.countryNames}
            salesChannels={filters.salesChannels}
            onRemoveBrand={removeBrand}
            onRemoveBrandGroup={removeBrandGroup}
            onRemoveCountry={removeCountry}
            onRemoveSalesChannel={removeSalesChannel}
            onOpenFilters={() => setIsCollapsed(false)}
          />
        </CardContent>
      )}
      
      <CardContent className={cn(
        "transition-all duration-300 ease-in-out space-y-4",
        compact ? "p-3" : "px-6 pb-6",
        isCollapsed && "hidden"
      )}>
        {/* Date Range Section */}
        <DateRangeFilter
          startDate={startDate}
          endDate={endDate}
          onStartDateChange={setStartDate}
          onEndDateChange={setEndDate}
          showCurrencyFilter={showCurrencyFilter}
          showGroupByFilter={showGroupByFilter}
          currency={filters.currency}
          groupBy={filters.groupBy}
          onCurrencyChange={handleCurrencyChange}
          onGroupByChange={handleGroupByChange}
        />

        <Separator />

        {/* Filter Buttons Section */}
        <FilterButtons
          showBrandFilter={showBrandFilter}
          showCountryFilter={showCountryFilter}
          brandsCount={filters.brands.length}
          brandGroupsCount={filters.brandGroups.length}
          countriesCount={filters.countryNames.length}
          salesChannelsCount={filters.salesChannels.length}
          brandButtonText={shouldUseBrandDeepDiveContext ? "Brand" : "Brands"}
          onBrandClick={() => {
            if (shouldUseBrandDeepDiveContext) {
              setTempSelectedBrand(filters.brands[0] || null);
              setBrandSearch('');
              setBrandDialogOpen(true);
            } else {
              setTempBrands(filters.brands);
              setTempBrandGroups(filters.brandGroups);
              setBrandSearch('');
              setBrandDialogOpen(true);
            }
          }}
          onCountryClick={() => {
            setTempCountries(filters.countryNames);
            setCountrySearch('');
            setCountryDialogOpen(true);
          }}
          onSalesChannelClick={() => {
            setTempSalesChannels(filters.salesChannels);
            setChannelSearch('');
            setSalesChannelDialogOpen(true);
          }}
        />

        {/* Active Filters Pills */}
        <FilterBadges
          brands={filters.brands}
          brandGroups={filters.brandGroups}
          countries={filters.countryNames}
          salesChannels={filters.salesChannels}
          onRemoveBrand={removeBrand}
          onRemoveBrandGroup={removeBrandGroup}
          onRemoveCountry={removeCountry}
          onRemoveSalesChannel={removeSalesChannel}
        />
      </CardContent>

      {/* Filter Dialogs */}
      {shouldUseBrandDeepDiveContext ? (
        <SingleBrandFilterDialog
          open={brandDialogOpen}
          onOpenChange={setBrandDialogOpen}
          availableBrands={availableBrands}
          selectedBrand={tempSelectedBrand}
          brandSearch={brandSearch}
          setBrandSearch={setBrandSearch}
          isLoading={isLoading}
          onBrandSelect={setTempSelectedBrand}
          onApply={applySingleBrandSelection}
        />
      ) : (
        <BrandFilterDialog
          open={brandDialogOpen}
          onOpenChange={setBrandDialogOpen}
          availableBrands={availableBrands}
          availableBrandGroups={availableBrandGroups}
          tempBrands={tempBrands}
          tempBrandGroups={tempBrandGroups}
          brandSearch={brandSearch}
          setBrandSearch={setBrandSearch}
          isLoading={isLoading}
          onBrandChange={handleBrandChangeInDialog}
          onBrandGroupChange={handleBrandGroupChangeInDialog}
          onSelectAllBrands={selectAllBrands}
          onSelectAllBrandGroups={selectAllBrandGroups}
          onApply={applyBrandSelections}
        />
      )}

      <CountryFilterDialog
        open={countryDialogOpen}
        onOpenChange={setCountryDialogOpen}
        availableCountries={availableCountries}
        tempCountries={tempCountries}
        countrySearch={countrySearch}
        setCountrySearch={setCountrySearch}
        isLoading={isLoading}
        onCountryChange={handleCountryChangeInDialog}
        onSelectAllCountries={selectAllCountries}
        onApply={applyCountrySelections}
      />

      <SalesChannelFilterDialog
        open={salesChannelDialogOpen}
        onOpenChange={setSalesChannelDialogOpen}
        availableSalesChannels={availableSalesChannels}
        tempSalesChannels={tempSalesChannels}
        channelSearch={channelSearch}
        setChannelSearch={setChannelSearch}
        isLoading={isLoading}
        onSalesChannelChange={handleSalesChannelChangeInDialog}
        onSelectAllSalesChannels={selectAllSalesChannels}
        onApply={applySalesChannelSelections}
      />
    </Card>
  );
}
