'use client';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { CircleDollarSign, Package, Users } from "lucide-react";

import React from 'react';

const AdditionalMetrics = () => {
  return (
    <Card className="shadow-md">
      <CardHeader className="bg-primary/5">
        <CardTitle className="text-xl flex items-center gap-2">
          <CircleDollarSign className="h-5 w-5" />
          Additional Metrics
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-6">
          <div className="text-center p-4 border border-dashed rounded-md bg-muted/20">
            <p className="text-muted-foreground mb-2">
              This section will display additional metrics once data sources are connected.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-md">
              <h3 className="font-medium flex items-center gap-2 mb-3">
                <Package className="h-4 w-4" />
                Supply Metrics
              </h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Inventory Turnover</li>
                <li>• Inventory Value</li>
                <li>• Supply Chain Efficiency</li>
                <li className="italic">Coming soon</li>
              </ul>
            </div>
            
            <div className="p-4 border rounded-md">
              <h3 className="font-medium flex items-center gap-2 mb-3">
                <Users className="h-4 w-4" />
                Customer Metrics
              </h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Net Promoter Score</li>
                <li>• Customer Satisfaction Score</li>
                <li>• Customer Retention Rate</li>
                <li className="italic">Coming soon</li>
              </ul>
            </div>
          </div>
          
          <div className="flex justify-center">
            <button className="text-sm text-primary hover:underline">
              Learn more about data integration
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdditionalMetrics;
