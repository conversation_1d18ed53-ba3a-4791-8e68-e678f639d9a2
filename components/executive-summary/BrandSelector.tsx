'use client';

import * as React from "react";

import { Check, ChevronsUpDown } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { BrandInfo } from "./types";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface BrandSelectorProps {
  brands: BrandInfo[];
  selectedBrand: BrandInfo | null;
  onBrandSelect: (brand: BrandInfo) => void;
  isLoading?: boolean;
  error?: Error | null;
}

export function BrandSelector({
  brands,
  selectedBrand,
  onBrandSelect,
  isLoading = false,
  error = null
}: BrandSelectorProps) {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {selectedBrand ? selectedBrand.name : "Select a brand..."}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <Command>
          <CommandInput placeholder="Search brands..." />
          <CommandEmpty>
            {isLoading ? "Loading brands..." : error ? error.message : "No brand found."}
          </CommandEmpty>
          <CommandGroup>
            {/* List individual brands */}
            {brands.map((brand) => (
              <CommandItem
                key={brand.name}
                value={brand.name}
                onSelect={() => {
                  onBrandSelect(brand);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    selectedBrand?.name === brand.name ? "opacity-100" : "opacity-0"
                  )}
                />
                {brand.name}
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export default BrandSelector;
