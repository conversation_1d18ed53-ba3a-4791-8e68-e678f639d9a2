'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { FlexibleKpiResponse, TrendData, ValidCurrency, isSimpleKpiResponse } from './types';
import { LineChart, TrendingDown, TrendingUp } from "lucide-react";
import { formatCurrency, formatPercentage } from './utils';

import React from 'react';

interface DataInsightsProps {
  kpiData: FlexibleKpiResponse;
  weekTrend: TrendData;
  monthTrend: TrendData;
  yearTrend: TrendData;
  selectedBrand: string;
  currency: ValidCurrency;
}

const DataInsights: React.FC<DataInsightsProps> = ({
  kpiData,
  weekTrend,
  monthTrend,
  yearTrend,
  selectedBrand,
  currency,
}) => {
  // Helper function to get trend indicator
  const getTrendIndicator = (value: number | null) => {
    if (value === null) {
      return {
        icon: <LineChart className="h-4 w-4" />,
        colorClass: 'text-gray-500 bg-gray-100 dark:bg-gray-800',
      };
    }
    
    if (value > 0) {
      return {
        icon: <TrendingUp className="h-4 w-4" />,
        colorClass: 'text-green-500 bg-green-700/20',
      };
    }
    
    return {
      icon: <TrendingDown className="h-4 w-4" />,
      colorClass: 'text-red-500 bg-red-700/20',
    };
  };
  
  // Helper function to get KPI value
  const getKpiValue = (kpiName: string): number | null => {
    if (isSimpleKpiResponse(kpiData)) {
      return kpiData[kpiName]?.summary?.value ?? null;
    }
    
    // For grouped responses, search in each group
    for (const groupName in kpiData) {
      if (kpiData[groupName][kpiName]) {
        return kpiData[groupName][kpiName].summary.value;
      }
    }
    
    return null;
  };
  
  // Get net revenue value
  const netRevenue = getKpiValue('Net Revenue');
  
  // Get trend indicators
  const weekTrendIndicator = getTrendIndicator(weekTrend.change);
  const monthTrendIndicator = getTrendIndicator(monthTrend.change);
  const yearTrendIndicator = getTrendIndicator(yearTrend.change);
  
  return (
    <Card className="shadow-md">
      <CardHeader className="bg-primary/5">
        <CardTitle className="text-xl">Data Insights</CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-6">
          {/* Revenue Analysis */}
          <div>
            <h3 className="text-lg font-medium mb-3">Revenue Analysis</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="p-4 border rounded-md">
                <div className="flex justify-between items-start mb-2">
                  <span className="text-sm text-muted-foreground">Current Month</span>
                  <span className={`p-1 rounded-full ${weekTrendIndicator.colorClass}`}>
                    {weekTrendIndicator.icon}
                  </span>
                </div>
                <p className="text-2xl font-bold">{formatCurrency(netRevenue, currency)}</p>
                <p className="text-sm mt-1">
                  {weekTrend.change !== null ? (
                    <>
                      <span className={weekTrend.change > 0 ? 'text-green-600' : 'text-red-600'}>
                        {weekTrend.change > 0 ? '+' : ''}{formatPercentage(weekTrend.change)}
                      </span>
                      <span className="text-muted-foreground"> week-over-week</span>
                    </>
                  ) : (
                    <span className="text-muted-foreground">No weekly trend data</span>
                  )}
                </p>
              </div>
              
              <div className="p-4 border rounded-md">
                <div className="flex justify-between items-start mb-2">
                  <span className="text-sm text-muted-foreground">Monthly Change</span>
                  <span className={`p-1 rounded-full ${monthTrendIndicator.colorClass}`}>
                    {monthTrendIndicator.icon}
                  </span>
                </div>
                <p className="text-2xl font-bold">
                  {monthTrend.change !== null ? formatPercentage(monthTrend.change) : 'N/A'}
                </p>
                <p className="text-sm mt-1 text-muted-foreground">
                  Compared to previous month
                </p>
              </div>
              
              <div className="p-4 border rounded-md">
                <div className="flex justify-between items-start mb-2">
                  <span className="text-sm text-muted-foreground">Annual Growth</span>
                  <span className={`p-1 rounded-full ${yearTrendIndicator.colorClass}`}>
                    {yearTrendIndicator.icon}
                  </span>
                </div>
                <p className="text-2xl font-bold">
                  {yearTrend.change !== null ? formatPercentage(yearTrend.change) : 'N/A'}
                </p>
                <p className="text-sm mt-1 text-muted-foreground">
                  Year-over-year comparison
                </p>
              </div>
            </div>
          </div>
          
          {/* Key Observations */}
          <div>
            <h3 className="text-lg font-medium mb-3">Key Observations</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-2">Monthly Revenue Trend</h4>
                <p className="text-sm text-muted-foreground">
                  {monthTrend.change === null ? (
                    "Insufficient data to analyze monthly revenue trends."
                  ) : monthTrend.change > 5 ? (
                    `${selectedBrand} is showing strong monthly growth (${formatPercentage(monthTrend.change)}), indicating positive momentum in the market.`
                  ) : monthTrend.change > 0 ? (
                    `${selectedBrand} is showing modest growth (${formatPercentage(monthTrend.change)}) compared to the previous month.`
                  ) : monthTrend.change > -5 ? (
                    `${selectedBrand} is experiencing a slight decline (${formatPercentage(monthTrend.change)}) compared to the previous month.`
                  ) : (
                    `${selectedBrand} is showing a significant decline (${formatPercentage(monthTrend.change)}) that requires attention.`
                  )}
                </p>
              </div>
              
              <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-2">Gross Margin Analysis</h4>
                <p className="text-sm text-muted-foreground">
                  {getKpiValue('Gross Margin') === null ? (
                    "Gross margin data is not available for analysis."
                  ) : (
                    `Gross margin is currently ${formatCurrency(getKpiValue('Gross Margin'), currency)}, representing ${
                      getKpiValue('% Gross Margin') !== null 
                        ? formatPercentage(getKpiValue('% Gross Margin')) 
                        : 'an unknown percentage'
                    } of net revenue.`
                  )}
                </p>
              </div>
            </div>
          </div>
          
          {/* Strategic Recommendations */}
          <div>
            <h3 className="text-lg font-medium mb-3">Strategic Recommendations</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-2">Focus Areas</h4>
                <ul className="text-sm text-muted-foreground space-y-2">
                  {getKpiValue('Adspend') !== null && netRevenue !== null && 
                   (getKpiValue('Adspend')! / netRevenue) > 0.3 && (
                    <li>• Review advertising efficiency as ad spend is high relative to revenue</li>
                  )}
                  
                  {monthTrend.change !== null && monthTrend.change < 0 && (
                    <li>• Investigate causes of revenue decline in the current month</li>
                  )}
                  
                  {yearTrend.change !== null && yearTrend.change > 10 && (
                    <li>• Build on strong annual growth by expanding successful strategies</li>
                  )}
                  
                  <li>• Monitor gross margin to ensure profitability targets are maintained</li>
                </ul>
              </div>
              
              <div className="p-4 border rounded-md">
                <h4 className="font-medium mb-2">Growth Opportunities</h4>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li>• Analyze top-performing products to identify expansion opportunities</li>
                  <li>• Review marketing channel performance to optimize spend allocation</li>
                  <li>• Consider cross-brand promotions to leverage brand synergies</li>
                  <li>• Explore new markets based on current performance indicators</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DataInsights;
