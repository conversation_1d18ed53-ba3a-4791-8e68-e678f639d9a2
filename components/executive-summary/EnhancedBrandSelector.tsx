'use client';

import * as React from "react";

import { Building2, Check, ChevronsUpDown, Search } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { BrandInfo } from "./types";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

interface EnhancedBrandSelectorProps {
  brands: BrandInfo[];
  selectedBrand: BrandInfo | null;
  onBrandSelect: (brand: BrandInfo) => void;
  isLoading?: boolean;
  error?: Error | null;
  compact?: boolean;
}

export function EnhancedBrandSelector({
  brands,
  selectedBrand,
  onBrandSelect,
  isLoading = false,
  error = null,
  compact = false
}: EnhancedBrandSelectorProps) {
  const [open, setOpen] = React.useState(false);

  return (
    <Card className="w-full shadow-sm border-border/50">
      <CardContent className={compact ? "p-3" : "p-4 lg:p-6"}>
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center gap-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <Label className="text-sm font-medium">Brand Selection</Label>
          </div>
          
          {/* Brand Selector */}
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">Select Brand</Label>
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className="w-full justify-between h-9"
                  disabled={isLoading}
                >
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">
                      {selectedBrand ? selectedBrand.name : "Select a brand..."}
                    </span>
                  </div>
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0" align="start">
                <Command>
                  <div className="flex items-center border-b px-3">
                    <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                    <CommandInput 
                      placeholder="Search brands..." 
                      className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                    />
                  </div>
                  <CommandEmpty className="py-6 text-center text-sm">
                    {isLoading ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                        Loading brands...
                      </div>
                    ) : error ? (
                      <div className="text-destructive">{error.message}</div>
                    ) : (
                      "No brand found."
                    )}
                  </CommandEmpty>
                  <CommandGroup className="max-h-64 overflow-auto">
                    {/* All Brands Option */}
                    <CommandItem
                      value="all-brands"
                      onSelect={() => {
                        onBrandSelect({ name: "All Brands" } as BrandInfo);
                        setOpen(false);
                      }}
                      className="flex items-center gap-2"
                    >
                      <Check
                        className={cn(
                          "h-4 w-4",
                          selectedBrand?.name === "All Brands" ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">All Brands</span>
                    </CommandItem>
                    
                    {/* Individual Brands */}
                    {brands.map((brand) => (
                      <CommandItem
                        key={brand.name}
                        value={brand.name}
                        onSelect={() => {
                          onBrandSelect(brand);
                          setOpen(false);
                        }}
                        className="flex items-center gap-2"
                      >
                        <Check
                          className={cn(
                            "h-4 w-4",
                            selectedBrand?.name === brand.name ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <div className="h-3 w-3 rounded-full bg-primary/20"></div>
                        <span>{brand.name}</span>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
          
          {/* Selected Brand Display */}
          {selectedBrand && (
            <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-md">
              <div className="h-2 w-2 bg-primary rounded-full"></div>
              <span className="text-sm font-medium">
                Currently viewing: {selectedBrand.name}
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default EnhancedBrandSelector;
