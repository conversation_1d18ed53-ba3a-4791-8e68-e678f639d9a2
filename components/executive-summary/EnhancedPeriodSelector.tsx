'use client';

import { Calendar, HelpCircle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Period, PeriodType } from './types';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import React from 'react';
import { format } from 'date-fns';

interface EnhancedPeriodSelectorProps {
  selectedPeriodType: PeriodType;
  selectedPeriod: Period;
  availablePeriods: Period[];
  onPeriodTypeChange: (type: PeriodType) => void;
  onPeriodChange: (period: Period) => void;
  isLoading: boolean;
  compact?: boolean;
}

const EnhancedPeriodSelector: React.FC<EnhancedPeriodSelectorProps> = ({
  selectedPeriodType,
  selectedPeriod,
  availablePeriods,
  onPeriodTypeChange,
  onPeriodChange,
  isLoading,
  compact = false,
}) => {
  return (
    <Card className="w-full shadow-sm border-border/50">
      <CardContent className={compact ? "p-3" : "p-4 lg:p-6"}>
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <Label className="text-sm font-medium">Time Period</Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs text-xs">
                    Select the time period for the report. Periods marked with an asterisk (*) are incomplete, meaning data may be partial.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          
          {/* Period Type Selection */}
          <div className="space-y-3">
            <Label className="text-xs text-muted-foreground">Period Type</Label>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedPeriodType === PeriodType.MONTH ? "default" : "outline"}
                size="sm"
                onClick={() => onPeriodTypeChange(PeriodType.MONTH)}
                disabled={isLoading}
                className="h-9"
              >
                Month
              </Button>
              <Button
                variant={selectedPeriodType === PeriodType.QUARTER ? "default" : "outline"}
                size="sm"
                onClick={() => onPeriodTypeChange(PeriodType.QUARTER)}
                disabled={isLoading}
                className="h-9"
              >
                Quarter
              </Button>
              <Button
                variant={selectedPeriodType === PeriodType.YEAR ? "default" : "outline"}
                size="sm"
                onClick={() => onPeriodTypeChange(PeriodType.YEAR)}
                disabled={isLoading}
                className="h-9"
              >
                Year
              </Button>
            </div>
          </div>
          
          {/* Period Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">Select Period</Label>
              <Select
                value={`${selectedPeriod.year}-${selectedPeriod.value}`}
                onValueChange={(value) => {
                  const [year, periodValue] = value.split('-').map(Number);
                  const period = availablePeriods.find(
                    p => p.year === year && p.value === periodValue
                  );
                  if (period) {
                    onPeriodChange(period);
                  }
                }}
                disabled={isLoading}
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  {availablePeriods.map((period) => (
                    <SelectItem
                      key={`${period.year}-${period.value}`}
                      value={`${period.year}-${period.value}`}
                    >
                      {period.label}
                      {!period.isComplete && " *"}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* Date Range Display */}
            <div className="space-y-1">
              <Label className="text-xs text-muted-foreground">Date Range</Label>
              <div className="flex items-center h-9 px-3 py-2 border border-input bg-background rounded-md text-sm">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-sm">
                  {format(selectedPeriod.startDate, 'MMM d, yyyy')} - {format(selectedPeriod.endDate, 'MMM d, yyyy')}
                </span>
              </div>
            </div>
          </div>
          
          {/* Incomplete Period Warning */}
          {!selectedPeriod.isComplete && (
            <div className="flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="h-2 w-2 bg-amber-500 rounded-full"></div>
              <p className="text-xs text-amber-700">
                This period is incomplete - data may be partial
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedPeriodSelector;
