'use client';

import { <PERSON>D<PERSON>, <PERSON>Up, Minus, TrendingUp } from "lucide-react";
import { Bar, BarChart } from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { KpiSummaryData, Period, TimeSeriesItem, ValidCurrency } from './types';
import React, { useMemo } from 'react';
import { formatCurrency, formatPercentage } from './utils';

interface KpiCardProps {
  kpiData: KpiSummaryData;
  currency: ValidCurrency;
  period: Period;
  timeSeries?: TimeSeriesItem[];
  className?: string;
  onDoubleClick?: () => void;
}

const KpiCard: React.FC<KpiCardProps> = ({
  kpiData,
  currency,
  period,
  timeSeries,
  className = "",
  onDoubleClick,
}) => {
  // Determine chart grouping based on period duration
  const getChartGrouping = (period: Period): 'daily' | 'weekly' | 'monthly' => {
    const startDate = period.startDate;
    const endDate = period.endDate;
    const diffInDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays <= 31) {
      return 'daily'; // One month or less
    } else if (diffInDays <= 93) {
      return 'weekly'; // Three months or less
    } else {
      return 'monthly'; // More than three months
    }
  };

  // Process time series data for chart
  const chartData = useMemo(() => {
    if (!timeSeries || timeSeries.length === 0) {
      return [];
    }

    const grouping = getChartGrouping(period);
    
    // Sort by date
    const sortedData = [...timeSeries].sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    if (grouping === 'daily') {
      // Return daily data as-is
      return sortedData.map(item => ({
        date: new Date(item.date).toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric' 
        }),
        value: item.value || 0,
        fullDate: item.date
      }));
    } else if (grouping === 'weekly') {
      // Group by week
      const weeklyData: { [key: string]: { value: number; count: number; startDate: string } } = {};
      
      sortedData.forEach(item => {
        const date = new Date(item.date);
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay()); // Start of week (Sunday)
        const weekKey = weekStart.toISOString().split('T')[0];
        
        if (!weeklyData[weekKey]) {
          weeklyData[weekKey] = { value: 0, count: 0, startDate: weekKey };
        }
        weeklyData[weekKey].value += item.value || 0;
        weeklyData[weekKey].count += 1;
      });

      return Object.values(weeklyData).map(week => ({
        date: new Date(week.startDate).toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric' 
        }),
        value: week.value,
        fullDate: week.startDate
      }));
    } else {
      // Group by month
      const monthlyData: { [key: string]: { value: number; count: number } } = {};
      
      sortedData.forEach(item => {
        const date = new Date(item.date);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
        if (!monthlyData[monthKey]) {
          monthlyData[monthKey] = { value: 0, count: 0 };
        }
        monthlyData[monthKey].value += item.value || 0;
        monthlyData[monthKey].count += 1;
      });

      return Object.entries(monthlyData).map(([monthKey, data]) => ({
        date: new Date(monthKey + '-01').toLocaleDateString('en-US', { 
          month: 'short', 
          year: 'numeric' 
        }),
        value: data.value,
        fullDate: monthKey + '-01'
      }));
    }
  }, [timeSeries, period]);

  // Render trend indicator
  const renderTrendIndicator = (value: number | null) => {
    if (value === null) return <Minus className="h-4 w-4 text-muted-foreground" />;
    if (value > 0) return <ArrowUp className="h-4 w-4 text-green-500" />;
    if (value < 0) return <ArrowDown className="h-4 w-4 text-red-500" />;
    return <Minus className="h-4 w-4 text-muted-foreground" />;
  };

  // Get color class for trend
  const getTrendColorClass = (value: number | null) => {
    if (value === null) return 'text-muted-foreground';
    if (value > 0) return 'text-green-600 dark:text-green-400';
    if (value < 0) return 'text-red-600 dark:text-red-400';
    return 'text-muted-foreground';
  };

  const chartConfig = {
    value: {
      label: kpiData.kpi,
      color: "hsl(var(--primary))",
    },
  };

  return (
    <Card
      className={`shadow-sm hover:shadow-md transition-shadow ${className}`}
      onDoubleClick={onDoubleClick}
    >
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {kpiData.kpi}
          </CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </div>
        <div className="space-y-1">
          <div className="text-2xl font-bold">
            {formatCurrency(kpiData.mtd, currency)}
          </div>
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              {renderTrendIndicator(kpiData.vsbudget)}
              <span className={getTrendColorClass(kpiData.vsbudget)}>
                {kpiData.vsbudget !== null ? formatPercentage(kpiData.vsbudget) : 'N/A'} vs Budget
              </span>
            </div>
            <div className="flex items-center gap-1">
              {renderTrendIndicator(kpiData.vslastyear)}
              <span className={getTrendColorClass(kpiData.vslastyear)}>
                {kpiData.vslastyear !== null ? formatPercentage(kpiData.vslastyear) : 'N/A'} vs Last Year
              </span>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {chartData.length > 0 ? (
          <ChartContainer config={chartConfig} className="h-[80px] w-full">
            <BarChart data={chartData}>
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    className="w-[150px]"
                    nameKey="value"
                    labelFormatter={(value) => {
                      const item = chartData.find(d => d.date === value);
                      return item ? new Date(item.fullDate).toLocaleDateString() : value;
                    }}
                    formatter={(value) => [
                      formatCurrency(Number(value), currency),
                      kpiData.kpi
                    ]}
                  />
                }
              />
              <Bar
                dataKey="value"
                fill="var(--color-value)"
                radius={[2, 2, 0, 0]}
                fillOpacity={0.8}
              />
            </BarChart>
          </ChartContainer>
        ) : (
          <div className="h-[80px] flex items-center justify-center text-xs text-muted-foreground">
            No chart data available
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default KpiCard;
