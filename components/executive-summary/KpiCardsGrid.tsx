'use client';

import { FlexibleKpiResponse, KpiSummaryData, Period, ValidCurrency, isSimpleKpiResponse } from './types';
import React, { useState } from 'react';

import { Button } from '@/components/ui/button';
import KpiCard from './KpiCard';
import { Presentation } from 'lucide-react';
import { SlideContainer } from './slides';

interface KpiCardsGridProps {
  kpiSummaryData: KpiSummaryData[];
  currency: ValidCurrency;
  period: Period;
  kpiData: FlexibleKpiResponse | null;
  isLoading: boolean;
  error: Error | null;
  onRetry: () => void;
}

const KpiCardsGrid: React.FC<KpiCardsGridProps> = ({
  kpiSummaryData,
  currency,
  period,
  kpiData,
  isLoading,
  error,
  onRetry,
}) => {
  const [showSlides, setShowSlides] = useState(false);
  // Extract time series data for each KPI
  const getTimeSeriesForKpi = (kpiName: string) => {
    if (!kpiData) return undefined;

    let kpiResult;
    if (isSimpleKpiResponse(kpiData)) {
      kpiResult = kpiData[kpiName];
    } else {
      // For grouped responses, find the KPI in any group
      for (const groupName in kpiData) {
        if (kpiData[groupName][kpiName]) {
          kpiResult = kpiData[groupName][kpiName];
          break;
        }
      }
    }

    return kpiResult?.timeSeries;
  };

  // Loading skeleton
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="h-[200px] bg-gray-200 dark:bg-gray-800 rounded-lg animate-pulse"></div>
        ))}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-12 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
        <div className="max-w-md mx-auto">
          <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-xl">!</span>
          </div>
          <h3 className="text-lg font-semibold text-red-700 dark:text-red-400 mb-2">
            Failed to Load KPI Data
          </h3>
          <p className="text-red-600 dark:text-red-400 mb-4">{error.message}</p>
          <button
            onClick={onRetry}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // No data state
  if (!kpiSummaryData || kpiSummaryData.length === 0) {
    return (
      <div className="text-center py-12 bg-slate-50 dark:bg-slate-900 rounded-lg border">
        <div className="max-w-md mx-auto">
          <div className="w-12 h-12 bg-slate-400 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-xl">?</span>
          </div>
          <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-400 mb-2">
            No Data Available
          </h3>
          <p className="text-slate-600 dark:text-slate-400">
            No KPI data found for the selected period and brand.
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">
            Key Performance Indicators
          </h2>
          <div className="flex items-center gap-4">
            <div className="text-sm text-slate-600 dark:text-slate-400">
              {period.label} • {kpiSummaryData.length} metrics
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSlides(true)}
              className="flex items-center gap-2"
              disabled={kpiSummaryData.length === 0}
            >
              <Presentation className="h-4 w-4" />
              Slide View
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
          {kpiSummaryData.map((kpi) => (
            <KpiCard
              key={kpi.kpi}
              kpiData={kpi}
              currency={currency}
              period={period}
              timeSeries={getTimeSeriesForKpi(kpi.kpi)}
              className="h-[200px] cursor-pointer hover:shadow-lg transition-shadow"
              onDoubleClick={() => setShowSlides(true)}
            />
          ))}
        </div>
      </div>

      {/* Slide Presentation Modal */}
      {showSlides && (
        <SlideContainer
          kpiData={kpiSummaryData}
          currency={currency}
          period={period}
          rawKpiData={kpiData}
          onClose={() => setShowSlides(false)}
        />
      )}
    </>
  );
};

export default KpiCardsGrid;
