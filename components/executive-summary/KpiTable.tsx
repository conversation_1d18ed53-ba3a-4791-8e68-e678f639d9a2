'use client';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Minus, RefreshCcw } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { KpiSummaryData, PeriodType, ValidCurrency } from './types';
import React, { useEffect, useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { formatCurrency, formatPercentage } from './utils';

interface KpiTableProps {
  kpiSummaryData: KpiSummaryData[];
  currency: ValidCurrency;
  isLoading: boolean;
  error: Error | null;
  onRetry: () => void;
  periodType: PeriodType;
}

// Client-side time component to avoid SSR hydration issues
const ClientTime = () => {
  const [time, setTime] = useState(new Date());
  
  useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date());
    }, 60000); // Update every minute
    
    return () => {
      clearInterval(timer);
    };
  }, []);
  
  return (
    <div className="flex items-center gap-1 text-xs text-muted-foreground mt-2">
      <Clock className="h-3 w-3" />
      <span>Last updated: {time.toLocaleTimeString()}</span>
    </div>
  );
};

const KpiTable: React.FC<KpiTableProps> = ({
  kpiSummaryData,
  currency,
  isLoading,
  error,
  onRetry,
  periodType,
}) => {
  // Get column headers based on period type
  const getHeaders = () => {
    switch (periodType) {
      case PeriodType.MONTH:
        return {
          current: 'Month-to-Date', 
          ytd: 'Year-to-Date',
          comparison: 'vs. Last Year'
        };
      case PeriodType.QUARTER:
        return {
          current: 'Quarter-to-Date',
          ytd: 'Year-to-Date',
          comparison: 'vs. Last Year'
        };
      case PeriodType.YEAR:
        return {
          current: 'Year-to-Date',
          ytd: 'YTD', // Same as current in this case
          comparison: 'vs. Previous Year'
        };
      default:
        return {
          current: 'Current Period',
          ytd: 'Year-to-Date',
          comparison: 'vs. Last Year'
        };
    }
  };
  
  const headers = getHeaders();
  
  // Render trend indicator for budget variance
  const renderTrendIndicator = (value: number | null) => {
    if (value === null) return <Minus className="h-4 w-4" />;
    if (value > 0) return <ArrowUp className="h-4 w-4 text-green-500" />;
    if (value < 0) return <ArrowDown className="h-4 w-4 text-red-500" />;
    return <Minus className="h-4 w-4" />;
  };
  
  // Get color class for trend
  const getTrendColorClass = (value: number | null) => {
    if (value === null) return '';
    if (value > 0) return 'text-green-600 dark:text-green-400';
    if (value < 0) return 'text-red-600 dark:text-red-400';
    return '';
  };
  
  // Get background color class for alternating rows
  const getRowBgClass = (index: number) => {
    return index % 2 === 0 ? 'bg-white dark:bg-gray-950' : 'bg-gray-50 dark:bg-gray-900';
  };
  
  // Loading skeleton
  if (isLoading) {
    return (
      <Card className="shadow-md">
        <CardHeader className="bg-primary/5">
          <CardTitle className="text-xl">Key Performance Indicators</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="p-4 space-y-3">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-10 bg-gray-200 dark:bg-gray-800 rounded animate-pulse"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }
  
  // Error state
  if (error) {
    return (
      <Card className="shadow-md border-red-200 dark:border-red-900">
        <CardHeader className="bg-red-50 dark:bg-red-900/20">
          <CardTitle className="text-xl text-red-700 dark:text-red-400">Key Performance Indicators</CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <div className="text-center py-6">
            <p className="text-red-600 dark:text-red-400 mb-3">{error.message}</p>
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 flex items-center gap-2 mx-auto"
            >
              <RefreshCcw className="h-4 w-4" />
              Retry
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className="shadow-md">
      <CardHeader className="bg-primary/5">
        <CardTitle className="text-xl">Key Performance Indicators</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">KPI</TableHead>
                <TableHead>{headers.current}</TableHead>
                <TableHead>vs Budget</TableHead>
                <TableHead>{headers.ytd}</TableHead>
                <TableHead>{headers.comparison}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {kpiSummaryData.map((row, index) => (
                <TableRow key={row.kpi} className={getRowBgClass(index)}>
                  <TableCell className="font-medium">{row.kpi}</TableCell>
                  <TableCell>{formatCurrency(row.mtd, currency)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {renderTrendIndicator(row.vsbudget)}
                      <span className={getTrendColorClass(row.vsbudget)}>
                        {row.vsbudget !== null ? formatPercentage(row.vsbudget) : 'N/A'}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>{formatCurrency(row.ytd, currency)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {renderTrendIndicator(row.vslastyear)}
                      <span className={getTrendColorClass(row.vslastyear)}>
                        {row.vslastyear !== null ? formatPercentage(row.vslastyear) : 'N/A'}
                      </span>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        
        <ClientTime />
      </CardContent>
    </Card>
  );
};

export default KpiTable;
