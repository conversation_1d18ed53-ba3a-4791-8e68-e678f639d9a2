'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { FileText, Info, LineChart } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

import React from 'react';
import { ValidCurrency } from './types';

interface NotesAndMethodologyProps {
  currency: ValidCurrency;
}

const NotesAndMethodology: React.FC<NotesAndMethodologyProps> = ({ currency }) => {
  return (
    <Card className="shadow-md">
      <CardHeader className="bg-primary/5">
        <CardTitle className="text-xl flex items-center gap-2">
          <Info className="h-5 w-5" />
          Notes & Methodology
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <Tabs defaultValue="data-sources">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="data-sources" className="flex items-center gap-1">
              <FileText className="h-4 w-4" />
              <span className="hidden sm:inline">Data Sources</span>
              <span className="sm:hidden">Sources</span>
            </TabsTrigger>
            <TabsTrigger value="methodology" className="flex items-center gap-1">
              <LineChart className="h-4 w-4" />
              <span className="hidden sm:inline">Methodology</span>
              <span className="sm:hidden">Method</span>
            </TabsTrigger>
            <TabsTrigger value="future-plans" className="flex items-center gap-1">
              <Info className="h-4 w-4" />
              <span className="hidden sm:inline">Future Plans</span>
              <span className="sm:hidden">Plans</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="data-sources" className="p-4 rounded-md bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/10">
            <h3 className="font-medium text-lg mb-2">Data Sources</h3>
            <p className="mb-2 text-sm">
              This report uses real-time data from our centralized API, which aggregates information from multiple sources:
            </p>
            <ul className="list-disc pl-5 text-sm space-y-1 mb-3">
              <li>Financial figures are presented in {currency}</li>
              <li>All data is standardized to JSON format before processing</li>
            </ul>
            <p className="text-xs text-muted-foreground">
              For questions about data sources, contact <span className="font-medium"><EMAIL></span>
            </p>
          </TabsContent>
          
          <TabsContent value="methodology" className="p-4 rounded-md bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/10">
            <h3 className="font-medium text-lg mb-2">Calculation Methodology</h3>
            <p className="mb-2 text-sm">
              The following approaches are used for trend analysis and calculations:
            </p>
            <ul className="list-disc pl-5 text-sm space-y-1 mb-3">
              <li><strong>Week Trend:</strong> Compares the last 7 days to the previous 7 days</li>
              <li><strong>Month Trend:</strong> Compares the current month to the previous month</li>
              <li><strong>Year Trend:</strong> Compares the last 12 months to the previous 12 months</li>
              <li>Percentage changes are calculated as: (Current - Previous) / |Previous| × 100</li>
            </ul>
            <p className="text-xs text-muted-foreground">
              For methodology questions, contact <span className="font-medium"><EMAIL></span>
            </p>
          </TabsContent>
          
          <TabsContent value="future-plans" className="p-4 rounded-md bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/10">
            <h3 className="font-medium text-lg mb-2">Upcoming Enhancements</h3>
            <p className="mb-2 text-sm">
              We&apos;re continuously improving this report with new features:
            </p>
            <ul className="list-disc pl-5 text-sm space-y-1 mb-3">
              <li><strong>Supply Chain Analytics:</strong> Inventory turnover, value, and efficiency metrics (Expected Q3 2025)</li>
              <li><strong>Customer Satisfaction:</strong> NPS, CSAT, and retention rate integration (Expected Q3 2025)</li>
              <li><strong>Budget Comparisons:</strong> More detailed budget variance analysis with forecasting (Expected Q4 2025)</li>
            </ul>
            <p className="text-xs text-muted-foreground">
              To suggest new features, contact <span className="font-medium"><EMAIL></span>
            </p>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default NotesAndMethodology;
