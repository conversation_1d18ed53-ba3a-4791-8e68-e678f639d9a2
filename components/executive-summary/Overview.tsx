'use client';

import { BarChart3, DollarSign, Users } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from "@/components/ui/card";

import React from 'react';

interface OverviewProps {
  selectedBrand: string;
}

const Overview: React.FC<OverviewProps> = ({ selectedBrand }) => {
  return (
    <Card className="shadow-md">
  
      <CardContent className="pt-6">
        <div className="space-y-6">
          <p className="text-muted-foreground">
            This executive summary provides a comprehensive overview of {selectedBrand}&apos;s performance across key metrics.
            The report highlights trends, compares against budget targets, and offers insights to support strategic decision-making.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-md flex flex-col items-center text-center">
              <BarChart3 className="h-8 w-8 text-primary mb-2" />
              <h3 className="font-medium mb-1">Revenue Trends</h3>
              <p className="text-sm text-muted-foreground">
                Analysis of revenue performance across weekly, monthly, and yearly timeframes
              </p>
            </div>
            
            <div className="p-4 border rounded-md flex flex-col items-center text-center">
              <DollarSign className="h-8 w-8 text-primary mb-2" />
              <h3 className="font-medium mb-1">Profitability Metrics</h3>
              <p className="text-sm text-muted-foreground">
                Gross and contribution margins with budget variance analysis
              </p>
            </div>
            
            <div className="p-4 border rounded-md flex flex-col items-center text-center">
              <Users className="h-8 w-8 text-primary mb-2" />
              <h3 className="font-medium mb-1">Customer Acquisition</h3>
              <p className="text-sm text-muted-foreground">
                Marketing efficiency and customer acquisition cost analysis
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default Overview;
