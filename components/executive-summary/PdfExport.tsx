'use client';

import { KpiSummaryData, Period, TrendData, ValidCurrency } from './types';
import React, { useCallback, useEffect } from 'react';
import { formatCurrency, formatPercentage } from './utils';

import { format } from 'date-fns';
import jsPDF from 'jspdf';

interface PdfExportProps {
  selectedBrand: string;
  selectedPeriod: Period;
  currency: ValidCurrency;
  kpiSummaryData: KpiSummaryData[];
  weekTrend: TrendData;
  monthTrend: TrendData;
  yearTrend: TrendData;
  onSuccess: () => void;
  onError: (error: Error) => void;
}

const PdfExport: React.FC<PdfExportProps> = ({
  selectedBrand,
  selectedPeriod,
  currency,
  kpiSummaryData,
  weekTrend,
  monthTrend,
  yearTrend,
  onSuccess,
  onError,
}) => {
  // Generate PDF filename
  const getFilename = () => {
    const brandName = selectedBrand.replace(/\s+/g, '-').toLowerCase();
    const periodLabel = selectedPeriod.label.replace(/\s+/g, '-').toLowerCase();
    return `nolk-executive-slides-${brandName}-${periodLabel}.pdf`;
  };

  // Generate PDF with slide-like design
  const generatePdf = useCallback(async () => {
    try {
      // Create a new PDF document in landscape for slide format
      const doc = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      });

      // Define slide-inspired colors (matching CSS custom properties)
      const primaryColor = [59, 130, 246] as [number, number, number]; // Blue-500
      const successColor = [34, 197, 94] as [number, number, number]; // Green-500  
      const dangerColor = [239, 68, 68] as [number, number, number]; // Red-500
      const textColor = [15, 23, 42] as [number, number, number]; // Slate-900
      const mutedColor = [100, 116, 139] as [number, number, number]; // Slate-500
      const backgroundColor = [248, 250, 252] as [number, number, number]; // Slate-50
      const cardColor = [255, 255, 255] as [number, number, number]; // White
      const borderColor = [226, 232, 240] as [number, number, number]; // Slate-200

      // Set document properties
      doc.setProperties({
        title: `Nolk Executive Slides - ${selectedBrand} - ${selectedPeriod.label}`,
        subject: 'Executive Summary Slides',
        author: 'Nolk Analytics',
        keywords: 'executive summary, analytics, performance, slides',
        creator: 'Nolk Analytics Platform'
      });

      // Helper function to draw slide header (matching SlideHeader component)
      const drawSlideHeader = (kpiName: string, slideNumber: number, totalSlides: number) => {
        // Header background with subtle card styling
        doc.setFillColor(cardColor[0], cardColor[1], cardColor[2]);
        doc.rect(0, 0, 297, 25, 'F');
        
        // Header border
        doc.setDrawColor(borderColor[0], borderColor[1], borderColor[2]);
        doc.setLineWidth(0.5);
        doc.line(0, 25, 297, 25);

        // KPI Title (matching text-2xl md:text-3xl font-bold)
        doc.setTextColor(textColor[0], textColor[1], textColor[2]);
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(18);
        doc.text(kpiName, 15, 12);

        // Period info (matching text-sm font-medium text-muted-foreground)
        doc.setTextColor(mutedColor[0], mutedColor[1], mutedColor[2]);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(10);
        doc.text(selectedPeriod.label, 15, 20);

        // Slide indicator (matching the slide counter design)
        doc.setFillColor(backgroundColor[0], backgroundColor[1], backgroundColor[2]);
        doc.roundedRect(240, 8, 25, 8, 4, 4, 'F');
        doc.setTextColor(mutedColor[0], mutedColor[1], mutedColor[2]);
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(8);
        doc.text(`${slideNumber} of ${totalSlides}`, 252.5, 13, { align: 'center' });
      };

      // Helper function to draw metrics panel (matching KpiMetricsPanel)
      const drawMetricsPanel = (kpiData: KpiSummaryData, startX: number, startY: number, width: number, height: number) => {
        const panelX = startX;
        const panelY = startY;
        
        // Panel background
        doc.setFillColor(cardColor[0], cardColor[1], cardColor[2]);
        doc.roundedRect(panelX, panelY, width, height, 3, 3, 'F');
        
        // Panel border
        doc.setDrawColor(borderColor[0], borderColor[1], borderColor[2]);
        doc.setLineWidth(0.5);
        doc.roundedRect(panelX, panelY, width, height, 3, 3, 'S');

        let currentY = panelY + 20;

        // Primary Value (matching text-2xl md:text-3xl lg:text-4xl font-bold)
        doc.setTextColor(textColor[0], textColor[1], textColor[2]);
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(24);
        const primaryValue = kpiData.mtd !== null ? formatCurrency(kpiData.mtd, currency) : 'N/A';
        doc.text(primaryValue, panelX + 10, currentY);

        // Primary value label
        doc.setTextColor(mutedColor[0], mutedColor[1], mutedColor[2]);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(10);
        doc.text(`Current ${selectedPeriod.type.toLowerCase()} value`, panelX + 10, currentY + 8);

        currentY += 25;

        // Budget Comparison Card (matching the card design)
        if (kpiData.vsbudget !== null) {
          // Card background
          doc.setFillColor(backgroundColor[0], backgroundColor[1], backgroundColor[2]);
          doc.roundedRect(panelX + 8, currentY, width - 16, 25, 2, 2, 'F');
          
          // Card border
          doc.setDrawColor(borderColor[0], borderColor[1], borderColor[2]);
          doc.setLineWidth(0.3);
          doc.roundedRect(panelX + 8, currentY, width - 16, 25, 2, 2, 'S');

          // Trend arrow
          const budgetTrend = kpiData.vsbudget;
          if (budgetTrend > 0) {
            // Up arrow (green)
            doc.setFillColor(successColor[0], successColor[1], successColor[2]);
            doc.triangle(panelX + 15, currentY + 8, panelX + 18, currentY + 5, panelX + 21, currentY + 8, 'F');
          } else if (budgetTrend < 0) {
            // Down arrow (red)
            doc.setFillColor(dangerColor[0], dangerColor[1], dangerColor[2]);
            doc.triangle(panelX + 15, currentY + 5, panelX + 18, currentY + 8, panelX + 21, currentY + 5, 'F');
          }

          // Budget comparison value
          const budgetColor = budgetTrend > 0 ? successColor : budgetTrend < 0 ? dangerColor : mutedColor;
          doc.setTextColor(budgetColor[0], budgetColor[1], budgetColor[2]);
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(14);
          doc.text(`${formatPercentage(budgetTrend)} vs Budget`, panelX + 28, currentY + 10);

          // Budget label
          doc.setTextColor(mutedColor[0], mutedColor[1], mutedColor[2]);
          doc.setFont('helvetica', 'normal');
          doc.setFontSize(8);
          doc.text('Budget comparison', panelX + 28, currentY + 18);

          currentY += 35;
        }

        // Year-over-Year Comparison Card
        if (kpiData.vslastyear !== null) {
          // Card background
          doc.setFillColor(backgroundColor[0], backgroundColor[1], backgroundColor[2]);
          doc.roundedRect(panelX + 8, currentY, width - 16, 25, 2, 2, 'F');
          
          // Card border
          doc.setDrawColor(borderColor[0], borderColor[1], borderColor[2]);
          doc.setLineWidth(0.3);
          doc.roundedRect(panelX + 8, currentY, width - 16, 25, 2, 2, 'S');

          // Trend arrow
          const yoyTrend = kpiData.vslastyear;
          if (yoyTrend > 0) {
            // Up arrow (green)
            doc.setFillColor(successColor[0], successColor[1], successColor[2]);
            doc.triangle(panelX + 15, currentY + 8, panelX + 18, currentY + 5, panelX + 21, currentY + 8, 'F');
          } else if (yoyTrend < 0) {
            // Down arrow (red)
            doc.setFillColor(dangerColor[0], dangerColor[1], dangerColor[2]);
            doc.triangle(panelX + 15, currentY + 5, panelX + 18, currentY + 8, panelX + 21, currentY + 5, 'F');
          }

          // YoY comparison value
          const yoyColor = yoyTrend > 0 ? successColor : yoyTrend < 0 ? dangerColor : mutedColor;
          doc.setTextColor(yoyColor[0], yoyColor[1], yoyColor[2]);
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(14);
          doc.text(`${formatPercentage(yoyTrend)} vs Last Year`, panelX + 28, currentY + 10);

          // YoY label
          doc.setTextColor(mutedColor[0], mutedColor[1], mutedColor[2]);
          doc.setFont('helvetica', 'normal');
          doc.setFontSize(8);
          doc.text('Year-over-year growth', panelX + 28, currentY + 18);

          currentY += 35;
        }

        // Additional Context Card (matching the context section)
        doc.setFillColor(backgroundColor[0], backgroundColor[1], backgroundColor[2]);
        doc.roundedRect(panelX + 8, currentY, width - 16, 30, 2, 2, 'F');

        doc.setTextColor(mutedColor[0], mutedColor[1], mutedColor[2]);
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(8);
        doc.text(`Period: ${selectedPeriod.label}`, panelX + 12, currentY + 8);

        if (kpiData.ytd !== null) {
          doc.setFont('helvetica', 'normal');
          doc.text(`YTD: ${formatCurrency(kpiData.ytd, currency)}`, panelX + 12, currentY + 15);
        }

        doc.text(`Last Updated: ${new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`, panelX + 12, currentY + 22);
      };

      // Helper function to draw chart placeholder (matching ChartVisualizationPanel)
      const drawChartPanel = (kpiName: string, startX: number, startY: number, width: number, height: number) => {
        // Chart panel background
        doc.setFillColor(cardColor[0], cardColor[1], cardColor[2]);
        doc.roundedRect(startX, startY, width, height, 3, 3, 'F');
        
        // Chart panel border
        doc.setDrawColor(borderColor[0], borderColor[1], borderColor[2]);
        doc.setLineWidth(0.5);
        doc.roundedRect(startX, startY, width, height, 3, 3, 'S');

        // Chart header
        doc.setTextColor(textColor[0], textColor[1], textColor[2]);
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(14);
        doc.text(`${kpiName} Trend`, startX + 10, startY + 15);

        doc.setTextColor(mutedColor[0], mutedColor[1], mutedColor[2]);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(9);
        doc.text(`${selectedPeriod.label} • Chart visualization`, startX + 10, startY + 23);

        // Chart placeholder (matching the no-data state)
        const chartAreaY = startY + 35;
        const chartAreaHeight = height - 50;
        
        // Dashed border for chart area
        doc.setDrawColor(borderColor[0], borderColor[1], borderColor[2]);
        doc.setLineDashPattern([2, 2], 0);
        doc.roundedRect(startX + 10, chartAreaY, width - 20, chartAreaHeight, 2, 2, 'S');
        doc.setLineDashPattern([], 0);

        // Chart icon and message
        const centerX = startX + width / 2;
        const centerY = chartAreaY + chartAreaHeight / 2;

        // Chart icon background
        doc.setFillColor(backgroundColor[0], backgroundColor[1], backgroundColor[2]);
        doc.circle(centerX, centerY - 10, 8, 'F');

        // Chart icon text
        doc.setTextColor(mutedColor[0], mutedColor[1], mutedColor[2]);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(16);
        doc.text('📊', centerX - 3, centerY - 7);

        // No data message
        doc.setTextColor(textColor[0], textColor[1], textColor[2]);
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(12);
        doc.text('Chart Data Unavailable', centerX, centerY + 5, { align: 'center' });

        doc.setTextColor(mutedColor[0], mutedColor[1], mutedColor[2]);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(9);
        doc.text('Time series data is not available for this KPI.', centerX, centerY + 12, { align: 'center' });
        doc.text('The current value and comparisons are shown in the metrics panel.', centerX, centerY + 18, { align: 'center' });
      };

      // Create slides for each KPI (matching slide structure)
      kpiSummaryData.forEach((kpiData, index) => {
        if (index > 0) {
          doc.addPage();
        }

        // Set page background
        doc.setFillColor(backgroundColor[0], backgroundColor[1], backgroundColor[2]);
        doc.rect(0, 0, 297, 210, 'F');

        // Draw slide header
        drawSlideHeader(kpiData.kpi, index + 1, kpiSummaryData.length);

        // Content area (matching the grid layout: 40% metrics, 60% chart)
        const contentStartY = 30;
        const contentHeight = 165;
        const metricsWidth = 110; // ~40% of 297mm width
        const chartWidth = 170; // ~60% of 297mm width
        const gap = 10;

        // Draw metrics panel (left side)
        drawMetricsPanel(kpiData, 15, contentStartY, metricsWidth, contentHeight);

        // Draw chart panel (right side)
        drawChartPanel(kpiData.kpi, 15 + metricsWidth + gap, contentStartY, chartWidth, contentHeight);

        // Add slide footer
        doc.setTextColor(mutedColor[0], mutedColor[1], mutedColor[2]);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(8);
        doc.text(`Nolk Analytics - ${selectedBrand} Executive Summary`, 15, 205);
        doc.text(`Generated: ${format(new Date(), 'yyyy-MM-dd HH:mm')}`, 282, 205, { align: 'right' });
      });

      // Add overview slide as first page
      doc.insertPage(1);
      
      // Set page background for overview
      doc.setFillColor(backgroundColor[0], backgroundColor[1], backgroundColor[2]);
      doc.rect(0, 0, 297, 210, 'F');

      // Overview slide header
      doc.setFillColor(cardColor[0], cardColor[1], cardColor[2]);
      doc.rect(0, 0, 297, 35, 'F');
      
      doc.setDrawColor(borderColor[0], borderColor[1], borderColor[2]);
      doc.setLineWidth(0.5);
      doc.line(0, 35, 297, 35);

      // Overview title
      doc.setTextColor(textColor[0], textColor[1], textColor[2]);
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(24);
      doc.text('Executive Summary', 15, 20);

      doc.setTextColor(mutedColor[0], mutedColor[1], mutedColor[2]);
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(12);
      doc.text(`${selectedBrand} • ${selectedPeriod.label}`, 15, 28);

      // Overview content
      let overviewY = 50;
      
      // Brand and period info
      doc.setTextColor(textColor[0], textColor[1], textColor[2]);
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(16);
      doc.text('Performance Overview', 15, overviewY);

      overviewY += 15;
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(11);
      const overviewText = `This executive summary provides a comprehensive overview of ${selectedBrand}'s performance across key metrics. The report highlights trends, compares against budget targets, and offers insights to support strategic decision-making.`;
      const splitOverview = doc.splitTextToSize(overviewText, 260);
      doc.text(splitOverview, 15, overviewY);
      
      overviewY += splitOverview.length * 6 + 20;

      // Trend summary cards
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(16);
      doc.text('Performance Trends', 15, overviewY);

      overviewY += 15;
      
      // Draw trend cards horizontally
      const trendCardWidth = 80;
      const trendCardHeight = 50;
      const trendCardSpacing = 15;

      [weekTrend, monthTrend, yearTrend].forEach((trend, index) => {
        const cardX = 15 + (trendCardWidth + trendCardSpacing) * index;
        
        // Card background
        doc.setFillColor(cardColor[0], cardColor[1], cardColor[2]);
        doc.roundedRect(cardX, overviewY, trendCardWidth, trendCardHeight, 3, 3, 'F');
        
        // Card border
        doc.setDrawColor(borderColor[0], borderColor[1], borderColor[2]);
        doc.setLineWidth(0.5);
        doc.roundedRect(cardX, overviewY, trendCardWidth, trendCardHeight, 3, 3, 'S');

        // Card header
        doc.setFillColor(backgroundColor[0], backgroundColor[1], backgroundColor[2]);
        doc.rect(cardX, overviewY, trendCardWidth, 15, 'F');

        doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(10);
        doc.text(trend.period, cardX + 5, overviewY + 10);

        // Card value
        doc.setTextColor(textColor[0], textColor[1], textColor[2]);
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(12);
        const trendValue = trend.value !== null ? formatCurrency(trend.value, currency) : 'N/A';
        doc.text(trendValue, cardX + 5, overviewY + 28);

        // Card change
        if (trend.change !== null) {
          const changeText = `${trend.change > 0 ? '+' : ''}${formatPercentage(trend.change)}`;
          const changeColor = trend.change > 0 ? successColor : trend.change < 0 ? dangerColor : mutedColor;
          doc.setTextColor(changeColor[0], changeColor[1], changeColor[2]);
          doc.setFont('helvetica', 'normal');
          doc.setFontSize(10);
          doc.text(changeText, cardX + 5, overviewY + 40);
        }
      });

      // Add KPI summary
      overviewY += 70;
      doc.setTextColor(textColor[0], textColor[1], textColor[2]);
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(16);
      doc.text('Key Metrics Summary', 15, overviewY);

      overviewY += 10;
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      doc.text(`This presentation contains ${kpiSummaryData.length} detailed KPI slides with comprehensive metrics and trend analysis.`, 15, overviewY);

      // Add incomplete period warning if applicable
      if (!selectedPeriod.isComplete) {
        overviewY += 15;
        doc.setTextColor(dangerColor[0], dangerColor[1], dangerColor[2]);
        doc.setFont('helvetica', 'bold');
        doc.text('⚠ Incomplete Period', 15, overviewY);
        doc.setFont('helvetica', 'normal');
        doc.text('Data may be partial as the current period is still in progress.', 15, overviewY + 8);
      }

      // Footer for overview
      doc.setTextColor(mutedColor[0], mutedColor[1], mutedColor[2]);
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(8);
      doc.text(`Nolk Analytics - ${selectedBrand} Executive Summary`, 15, 205);
      doc.text(`Generated: ${format(new Date(), 'yyyy-MM-dd HH:mm')}`, 282, 205, { align: 'right' });

      // Save the PDF
      doc.save(getFilename());
      
      // Call success callback
      onSuccess();
    } catch (error) {
      console.error('Error generating PDF:', error);
      onError(error instanceof Error ? error : new Error('Unknown error generating PDF'));
    }
  }, [
    selectedBrand,
    selectedPeriod,
    currency,
    kpiSummaryData,
    weekTrend,
    monthTrend,
    yearTrend,
    onSuccess,
    onError,
  ]);

  // Generate PDF when component mounts
  useEffect(() => {
    generatePdf();
  }, [generatePdf]);

  // This component doesn't render anything visible
  return <div className="hidden">PDF Export Component</div>;
};

export default PdfExport;
