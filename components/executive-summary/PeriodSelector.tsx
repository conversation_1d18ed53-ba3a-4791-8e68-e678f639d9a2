'use client';

import { Calendar, HelpCircle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Period, PeriodType } from './types';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { Button } from "@/components/ui/button";
import React from 'react';
import { format } from 'date-fns';

interface PeriodSelectorProps {
  selectedPeriodType: PeriodType;
  selectedPeriod: Period;
  availablePeriods: Period[];
  onPeriodTypeChange: (type: PeriodType) => void;
  onPeriodChange: (period: Period) => void;
  isLoading: boolean;
}

const PeriodSelector: React.FC<PeriodSelectorProps> = ({
  selectedPeriodType,
  selectedPeriod,
  availablePeriods,
  onPeriodTypeChange,
  onPeriodChange,
  isLoading,
}) => {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          <div className="flex items-center gap-1">
            <span className="text-sm font-medium">Time Period</span>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs text-xs">
                    Select the time period for the report. Periods marked with an asterisk (*) are incomplete, meaning data may be partial.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedPeriodType === PeriodType.MONTH ? "default" : "outline"}
              size="sm"
              onClick={() => onPeriodTypeChange(PeriodType.MONTH)}
              disabled={isLoading}
            >
              Month
            </Button>
            <Button
              variant={selectedPeriodType === PeriodType.QUARTER ? "default" : "outline"}
              size="sm"
              onClick={() => onPeriodTypeChange(PeriodType.QUARTER)}
              disabled={isLoading}
            >
              Quarter
            </Button>
            <Button
              variant={selectedPeriodType === PeriodType.YEAR ? "default" : "outline"}
              size="sm"
              onClick={() => onPeriodTypeChange(PeriodType.YEAR)}
              disabled={isLoading}
            >
              Year
            </Button>
          </div>
          
          <div className="flex-1 min-w-[200px]">
            <Select
              value={`${selectedPeriod.year}-${selectedPeriod.value}`}
              onValueChange={(value) => {
                const [year, periodValue] = value.split('-').map(Number);
                const period = availablePeriods.find(
                  p => p.year === year && p.value === periodValue
                );
                if (period) {
                  onPeriodChange(period);
                }
              }}
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                {availablePeriods.map((period) => (
                  <SelectItem
                    key={`${period.year}-${period.value}`}
                    value={`${period.year}-${period.value}`}
                  >
                    {period.label}
                    {!period.isComplete && " *"}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>
              {format(selectedPeriod.startDate, 'MMM d, yyyy')} - {format(selectedPeriod.endDate, 'MMM d, yyyy')}
            </span>
          </div>
        </div>
        
        {!selectedPeriod.isComplete && (
          <p className="text-xs text-amber-500 mt-2">
            * Incomplete period - data may be partial
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default PeriodSelector;
