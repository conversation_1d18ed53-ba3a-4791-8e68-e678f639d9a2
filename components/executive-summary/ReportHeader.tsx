'use client';

import { Calendar, DollarSign, Store } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Period, ValidCurrency } from './types';

import React from 'react';
import { format } from 'date-fns';

interface ReportHeaderProps {
  selectedBrand: string;
  currency: ValidCurrency;
  selectedPeriod: Period;
}

const ReportHeader: React.FC<ReportHeaderProps> = ({ selectedBrand, currency, selectedPeriod }) => {
  // Generate a report ID based on period type and start date
  const reportId = `${selectedPeriod.type.charAt(0)}${format(selectedPeriod.startDate, 'yyyyMMdd')}`;
  
  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
        <div>
          <h1 className="text-2xl font-bold">Nolk Executive Report</h1>
          <p className="text-muted-foreground">
            {selectedPeriod.label}
            {!selectedPeriod.isComplete && <span className="text-amber-500 ml-1">*</span>}
          </p>
        </div>
        
        <div className="flex items-center gap-1 text-xs bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400 px-2 py-1 rounded-full">
          <span className="h-2 w-2 rounded-full bg-green-500"></span>
          <span>Updated daily</span>
        </div>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3">
        <Card>
          <CardContent className="p-3 flex items-center gap-3">
            <Store className="h-5 w-5 text-primary" />
            <div>
              <p className="text-xs text-muted-foreground">Brand</p>
              <p className="font-medium">{selectedBrand}</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-3 flex items-center gap-3">
            <Calendar className="h-5 w-5 text-primary" />
            <div>
              <p className="text-xs text-muted-foreground">Period</p>
              <p className="font-medium">
                {format(selectedPeriod.startDate, 'MMM d')} - {format(selectedPeriod.endDate, 'MMM d, yyyy')}
                {!selectedPeriod.isComplete && <span className="text-amber-500 ml-1">*</span>}
              </p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-3 flex items-center gap-3">
            <DollarSign className="h-5 w-5 text-primary" />
            <div>
              <p className="text-xs text-muted-foreground">Currency</p>
              <p className="font-medium">{currency}</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-3 flex items-center gap-3">
            <div className="h-5 w-5 flex items-center justify-center text-primary font-bold text-sm">#</div>
            <div>
              <p className="text-xs text-muted-foreground">Report ID</p>
              <p className="font-medium">{reportId}</p>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {!selectedPeriod.isComplete && (
        <p className="text-xs text-amber-500">
          * Incomplete period - data may be partial
        </p>
      )}
    </div>
  );
};

export default ReportHeader;
