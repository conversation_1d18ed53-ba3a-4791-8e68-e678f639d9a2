"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import { Badge } from '@/components/ui/badge';
import { BrandInfo } from './types';
import { Button } from '@/components/ui/button';
import { ChevronDown } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Search } from 'lucide-react';
import { useState } from 'react';

interface SimpleBrandSelectorProps {
  brands: BrandInfo[];
  selectedBrand: BrandInfo | null;
  onBrandSelect: (brand: BrandInfo) => void;
  isLoading?: boolean;
  error?: Error | null;
}

export function SimpleBrandSelector({
  brands,
  selectedBrand,
  onBrandSelect,
  isLoading = false,
  error = null
}: SimpleBrandSelectorProps) {
  const [open, setOpen] = useState(false);
  const [brandSearch, setBrandSearch] = useState('');
  const [tempSelectedBrand, setTempSelectedBrand] = useState<string | null>(
    selectedBrand?.name || null
  );

  const filteredBrands = brands.filter(brand => 
    brand.name.toLowerCase().includes(brandSearch.toLowerCase())
  );

  const handleApply = () => {
    if (tempSelectedBrand === "All Brands") {
      onBrandSelect({ name: "All Brands" } as BrandInfo);
    } else if (tempSelectedBrand) {
      const brand = brands.find(b => b.name === tempSelectedBrand);
      if (brand) {
        onBrandSelect(brand);
      }
    }
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen) {
      setTempSelectedBrand(selectedBrand?.name || null);
      setBrandSearch('');
    }
    setOpen(newOpen);
  };

  return (
    <>
      <Button 
        variant="outline" 
        size="sm" 
        className="h-9 min-w-0" 
        onClick={() => setOpen(true)}
        disabled={isLoading}
      >
        Brand
        <ChevronDown className="ml-1 h-3 w-3" />
        {selectedBrand && selectedBrand.name !== "All Brands" && (
          <Badge variant="secondary" className="ml-2 text-xs">
            1
          </Badge>
        )}
      </Button>

      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>Select Brand</DialogTitle>
          </DialogHeader>
          
          <div className="flex-1 overflow-hidden flex flex-col space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search brands..."
                className="pl-9"
                value={brandSearch}
                onChange={(e) => setBrandSearch(e.target.value)}
              />
            </div>
            
            <div className="space-y-3 flex-1 overflow-hidden flex flex-col">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Available Brands</h4>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setTempSelectedBrand("All Brands")}
                >
                  All Brands
                </Button>
              </div>
              
              <div className="flex-1 overflow-y-auto border rounded-lg p-3">
                {isLoading ? (
                  <div className="text-center py-4 text-sm text-muted-foreground">Loading...</div>
                ) : error ? (
                  <div className="text-center py-4 text-sm text-destructive">{error.message}</div>
                ) : filteredBrands.length === 0 ? (
                  <div className="text-center py-4 text-sm text-muted-foreground">No brands match your search</div>
                ) : (
                  <RadioGroup 
                    value={tempSelectedBrand || ""} 
                    onValueChange={(value) => setTempSelectedBrand(value || null)}
                    className="space-y-2"
                  >
                    {/* All Brands Option */}
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="All Brands" id="brand-all" />
                      <Label htmlFor="brand-all" className="text-sm cursor-pointer flex-1 font-medium">
                        All Brands
                      </Label>
                    </div>
                    
                    {/* Individual Brands */}
                    {filteredBrands.map((brand) => (
                      <div key={brand.name} className="flex items-center space-x-2">
                        <RadioGroupItem value={brand.name} id={`brand-${brand.name}`} />
                        <Label htmlFor={`brand-${brand.name}`} className="text-sm cursor-pointer flex-1">
                          {brand.name}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                )}
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <DialogClose asChild>
              <Button onClick={handleApply}>Apply</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
