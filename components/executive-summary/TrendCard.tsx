'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Minus, RefreshCw, TrendingDown, TrendingUp } from "lucide-react";
import { TrendData, ValidCurrency } from './types';
import { formatCurrency, formatPercentage } from './utils';

import React from 'react';

interface TrendCardProps {
  trend: TrendData;
  currency: ValidCurrency;
  isLoading: boolean;
}

const TrendCard: React.FC<TrendCardProps> = ({ trend, currency, isLoading }) => {
  // Get appropriate colors based on trend direction
  const getTrendColors = () => {
    if (isLoading) {
      return {
        bg: 'bg-gray-100 dark:bg-gray-800',
        text: 'text-gray-500 dark:text-gray-400',
        border: 'border-gray-200 dark:border-gray-700',
        progressBg: 'bg-gray-200 dark:bg-gray-700',
        icon: 'text-gray-400 dark:text-gray-500',
      };
    }
    
    if (trend.change === null) {
      return {
        bg: 'bg-gray-100 dark:bg-gray-800',
        text: 'text-gray-500 dark:text-gray-400',
        border: 'border-gray-200 dark:border-gray-700',
        progressBg: 'bg-gray-200 dark:bg-gray-700',
        icon: 'text-gray-400 dark:text-gray-500',
      };
    }
    
    if (trend.change > 0) {
      return {
        bg: 'bg-green-50 dark:bg-green-950/30',
        text: 'text-green-700 dark:text-green-400',
        border: 'border-green-200 dark:border-green-800',
        progressBg: 'bg-green-200 dark:bg-green-800/50',
        icon: 'text-green-500',
      };
    }
    
    if (trend.change < 0) {
      return {
        bg: 'bg-red-50 dark:bg-red-950/30',
        text: 'text-red-700 dark:text-red-400',
        border: 'border-red-200 dark:border-red-800',
        progressBg: 'bg-red-200 dark:bg-red-800/50',
        icon: 'text-red-500',
      };
    }
    
    return {
      bg: 'bg-gray-100 dark:bg-gray-800',
      text: 'text-gray-500 dark:text-gray-400',
      border: 'border-gray-200 dark:border-gray-700',
      progressBg: 'bg-gray-200 dark:bg-gray-700',
      icon: 'text-gray-400 dark:text-gray-500',
    };
  };
  
  // Render appropriate trend indicator icon
  const renderTrendIndicator = () => {
    if (isLoading) {
      return <RefreshCw className="h-5 w-5 animate-spin" />;
    }
    
    if (trend.change === null) {
      return <Minus className="h-5 w-5" />;
    }
    
    if (trend.change > 0) {
      return <TrendingUp className="h-5 w-5" />;
    }
    
    if (trend.change < 0) {
      return <TrendingDown className="h-5 w-5" />;
    }
    
    return <Minus className="h-5 w-5" />;
  };
  
  // Calculate width for progress bar
  const getPercentageWidth = () => {
    if (trend.change === null || isLoading) {
      return '0%';
    }
    
    // Cap at 100% for visual purposes
    const absChange = Math.min(Math.abs(trend.change), 100);
    return `${absChange}%`;
  };
  
  const trendColors = getTrendColors();
  
  return (
    <Card className={`border ${trendColors.border}`}>
      <CardHeader className={`${trendColors.bg} py-3 px-4`}>
        <CardTitle className="text-sm font-medium flex justify-between items-center">
          <span>{trend.period}</span>
          <span className={`${trendColors.icon}`}>{renderTrendIndicator()}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="space-y-2">
          <div className="flex justify-between items-baseline">
            <h3 className="text-2xl font-bold">
              {isLoading ? (
                <div className="h-8 w-24 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div>
              ) : (
                formatCurrency(trend.value, currency)
              )}
            </h3>
            <p className={`text-sm font-medium ${trendColors.text}`}>
              {isLoading ? (
                'Loading...'
              ) : trend.change === null ? (
                'N/A'
              ) : (
                `${trend.change > 0 ? '+' : ''}${formatPercentage(trend.change)}`
              )}
            </p>
          </div>
          
          <div className="h-1.5 w-full bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden">
            <div 
              className={`h-full ${trendColors.progressBg} rounded-full`} 
              style={{ width: getPercentageWidth() }}
            ></div>
          </div>
          
          <p className="text-xs text-muted-foreground">
            {trend.kpiName || 'Net Revenue'}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default TrendCard;
