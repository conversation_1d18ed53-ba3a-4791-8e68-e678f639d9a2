'use client';

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FlexibleKpiResponse, TrendData, ValidCurrency } from './types';
import React, { useEffect, useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { LineChart } from "lucide-react";
import TrendCard from './TrendCard';
import { calculateTrendData } from './utils';

interface TrendSectionProps {
  initialWeekTrend: TrendData;
  initialMonthTrend: TrendData;
  initialYearTrend: TrendData;
  currency: ValidCurrency;
  isLoading: boolean;
  weekTrendData: FlexibleKpiResponse | null;
  monthTrendData: FlexibleKpiResponse | null;
  yearTrendData: FlexibleKpiResponse | null;
}

const kpiOptions = [
  { value: 'Net Revenue', label: 'Net Revenue' },
  { value: 'Gross Revenue', label: 'Gross Revenue' },
  { value: 'Gross Margin', label: 'Gross Margin' },
  { value: 'Adspend', label: 'Adspend' },
  { value: 'Contribution Margin', label: 'Contribution Margin' },
];

const TrendSection: React.FC<TrendSectionProps> = ({
  initialWeekTrend,
  initialMonthTrend,
  initialYearTrend,
  currency,
  isLoading,
  weekTrendData,
  monthTrendData,
  yearTrendData,
}) => {
  const [selectedKpi, setSelectedKpi] = useState('Net Revenue');
  const [weekTrend, setWeekTrend] = useState<TrendData>(initialWeekTrend);
  const [monthTrend, setMonthTrend] = useState<TrendData>(initialMonthTrend);
  const [yearTrend, setYearTrend] = useState<TrendData>(initialYearTrend);

  // Recalculate trends when KPI selection changes
  useEffect(() => {
    if (weekTrendData) {
      const calculatedWeekTrend = calculateTrendData(weekTrendData, 'Week Trend', selectedKpi);
      setWeekTrend(calculatedWeekTrend);
    }
    
    if (monthTrendData) {
      const calculatedMonthTrend = calculateTrendData(monthTrendData, 'Month Trend', selectedKpi);
      setMonthTrend(calculatedMonthTrend);
    }
    
    if (yearTrendData) {
      const calculatedYearTrend = calculateTrendData(yearTrendData, 'Last Twelve Months', selectedKpi);
      setYearTrend(calculatedYearTrend);
    }
  }, [selectedKpi, weekTrendData, monthTrendData, yearTrendData]);

  return (
    <Card className="shadow-md">
      <CardHeader className="bg-primary/5 flex flex-row items-center justify-between">
        <CardTitle className="text-xl flex items-center gap-2">
          <LineChart className="h-5 w-5" />
          Performance Trends
        </CardTitle>
        
        <div className="w-48">
          <Select
            value={selectedKpi}
            onValueChange={setSelectedKpi}
            disabled={isLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select KPI" />
            </SelectTrigger>
            <SelectContent>
              {kpiOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <TrendCard
            trend={weekTrend}
            currency={currency}
            isLoading={isLoading}
          />
          <TrendCard
            trend={monthTrend}
            currency={currency}
            isLoading={isLoading}
          />
          <TrendCard
            trend={yearTrend}
            currency={currency}
            isLoading={isLoading}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default TrendSection;
