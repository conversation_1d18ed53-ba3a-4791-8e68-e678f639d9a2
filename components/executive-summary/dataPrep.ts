'use client';

import { FlexibleKpiResponse, KpiSummaryData, isSimpleKpiResponse } from './types';

// Helper function to get KPI value from flexible response
function getKpiValue(data: FlexibleKpiResponse, kpiName: string): number | null {
  if (isSimpleKpiResponse(data)) {
    return data[kpiName]?.summary?.value ?? null;
  }

  // For grouped responses, search in each group
  for (const groupName in data) {
    if (data[groupName][kpiName]) {
      return data[groupName][kpiName].summary.value;
    }
  }

  return null;
}

// Helper function to calculate percentage difference between two values
function calculatePercentageDiff(current: number | null, reference: number | null): number | null {
  if (current === null || reference === null || reference === 0) return null;
  const result = ((current - reference) / reference) * 100;
  return Math.round(result * 100) / 100; // Round to 2 decimal places
}

// Prepare data for KPI table
export function prepareKpiSummaryData(
  kpiData: FlexibleKpiResponse | null,
  ytdData: FlexibleKpiResponse | null,
  lastYearData: FlexibleKpiResponse | null,
  budgetData: Record<string, Record<string, number>> | null,
  selectedPeriod: { months: string[], year: string } | null
): KpiSummaryData[] {
  if (!kpiData || !ytdData) return [];

  // Extract values for specific KPIs
  const grossRevenue = getKpiValue(kpiData, 'Gross Revenue');
  const grossMargin = getKpiValue(kpiData, 'Gross Margin');
  const contributionMargin = getKpiValue(kpiData, 'Contribution Margin');
  const adspend = getKpiValue(kpiData, 'Adspend');
  const netRevenue = getKpiValue(kpiData, 'Net Revenue');

  // Extract YTD values
  const ytdGrossRevenue = getKpiValue(ytdData, 'Gross Revenue');
  const ytdGrossMargin = getKpiValue(ytdData, 'Gross Margin');
  const ytdContributionMargin = getKpiValue(ytdData, 'Contribution Margin');
  const ytdAdspend = getKpiValue(ytdData, 'Adspend');
  const ytdNetRevenue = getKpiValue(ytdData, 'Net Revenue');

  // Extract last year values
  const lastYearGrossRevenue = lastYearData ? getKpiValue(lastYearData, 'Gross Revenue') : null;
  const lastYearNetRevenue = lastYearData ? getKpiValue(lastYearData, 'Net Revenue') : null;
  const lastYearGrossMargin = lastYearData ? getKpiValue(lastYearData, 'Gross Margin') : null;
  const lastYearContributionMargin = lastYearData ? getKpiValue(lastYearData, 'Contribution Margin') : null;
  const lastYearAdspend = lastYearData ? getKpiValue(lastYearData, 'Adspend') : null;

  // Extract budget values if available
  let budgetGrossRevenue = null;
  let budgetNetRevenue = null;
  let budgetGrossMargin = null;
  let budgetContributionMargin = null;
  let budgetAdspend = null;

  // Handle budget data based on selected period
  if (budgetData && selectedPeriod) {

    // Aggregate budget values for all months in the selected period
    let totalGrossRevenue = 0;
    let totalNetRevenue = 0;
    let totalGrossMargin = 0;
    let totalContributionMargin = 0;
    let totalAdspend = 0;

    let validGrossRevenueMonths = 0;
    let validNetRevenueMonths = 0;
    let validGrossMarginMonths = 0;
    let validContributionMarginMonths = 0;
    let validAdspendMonths = 0;

    // Iterate through all months in the selected period
    selectedPeriod.months.forEach(month => {
      const periodKey = `${month}-${selectedPeriod.year}`;

      // Sum up budget values across all accounts/brands for each month
      if (budgetData[periodKey]) {
        const monthBudgets = budgetData[periodKey];

        // Aggregate across all accounts in this month
        Object.entries(monthBudgets).forEach(([account, value]) => {
          if (account === 'Gross Revenue' && typeof value === 'number') {
            totalGrossRevenue += value;
            validGrossRevenueMonths++;
          }

          if (account === 'Net Revenue' && typeof value === 'number') {
            totalNetRevenue += value;
            validNetRevenueMonths++;
          }

          if (account === 'Gross Margin' && typeof value === 'number') {
            totalGrossMargin += value;
            validGrossMarginMonths++;
          }

          if (account === 'Contribution Margin' && typeof value === 'number') {
            totalContributionMargin += value;
            validContributionMarginMonths++;
          }

          // Map marketing accounts to Adspend budget
          if ((account === 'Pay-Per-Click' ||
               account === 'Content' ||
               account === 'D2C Marketing' ||
               account === 'Paid Marketing Marketplaces') && typeof value === 'number') {
            totalAdspend += value;
            validAdspendMonths++;
          }
        });
      }
    });

    // Only set budget values if there was valid data
    budgetGrossRevenue = validGrossRevenueMonths > 0 ? totalGrossRevenue : null;
    budgetNetRevenue = validNetRevenueMonths > 0 ? totalNetRevenue : null;
    budgetGrossMargin = validGrossMarginMonths > 0 ? totalGrossMargin : null;
    budgetContributionMargin = validContributionMarginMonths > 0 ? totalContributionMargin : null;
    budgetAdspend = validAdspendMonths > 0 ? totalAdspend : null;
  }

  // Calculate budget variances (as percentages)
  const grossRevenueVsBudget = calculatePercentageDiff(grossRevenue, budgetGrossRevenue);
  const netRevenueVsBudget = calculatePercentageDiff(netRevenue, budgetNetRevenue);
  const grossMarginVsBudget = calculatePercentageDiff(grossMargin, budgetGrossMargin);
  const contributionMarginVsBudget = calculatePercentageDiff(contributionMargin, budgetContributionMargin);
  const adspendVsBudget = calculatePercentageDiff(adspend, budgetAdspend);

  // Calculate year-over-year variances (as percentages)
  const grossRevenueVsLastYear = calculatePercentageDiff(grossRevenue, lastYearGrossRevenue);
  const netRevenueVsLastYear = calculatePercentageDiff(netRevenue, lastYearNetRevenue);
  const grossMarginVsLastYear = calculatePercentageDiff(grossMargin, lastYearGrossMargin);
  const contributionMarginVsLastYear = calculatePercentageDiff(contributionMargin, lastYearContributionMargin);
  const adspendVsLastYear = calculatePercentageDiff(adspend, lastYearAdspend);

  // Return structured data for the KPI table
  return [
    {
      kpi: 'Total Revenue',
      mtd: grossRevenue,
      vsbudget: grossRevenueVsBudget,
      ytd: ytdGrossRevenue,
      lastyear: lastYearGrossRevenue,
      vslastyear: grossRevenueVsLastYear,
    },
    {
      kpi: 'Net Revenue',
      mtd: netRevenue,
      vsbudget: netRevenueVsBudget,
      ytd: ytdNetRevenue,
      lastyear: lastYearNetRevenue,
      vslastyear: netRevenueVsLastYear,
    },
    {
      kpi: 'Gross Profit',
      mtd: grossMargin,
      vsbudget: grossMarginVsBudget,
      ytd: ytdGrossMargin,
      lastyear: lastYearGrossMargin,
      vslastyear: grossMarginVsLastYear,
    },
    {
      kpi: 'Contribution Margin',
      mtd: contributionMargin,
      vsbudget: contributionMarginVsBudget,
      ytd: ytdContributionMargin,
      lastyear: lastYearContributionMargin,
      vslastyear: contributionMarginVsLastYear,
    },
    {
      kpi: 'Adspend',
      mtd: adspend,
      vsbudget: adspendVsBudget,
      ytd: ytdAdspend,
      lastyear: lastYearAdspend,
      vslastyear: adspendVsLastYear,
    },
  ];
}
