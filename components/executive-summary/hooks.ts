'use client';

import { BrandInfo, FilterData, FlexibleKpiResponse, Period, PeriodType, TrendData, ValidCurrency } from './types';
// Removed FilterState import as it's no longer used
// import { FilterState } from '@/lib/contexts/filter-context';
import { calculateTrendData, generateAvailablePeriods, getLastCompletePeriod, getMonthTrendParams, getPeriodParams, getWeekTrendParams, getYearTrendParams } from './utils';
import { useCallback, useEffect, useState } from 'react';

// Reinstated useFilterData hook
export function useFilterData() {
  const [filterData, setFilterData] = useState<FilterData | null>(null);
  const [loadingFilters, setLoadingFilters] = useState(true);
  const [filterError, setFilterError] = useState<Error | null>(null);
  const [selectedBrand, setSelectedBrand] = useState<BrandInfo | null>(null);

  useEffect(() => {
    const abortController = new AbortController();
    const signal = abortController.signal;

    async function fetchFilterOptions() {
      setLoadingFilters(true);
      setFilterError(null);
      try {
        // Fetch brands the user has access to using the secure brands endpoint
        const response = await fetch('/api/dashboard/brands', { signal });
        if (!response.ok) {
          throw new Error(`Failed to fetch brands: ${response.status} ${response.statusText}`);
        }
        
        const brandsData = await response.json();
        
        // Check if the response is an error object
        if (brandsData.error) {
          console.warn(`Error in brands response: ${brandsData.error}`);
          throw new Error(brandsData.error);
        }
        
        // Format brands for the selector and ensure they conform to BrandInfo interface
        const availableBrands: BrandInfo[] = brandsData.map((brand: { name: string; group?: string }) => ({
          name: brand.name,
          group: brand.group || 'default' // Provide a default group if not present
        }));
        
        // Extract unique brand groups
        const brandGroups = [...new Set(['All', ...availableBrands.map(brand => brand.group)])].filter(Boolean);
        
        setFilterData({
          brands: availableBrands,
          brandGroups,
        });

        // Always select "All Brands" by default
        if (!selectedBrand) {
          // Create an "All Brands" option and select it by default
          const allBrandsOption: BrandInfo = { name: "All Brands", group: "default" };
          setSelectedBrand(allBrandsOption);
        }
        
        setLoadingFilters(false);
      } catch (error) {
        if (!signal.aborted) {
          console.error('Error fetching filter options for Executive Summary:', error);
          setFilterError(error instanceof Error ? error : new Error(String(error)));
          setLoadingFilters(false);
        }
      }
    }
    
    fetchFilterOptions();
    return () => abortController.abort();
  }, []); // No dependency on selectedBrand as it would create an infinite loop

  return { filterData, loadingFilters, filterError, selectedBrand, setSelectedBrand };
}

// Reinstated useTimePeriod hook
export function useTimePeriod() {
  const [selectedPeriodType, setSelectedPeriodType] = useState<PeriodType>(PeriodType.MONTH);
  const [availablePeriods, setAvailablePeriods] = useState<Period[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState<Period | null>(null);

  useEffect(() => {
    console.log(`[Executive Summary Debug - useTimePeriod] Period type changed to: ${selectedPeriodType}`);
    const periods = generateAvailablePeriods(selectedPeriodType);
    setAvailablePeriods(periods);
    
    // Always reset to the most appropriate period when changing types
    const initialSelectedPeriod = getLastCompletePeriod(selectedPeriodType);
    setSelectedPeriod(initialSelectedPeriod);
    
    console.log(`[Executive Summary Debug - useTimePeriod] Generated ${periods.length} periods for ${selectedPeriodType}`);
    console.log(`[Executive Summary Debug - useTimePeriod] Selected period: ${JSON.stringify(initialSelectedPeriod)}`);
    console.log(`[Executive Summary Debug - useTimePeriod] Available periods:`, periods.map(p => ({ value: p.value, label: p.label, type: p.type })));
  }, [selectedPeriodType]);

  const changePeriodType = useCallback((type: PeriodType) => {
    console.log(`[Executive Summary Debug - useTimePeriod] Changing period type from ${selectedPeriodType} to ${type}`);
    setSelectedPeriodType(type);
  }, [selectedPeriodType]);

  const selectPeriod = useCallback((period: Period) => {
    console.log(`[Executive Summary Debug - useTimePeriod] Selecting period: ${JSON.stringify(period)}`);
    setSelectedPeriod(period);
  }, []);

  return { selectedPeriodType, selectedPeriod, availablePeriods, changePeriodType, selectPeriod };
}

// Reinstated useCurrencyPreference hook
export function useCurrencyPreference() {
  const [currency, setCurrency] = useState<ValidCurrency>('CAD');
  useEffect(() => {
    const savedCurrency = localStorage.getItem('executiveSummaryCurrency') as ValidCurrency | null;
    if (savedCurrency && (savedCurrency === 'CAD' || savedCurrency === 'USD')) {
      setCurrency(savedCurrency);
    }
  }, []);
  useEffect(() => {
    localStorage.setItem('executiveSummaryCurrency', currency);
  }, [currency]);
  return { currency, setCurrency };
}


// Hook for fetching KPI data
export function useKpiData(
  currency: ValidCurrency,
  selectedBrandName?: string,
  selectedPeriod?: Period | null
) {
  // Log received parameters
  console.log(`[Executive Summary Debug - useKpiData] currency: ${currency}, selectedBrandName: ${selectedBrandName}, selectedPeriod: ${JSON.stringify(selectedPeriod)}`);

  const [kpiData, setKpiData] = useState<FlexibleKpiResponse | null>(null);
  const [ytdData, setYtdData] = useState<FlexibleKpiResponse | null>(null);
  const [lastYearData, setLastYearData] = useState<FlexibleKpiResponse | null>(null);
  const [budgetData, setBudgetData] = useState<Record<string, Record<string, number>> | null>(null);
  const [weekTrendData, setWeekTrendData] = useState<FlexibleKpiResponse | null>(null);
  const [monthTrendData, setMonthTrendData] = useState<FlexibleKpiResponse | null>(null);
  const [yearTrendData, setYearTrendData] = useState<FlexibleKpiResponse | null>(null);
  
  const [weekTrend, setWeekTrend] = useState<TrendData>({ period: 'Week Trend', value: null, change: null });
  const [monthTrend, setMonthTrend] = useState<TrendData>({ period: 'Month Trend', value: null, change: null });
  const [yearTrend, setYearTrend] = useState<TrendData>({ period: 'Year Trend', value: null, change: null });
  
  const [loadingData, setLoadingData] = useState(true);
  const [apiError, setApiError] = useState<Error | null>(null);

  // Helper function to format period for budget data
  const formatPeriodForBudget = useCallback((period: Period | null): { months: string[], year: string } | null => {
    if (!period) return null;
    
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const year = period.startDate.getFullYear().toString().slice(-2); // Get last 2 digits of year
    
    // Get all months in the selected period
    const months: string[] = [];
    const startMonth = period.startDate.getMonth();
    const endMonth = period.endDate.getMonth();
    const startYear = period.startDate.getFullYear();
    const endYear = period.endDate.getFullYear();
    
    // Handle periods that span multiple years
    if (startYear === endYear) {
      // Same year - add months from start to end
      for (let i = startMonth; i <= endMonth; i++) {
        months.push(monthNames[i]);
      }
    } else {
      // Multiple years - add remaining months from start year, then months from end year
      for (let i = startMonth; i <= 11; i++) {
        months.push(monthNames[i]);
      }
      for (let i = 0; i <= endMonth; i++) {
        months.push(monthNames[i]);
      }
    }
    
    return { months, year };
  }, []);

  // Fetch budget data
  const fetchBudgetData = useCallback(async (signal: AbortSignal) => {
    try {
      const url = selectedBrandName && selectedBrandName.toLowerCase() !== 'all brands'
        ? `/api/budget?brand=${encodeURIComponent(selectedBrandName)}`
        : "/api/budget";
      
      const response = await fetch(url, { signal });
      if (!response.ok) throw new Error(`Failed to fetch budget data: ${response.status} ${response.statusText}`);
      
      const data = await response.json();
      const budgets = data.budgets || [];
      
      // Process budget data into a format that's easy to use
      const processedBudgetData: Record<string, Record<string, number>> = {};
      
      budgets.forEach((budget: { brand: string, month: string, account: string, budget_value: number }) => {
        if (!processedBudgetData[budget.month]) {
          processedBudgetData[budget.month] = {};
        }
        processedBudgetData[budget.month][budget.account] = budget.budget_value;
      });
      
      setBudgetData(processedBudgetData);
      return processedBudgetData;
    } catch (err) {
      if (!signal.aborted && err instanceof Error) {
        console.error('Error fetching budget data:', err);
        return null;
      }
      return null;
    }
  }, [selectedBrandName]);

  const fetchKpiData = useCallback(async () => {
    if (!selectedPeriod || !currency) return; // Ensure currency is also present
    
    setLoadingData(true);
    setApiError(null);
    const abortController = new AbortController();
    const signal = abortController.signal;
    
    // Define a fixed set of KPIs for the Executive Summary
    const executiveSummaryKpis = ['Gross Revenue', 'Net Revenue', 'Gross Margin', 'Adspend', 'Contribution Margin'];

    try {
      // Determine the brand name to use for API calls. If 'All Brands' (case-insensitive) or undefined, pass undefined.
      const apiBrandName = selectedBrandName && selectedBrandName.toLowerCase() !== 'all brands' ? selectedBrandName : undefined;
      console.log(`[Executive Summary Debug - useKpiData] apiBrandName for utils: ${apiBrandName}`);

      const fetchPeriodDataInternal = async () => {
        const periodParams = getPeriodParams(selectedPeriod, currency, apiBrandName, executiveSummaryKpis);
        const periodResponse = await fetch(`/api/dashboard/flexible-kpis?${periodParams.toString()}`, { signal });
        if (!periodResponse.ok) throw new Error(`Failed to fetch period data: ${periodResponse.status} ${periodResponse.statusText}`);
        const periodDataResult: FlexibleKpiResponse = await periodResponse.json();
        setKpiData(periodDataResult);
        
        const ytdPeriod = {
          ...selectedPeriod,
          startDate: new Date(selectedPeriod.startDate.getFullYear(), 0, 1),
        };
        const ytdParams = getPeriodParams(ytdPeriod, currency, apiBrandName, executiveSummaryKpis);
        const ytdResponse = await fetch(`/api/dashboard/flexible-kpis?${ytdParams.toString()}`, { signal });
        if (!ytdResponse.ok) throw new Error(`Failed to fetch YTD data: ${ytdResponse.status} ${ytdResponse.statusText}`);
        const ytdDataResult: FlexibleKpiResponse = await ytdResponse.json();
        setYtdData(ytdDataResult);
        
        // Fetch last year data for the same period
        const lastYearPeriod = {
          ...selectedPeriod,
          startDate: new Date(selectedPeriod.startDate.getFullYear() - 1, selectedPeriod.startDate.getMonth(), selectedPeriod.startDate.getDate()),
          endDate: new Date(selectedPeriod.endDate.getFullYear() - 1, selectedPeriod.endDate.getMonth(), selectedPeriod.endDate.getDate()),
        };
        const lastYearParams = getPeriodParams(lastYearPeriod, currency, apiBrandName, executiveSummaryKpis);
        const lastYearResponse = await fetch(`/api/dashboard/flexible-kpis?${lastYearParams.toString()}`, { signal });
        if (!lastYearResponse.ok) throw new Error(`Failed to fetch last year data: ${lastYearResponse.status} ${lastYearResponse.statusText}`);
        const lastYearDataResult: FlexibleKpiResponse = await lastYearResponse.json();
        setLastYearData(lastYearDataResult);
      };
      
      const fetchTrendDataInternal = async (
        trendType: 'Week' | 'Month' | 'Year',
        paramsGetter: (curr: ValidCurrency, brand?: string) => URLSearchParams, // brand is optional here
        setter: React.Dispatch<React.SetStateAction<FlexibleKpiResponse | null>>,
        trendSetter: React.Dispatch<React.SetStateAction<TrendData>>
        ) => {
        const trendParams = paramsGetter(currency, apiBrandName); // Pass apiBrandName
         // Ensure trend KPIs are also explicitly requested if needed by calculateTrendData
        executiveSummaryKpis.forEach(kpi => {
            if (!trendParams.has('kpis') || !trendParams.get('kpis')?.includes(kpi)) {
                 trendParams.append('kpis', kpi);
            }
        });

        const response = await fetch(`/api/dashboard/flexible-kpis?${trendParams.toString()}`, { signal });
        if (!response.ok) throw new Error(`Failed to fetch ${trendType.toLowerCase()} trend data: ${response.status} ${response.statusText}`);
        const data: FlexibleKpiResponse = await response.json();
        setter(data);
        // Assuming calculateTrendData primarily uses 'Net Revenue' or that data is present
        const calculatedTrend = calculateTrendData(data, `${trendType} Trend`);
        trendSetter(calculatedTrend);
      };
      
      // Fetch budget data
      await fetchBudgetData(signal);
      
      await Promise.all([
        fetchPeriodDataInternal(),
        fetchTrendDataInternal('Week', getWeekTrendParams, setWeekTrendData, setWeekTrend),
        fetchTrendDataInternal('Month', getMonthTrendParams, setMonthTrendData, setMonthTrend),
        fetchTrendDataInternal('Year', getYearTrendParams, setYearTrendData, setYearTrend),
      ]);
      
      setLoadingData(false);
    } catch (err) {
      if (!signal.aborted && err instanceof Error) {
        console.error('Error fetching KPI data for Executive Summary:', err);
        setApiError(err);
        setLoadingData(false);
      }
    }
    return () => abortController.abort();
  }, [currency, selectedBrandName, selectedPeriod, fetchBudgetData]);

  useEffect(() => {
    if (selectedPeriod && currency) { // Ensure currency is present
      fetchKpiData();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPeriod, currency, selectedBrandName]); // Removed fetchKpiData from dependency array to prevent infinite loop

  return {
    kpiData,
    ytdData,
    lastYearData,
    budgetData,
    weekTrendData,
    monthTrendData,
    yearTrendData,
    weekTrend,
    monthTrend,
    yearTrend,
    loadingData,
    apiError,
    fetchKpiData,
    formatPeriodForBudget,
  };
}
