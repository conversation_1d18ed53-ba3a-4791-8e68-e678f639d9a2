'use client';

import { Bar, Bar<PERSON>hart, CartesianGrid, ResponsiveContainer, XAxis, YAxis } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import React, { useMemo } from 'react';

import { ChartVisualizationPanelProps } from '../types/slide-types';
import { formatCurrency } from '@/components/executive-summary/utils';

export const ChartVisualizationPanel: React.FC<ChartVisualizationPanelProps> = ({
  timeSeries,
  kpiName,
  currency,
  period,
  showControls = false
}) => {
  // Process time series data for enhanced chart
  const chartData = useMemo(() => {
    if (!timeSeries || timeSeries.length === 0) {
      return [];
    }

    // Sort by date
    const sortedData = [...timeSeries].sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    // Determine grouping based on data length and period
    const dataLength = sortedData.length;
    let grouping: 'daily' | 'weekly' | 'monthly' = 'daily';
    
    if (dataLength > 90) {
      grouping = 'monthly';
    } else if (dataLength > 31) {
      grouping = 'weekly';
    }

    if (grouping === 'daily') {
      return sortedData.map(item => ({
        date: new Date(item.date).toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric' 
        }),
        value: item.value || 0,
        fullDate: item.date
      }));
    } else if (grouping === 'weekly') {
      // Group by week
      const weeklyData: { [key: string]: { value: number; count: number; startDate: string } } = {};
      
      sortedData.forEach(item => {
        const date = new Date(item.date);
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        const weekKey = weekStart.toISOString().split('T')[0];
        
        if (!weeklyData[weekKey]) {
          weeklyData[weekKey] = { value: 0, count: 0, startDate: weekKey };
        }
        weeklyData[weekKey].value += item.value || 0;
        weeklyData[weekKey].count += 1;
      });

      return Object.values(weeklyData).map(week => ({
        date: new Date(week.startDate).toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric' 
        }),
        value: week.value,
        fullDate: week.startDate
      }));
    } else {
      // Group by month
      const monthlyData: { [key: string]: { value: number; count: number } } = {};
      
      sortedData.forEach(item => {
        const date = new Date(item.date);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
        if (!monthlyData[monthKey]) {
          monthlyData[monthKey] = { value: 0, count: 0 };
        }
        monthlyData[monthKey].value += item.value || 0;
        monthlyData[monthKey].count += 1;
      });

      return Object.entries(monthlyData).map(([monthKey, data]) => ({
        date: new Date(monthKey + '-01').toLocaleDateString('en-US', { 
          month: 'short', 
          year: 'numeric' 
        }),
        value: data.value,
        fullDate: monthKey + '-01'
      }));
    }
  }, [timeSeries, period]);

  const chartConfig = {
    value: {
      label: kpiName,
      color: "hsl(var(--chart-1))",
    },
  };

  if (!chartData.length) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-8 bg-muted/20 rounded-lg">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
            <span className="text-2xl text-muted-foreground">📊</span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground mb-2">
              No Chart Data Available
            </h3>
            <p className="text-sm text-muted-foreground max-w-md">
              Chart data is not available for this KPI in the selected period. 
              The metrics panel shows the current values.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full p-6">
      {/* Chart Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-semibold text-foreground">
            {kpiName} Trend
          </h3>
          <p className="text-sm text-muted-foreground">
            {period.label} • {chartData.length} data points
          </p>
        </div>
        {showControls && (
          <div className="flex items-center gap-2">
            {/* Future: Chart type controls */}
          </div>
        )}
      </div>

      {/* Enhanced Chart */}
      <div className="flex-1 min-h-0">
        <ChartContainer config={chartConfig} className="h-full w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart 
              data={chartData}
              margin={{ top: 20, right: 30, bottom: 60, left: 80 }}
            >
              <CartesianGrid 
                strokeDasharray="3 3" 
                stroke="hsl(var(--border))"
                horizontal={true}
                vertical={false}
              />
              <XAxis 
                dataKey="date"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                angle={-45}
                textAnchor="end"
                height={60}
                stroke="hsl(var(--muted-foreground))"
              />
              <YAxis 
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => formatCurrency(value, currency)}
                stroke="hsl(var(--muted-foreground))"
                width={80}
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    className="w-[200px]"
                    nameKey="value"
                    labelFormatter={(value) => {
                      const item = chartData.find(d => d.date === value);
                      return item ? new Date(item.fullDate).toLocaleDateString() : value;
                    }}
                    formatter={(value) => [
                      formatCurrency(Number(value), currency),
                      kpiName
                    ]}
                  />
                }
              />
              <Bar
                dataKey="value"
                fill="var(--color-value)"
                radius={[4, 4, 0, 0]}
                fillOpacity={0.8}
                className="transition-all duration-300 hover:opacity-100"
              />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Chart Footer */}
      <div className="flex items-center justify-between pt-4 mt-4 border-t border-border">
        <div className="text-xs text-muted-foreground">
          Data aggregated by {chartData.length > 90 ? 'month' : chartData.length > 31 ? 'week' : 'day'}
        </div>
        <div className="text-xs text-muted-foreground">
          Total: {formatCurrency(chartData.reduce((sum, item) => sum + item.value, 0), currency)}
        </div>
      </div>
    </div>
  );
};