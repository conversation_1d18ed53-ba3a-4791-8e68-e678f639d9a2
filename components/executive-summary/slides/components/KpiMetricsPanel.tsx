'use client';

import { ArrowDown, ArrowUp, Minus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { formatCurrency, formatPercentage } from '@/components/executive-summary/utils';

import { KpiMetricsPanelProps } from '../types/slide-types';

export const KpiMetricsPanel: React.FC<KpiMetricsPanelProps> = ({
  kpiData,
  currency,
  period,
  className = ""
}) => {
  // Set timestamp once when component mounts to prevent re-render loops
  const [lastUpdated, setLastUpdated] = useState('');

  useEffect(() => {
    setLastUpdated(new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
  }, []);
  // Render trend indicator
  const renderTrendIndicator = (value: number | null) => {
    if (value === null) return <Minus className="h-5 w-5 text-muted-foreground" />;
    if (value > 0) return <ArrowUp className="h-5 w-5 text-green-500" />;
    if (value < 0) return <ArrowDown className="h-5 w-5 text-red-500" />;
    return <Minus className="h-5 w-5 text-muted-foreground" />;
  };

  // Get color class for trend
  const getTrendColorClass = (value: number | null) => {
    if (value === null) return 'text-muted-foreground';
    if (value > 0) return 'text-green-600 dark:text-green-400';
    if (value < 0) return 'text-red-600 dark:text-red-400';
    return 'text-muted-foreground';
  };

  return (
    <div className={`flex flex-col justify-center p-8 space-y-8 ${className}`}>
      {/* Primary Value */}
      <div className="space-y-2">
        <div className="text-2xl md:text-3xl lg:text-4xl font-bold text-foreground leading-tight">
          {formatCurrency(kpiData.mtd, currency)}
        </div>
        <div className="text-base text-muted-foreground">
          Current {period.type.toLowerCase()} value
        </div>
      </div>

      {/* Budget Comparison */}
      {kpiData.vsbudget !== null && (
        <div className="bg-muted/30 border border-border rounded-xl p-4">
          <div className="flex items-center gap-3">
            {renderTrendIndicator(kpiData.vsbudget)}
            <div>
              <div className={`text-lg md:text-xl font-semibold ${getTrendColorClass(kpiData.vsbudget)}`}>
                {formatPercentage(kpiData.vsbudget)} vs Budget
              </div>
              <div className="text-sm text-muted-foreground">
                Budget comparison
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Year-over-Year Comparison */}
      {kpiData.vslastyear !== null && (
        <div className="bg-muted/30 border border-border rounded-xl p-4">
          <div className="flex items-center gap-3">
            {renderTrendIndicator(kpiData.vslastyear)}
            <div>
              <div className={`text-lg md:text-xl font-semibold ${getTrendColorClass(kpiData.vslastyear)}`}>
                {formatPercentage(kpiData.vslastyear)} vs Last Year
              </div>
              <div className="text-sm text-muted-foreground">
                Year-over-year growth
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Additional Context */}
      <div className="bg-muted/20 rounded-lg p-4 space-y-2">
        <div className="text-sm font-medium text-muted-foreground">
          Period: {period.label}
        </div>
        {kpiData.ytd !== null && (
          <div className="text-sm text-muted-foreground">
            YTD: {formatCurrency(kpiData.ytd, currency)}
          </div>
        )}
        <div className="text-sm text-muted-foreground">
          Last Updated: {lastUpdated}
        </div>
      </div>
    </div>
  );
};