'use client';

import React, { useEffect, useRef } from 'react';

import { SlideContainerProps } from '../types/slide-types';
import { SlideContent } from './SlideContent';
import { useSlideData } from '../hooks/useSlideData';

interface ScrollableSlideViewProps extends Omit<SlideContainerProps, 'onClose' | 'initialSlide'> {
  onSlideChange?: (slideIndex: number) => void;
}

export const ScrollableSlideView: React.FC<ScrollableSlideViewProps> = ({
  kpiData,
  currency,
  period,
  rawKpiData,
  onSlideChange,
  className = ""
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const slideRefs = useRef<(HTMLDivElement | null)[]>([]);
  
  // Process slide data
  const { slideData } = useSlideData({
    kpiSummaryData: kpiData,
    rawKpiData
  });

  // Intersection Observer to track which slide is in view
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const slideIndex = slideRefs.current.findIndex(ref => ref === entry.target);
            if (slideIndex !== -1) {
              onSlideChange?.(slideIndex);
            }
          }
        });
      },
      {
        root: null,
        rootMargin: '-20% 0px -20% 0px', // Trigger when slide is 20% visible from top/bottom
        threshold: 0.5
      }
    );

    slideRefs.current.forEach((ref) => {
      if (ref) {
        observer.observe(ref);
      }
    });

    return () => {
      slideRefs.current.forEach((ref) => {
        if (ref) {
          observer.unobserve(ref);
        }
      });
    };
  }, [slideData.length]); // Removed onSlideChange from dependency array to prevent re-render loop

  if (!slideData.length) {
    return (
      <div className="flex items-center justify-center p-12 bg-slate-50 dark:bg-slate-900 rounded-lg border">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">No Slide Data</h2>
          <p className="text-muted-foreground mb-4">Unable to load slide data.</p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`
        w-full space-y-8
        ${className}
      `}
      role="region"
      aria-label="KPI Slides"
    >
      {slideData.map((slide, index) => (
        <div
          key={`${slide.kpi}-${index}`}
          ref={(el) => {
            slideRefs.current[index] = el;
          }}
          className="
            min-h-[80vh] 
            bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800
            rounded-xl border border-slate-200 dark:border-slate-700 shadow-xl
            flex flex-col overflow-hidden
            scroll-mt-8
          "
          id={`slide-${index}`}
          role="article"
          aria-label={`Slide ${index + 1}: ${slide.kpi}`}
        >
          {/* Slide Header */}
          <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                  {slide.kpi}
                </h2>
                <p className="text-slate-600 dark:text-slate-400 mt-1">
                  {period.label} • Slide {index + 1} of {slideData.length}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-600 dark:text-green-400">
                  Live Data
                </span>
              </div>
            </div>
          </div>

          {/* Slide Content */}
          <div className="flex-1 p-8">
            <SlideContent
              slideData={slide}
              currency={currency}
              period={period}
              className="h-full"
            />
          </div>

          {/* Slide Footer with Navigation Hint */}
          <div className="bg-white dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-slate-500 dark:text-slate-400">
                {slide.timeSeries && slide.timeSeries.length > 0 ? (
                  `${slide.timeSeries.length} data points available`
                ) : (
                  'Current period data only'
                )}
              </div>
              {index < slideData.length - 1 && (
                <div className="text-sm text-slate-500 dark:text-slate-400 flex items-center gap-1">
                  <span>Scroll down for next KPI</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                  </svg>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}

      {/* Floating Navigation Indicator */}
      <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-10">
        <div className="bg-white dark:bg-slate-800 rounded-full border border-slate-200 dark:border-slate-700 shadow-lg p-2">
          <div className="space-y-1">
            {slideData.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  const slideElement = document.getElementById(`slide-${index}`);
                  slideElement?.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }}
                className="
                  block w-2 h-2 rounded-full transition-all duration-200
                  bg-slate-300 dark:bg-slate-600 hover:bg-slate-400 dark:hover:bg-slate-500
                "
                aria-label={`Go to slide ${index + 1}: ${slideData[index].kpi}`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Screen reader announcements */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        Scrollable slide view with {slideData.length} KPI slides
      </div>
    </div>
  );
};