'use client';

import React, { useEffect, useRef } from 'react';

import { SlideContainerProps } from '../types/slide-types';
import { SlideContent } from './SlideContent';
import { SlideHeader } from './SlideHeader';
import { SlideNavigation } from './SlideNavigation';
import { useKeyboardControls } from '../hooks/useKeyboardControls';
import { useSlideData } from '../hooks/useSlideData';
import { useSlideNavigation } from '../hooks/useSlideNavigation';

export const SlideContainer: React.FC<SlideContainerProps> = ({
  kpiData,
  currency,
  period,
  rawKpiData,
  initialSlide = 0,
  onClose,
  className = ""
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Process slide data
  const { slideData, getSlideData } = useSlideData({
    kpiSummaryData: kpiData,
    rawKpiData
  });

  // Navigation logic
  const {
    currentSlide,
    isTransitioning,
    nextSlide,
    previousSlide,
    goToSlide
  } = useSlideNavigation({
    totalSlides: slideData.length,
    initialSlide,
    onSlideChange: (slideIndex) => {
      // Announce slide change for screen readers
      const currentSlideData = getSlideData(slideIndex);
      if (currentSlideData) {
        const announcement = `Slide ${slideIndex + 1} of ${slideData.length}: ${currentSlideData.kpi}`;
        // This would be announced by screen readers
        console.log('Screen reader announcement:', announcement);
      }
    }
  });

  // Keyboard controls
  useKeyboardControls({
    onNext: nextSlide,
    onPrevious: previousSlide,
    onExit: onClose,
    onGoToSlide: goToSlide,
    totalSlides: slideData.length,
    enabled: true
  });

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // Prevent body scroll when slide is open
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  const currentSlideData = getSlideData(currentSlide);

  if (!currentSlideData) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
        <div className="bg-card rounded-lg p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">No Slide Data</h2>
          <p className="text-muted-foreground mb-4">Unable to load slide data.</p>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
      role="dialog"
      aria-modal="true"
      aria-label="KPI Slide Presentation"
    >
      <div 
        ref={containerRef}
        className={`
          mx-auto my-4 h-[calc(100vh-2rem)] max-w-7xl
          bg-background border border-border rounded-lg shadow-2xl
          flex flex-col overflow-hidden
          ${isTransitioning ? 'pointer-events-none' : ''}
          ${className}
        `}
      >
        {/* Slide Header */}
        <SlideHeader
          kpiName={currentSlideData.kpi}
          period={period}
          currentSlide={currentSlide}
          totalSlides={slideData.length}
          onClose={onClose}
          brandContext={undefined} // Could be passed from parent if needed
        />

        {/* Slide Content */}
        <SlideContent
          slideData={currentSlideData}
          currency={currency}
          period={period}
          className={`
            transition-opacity duration-300
            ${isTransitioning ? 'opacity-50' : 'opacity-100'}
          `}
        />

        {/* Slide Navigation */}
        <SlideNavigation
          currentSlide={currentSlide}
          totalSlides={slideData.length}
          onPrevious={previousSlide}
          onNext={nextSlide}
          onSlideSelect={goToSlide}
          disabled={isTransitioning}
        />
      </div>

      {/* Screen reader announcements */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        Slide {currentSlide + 1} of {slideData.length}: {currentSlideData.kpi}
      </div>
      <div aria-live="assertive" aria-atomic="true" className="sr-only">
        {/* For urgent announcements */}
      </div>
    </div>
  );
};