'use client';

import { ChartVisualizationPanel } from './ChartVisualizationPanel';
import { KpiMetricsPanel } from './KpiMetricsPanel';
import React from 'react';
import { SlideContentProps } from '../types/slide-types';

export const SlideContent: React.FC<SlideContentProps> = ({
  slideData,
  currency,
  period,
  className = ""
}) => {
  const hasChartData = slideData.timeSeries && slideData.timeSeries.length > 0;

  return (
    <main 
      className={`flex-1 min-h-0 ${className}`}
      aria-label="KPI Content"
    >
      <div className="h-full grid grid-cols-1 lg:grid-cols-5 gap-6 p-6">
        {/* Metrics Panel - Left side (40% on desktop) */}
        <section 
          className="lg:col-span-2 flex flex-col"
          aria-label="Primary Metrics"
        >
          <KpiMetricsPanel
            kpiData={slideData}
            currency={currency}
            period={period}
            className="h-full"
          />
        </section>

        {/* Chart Panel - Right side (60% on desktop) */}
        <section 
          className="lg:col-span-3 flex flex-col"
          aria-label="Chart Visualization"
        >
          {hasChartData ? (
            <ChartVisualizationPanel
              timeSeries={slideData.timeSeries!}
              kpiName={slideData.kpi}
              currency={currency}
              period={period}
              showControls={false}
            />
          ) : (
            <div className="flex flex-col items-center justify-center h-full p-8 bg-muted/20 rounded-lg border-2 border-dashed border-muted-foreground/20">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl text-muted-foreground">📊</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">
                    Chart Data Unavailable
                  </h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    Time series data is not available for this KPI. 
                    The current value and comparisons are shown in the metrics panel.
                  </p>
                </div>
              </div>
            </div>
          )}
        </section>
      </div>
    </main>
  );
};