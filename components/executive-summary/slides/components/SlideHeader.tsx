'use client';

import { But<PERSON> } from '@/components/ui/button';
import React from 'react';
import { SlideHeaderProps } from '../types/slide-types';
import { X } from 'lucide-react';

export const SlideHeader: React.FC<SlideHeaderProps> = ({
  kpiName,
  period,
  currentSlide,
  totalSlides,
  onClose,
  brandContext
}) => {
  return (
    <header className="flex items-center justify-between p-6 border-b border-border bg-card/50 backdrop-blur-sm">
      <div className="flex-1">
        <h1 className="text-2xl md:text-3xl font-bold text-foreground leading-tight">
          {kpiName}
        </h1>
        <div className="flex items-center gap-4 mt-2">
          <span className="text-sm font-medium text-muted-foreground">
            {period.label}
          </span>
          {brandContext && (
            <>
              <span className="text-muted-foreground">•</span>
              <span className="text-sm font-medium text-muted-foreground">
                {brandContext}
              </span>
            </>
          )}
        </div>
      </div>
      
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-muted-foreground bg-muted px-3 py-1 rounded-full">
            {currentSlide + 1} of {totalSlides}
          </span>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
          aria-label="Close slide presentation"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </header>
  );
};