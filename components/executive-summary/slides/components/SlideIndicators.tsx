'use client';

import React from 'react';
import { SlideIndicatorsProps } from '../types/slide-types';

export const SlideIndicators: React.FC<SlideIndicatorsProps> = ({
  currentSlide,
  totalSlides,
  onSlideSelect,
  className = ""
}) => {
  // Limit the number of visible indicators for better UX
  const maxVisibleIndicators = 9;
  const shouldShowAll = totalSlides <= maxVisibleIndicators;
  
  const getVisibleIndicators = () => {
    if (shouldShowAll) {
      return Array.from({ length: totalSlides }, (_, i) => i);
    }
    
    // Show current slide and surrounding slides
    const halfVisible = Math.floor(maxVisibleIndicators / 2);
    let start = Math.max(0, currentSlide - halfVisible);
    const end = Math.min(totalSlides - 1, start + maxVisibleIndicators - 1);
    
    // Adjust start if we're near the end
    if (end - start < maxVisibleIndicators - 1) {
      start = Math.max(0, end - maxVisibleIndicators + 1);
    }
    
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  };

  const visibleIndicators = getVisibleIndicators();
  const showStartEllipsis = !shouldShowAll && visibleIndicators[0] > 0;
  const showEndEllipsis = !shouldShowAll && visibleIndicators[visibleIndicators.length - 1] < totalSlides - 1;

  return (
    <div 
      className={`flex items-center gap-2 ${className}`}
      role="tablist"
      aria-label="Slide indicators"
    >
      {/* First slide indicator if not visible */}
      {showStartEllipsis && (
        <>
          <button
            onClick={() => onSlideSelect(0)}
            className={`w-3 h-3 rounded-full border-2 transition-all duration-200 hover:scale-110 ${
              currentSlide === 0
                ? 'bg-primary border-primary'
                : 'bg-transparent border-muted-foreground hover:border-primary'
            }`}
            aria-label={`Go to slide 1`}
            role="tab"
            aria-selected={currentSlide === 0}
          />
          <span className="text-muted-foreground text-xs">…</span>
        </>
      )}

      {/* Visible indicators */}
      {visibleIndicators.map((slideIndex) => (
        <button
          key={slideIndex}
          onClick={() => onSlideSelect(slideIndex)}
          className={`w-3 h-3 rounded-full border-2 transition-all duration-200 hover:scale-110 ${
            slideIndex === currentSlide
              ? 'bg-primary border-primary scale-125'
              : 'bg-transparent border-muted-foreground hover:border-primary'
          }`}
          aria-label={`Go to slide ${slideIndex + 1}`}
          role="tab"
          aria-selected={slideIndex === currentSlide}
        />
      ))}

      {/* Last slide indicator if not visible */}
      {showEndEllipsis && (
        <>
          <span className="text-muted-foreground text-xs">…</span>
          <button
            onClick={() => onSlideSelect(totalSlides - 1)}
            className={`w-3 h-3 rounded-full border-2 transition-all duration-200 hover:scale-110 ${
              currentSlide === totalSlides - 1
                ? 'bg-primary border-primary'
                : 'bg-transparent border-muted-foreground hover:border-primary'
            }`}
            aria-label={`Go to slide ${totalSlides}`}
            role="tab"
            aria-selected={currentSlide === totalSlides - 1}
          />
        </>
      )}
    </div>
  );
};