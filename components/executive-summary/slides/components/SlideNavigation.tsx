'use client';

import { ChevronLeft, ChevronRight } from 'lucide-react';

import { Button } from '@/components/ui/button';
import React from 'react';
import { SlideIndicators } from './SlideIndicators';
import { SlideNavigationProps } from '../types/slide-types';

export const SlideNavigation: React.FC<SlideNavigationProps> = ({
  currentSlide,
  totalSlides,
  onPrevious,
  onNext,
  onSlideSelect,
  disabled = false
}) => {
  const canGoPrevious = currentSlide > 0 && !disabled;
  const canGoNext = currentSlide < totalSlides - 1 && !disabled;

  return (
    <nav 
      className="flex items-center justify-center gap-6 p-4 bg-card/50 backdrop-blur-sm border-t border-border"
      aria-label="Slide Navigation"
    >
      {/* Previous Button */}
      <Button
        variant="outline"
        size="lg"
        onClick={onPrevious}
        disabled={!canGoPrevious}
        className="h-12 w-12 p-0 rounded-full transition-all duration-200 hover:scale-105"
        aria-label="Previous slide"
      >
        <ChevronLeft className="h-5 w-5" />
      </Button>

      {/* Slide Indicators */}
      <SlideIndicators
        currentSlide={currentSlide}
        totalSlides={totalSlides}
        onSlideSelect={onSlideSelect}
        className="mx-4"
      />

      {/* Next Button */}
      <Button
        variant="outline"
        size="lg"
        onClick={onNext}
        disabled={!canGoNext}
        className="h-12 w-12 p-0 rounded-full transition-all duration-200 hover:scale-105"
        aria-label="Next slide"
      >
        <ChevronRight className="h-5 w-5" />
      </Button>
    </nav>
  );
};