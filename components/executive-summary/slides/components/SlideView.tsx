'use client';

import { useRouter, useSearchParams } from 'next/navigation';

import React from 'react';
import { SlideContainerProps } from '../types/slide-types';
import { SlideContent } from './SlideContent';
import { SlideHeader } from './SlideHeader';
import { SlideNavigation } from './SlideNavigation';
import { useKeyboardControls } from '../hooks/useKeyboardControls';
import { useSlideData } from '../hooks/useSlideData';
import { useSlideNavigation } from '../hooks/useSlideNavigation';

interface SlideViewProps extends Omit<SlideContainerProps, 'onClose'> {
  onSlideChange?: (slideIndex: number) => void;
}

export const SlideView: React.FC<SlideViewProps> = ({
  kpiData,
  currency,
  period,
  rawKpiData,
  initialSlide = 0,
  onSlideChange,
  className = ""
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Process slide data
  const { slideData, getSlideData } = useSlideData({
    kpiSummaryData: kpiData,
    rawKpiData
  });

  // Navigation logic
  const {
    currentSlide,
    isTransitioning,
    nextSlide,
    previousSlide,
    goToSlide
  } = useSlideNavigation({
    totalSlides: slideData.length,
    initialSlide,
    onSlideChange: (slideIndex) => {
      // Update URL with current slide
      const params = new URLSearchParams(searchParams);
      params.set('view', 'slides');
      params.set('slide', (slideIndex + 1).toString());
      router.push(`?${params.toString()}`, { scroll: false });
      
      // Call external callback
      onSlideChange?.(slideIndex);
      
      // Announce slide change for screen readers
      const currentSlideData = getSlideData(slideIndex);
      if (currentSlideData) {
        const announcement = `Slide ${slideIndex + 1} of ${slideData.length}: ${currentSlideData.kpi}`;
        console.log('Screen reader announcement:', announcement);
      }
    }
  });

  // Keyboard controls
  useKeyboardControls({
    onNext: nextSlide,
    onPrevious: previousSlide,
    onExit: () => {
      // Switch to grid view on ESC
      const params = new URLSearchParams(searchParams);
      params.set('view', 'grid');
      params.delete('slide');
      router.push(`?${params.toString()}`, { scroll: false });
    },
    onGoToSlide: goToSlide,
    totalSlides: slideData.length,
    enabled: true
  });

  const currentSlideData = getSlideData(currentSlide);

  if (!currentSlideData) {
    return (
      <div className="flex items-center justify-center p-12 bg-slate-50 dark:bg-slate-900 rounded-lg border">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">No Slide Data</h2>
          <p className="text-muted-foreground mb-4">Unable to load slide data.</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`
        w-full min-h-[80vh]
        bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800
        rounded-xl border border-slate-200 dark:border-slate-700 shadow-xl
        flex flex-col overflow-hidden
        ${isTransitioning ? 'pointer-events-none' : ''}
        ${className}
      `}
      role="region"
      aria-label="KPI Slide Presentation"
    >
      {/* Slide Header - Enhanced */}
      <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 px-8 py-6">
        <SlideHeader
          kpiName={currentSlideData.kpi}
          period={period}
          currentSlide={currentSlide}
          totalSlides={slideData.length}
          onClose={() => {
            // Switch to grid view when close is clicked
            const params = new URLSearchParams(searchParams);
            params.set('view', 'grid');
            params.delete('slide');
            router.push(`?${params.toString()}`, { scroll: false });
          }}
          brandContext={undefined}
        />
      </div>

      {/* Slide Content - Enhanced */}
      <div className="flex-1 p-8">
        <SlideContent
          slideData={currentSlideData}
          currency={currency}
          period={period}
          className={`
            h-full
            transition-all duration-300 ease-in-out
            ${isTransitioning ? 'opacity-50 scale-95' : 'opacity-100 scale-100'}
          `}
        />
      </div>

      {/* Slide Navigation - Enhanced */}
      <div className="bg-white dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 px-8 py-4">
        <SlideNavigation
          currentSlide={currentSlide}
          totalSlides={slideData.length}
          onPrevious={previousSlide}
          onNext={nextSlide}
          onSlideSelect={goToSlide}
          disabled={isTransitioning}
        />
      </div>

      {/* Screen reader announcements */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        Slide {currentSlide + 1} of {slideData.length}: {currentSlideData.kpi}
      </div>
      <div aria-live="assertive" aria-atomic="true" className="sr-only">
        {/* For urgent announcements */}
      </div>
    </div>
  );
};