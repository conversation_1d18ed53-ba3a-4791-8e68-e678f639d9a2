'use client';

import { useCallback, useEffect } from 'react';

interface UseKeyboardControlsProps {
  onNext: () => void;
  onPrevious: () => void;
  onExit: () => void;
  onGoToSlide: (index: number) => void;
  totalSlides: number;
  enabled?: boolean;
}

export const useKeyboardControls = ({
  onNext,
  onPrevious,
  onExit,
  onGoToSlide,
  totalSlides,
  enabled = true
}: UseKeyboardControlsProps) => {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // Prevent default behavior for handled keys
    const handledKeys = [
      'ArrowLeft', 'ArrowRight', 'Space', 'Backspace', 'Escape', 'Home', 'End',
      '1', '2', '3', '4', '5', '6', '7', '8', '9'
    ];

    if (handledKeys.includes(event.key)) {
      event.preventDefault();
    }

    switch (event.key) {
      case 'ArrowLeft':
      case 'Backspace':
        onPrevious();
        break;
        
      case 'ArrowRight':
      case ' ': // Space key
        onNext();
        break;
        
      case 'Home':
        onGoToSlide(0);
        break;
        
      case 'End':
        onGoToSlide(totalSlides - 1);
        break;
        
      case 'Escape':
        onExit();
        break;
        
      // Number keys for direct navigation (1-9)
      case '1':
      case '2':
      case '3':
      case '4':
      case '5':
      case '6':
      case '7':
      case '8':
      case '9':
        const slideIndex = parseInt(event.key) - 1;
        if (slideIndex < totalSlides) {
          onGoToSlide(slideIndex);
        }
        break;
    }
  }, [enabled, onNext, onPrevious, onExit, onGoToSlide, totalSlides]);

  useEffect(() => {
    if (!enabled) return;

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, enabled]);

  // Return keyboard shortcuts for display/help
  const shortcuts = {
    navigation: [
      { key: '← →', description: 'Navigate slides' },
      { key: 'Space', description: 'Next slide' },
      { key: 'Backspace', description: 'Previous slide' },
      { key: '1-9', description: 'Go to slide' },
      { key: 'Home', description: 'First slide' },
      { key: 'End', description: 'Last slide' },
      { key: 'Escape', description: 'Exit slide mode' }
    ]
  };

  return { shortcuts };
};