'use client';

import { FlexibleKpiResponse, KpiSummaryData, isSimpleKpiResponse } from '@/components/executive-summary/types';
import { useCallback, useMemo } from 'react';

import { SlideData } from '../types/slide-types';

interface UseSlideDataProps {
  kpiSummaryData: KpiSummaryData[];
  rawKpiData: FlexibleKpiResponse | null;
}

export const useSlideData = ({ kpiSummaryData, rawKpiData }: UseSlideDataProps) => {
  // Extract time series data for each KPI - memoized to prevent re-renders
  const getTimeSeriesForKpi = useCallback((kpiName: string) => {
    if (!rawKpiData) return undefined;

    let kpiResult;
    if (isSimpleKpiResponse(rawKpiData)) {
      kpiResult = rawKpiData[kpiName];
    } else {
      // For grouped responses, find the KPI in any group
      for (const groupName in rawKpiData) {
        if (rawKpiData[groupName][kpiName]) {
          kpiResult = rawKpiData[groupName][kpiName];
          break;
        }
      }
    }

    return kpiResult?.timeSeries;
  }, [rawKpiData]);

  // Transform KPI summary data into slide data with time series
  const slideData: SlideData[] = useMemo(() => {
    return kpiSummaryData.map((kpi) => ({
      ...kpi,
      timeSeries: getTimeSeriesForKpi(kpi.kpi)
    }));
  }, [kpiSummaryData, getTimeSeriesForKpi]);

  // Get slide by index
  const getSlideData = (index: number): SlideData | null => {
    if (index < 0 || index >= slideData.length) return null;
    return slideData[index];
  };

  // Get slide index by KPI name
  const getSlideIndex = (kpiName: string): number => {
    return slideData.findIndex(slide => slide.kpi === kpiName);
  };

  // Check if slide has chart data
  const hasChartData = (index: number): boolean => {
    const slide = getSlideData(index);
    return Boolean(slide?.timeSeries && slide.timeSeries.length > 0);
  };

  // Get slides with chart data
  const slidesWithCharts = useMemo(() => {
    return slideData.filter(slide => slide.timeSeries && slide.timeSeries.length > 0);
  }, [slideData]);

  // Get slides without chart data
  const slidesWithoutCharts = useMemo(() => {
    return slideData.filter(slide => !slide.timeSeries || slide.timeSeries.length === 0);
  }, [slideData]);

  // Statistics
  const stats = useMemo(() => ({
    totalSlides: slideData.length,
    slidesWithCharts: slidesWithCharts.length,
    slidesWithoutCharts: slidesWithoutCharts.length,
    chartDataPercentage: slideData.length > 0 ? (slidesWithCharts.length / slideData.length) * 100 : 0
  }), [slideData.length, slidesWithCharts.length, slidesWithoutCharts.length]);

  return {
    slideData,
    getSlideData,
    getSlideIndex,
    hasChartData,
    slidesWithCharts,
    slidesWithoutCharts,
    stats
  };
};