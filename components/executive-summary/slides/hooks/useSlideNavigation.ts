'use client';

import { useCallback, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

interface UseSlideNavigationProps {
  totalSlides: number;
  initialSlide?: number;
  onSlideChange?: (slideIndex: number) => void;
}

export const useSlideNavigation = ({
  totalSlides,
  initialSlide = 0,
  onSlideChange
}: UseSlideNavigationProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentSlide, setCurrentSlide] = useState(initialSlide);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Initialize slide from URL parameter
  useEffect(() => {
    const slideParam = searchParams.get('slide');
    if (slideParam) {
      const slideIndex = Math.max(0, Math.min(parseInt(slideParam) - 1, totalSlides - 1));
      setCurrentSlide(slideIndex);
    }
  }, [searchParams, totalSlides]);

  // Update URL when slide changes
  const updateURL = useCallback((slideIndex: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('view', 'slides');
    params.set('slide', String(slideIndex + 1));
    router.push(`?${params.toString()}`, { scroll: false });
  }, [router, searchParams]);

  // Navigation functions
  const goToSlide = useCallback((slideIndex: number) => {
    if (slideIndex < 0 || slideIndex >= totalSlides || slideIndex === currentSlide || isTransitioning) {
      return;
    }

    setIsTransitioning(true);
    setCurrentSlide(slideIndex);
    updateURL(slideIndex);
    onSlideChange?.(slideIndex);

    // Reset transition state after animation
    setTimeout(() => {
      setIsTransitioning(false);
    }, 300);
  }, [currentSlide, totalSlides, isTransitioning, updateURL, onSlideChange]);

  const nextSlide = useCallback(() => {
    if (currentSlide < totalSlides - 1) {
      goToSlide(currentSlide + 1);
    }
  }, [currentSlide, totalSlides, goToSlide]);

  const previousSlide = useCallback(() => {
    if (currentSlide > 0) {
      goToSlide(currentSlide - 1);
    }
  }, [currentSlide, goToSlide]);

  // Computed properties
  const canGoNext = currentSlide < totalSlides - 1;
  const canGoPrevious = currentSlide > 0;
  const progress = totalSlides > 0 ? (currentSlide + 1) / totalSlides : 0;

  return {
    currentSlide,
    isTransitioning,
    canGoNext,
    canGoPrevious,
    progress,
    nextSlide,
    previousSlide,
    goToSlide,
    totalSlides
  };
};