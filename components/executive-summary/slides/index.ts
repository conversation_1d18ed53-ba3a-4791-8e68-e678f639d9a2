'use client';

// Main container
export { SlideContainer } from './components/SlideContainer';
export { SlideView } from './components/SlideView';

// Individual components
export { SlideHeader } from './components/SlideHeader';
export { SlideContent } from './components/SlideContent';
export { SlideNavigation } from './components/SlideNavigation';
export { SlideIndicators } from './components/SlideIndicators';
export { KpiMetricsPanel } from './components/KpiMetricsPanel';
export { ChartVisualizationPanel } from './components/ChartVisualizationPanel';

// Hooks
export { useSlideNavigation } from './hooks/useSlideNavigation';
export { useKeyboardControls } from './hooks/useKeyboardControls';
export { useSlideData } from './hooks/useSlideData';

// Types
export type {
  SlideData,
  SlideContainerProps,
  SlideHeaderProps,
  SlideContentProps,
  SlideNavigationProps,
  SlideIndicatorsProps,
  KpiMetricsPanelProps,
  ChartVisualizationPanelProps,
  SlideContextValue,
  SwipeDirection,
  SwipeConfig
} from './types/slide-types';

export type {
  AnimationConfig,
  ChartAnimationConfig,
  SlideAnimationConfig
} from './types/animation-types';

export { defaultAnimationConfig } from './types/animation-types';