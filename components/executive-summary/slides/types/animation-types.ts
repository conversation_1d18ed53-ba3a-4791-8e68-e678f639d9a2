'use client';

export interface AnimationConfig {
  duration: number;
  ease: string;
  delay?: number;
}

export interface ChartAnimationConfig {
  initial: AnimationConfig;
  update: AnimationConfig;
  hover: AnimationConfig;
}

export interface SlideAnimationConfig {
  slideTransition: AnimationConfig;
  contentStagger: {
    header: { delay: number };
    metrics: { delay: number };
    chart: { delay: number };
    navigation: { delay: number };
  };
  chartAnimations: {
    bars: {
      initial: { scaleY: number; transformOrigin: string };
      animate: { scaleY: number };
      transition: AnimationConfig;
    };
    axes: {
      initial: { opacity: number };
      animate: { opacity: number };
      transition: AnimationConfig;
    };
    labels: {
      initial: { opacity: number; y: number };
      animate: { opacity: number; y: number };
      transition: AnimationConfig;
    };
  };
  updateAnimations: {
    bars: {
      transition: AnimationConfig;
    };
    values: {
      initial: { scale: number };
      animate: { scale: number[] };
      transition: AnimationConfig;
    };
  };
}

export const defaultAnimationConfig: SlideAnimationConfig = {
  slideTransition: {
    duration: 300,
    ease: 'ease-in-out'
  },
  contentStagger: {
    header: { delay: 0 },
    metrics: { delay: 0.1 },
    chart: { delay: 0.2 },
    navigation: { delay: 0.3 }
  },
  chartAnimations: {
    bars: {
      initial: { scaleY: 0, transformOrigin: 'bottom' },
      animate: { scaleY: 1 },
      transition: { duration: 800, ease: 'easeOutCubic', delay: 0.1 }
    },
    axes: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      transition: { duration: 400, ease: 'ease-in-out', delay: 0.6 }
    },
    labels: {
      initial: { opacity: 0, y: 10 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 300, ease: 'ease-out', delay: 0.8 }
    }
  },
  updateAnimations: {
    bars: {
      transition: { duration: 400, ease: 'easeInOut' }
    },
    values: {
      initial: { scale: 1 },
      animate: { scale: [1, 1.1, 1] },
      transition: { duration: 300, ease: 'ease-in-out' }
    }
  }
};