'use client';

import { FlexibleKpiResponse, KpiSummaryData, Period, ValidCurrency } from '@/components/executive-summary/types';

export interface SlideData extends KpiSummaryData {
  timeSeries?: Array<{
    date: string;
    value: number | null;
  }>;
}

export interface SlideContainerProps {
  kpiData: KpiSummaryData[];
  currency: ValidCurrency;
  period: Period;
  rawKpiData: FlexibleKpiResponse | null;
  initialSlide?: number;
  onClose: () => void;
  className?: string;
}

export interface SlideHeaderProps {
  kpiName: string;
  period: Period;
  currentSlide: number;
  totalSlides: number;
  onClose: () => void;
  brandContext?: string;
}

export interface KpiMetricsPanelProps {
  kpiData: KpiSummaryData;
  currency: ValidCurrency;
  period: Period;
  className?: string;
}

export interface ChartVisualizationPanelProps {
  timeSeries: Array<{
    date: string;
    value: number | null;
  }>;
  kpiName: string;
  currency: ValidCurrency;
  period: Period;
  chartType?: 'bar' | 'line' | 'area';
  showControls?: boolean;
}

export interface SlideNavigationProps {
  currentSlide: number;
  totalSlides: number;
  onPrevious: () => void;
  onNext: () => void;
  onSlideSelect: (index: number) => void;
  disabled?: boolean;
}

export interface SlideIndicatorsProps {
  currentSlide: number;
  totalSlides: number;
  onSlideSelect: (index: number) => void;
  className?: string;
}

export interface SlideContentProps {
  slideData: SlideData;
  currency: ValidCurrency;
  period: Period;
  className?: string;
}

export interface SlideTransitionProps {
  children: React.ReactNode;
  isVisible: boolean;
  direction: 'left' | 'right' | 'none';
  className?: string;
}

export interface SlideContextValue {
  // State
  currentSlide: number;
  totalSlides: number;
  isTransitioning: boolean;
  slideData: SlideData[];
  
  // Navigation
  nextSlide: () => void;
  previousSlide: () => void;
  goToSlide: (index: number) => void;
  
  // Configuration
  transitionDuration: number;
  autoAdvance: boolean;
  showIndicators: boolean;
  
  // Export (for future implementation)
  exportToPDF?: () => Promise<void>;
  exportCurrentSlide?: () => Promise<void>;
}

export type SwipeDirection = 'left' | 'right';

export interface SwipeConfig {
  threshold: number;
  velocity: number;
  directional: boolean;
  preventScrollOnSwipe: boolean;
}