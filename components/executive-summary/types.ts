'use client';

export enum PeriodType {
  MONTH = 'MONTH',
  QUARTER = 'QUARTER',
  YEAR = 'YEAR'
}

export interface Period {
  type: PeriodType;
  year: number;
  value: number; // month/quarter number
  label: string;
  startDate: Date;
  endDate: Date;
  isComplete: boolean;
}

export interface TimePeriodState {
  selectedPeriodType: PeriodType;
  selectedPeriod: Period | null;
  availablePeriods: Period[];
  changePeriodType: (type: PeriodType) => void;
  selectPeriod: (period: Period) => void;
}

export interface BrandInfo {
  name: string;
  group: string;
}

export interface FilterData {
  brands: BrandInfo[];
  brandGroups: string[];
}

export interface TimeSeriesItem {
  date: string;
  value: number | null;
}

export interface KpiResultData {
  summary: {
    value: number | null;
  };
  timeSeries?: TimeSeriesItem[];
}

export interface SimpleKpiResponse {
  [kpiName: string]: KpiResultData;
}

export interface GroupedKpiResponse {
  [groupName: string]: {
    [kpiName: string]: KpiResultData;
  };
}

// This is a type guard to check if a response is a SimpleKpiResponse
export function isSimpleKpiResponse(response: unknown): response is SimpleKpiResponse {
  if (!response || typeof response !== 'object') return false;
  
  return Object.values(response as Record<string, unknown>).every(value => 
    value && typeof value === 'object' && 'summary' in (value as object)
  );
}

export type FlexibleKpiResponse = SimpleKpiResponse | GroupedKpiResponse;

export interface KpiSummaryData {
  kpi: string;
  mtd: number | null;
  vsbudget: number | null;
  ytd: number | null;
  lastyear: number | null;
  vslastyear: number | null;
}

export interface TrendData {
  period: string;
  value: number | null;
  change: number | null;
  kpiName?: string;
}

export type ValidCurrency = 'CAD' | 'USD';
