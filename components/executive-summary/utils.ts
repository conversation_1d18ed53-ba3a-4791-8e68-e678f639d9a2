'use client';

import { FlexibleKpiResponse, KpiResultData, Period, PeriodType, TrendData, ValidCurrency, isSimpleKpiResponse } from './types';
import { endOfMonth, endOfQuarter, endOfYear, format, isAfter, startOfMonth, startOfQuarter, startOfYear, subMonths, subQuarters, subYears } from 'date-fns';

// Currency formatting
export function formatCurrency(value: number | null, currency: ValidCurrency): string {
  if (value === null) return 'N/A';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    maximumFractionDigits: 0,
  }).format(value);
}

// Percentage formatting
export function formatPercentage(value: number | null): string {
  if (value === null) return 'N/A';
  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
  }).format(value / 100);
}

// Trend calculation
export function calculateTrendData(
  data: FlexibleKpiResponse | null,
  period: string,
  kpiName = 'Net Revenue'
): TrendData {
  console.log(`[Executive Summary Debug - calculateTrendData] Called for period: ${period}, kpiName: ${kpiName}`);
  console.log(`[Executive Summary Debug - calculateTrendData] Received data:`, JSON.stringify(data, null, 2));

  if (!data) {
    console.log(`[Executive Summary Debug - calculateTrendData] No data received, returning N/A.`);
    return {
      period,
      value: null,
      change: null,
      kpiName,
    };
  }
  
  // Handle different response types
  let kpiData: KpiResultData | undefined;
  
  if (isSimpleKpiResponse(data)) {
    kpiData = data[kpiName];
  } else {
    // For grouped responses, we need to find the KPI in each group
    // This is a simplified approach - in a real app, you might need to specify which group to use
    for (const groupName in data) {
      if (data[groupName][kpiName]) {
        kpiData = data[groupName][kpiName];
        break;
      }
    }
  }
  
  if (!kpiData) {
    console.log(`[Executive Summary Debug - calculateTrendData] KPI data for ${kpiName} not found in response, returning N/A.`);
    return {
      period,
      value: null,
      change: null,
      kpiName,
    };
  }
  
  const timeSeries = kpiData.timeSeries;
  console.log(`[Executive Summary Debug - calculateTrendData] TimeSeries for ${kpiName}:`, JSON.stringify(timeSeries, null, 2));
  
  if (!timeSeries || timeSeries.length < 2) {
    console.log(`[Executive Summary Debug - calculateTrendData] TimeSeries too short or undefined for ${kpiName}. Using summary value: ${kpiData.summary.value}`);
    return {
      period,
      value: kpiData.summary.value,
      change: null,
      kpiName,
    };
  }

  // Sort time series by date
  const sortedTimeSeries = [...timeSeries].sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  // Split into current and previous periods
  const midpoint = Math.floor(sortedTimeSeries.length / 2);
  const currentPeriodData = sortedTimeSeries.slice(midpoint);
  const previousPeriodData = sortedTimeSeries.slice(0, midpoint);

  // Calculate totals
  const currentTotal = currentPeriodData.reduce(
    (sum, item) => sum + (item.value || 0),
    0
  );
  const previousTotal = previousPeriodData.reduce(
    (sum, item) => sum + (item.value || 0),
    0
  );
  console.log(`[Executive Summary Debug - calculateTrendData] For ${kpiName} - CurrentTotal: ${currentTotal}, PreviousTotal: ${previousTotal}`);

  // Calculate percentage change
  let percentChange = null;
  if (previousTotal !== 0) {
    percentChange = ((currentTotal - previousTotal) / Math.abs(previousTotal)) * 100;
  }
  console.log(`[Executive Summary Debug - calculateTrendData] For ${kpiName} - PercentChange: ${percentChange}`);

  return {
    period,
    value: currentTotal, // This is the value being displayed on the trend card
    change: percentChange,
    kpiName,
  };
}

// API parameter generators
export function getMtdParams(currency: ValidCurrency, selectedBrand?: string): URLSearchParams {
  const params = new URLSearchParams();
  params.append('currency', currency);
  params.append('groupByTime', 'month');
  params.append('kpis', 'Gross Revenue,Net Revenue,Gross Margin,Adspend,Contribution Margin,% Gross Margin,% Contribution Margin');
  
  if (selectedBrand && selectedBrand.toLowerCase() !== 'all brands') {
    params.append('brands', selectedBrand);
  }
  
  return params;
}

export function getYtdParams(currency: ValidCurrency, selectedBrand?: string): URLSearchParams {
  const params = new URLSearchParams();
  params.append('currency', currency);
  params.append('groupByTime', 'year');
  params.append('kpis', 'Gross Revenue,Net Revenue,Gross Margin,Adspend,Contribution Margin,% Gross Margin,% Contribution Margin');
  
  if (selectedBrand && selectedBrand.toLowerCase() !== 'all brands') {
    params.append('brands', selectedBrand);
  }
  
  return params;
}

export function getWeekTrendParams(currency: ValidCurrency, selectedBrand?: string): URLSearchParams {
  const params = new URLSearchParams();
  params.append('currency', currency);
  params.append('groupByTime', 'day');
  params.append('kpis', 'Gross Revenue,Net Revenue,Gross Margin,Adspend,Contribution Margin');
  params.append('timeRange', 'last14days');
  
  if (selectedBrand && selectedBrand.toLowerCase() !== 'all brands') {
    params.append('brands', selectedBrand);
  }
  
  return params;
}

export function getMonthTrendParams(currency: ValidCurrency, selectedBrand?: string): URLSearchParams {
  const params = new URLSearchParams();
  params.append('currency', currency);
  params.append('groupByTime', 'week');
  params.append('kpis', 'Gross Revenue,Net Revenue,Gross Margin,Adspend,Contribution Margin');
  params.append('timeRange', 'last2months');
  
  if (selectedBrand && selectedBrand.toLowerCase() !== 'all brands') {
    params.append('brands', selectedBrand);
  }
  
  return params;
}

export function getYearTrendParams(currency: ValidCurrency, selectedBrand?: string): URLSearchParams {
  const params = new URLSearchParams();
  params.append('currency', currency);
  params.append('groupByTime', 'month');
  params.append('kpis', 'Gross Revenue,Net Revenue,Gross Margin,Adspend,Contribution Margin');
  params.append('timeRange', 'last12months');
  
  if (selectedBrand && selectedBrand.toLowerCase() !== 'all brands') {
    params.append('brands', selectedBrand);
  }
  
  return params;
}

export function getPeriodParams(period: Period, currency: ValidCurrency, selectedBrand?: string, kpis?: string[]): URLSearchParams {
  const params = new URLSearchParams();
  console.log(`[Executive Summary Debug - getPeriodParams] selectedBrand: ${selectedBrand}`);
  params.append('currency', currency);
  params.append('startDate', format(period.startDate, 'yyyy-MM-dd'));
  params.append('endDate', format(period.endDate, 'yyyy-MM-dd'));
  
  // Set groupByTime based on period type
  switch (period.type) {
    case PeriodType.MONTH:
      params.append('groupByTime', 'day');
      break;
    case PeriodType.QUARTER:
      params.append('groupByTime', 'month');
      break;
    case PeriodType.YEAR:
      params.append('groupByTime', 'month');
      break;
  }
  
  params.append('kpis', kpis ? kpis.join(',') : 'Gross Revenue,Net Revenue,Gross Margin,Adspend,Contribution Margin,% Gross Margin,% Contribution Margin');
  
  if (selectedBrand && selectedBrand.toLowerCase() !== 'all brands') {
    params.append('brands', selectedBrand);
  }
  console.log(`[Executive Summary Debug - getPeriodParams] Generated params: ${params.toString()}`);
  return params;
}

// Period generation & handling
export function isPeriodComplete(period: Period): boolean {
  return isAfter(new Date(), period.endDate);
}

export function generateAvailablePeriods(periodType: PeriodType, count = 12): Period[] {
  const today = new Date();
  const periods: Period[] = [];
  
  for (let i = 0; i < count; i++) {
    let startDate: Date;
    let endDate: Date;
    let year: number;
    let value: number;
    let label: string;
    
    switch (periodType) {
      case PeriodType.MONTH:
        startDate = startOfMonth(subMonths(today, i));
        endDate = endOfMonth(subMonths(today, i));
        year = startDate.getFullYear();
        value = startDate.getMonth() + 1; // 1-12
        label = format(startDate, 'MMMM yyyy');
        break;
        
      case PeriodType.QUARTER:
        startDate = startOfQuarter(subQuarters(today, i));
        endDate = endOfQuarter(subQuarters(today, i));
        year = startDate.getFullYear();
        value = Math.floor(startDate.getMonth() / 3) + 1; // 1-4
        label = `Q${value} ${year}`;
        break;
        
      case PeriodType.YEAR:
        startDate = startOfYear(subYears(today, i));
        endDate = endOfYear(subYears(today, i));
        year = startDate.getFullYear();
        value = year;
        label = `${year}`;
        break;
    }
    
    periods.push({
      type: periodType,
      year,
      value,
      label,
      startDate,
      endDate,
      isComplete: isPeriodComplete({ type: periodType, year, value, label, startDate, endDate, isComplete: false }),
    });
  }
  
  return periods;
}

export function getLastCompletePeriod(periodType: PeriodType): Period {
  const periods = generateAvailablePeriods(periodType);

  // For year-to-date mode, we need special handling
  if (periodType === PeriodType.YEAR) {
    // Get the current year period
    const currentYearPeriod = periods.find(p => p.year === new Date().getFullYear());
    if (currentYearPeriod) {
      return currentYearPeriod;
    }
  }

  // For other period types, find the most recent complete period
  const completePeriods = periods.filter(p => p.isComplete);
  
  if (completePeriods.length > 0) {
    return completePeriods[0]; // First complete period (most recent)
  }
  
  // If no complete periods, return the oldest available period
  return periods[periods.length - 1];
}
