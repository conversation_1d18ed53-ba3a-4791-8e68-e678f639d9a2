"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>lose, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Search } from 'lucide-react';

interface BrandFilterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  availableBrands: string[];
  availableBrandGroups: string[];
  tempBrands: string[];
  tempBrandGroups: string[];
  brandSearch: string;
  setBrandSearch: (search: string) => void;
  isLoading: boolean;
  onBrandChange: (brand: string, checked: boolean | string) => void;
  onBrandGroupChange: (group: string, checked: boolean | string) => void;
  onSelectAllBrands: (select: boolean) => void;
  onSelectAllBrandGroups: (select: boolean) => void;
  onApply: () => void;
}

export function BrandFilterDialog({
  open,
  onOpenChange,
  availableBrands,
  availableBrandGroups,
  tempBrands,
  tempBrandGroups,
  brandSearch,
  setBrandSearch,
  isLoading,
  onBrandChange,
  onBrandGroupChange,
  onSelectAllBrands,
  onSelectAllBrandGroups,
  onApply,
}: BrandFilterDialogProps) {
  const filteredBrands = availableBrands.filter(brand => 
    brand.toLowerCase().includes(brandSearch.toLowerCase())
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Select Brands</DialogTitle>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search brands..."
              className="pl-9"
              value={brandSearch}
              onChange={(e) => setBrandSearch(e.target.value)}
            />
          </div>
          
          {availableBrandGroups.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">Brand Groups</h4>
                <div className="flex gap-2">
                  <Button variant="ghost" size="sm" onClick={() => onSelectAllBrandGroups(true)}>
                    Select All
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => onSelectAllBrandGroups(false)}>
                    Clear
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border rounded-lg p-3">
                {isLoading ? (
                  <div className="col-span-2 text-center py-4 text-sm text-muted-foreground">Loading...</div>
                ) : (
                  availableBrandGroups.map((group) => (
                    <div key={group} className="flex items-center space-x-2">
                      <Checkbox
                        id={`group-${group}`}
                        checked={tempBrandGroups.includes(group)}
                        onCheckedChange={(checked) => onBrandGroupChange(group, checked)}
                      />
                      <Label htmlFor={`group-${group}`} className="text-sm cursor-pointer">
                        {group}
                      </Label>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
          
          <div className="space-y-3 flex-1 overflow-hidden flex flex-col">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Individual Brands</h4>
              <div className="flex gap-2">
                <Button variant="ghost" size="sm" onClick={() => onSelectAllBrands(true)}>
                  Select All
                </Button>
                <Button variant="ghost" size="sm" onClick={() => onSelectAllBrands(false)}>
                  Clear
                </Button>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2 flex-1 overflow-y-auto border rounded-lg p-3">
              {isLoading ? (
                <div className="col-span-2 text-center py-4 text-sm text-muted-foreground">Loading...</div>
              ) : filteredBrands.length === 0 ? (
                <div className="col-span-2 text-center py-4 text-sm text-muted-foreground">No brands match your search</div>
              ) : (
                filteredBrands.map((brand) => (
                  <div key={brand} className="flex items-center space-x-2">
                    <Checkbox
                      id={`brand-${brand}`}
                      checked={tempBrands.includes(brand)}
                      onCheckedChange={(checked) => onBrandChange(brand, checked)}
                    />
                    <Label htmlFor={`brand-${brand}`} className="text-sm cursor-pointer">
                      {brand}
                    </Label>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
          <DialogClose asChild>
            <Button onClick={onApply}>Apply</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
