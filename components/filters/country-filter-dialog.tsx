"use client"

import { <PERSON><PERSON>, <PERSON><PERSON>Close, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';

import { Button } from '@/components/ui/button';
import { CORE_MARKETS } from '@/lib/constants';
import { Checkbox } from '@/components/ui/checkbox';
import { CountryData } from '@/lib/contexts/filter-context';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Search } from 'lucide-react';

interface CountryFilterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  availableCountries: CountryData[];
  tempCountries: string[];
  countrySearch: string;
  setCountrySearch: (search: string) => void;
  isLoading: boolean;
  onCountryChange: (country: string, checked: boolean | string) => void;
  onSelectAllCountries: (select: boolean) => void;
  onApply: () => void;
}

export function CountryFilterDialog({
  open,
  onOpenChange,
  availableCountries,
  tempCountries,
  countrySearch,
  setCountrySearch,
  isLoading,
  onCountryChange,
  onSelectAllCountries,
  onApply,
}: CountryFilterDialogProps) {
  // Separate core markets from other countries
  const coreMarketCountries = availableCountries.filter(country =>
    (CORE_MARKETS.countries as readonly string[]).includes(country.name) &&
    country.name.toLowerCase().includes(countrySearch.toLowerCase())
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Select Countries</DialogTitle>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search countries..."
              className="pl-9"
              value={countrySearch}
              onChange={(e) => setCountrySearch(e.target.value)}
            />
          </div>
          
          <div className="flex justify-between">
            <Button variant="ghost" size="sm" onClick={() => onSelectAllCountries(true)}>
              Select All
            </Button>
            <Button variant="ghost" size="sm" onClick={() => onSelectAllCountries(false)}>
              Clear All
            </Button>
          </div>
          
          <div className="flex-1 overflow-y-auto space-y-4">
            {isLoading ? (
              <div className="text-center py-4 text-sm text-muted-foreground">Loading...</div>
            ) : (
              <>
                {/* Core Markets Zone */}
                <div className="border rounded-lg p-3">
                  <Label className="text-sm font-medium mb-3 block text-blue-600">Core Markets</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {coreMarketCountries.length === 0 ? (
                      <div className="col-span-2 text-center py-2 text-xs text-muted-foreground">
                        {countrySearch ? 'No core markets match your search' : 'No core markets available'}
                      </div>
                    ) : (
                      coreMarketCountries.map((country: CountryData) => (
                        <div key={`core-${country.name}`} className="flex items-center space-x-2">
                          <Checkbox
                            id={`core-country-${country.name}`}
                            checked={tempCountries.includes(country.name)}
                            disabled={!country.hasSales}
                            onCheckedChange={(checked) => onCountryChange(country.name, checked)}
                          />
                          <Label
                            htmlFor={`core-country-${country.name}`}
                            className={`text-sm ${!country.hasSales ? 'text-muted-foreground cursor-not-allowed' : 'cursor-pointer'}`}
                          >
                            {country.name}
                          </Label>
                        </div>
                      ))
                    )}
                  </div>
                </div>

                {/* All Markets Zone */}
                <div className="border rounded-lg p-3">
                  <Label className="text-sm font-medium mb-3 block">All Markets</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {availableCountries.filter(country =>
                      country.name.toLowerCase().includes(countrySearch.toLowerCase())
                    ).length === 0 ? (
                      <div className="col-span-2 text-center py-2 text-xs text-muted-foreground">
                        {countrySearch ? 'No countries match your search' : 'No countries available'}
                      </div>
                    ) : (
                      availableCountries
                        .filter(country => country.name.toLowerCase().includes(countrySearch.toLowerCase()))
                        .map((country: CountryData) => (
                          <div key={`all-${country.name}`} className="flex items-center space-x-2">
                            <Checkbox
                              id={`all-country-${country.name}`}
                              checked={tempCountries.includes(country.name)}
                              disabled={!country.hasSales}
                              onCheckedChange={(checked) => onCountryChange(country.name, checked)}
                            />
                            <Label
                              htmlFor={`all-country-${country.name}`}
                              className={`text-sm ${!country.hasSales ? 'text-muted-foreground cursor-not-allowed' : 'cursor-pointer'}`}
                            >
                              {country.name}
                            </Label>
                          </div>
                        ))
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
        
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
          <DialogClose asChild>
            <Button onClick={onApply}>Apply</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
