"use client"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/client-only-select';

import { Calendar } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface DateRangeFilterProps {
  startDate: string;
  endDate: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  showCurrencyFilter?: boolean;
  showGroupByFilter?: boolean;
  currency: 'CAD' | 'USD';
  groupBy: string;
  onCurrencyChange: (currency: string) => void;
  onGroupByChange: (groupBy: string) => void;
}

const datePresets = [
  { label: 'Last 7 days', type: 'last_days' as const, days: 7 },
  { label: 'Last 30 days', type: 'last_days' as const, days: 30 },
  { label: 'Last 90 days', type: 'last_days' as const, days: 90 },
  { label: 'Month to Date', type: 'month_to_date' as const },
  { label: 'Year to Date', type: 'year_to_date' as const },
];

export function DateRangeFilter({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  showCurrencyFilter = true,
  showGroupByFilter = true,
  currency,
  groupBy,
  onCurrencyChange,
  onGroupByChange,
}: DateRangeFilterProps) {
  const applyDatePreset = (preset: typeof datePresets[0]) => {
    const today = new Date();
    let newStartDate = new Date();
    const newEndDate = new Date(today);

    if (preset.type === 'last_days' && preset.days !== undefined) {
      newStartDate.setDate(today.getDate() - preset.days);
    } else if (preset.type === 'month_to_date') {
      newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);
    } else if (preset.type === 'year_to_date') {
      newStartDate = new Date(today.getFullYear(), 0, 1);
    }
    
    const formattedStartDate = newStartDate.toISOString().split('T')[0];
    const formattedEndDate = newEndDate.toISOString().split('T')[0];
    
    onStartDateChange(formattedStartDate);
    onEndDateChange(formattedEndDate);
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Calendar className="h-4 w-4 text-muted-foreground" />
        <Label className="text-sm font-medium">Date Range</Label>
      </div>
      
      <div className="flex flex-wrap gap-3">
        {/* Start Date */}
        <div className="space-y-1 min-w-0">
          <Label htmlFor="start-date" className="text-xs text-muted-foreground">Start Date</Label>
          <Input
            id="start-date"
            type="date"
            value={startDate}
            onChange={(e) => onStartDateChange(e.target.value)}
            className="h-9 w-[140px]"
          />
        </div>
        
        {/* End Date */}
        <div className="space-y-1 min-w-0">
          <Label htmlFor="end-date" className="text-xs text-muted-foreground">End Date</Label>
          <Input
            id="end-date"
            type="date"
            value={endDate}
            onChange={(e) => onEndDateChange(e.target.value)}
            className="h-9 w-[140px]"
          />
        </div>
        
        {/* Date Presets */}
        <div className="space-y-1 min-w-0">
          <Label className="text-xs text-muted-foreground">Quick Select</Label>
          <Select onValueChange={(value) => {
            const preset = datePresets.find(p => p.label === value);
            if (preset) applyDatePreset(preset);
          }}>
            <SelectTrigger className="h-9 w-[140px]">
              <SelectValue placeholder="Date presets" />
            </SelectTrigger>
            <SelectContent>
              {datePresets.map((preset) => (
                <SelectItem key={preset.label} value={preset.label}>
                  {preset.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Currency */}
        {showCurrencyFilter && (
          <div className="space-y-1 min-w-0">
            <Label className="text-xs text-muted-foreground">Currency</Label>
            <Select value={currency} onValueChange={onCurrencyChange}>
              <SelectTrigger className="h-9 w-[100px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CAD">CAD</SelectItem>
                <SelectItem value="USD">USD</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
        
        {/* Group By */}
        {showGroupByFilter && (
          <div className="space-y-1 min-w-0">
            <Label className="text-xs text-muted-foreground">Group By</Label>
            <Select value={groupBy} onValueChange={onGroupByChange}>
              <SelectTrigger className="h-9 w-[100px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">Day</SelectItem>
                <SelectItem value="week">Week</SelectItem>
                <SelectItem value="month">Month</SelectItem>
                <SelectItem value="quarter">Quarter</SelectItem>
                <SelectItem value="year">Year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
    </div>
  );
}
