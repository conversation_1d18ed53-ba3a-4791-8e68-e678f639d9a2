"use client"

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { X } from 'lucide-react';

interface FilterBadgesProps {
  brands: string[];
  brandGroups: string[];
  countries: string[];
  salesChannels: string[];
  onRemoveBrand: (brand: string) => void;
  onRemoveBrandGroup: (group: string) => void;
  onRemoveCountry: (country: string) => void;
  onRemoveSalesChannel: (channel: string) => void;
}

export function FilterBadges({
  brands,
  brandGroups,
  countries,
  salesChannels,
  onRemoveBrand,
  onRemoveBrandGroup,
  onRemoveCountry,
  onRemoveSalesChannel,
}: FilterBadgesProps) {
  const hasActiveFilters = brands.length > 0 || brandGroups.length > 0 || 
    countries.length > 0 || salesChannels.length > 0;

  if (!hasActiveFilters) {
    return null;
  }

  return (
    <div className="space-y-3">
      <Separator />
      <div className="space-y-2">
        <Label className="text-sm font-medium">Active Filters</Label>
        <div className="flex flex-wrap gap-2">
          {/* Brand Pills */}
          {brands.map(brand => (
            <Badge key={brand} variant="default" className="flex items-center gap-1 px-3 py-1">
              {brand}
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => onRemoveBrand(brand)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          
          {/* Brand Group Pills */}
          {brandGroups.map(group => (
            <Badge key={group} variant="outline" className="flex items-center gap-1 px-3 py-1">
              Group: {group}
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => onRemoveBrandGroup(group)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          
          {/* Country Pills */}
          {countries.map(country => (
            <Badge key={country} variant="secondary" className="flex items-center gap-1 px-3 py-1">
              {country}
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => onRemoveCountry(country)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          
          {/* Sales Channel Pills */}
          {salesChannels.map(channel => (
            <Badge key={channel} variant="secondary" className="flex items-center gap-1 px-3 py-1">
              {channel}
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => onRemoveSalesChannel(channel)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      </div>
    </div>
  );
}
