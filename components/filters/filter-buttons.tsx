"use client"

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ChevronDown } from 'lucide-react';
import { Label } from '@/components/ui/label';

interface FilterButtonsProps {
  showBrandFilter?: boolean;
  showCountryFilter?: boolean;
  brandsCount: number;
  brandGroupsCount: number;
  countriesCount: number;
  salesChannelsCount: number;
  onBrandClick: () => void;
  onCountryClick: () => void;
  onSalesChannelClick: () => void;
  brandButtonText?: string;
}

export function FilterButtons({
  showBrandFilter = true,
  showCountryFilter = true,
  brandsCount,
  brandGroupsCount,
  countriesCount,
  salesChannelsCount,
  onBrandClick,
  onCountryClick,
  onSalesChannelClick,
  brandButtonText = "Brands",
}: FilterButtonsProps) {
  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium">Data Filters</Label>
      
      <div className="flex flex-wrap gap-2">
        {/* Brand Filter */}
        {showBrandFilter && (
          <Button variant="outline" size="sm" className="h-9 min-w-0" onClick={onBrandClick}>
            {brandButtonText}
            <ChevronDown className="ml-1 h-3 w-3" />
            {(brandsCount > 0 || brandGroupsCount > 0) && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {brandsCount + brandGroupsCount}
              </Badge>
            )}
          </Button>
        )}

        {/* Country Filter */}
        {showCountryFilter && (
          <Button variant="outline" size="sm" className="h-9 min-w-0" onClick={onCountryClick}>
            Countries
            <ChevronDown className="ml-1 h-3 w-3" />
            {countriesCount > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {countriesCount}
              </Badge>
            )}
          </Button>
        )}

        {/* Sales Channel Filter */}
        <Button variant="outline" size="sm" className="h-9 min-w-0" onClick={onSalesChannelClick}>
          Sales Channels
          <ChevronDown className="ml-1 h-3 w-3" />
          {salesChannelsCount > 0 && (
            <Badge variant="secondary" className="ml-2 text-xs">
              {salesChannelsCount}
            </Badge>
          )}
        </Button>
      </div>
    </div>
  );
}
