"use client"

import { Calendar, Filter, X } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';

interface FilterSummaryProps {
  startDate: string;
  endDate: string;
  currency: 'CAD' | 'USD';
  brands: string[];
  brandGroups: string[];
  countries: string[];
  salesChannels: string[];
  onRemoveBrand: (brand: string) => void;
  onRemoveBrandGroup: (group: string) => void;
  onRemoveCountry: (country: string) => void;
  onRemoveSalesChannel: (channel: string) => void;
  onOpenFilters: () => void;
}

export function FilterSummary({
  startDate,
  endDate,
  currency,
  brands,
  brandGroups,
  countries,
  salesChannels,
  onRemoveBrand,
  onRemoveBrandGroup,
  onRemoveCountry,
  onRemoveSalesChannel,
  onOpenFilters,
}: FilterSummaryProps) {
  const activeFilterCount = brands.length + brandGroups.length + 
    countries.length + salesChannels.length;

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch {
      return dateString;
    }
  };

  return (
    <div className="flex flex-wrap items-center gap-3 p-4 bg-muted/30 border rounded-lg">
      {/* Date Range Display */}
      <div className="flex items-center gap-2 text-sm">
        <Calendar className="h-4 w-4 text-muted-foreground" />
        <span className="font-medium">
          {formatDate(startDate)} - {formatDate(endDate)}
        </span>
        <Badge variant="outline" className="text-xs">
          {currency}
        </Badge>
      </div>

      {/* Filter Count and Open Button */}
      <div className="flex items-center gap-2">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={onOpenFilters}
          className="h-8 text-xs"
        >
          <Filter className="h-3 w-3 mr-1" />
          Filters
          {activeFilterCount > 0 && (
            <Badge variant="secondary" className="ml-2 text-xs">
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </div>

      {/* Active Filter Badges */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-1">
          {/* Brand Pills */}
          {brands.map(brand => (
            <Badge key={brand} variant="default" className="flex items-center gap-1 px-2 py-1 text-xs">
              {brand}
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-3 w-3 p-0 ml-1 hover:bg-transparent"
                onClick={() => onRemoveBrand(brand)}
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          ))}
          
          {/* Brand Group Pills */}
          {brandGroups.map(group => (
            <Badge key={group} variant="outline" className="flex items-center gap-1 px-2 py-1 text-xs">
              Group: {group}
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-3 w-3 p-0 ml-1 hover:bg-transparent"
                onClick={() => onRemoveBrandGroup(group)}
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          ))}
          
          {/* Country Pills */}
          {countries.map(country => (
            <Badge key={country} variant="secondary" className="flex items-center gap-1 px-2 py-1 text-xs">
              {country}
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-3 w-3 p-0 ml-1 hover:bg-transparent"
                onClick={() => onRemoveCountry(country)}
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          ))}
          
          {/* Sales Channel Pills */}
          {salesChannels.map(channel => (
            <Badge key={channel} variant="secondary" className="flex items-center gap-1 px-2 py-1 text-xs">
              {channel}
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-3 w-3 p-0 ml-1 hover:bg-transparent"
                onClick={() => onRemoveSalesChannel(channel)}
              >
                <X className="h-2 w-2" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}
