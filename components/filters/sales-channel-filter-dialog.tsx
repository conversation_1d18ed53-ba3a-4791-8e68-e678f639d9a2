"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Search } from 'lucide-react';

interface SalesChannelFilterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  availableSalesChannels: string[];
  tempSalesChannels: string[];
  channelSearch: string;
  setChannelSearch: (search: string) => void;
  isLoading: boolean;
  onSalesChannelChange: (channel: string, checked: boolean | string) => void;
  onSelectAllSalesChannels: (select: boolean) => void;
  onApply: () => void;
}

export function SalesChannelFilterDialog({
  open,
  onOpenChange,
  availableSalesChannels,
  tempSales<PERSON><PERSON>nels,
  channel<PERSON><PERSON>ch,
  set<PERSON>hannel<PERSON>earch,
  isLoading,
  onSalesChannelChange,
  onSelectAllSalesChannels,
  onApply,
}: SalesChannelFilterDialogProps) {
  const filteredSalesChannels = availableSalesChannels.filter(channel => 
    channel.toLowerCase().includes(channelSearch.toLowerCase())
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Select Sales Channels</DialogTitle>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search channels..."
              className="pl-9"
              value={channelSearch}
              onChange={(e) => setChannelSearch(e.target.value)}
            />
          </div>
          
          <div className="flex justify-between">
            <Button variant="ghost" size="sm" onClick={() => onSelectAllSalesChannels(true)}>
              Select All
            </Button>
            <Button variant="ghost" size="sm" onClick={() => onSelectAllSalesChannels(false)}>
              Clear All
            </Button>
          </div>
          
          <div className="grid grid-cols-2 gap-2 flex-1 overflow-y-auto border rounded-lg p-3">
            {isLoading ? (
              <div className="col-span-2 text-center py-4 text-sm text-muted-foreground">Loading...</div>
            ) : filteredSalesChannels.length === 0 ? (
              <div className="col-span-2 text-center py-4 text-sm text-muted-foreground">No channels match your search</div>
            ) : (
              filteredSalesChannels.map((channel) => (
                <div key={channel} className="flex items-center space-x-2">
                  <Checkbox
                    id={`channel-${channel}`}
                    checked={tempSalesChannels.includes(channel)}
                    onCheckedChange={(checked) => onSalesChannelChange(channel, checked)}
                  />
                  <Label htmlFor={`channel-${channel}`} className="text-sm cursor-pointer">
                    {channel}
                  </Label>
                </div>
              ))
            )}
          </div>
        </div>
        
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
          <DialogClose asChild>
            <Button onClick={onApply}>Apply</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
