"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>lose, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Search } from 'lucide-react';

interface SingleBrandFilterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  availableBrands: string[];
  selectedBrand: string | null;
  brandSearch: string;
  setBrandSearch: (search: string) => void;
  isLoading: boolean;
  onBrandSelect: (brand: string | null) => void;
  onApply: () => void;
}

export function SingleBrandFilterDialog({
  open,
  onOpenChange,
  availableBrands,
  selectedBrand,
  brandSearch,
  setBrandSearch,
  isLoading,
  onBrandSelect,
  onApply,
}: SingleBrandFilterDialogProps) {
  const filteredBrands = availableBrands.filter(brand => 
    brand.toLowerCase().includes(brandSearch.toLowerCase())
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Select Brand</DialogTitle>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden flex flex-col space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search brands..."
              className="pl-9"
              value={brandSearch}
              onChange={(e) => setBrandSearch(e.target.value)}
            />
          </div>
          
          <div className="space-y-3 flex-1 overflow-hidden flex flex-col">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Available Brands</h4>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => onBrandSelect(null)}
                disabled={!selectedBrand}
              >
                Clear Selection
              </Button>
            </div>
            
            <div className="flex-1 overflow-y-auto border rounded-lg p-3">
              {isLoading ? (
                <div className="text-center py-4 text-sm text-muted-foreground">Loading...</div>
              ) : filteredBrands.length === 0 ? (
                <div className="text-center py-4 text-sm text-muted-foreground">No brands match your search</div>
              ) : (
                <RadioGroup 
                  value={selectedBrand || ""} 
                  onValueChange={(value) => onBrandSelect(value || null)}
                  className="space-y-2"
                >
                  {filteredBrands.map((brand) => (
                    <div key={brand} className="flex items-center space-x-2">
                      <RadioGroupItem value={brand} id={`brand-${brand}`} />
                      <Label htmlFor={`brand-${brand}`} className="text-sm cursor-pointer flex-1">
                        {brand}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              )}
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
          <DialogClose asChild>
            <Button onClick={onApply}>Apply</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
