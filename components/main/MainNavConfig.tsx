"use client"

import {
  IconChartBar,
  IconDashboard,
  IconFileAi,
  IconListDetails,
  IconReport,
  IconSettings,
  IconShoppingCart,
  IconWallet,
} from "@tabler/icons-react"

import type { NavItem } from "@/components/shared/AppLayout"

export const mainNavItems: NavItem[] = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: IconDashboard,
  },
  {
    title: "Brand Deep Dive",
    url: "/brand-deep-dive",
    icon: IconListDetails,
  },
  {
    title: "Marketing Dashboard",
    url: "/marketing-dashboard",
    icon: IconChartBar,
  },
  {
    title: "Amazon Dashboard",
    url: "/amazon-dashboard",
    icon: IconShoppingCart,
  },
  {
    title: "Executive Summary",
    url: "/executive-summary",
    icon: IconReport,
  },
  {
    title: "Budget",
    url: "/budget",
    icon: IconWallet,
  },
  {
    title: "AI Assistant",
    url: "/ai-assistant",
    icon: IconFileAi,
  },
]

export const mainSecondaryNavItems: NavItem[] = [
  {
    title: "Settings",
    url: "#",
    icon: IconSettings,
  },
]
