"use client"

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as Re<PERSON>rts<PERSON><PERSON><PERSON>,
  Responsive<PERSON>ontaine<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"
import {
  CHART_COLORS,
  formatCurrency as formatCurrencyUtil,
  formatDate as formatDateUtil
} from '@/lib/chart-utils'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "./ui/tabs"
import { useEffect, useState } from "react"

import { Skeleton } from "./ui/skeleton"
import { useBrandDeepDive } from '@/lib/contexts/brand-deep-dive-context'
import { useFilters } from '@/lib/contexts/filter-context'

interface CampaignDataPoint {
  date: string;
  totalSpend: number;
  [key: string]: number | string;
}

// Helper components to guarantee hooks are called in a consistent order
function DashboardCampaigns() {
  const dashboardContext = useFilters();
  return <CampaignsInternal filters={dashboardContext.filters} getQueryParams={dashboardContext.getQueryParams} />;
}

function BrandDeepDiveCampaigns() {
  const brandDeepDiveContext = useBrandDeepDive();
  
  console.log('[DEBUG] BrandDeepDiveCampaigns - Context state:', brandDeepDiveContext.state);
  
  // Convert brand deep dive context to the filter format
  const filters = {
    startDate: brandDeepDiveContext.state.startDate,
    endDate: brandDeepDiveContext.state.endDate,
    currency: brandDeepDiveContext.state.currency,
    groupBy: brandDeepDiveContext.state.groupBy,
    brands: brandDeepDiveContext.state.selectedBrand ? [brandDeepDiveContext.state.selectedBrand] : [],
    brandGroups: [],
    salesChannels: brandDeepDiveContext.state.salesChannels,
    countryNames: brandDeepDiveContext.state.countryNames,
  };
  
  console.log('[DEBUG] BrandDeepDiveCampaigns - Converted filters:', filters);
  
  // Simple getQueryParams function
  const getQueryParams = () => {
    const params = new URLSearchParams();
    if (filters.startDate) params.append('startDate', filters.startDate);
    if (filters.endDate) params.append('endDate', filters.endDate);
    if (filters.currency) params.append('currency', filters.currency);
    if (filters.groupBy) params.append('groupByTime', filters.groupBy);
    if (filters.brands.length > 0) params.append('brands', filters.brands.join(','));
    if (filters.salesChannels.length > 0) params.append('salesChannels', filters.salesChannels.join(','));
    if (filters.countryNames.length > 0) params.append('countryNames', filters.countryNames.join(','));
    const queryString = params.toString();
    console.log('[DEBUG] BrandDeepDiveCampaigns - Generated query params:', queryString);
    return queryString;
  };
  
  return <CampaignsInternal filters={filters} getQueryParams={getQueryParams} />;
}

export function MarketingCampaigns({ contextType = 'dashboard' }: { contextType?: 'dashboard' | 'brandDeepDive' }) {
  // Render the appropriate component based on context type
  if (contextType === 'brandDeepDive') {
    try {
      return <BrandDeepDiveCampaigns />;
    } catch (error) {
      console.warn('Brand deep dive context not available, falling back to dashboard context:', error);
      return <DashboardCampaigns />;
    }
  }
  
  return <DashboardCampaigns />;
}

// Internal component that accepts filters and query params directly
function CampaignsInternal({ 
  filters, 
  getQueryParams 
}: { 
  filters: {
    startDate: string;
    endDate: string;
    currency: string;
    groupBy: string;
    brands: string[];
    brandGroups: string[];
    salesChannels: string[];
    countryNames: string[];
  }; 
  getQueryParams: () => string; 
}) {
  const [campaignData, setCampaignData] = useState<CampaignDataPoint[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchData() {
      // Removed explicit brandName check
      try {
        setLoading(true)
        setError(null)

        const paramsString = getQueryParams()
        // Assuming an API endpoint exists or will be created for this
        // This replaces the mock data generation
        const query = paramsString; // The getQueryParams already returns a properly formatted URL query string
        const response = await fetch(`/api/marketing/campaign-data?${query}`)
        
        if (!response.ok) {
          throw new Error(`Failed to fetch campaign data: ${response.statusText}`)
        }
        const data = await response.json()
        
        console.log(`[DEBUG] Marketing Campaign Data Analysis:`, {
          totalDataPoints: data.length,
          dateRange: data.length > 0 ? {
            first: data[0]?.date,
            last: data[data.length - 1]?.date
          } : null,
          sampleData: data.slice(0, 3),
          groupByFromFilters: filters.groupBy
        });
        
        console.log(`[DEBUG] Chart will render with ${data.length} data points, XAxis interval=0 (showing all ticks)`);
        
        setCampaignData(data)

      } catch (err) {
        console.error("Failed to load campaign data:", err)
        setError(err instanceof Error ? err.message : "Failed to load campaign data")
      } finally {
        setLoading(false)
      }
    }

    // Only fetch if there are brands selected, or adjust as needed for your logic
    if (filters.brands && filters.brands.length > 0) {
      fetchData()
    } else {
      setCampaignData([]) // Clear data if no brand is selected
      setLoading(false)
    }
  }, [filters, getQueryParams])

  // Mock data generation function is no longer called directly but kept for reference if needed
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const generateMockCampaignData = (_brand: string | null | undefined) => {
    const platforms = ["Facebook", "Instagram", "Google", "TikTok", "Amazon"]
    const campaignTypes = ["Awareness", "Conversion", "Retargeting", "Seasonal"]
    
    // Generate 90 days of data
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 90)
    
    const data: CampaignDataPoint[] = []
    const currentDate = new Date(startDate)
    
    while (currentDate <= endDate) {
      const dateStr = currentDate.toISOString().split('T')[0]
      
      // Create data point for this date
      const dataPoint: CampaignDataPoint = { date: dateStr, totalSpend: 0 }
      
      // Add spend data for each platform
      let totalSpend = 0
      platforms.forEach((platform, index) => {
        // Generate a semi-random value that's consistent for the same date and platform
        const seed = dateStr.charCodeAt(dateStr.length - 1) + index
        const baseValue = (seed % 10) * 100 + 500
        const fluctuation = Math.sin(currentDate.getDate() / 7) * 200
        const spend = Math.max(100, baseValue + fluctuation)
        
        dataPoint[`${platform}Spend`] = Math.round(spend)
        totalSpend += spend
      })
      
      dataPoint.totalSpend = Math.round(totalSpend)
      
      // Add campaign type data
      campaignTypes.forEach((type, index) => {
        const seed = dateStr.charCodeAt(0) + index
        const baseValue = (seed % 5) * 200 + 300
        const fluctuation = Math.cos(currentDate.getDate() / 5) * 150
        dataPoint[`${type}Spend`] = Math.round(Math.max(100, baseValue + fluctuation))
      })
      
      data.push(dataPoint)
      
      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1)
    }
    
    return data
  }

  // Format helpers
  const formatCurrency = (value: number | null) => formatCurrencyUtil(value, filters.currency) // Use currency from filters

  // Recharts formatters need specific signatures
  const rechartsDateFormatter = (value: string) => formatDateUtil(value);
  const rechartsLabelFormatter = (label: string) => formatDateUtil(label);

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">{error}</div>
        </CardContent>
      </Card>
    )
  }

  // Removed !brandName check
  return (
    <Tabs defaultValue="platforms">
      <div className="flex items-center justify-between">
        <TabsList>
          <TabsTrigger value="platforms">Platforms</TabsTrigger>
          <TabsTrigger value="campaigns">Campaign Types</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>
      </div>
      
      <TabsContent value="platforms" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Ad Spend by Platform</CardTitle>
            <CardDescription>
              Breakdown of advertising spend across different platforms
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[400px]">
            {loading ? (
              <Skeleton className="h-full w-full" />
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={campaignData}
                  margin={{ top: 20, right: 30, left: 40, bottom: 100 }}
                  barCategoryGap="15%"
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis
                    dataKey="date"
                    tickFormatter={rechartsDateFormatter}
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={11}
                    interval={0}
                    tick={{ fontSize: 11 }}
                  />
                  <YAxis
                    tickFormatter={(value) =>
                      new Intl.NumberFormat("en-CA", {
                        notation: "compact",
                        compactDisplay: "short",
                      }).format(value)
                    }
                    fontSize={11}
                    width={60}
                  />
                  <RechartsTooltip
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    formatter={(value: any) => formatCurrency(value)}
                    labelFormatter={rechartsLabelFormatter}
                    contentStyle={{
                      backgroundColor: '#ffffff',
                      border: '1px solid #e2e8f0',
                      borderRadius: '8px',
                      fontSize: '12px',
                      boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05)',
                      padding: '12px',
                      color: '#1e293b',
                      opacity: 1,
                      minWidth: '200px',
                    }}
                    cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
                  />
                  <Legend
                    wrapperStyle={{
                      fontSize: '12px',
                      paddingTop: '20px'
                    }}
                    iconType="rect"
                  />
                  <Bar
                    dataKey="FacebookSpend"
                    name="Facebook"
                    fill={CHART_COLORS.blue}
                    radius={[3, 3, 0, 0]}
                    maxBarSize={80}
                    stroke="rgba(255,255,255,0.1)"
                    strokeWidth={1}
                  />
                  <Bar
                    dataKey="InstagramSpend"
                    name="Instagram"
                    fill={CHART_COLORS.purple}
                    radius={[3, 3, 0, 0]}
                    maxBarSize={80}
                    stroke="rgba(255,255,255,0.1)"
                    strokeWidth={1}
                  />
                  <Bar
                    dataKey="GoogleSpend"
                    name="Google"
                    fill={CHART_COLORS.red}
                    radius={[3, 3, 0, 0]}
                    maxBarSize={80}
                    stroke="rgba(255,255,255,0.1)"
                    strokeWidth={1}
                  />
                  <Bar
                    dataKey="TikTokSpend"
                    name="TikTok"
                    fill={CHART_COLORS.teal}
                    radius={[3, 3, 0, 0]}
                    maxBarSize={80}
                    stroke="rgba(255,255,255,0.1)"
                    strokeWidth={1}
                  />
                  <Bar
                    dataKey="AmazonSpend"
                    name="Amazon"
                    fill={CHART_COLORS.orange}
                    radius={[3, 3, 0, 0]}
                    maxBarSize={80}
                    stroke="rgba(255,255,255,0.1)"
                    strokeWidth={1}
                  />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="campaigns" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Ad Spend by Campaign Type</CardTitle>
            <CardDescription>
              Breakdown of advertising spend across different campaign types
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[400px]">
            {loading ? (
              <Skeleton className="h-full w-full" />
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={campaignData}
                  margin={{ top: 20, right: 30, left: 40, bottom: 100 }}
                  barCategoryGap="15%"
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis
                    dataKey="date"
                    tickFormatter={rechartsDateFormatter}
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={11}
                    interval={0}
                    tick={{ fontSize: 11 }}
                  />
                  <YAxis
                    tickFormatter={(value) =>
                      new Intl.NumberFormat("en-CA", {
                        notation: "compact",
                        compactDisplay: "short",
                      }).format(value)
                    }
                    fontSize={11}
                    width={60}
                  />
                  <RechartsTooltip
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    formatter={(value: any) => formatCurrency(value)}
                    labelFormatter={rechartsLabelFormatter}
                    contentStyle={{
                      backgroundColor: '#ffffff',
                      border: '1px solid #e2e8f0',
                      borderRadius: '8px',
                      fontSize: '12px',
                      boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05)',
                      padding: '12px',
                      color: '#1e293b',
                      opacity: 1,
                      minWidth: '200px',
                    }}
                    cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
                  />
                  <Legend
                    wrapperStyle={{
                      fontSize: '12px',
                      paddingTop: '20px'
                    }}
                    iconType="rect"
                  />
                  <Bar
                    dataKey="AwarenessSpend"
                    name="Awareness"
                    fill={CHART_COLORS.green}
                    radius={[3, 3, 0, 0]}
                    maxBarSize={80}
                    stroke="rgba(255,255,255,0.1)"
                    strokeWidth={1}
                  />
                  <Bar
                    dataKey="ConversionSpend"
                    name="Conversion"
                    fill={CHART_COLORS.blue}
                    radius={[3, 3, 0, 0]}
                    maxBarSize={80}
                    stroke="rgba(255,255,255,0.1)"
                    strokeWidth={1}
                  />
                  <Bar
                    dataKey="RetargetingSpend"
                    name="Retargeting"
                    fill={CHART_COLORS.purple}
                    radius={[3, 3, 0, 0]}
                    maxBarSize={80}
                    stroke="rgba(255,255,255,0.1)"
                    strokeWidth={1}
                  />
                  <Bar
                    dataKey="SeasonalSpend"
                    name="Seasonal"
                    fill={CHART_COLORS.orange}
                    radius={[3, 3, 0, 0]}
                    maxBarSize={80}
                    stroke="rgba(255,255,255,0.1)"
                    strokeWidth={1}
                  />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="performance" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Total Ad Spend Trend</CardTitle>
            <CardDescription>
              Overall advertising spend over time
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[400px]">
            {loading ? (
              <Skeleton className="h-full w-full" />
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={campaignData}
                  margin={{ top: 20, right: 30, left: 40, bottom: 100 }}
                  barCategoryGap="15%"
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis
                    dataKey="date"
                    tickFormatter={rechartsDateFormatter}
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={11}
                    interval={0}
                    tick={{ fontSize: 11 }}
                  />
                  <YAxis
                    tickFormatter={(value) =>
                      new Intl.NumberFormat("en-CA", {
                        notation: "compact",
                        compactDisplay: "short",
                      }).format(value)
                    }
                    fontSize={11}
                    width={60}
                  />
                  <RechartsTooltip
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    formatter={(value: any) => formatCurrency(value)}
                    labelFormatter={rechartsLabelFormatter}
                    contentStyle={{
                      backgroundColor: '#ffffff',
                      border: '1px solid #e2e8f0',
                      borderRadius: '8px',
                      fontSize: '12px',
                      boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05)',
                      padding: '12px',
                      color: '#1e293b',
                      opacity: 1,
                      minWidth: '200px',
                    }}
                    cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
                  />
                  <Legend
                    wrapperStyle={{
                      fontSize: '12px',
                      paddingTop: '20px'
                    }}
                    iconType="rect"
                  />
                  <Bar
                    dataKey="totalSpend"
                    name="Total Spend"
                    fill={CHART_COLORS.blue}
                    radius={[3, 3, 0, 0]}
                    maxBarSize={80}
                    stroke="rgba(255,255,255,0.1)"
                    strokeWidth={1}
                  />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
