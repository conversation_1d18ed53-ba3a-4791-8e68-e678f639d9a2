"use client"

import {
  // GroupedKpiResponse, // Removed as unused
  // SimpleKpiResponse // Removed as unused
} from '@/lib/api/dashboard-client'

import {
  Bar,
  BarChart,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"
import {
  CHART_COLORS,
  formatCurrency as formatCurrencyUtil,
  formatDate as formatDateUtil,
  formatPercent as formatPercentUtil,
} from '@/lib/chart-utils'
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card"
import { DollarSign, HelpCircle, Percent } from "lucide-react"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip"
import { fetchCampaignData, useCampaignData } from '@/lib/api/campaign-data-service'
import { useEffect, useState } from "react"

import { Button } from "./ui/button"
import { Skeleton } from "./ui/skeleton"
import { useBrandDeepDive } from '@/lib/contexts/brand-deep-dive-context'
import { useFilters } from '@/lib/contexts/filter-context'

type DisplayMode = "value" | "percent"

type KpiPair = {
  id: string
  name: string
  valueKey: string
  percentKey: string | null
  hasPercentage: boolean
  definition: string
  formatType: "currency" | "number" | "percent" | "ratio" // Added ratio for ROAS
}

// Using CampaignDataResponse from campaign-data-service.ts instead of defining a separate interface

// Structure for calculated summary KPIs
interface CalculatedSummaryKpis {
  totalSpend: number | null;
  totalImpressions: number | null;
  totalClicks: number | null;
  totalConversions: number | null;
  totalConversionValue: number | null;
  overallROAS: number | null;
  overallCPA: number | null;
  overallCTR: number | null;
}

// Structure for data points used in charts
interface ChartDataPoint {
  date: string;
  [key: string]: string | number | null; // To hold values like totalSpend, overallCTR for a given date
}


// Helper components to guarantee hooks are called in a consistent order
function DashboardKPICards() {
  const dashboardContext = useFilters();
  return <KPICardsInternal filters={dashboardContext.filters} getQueryParams={dashboardContext.getQueryParams} />;
}

function BrandDeepDiveKPICards() {
  const brandDeepDiveContext = useBrandDeepDive();
  
  // Convert brand deep dive context to the filter format
  const filters = {
    startDate: brandDeepDiveContext.state.startDate,
    endDate: brandDeepDiveContext.state.endDate,
    currency: brandDeepDiveContext.state.currency,
    groupBy: brandDeepDiveContext.state.groupBy,
    brands: brandDeepDiveContext.state.selectedBrand ? [brandDeepDiveContext.state.selectedBrand] : [],
    brandGroups: [],
    salesChannels: brandDeepDiveContext.state.salesChannels,
    countryNames: brandDeepDiveContext.state.countryNames,
  };
  
  // Simple getQueryParams function
  const getQueryParams = () => {
    const params = new URLSearchParams();
    if (filters.startDate) params.append('startDate', filters.startDate);
    if (filters.endDate) params.append('endDate', filters.endDate);
    if (filters.currency) params.append('currency', filters.currency);
    if (filters.groupBy) params.append('groupByTime', filters.groupBy);
    if (filters.brands.length > 0) params.append('brands', filters.brands.join(','));
    if (filters.salesChannels.length > 0) params.append('salesChannels', filters.salesChannels.join(','));
    if (filters.countryNames.length > 0) params.append('countryNames', filters.countryNames.join(','));
    return params.toString();
  };
  
  return <KPICardsInternal filters={filters} getQueryParams={getQueryParams} />;
}

export function OverallKPICards({ contextType = 'dashboard' }: { contextType?: 'dashboard' | 'brandDeepDive' }) {
  // Render the appropriate component based on context type
  if (contextType === 'brandDeepDive') {
    try {
      return <BrandDeepDiveKPICards />;
    } catch (error) {
      console.warn('Brand deep dive context not available, falling back to dashboard context:', error);
      return <DashboardKPICards />;
    }
  }
  
  return <DashboardKPICards />;
}

// Internal component that accepts filters and query params directly
function KPICardsInternal({ 
  filters, 
  getQueryParams 
}: { 
  filters: {
    startDate: string;
    endDate: string;
    currency: string;
    groupBy: string;
    brands: string[];
    brandGroups: string[];
    salesChannels: string[];
    countryNames: string[];
  }; 
  getQueryParams: () => string; 
}) {
  
  const queryParamsString = getQueryParams().toString()
  const { data: campaignDataPoints, loading, error: apiError } = useCampaignData(queryParamsString)
  
  // Force a refresh when the component mounts to ensure we're not using stale data
  useEffect(() => {
    // Immediate fetch when component mounts
    const refreshData = async () => {
      try {
        // Using the imported function directly ensures we get fresh data
        await fetchCampaignData(queryParamsString);
      } catch (error) {
        console.error("Error refreshing data on mount:", error);
      }
    };
    
    refreshData();
  }, [queryParamsString]); // Re-run when query params change
  
  const [summaryKpis, setSummaryKpis] = useState<CalculatedSummaryKpis | null>(null)
  const [chartTimeSeriesData, setChartTimeSeriesData] = useState<ChartDataPoint[]>([])
  const [error, setError] = useState<string | null>(null)
  const [displayModes, setDisplayModes] = useState<Record<string, DisplayMode>>({})

  // Process campaign data when it's loaded
  useEffect(() => {
    if (loading) {
      // Reset states while loading
      setSummaryKpis(null)
      setChartTimeSeriesData([])
      setError(null)
      return
    }

    if (apiError) {
      console.error("API Error:", apiError)
      setError(apiError)
      setSummaryKpis({
        totalSpend: null, totalImpressions: null, totalClicks: null,
        totalConversions: null, totalConversionValue: null,
        overallROAS: null, overallCPA: null, overallCTR: null,
      })
      setChartTimeSeriesData([])
      return
    }

    try {
      if (!Array.isArray(campaignDataPoints) || campaignDataPoints.length === 0) {
        console.warn("No campaign data returned from API or data is not an array.")
        setSummaryKpis({
          totalSpend: null, totalImpressions: null, totalClicks: null,
          totalConversions: null, totalConversionValue: null,
          overallROAS: null, overallCPA: null, overallCTR: null,
        })
        setChartTimeSeriesData([])
        return
      }

      // 1. Aggregate data for summary KPIs
      let aggSpend = 0
      let aggImpressions = 0
      let aggClicks = 0
      let aggConversions = 0
      let aggConversionValue = 0

      campaignDataPoints.forEach((point) => {
        aggSpend += point.totalSpend || 0
        aggImpressions += point.totalImpressions || 0
        aggClicks += point.totalClicks || 0
        aggConversions += point.totalConversions || 0
        aggConversionValue += point.totalConversionValue || 0
      })

      // 2. Calculate derived summary KPIs
      const roas = aggSpend > 0 ? aggConversionValue / aggSpend : null
      const cpa = aggConversions > 0 ? aggSpend / aggConversions : null
      const ctr = aggImpressions > 0 ? aggClicks / aggImpressions : null

      setSummaryKpis({
        totalSpend: aggSpend,
        totalImpressions: aggImpressions,
        totalClicks: aggClicks,
        totalConversions: aggConversions,
        totalConversionValue: aggConversionValue,
        overallROAS: roas,
        overallCPA: cpa,
        overallCTR: ctr,
      })

      // 3. Process data for charts (aggregating by date if 'date' field is present)
      const dailyAggregates: Record<string, ChartDataPoint> = {}

      campaignDataPoints.forEach((point) => {
        if (point.date) {
          if (!dailyAggregates[point.date]) {
            // Initialize with all potential keys to ensure chart data consistency
            dailyAggregates[point.date] = {
              date: point.date,
              totalSpend: 0, totalImpressions: 0, totalClicks: 0,
              totalConversions: 0, totalConversionValue: 0,
              overallROAS: 0, overallCPA: 0, overallCTR: 0, // Initialize derived metrics for daily calculation
            }
          }
          const dayData = dailyAggregates[point.date];
          // Aggregate base metrics
          dayData.totalSpend = (dayData.totalSpend as number || 0) + (point.totalSpend || 0);
          dayData.totalImpressions = (dayData.totalImpressions as number || 0) + (point.totalImpressions || 0);
          dayData.totalClicks = (dayData.totalClicks as number || 0) + (point.totalClicks || 0);
          dayData.totalConversions = (dayData.totalConversions as number || 0) + (point.totalConversions || 0);
          dayData.totalConversionValue = (dayData.totalConversionValue as number || 0) + (point.totalConversionValue || 0);
        }
      })
      
      // Calculate derived metrics for each day for chart data
      Object.values(dailyAggregates).forEach(dayData => {
        const dailySpend = dayData.totalSpend as number || 0;
        const dailyImpressions = dayData.totalImpressions as number || 0;
        const dailyClicks = dayData.totalClicks as number || 0;
        const dailyConversions = dayData.totalConversions as number || 0;
        const dailyConversionValue = dayData.totalConversionValue as number || 0;

        dayData.overallROAS = dailySpend > 0 ? dailyConversionValue / dailySpend : null;
        dayData.overallCPA = dailyConversions > 0 ? dailySpend / dailyConversions : null;
        dayData.overallCTR = dailyImpressions > 0 ? dailyClicks / dailyImpressions : null;
      });

      const newChartData = Object.values(dailyAggregates).sort(
        (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
      )
      setChartTimeSeriesData(newChartData);
    } catch (err) {
      console.error("Failed to process marketing KPI data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      setSummaryKpis({
        totalSpend: null, totalImpressions: null, totalClicks: null,
        totalConversions: null, totalConversionValue: null,
        overallROAS: null, overallCPA: null, overallCTR: null,
      })
      setChartTimeSeriesData([])
    }
  }, [campaignDataPoints, loading, apiError])

  // formatting helpers
  const formatCurrency = (v: number | null) => formatCurrencyUtil(v, filters.currency) // Use currency from filters
  const formatPercent = formatPercentUtil
  const formatDate = formatDateUtil
  const formatNumber = (v: number | null) => {
    if (v === null || isNaN(v)) return "N/A"
    return new Intl.NumberFormat("en-US", {
      maximumFractionDigits: 0,
    }).format(v)
  }

  const formatRatio = (v: number | null) => {
    if (v === null || isNaN(v)) return "N/A";
    return `${v.toFixed(2)}x`; // Example: 2.50x
  };

  // Recharts formatters need specific signatures
  const rechartsDateFormatter = (value: string) => formatDateUtil(value);
  const rechartsLabelFormatter = (label: string) => formatDateUtil(label);

  // define your KPIs
  const kpiPairs: KpiPair[] = [
    {
      id: "totalSpend",
      name: "Total Spend",
      valueKey: "totalSpend", // Changed to camelCase
      percentKey: null,
      hasPercentage: false,
      definition: "Total amount spent on marketing activities.",
      formatType: "currency",
    },
    {
      id: "totalImpressions",
      name: "Total Impressions",
      valueKey: "totalImpressions", // Changed to camelCase
      percentKey: null,
      hasPercentage: false,
      definition: "Total number of times ads were displayed.",
      formatType: "number",
    },
    {
      id: "totalClicks",
      name: "Total Clicks",
      valueKey: "totalClicks", // Changed to camelCase
      percentKey: null,
      hasPercentage: false,
      definition: "Total number of clicks on ads.",
      formatType: "number",
    },
    {
      id: "totalConversions",
      name: "Total Conversions",
      valueKey: "totalConversions", // Changed to camelCase
      percentKey: null,
      hasPercentage: false,
      definition: "Total number of desired actions taken (e.g., purchases, sign-ups).",
      formatType: "number",
    },
    {
      id: "totalConversionValue",
      name: "Total Conversion Value",
      valueKey: "totalConversionValue", // Changed to camelCase
      percentKey: null,
      hasPercentage: false,
      definition: "Total monetary value generated from conversions.",
      formatType: "currency",
    },
    {
      id: "overallROAS",
      name: "Overall ROAS",
      valueKey: "overallROAS", // Changed to camelCase
      percentKey: null,
      hasPercentage: false,
      definition: "Return On Ad Spend. Calculated as (Sum of totalConversionValue) / (Sum of totalSpend).",
      formatType: "ratio",
    },
    {
      id: "overallCPA",
      name: "Overall CPA",
      valueKey: "overallCPA", // Changed to camelCase
      percentKey: null,
      hasPercentage: false,
      definition: "Cost Per Acquisition. Calculated as (Sum of totalSpend) / (Sum of totalConversions).",
      formatType: "currency",
    },
    {
      id: "overallCTR",
      name: "Overall CTR",
      valueKey: "overallCTR", // Changed to camelCase
      percentKey: null,
      hasPercentage: false,
      definition: "Click-Through Rate. Calculated as (Sum of totalClicks) / (Sum of totalImpressions). Displayed as a percentage.",
      formatType: "percent",
    },
  ]

  // initialize display modes
  useEffect(() => {
    const modes: Record<string, DisplayMode> = {}
    kpiPairs.forEach((k) => {
      modes[k.id] = "value"
    })
    setDisplayModes(modes)
  }, [])

  // build chart data by date is now handled in useEffect, populating chartTimeSeriesData

  const customTooltipFormatter = (value: unknown, name: string) => {
    if (typeof value !== "number") return "N/A"
    // Find the kpiPair by dataKey (which is `name` in this context)
    const kpiConfig = kpiPairs.find(k => k.valueKey === name || k.percentKey === name);

    if (kpiConfig) {
      // If it's the percentKey being displayed, always format as percent
      if (kpiConfig.percentKey === name) {
        return formatPercent(value);
      }
      // Otherwise, use the defined formatType for the valueKey
      switch (kpiConfig.formatType) {
        case "currency":
          return formatCurrency(value);
        case "number":
          return formatNumber(value);
        case "percent":
          return formatPercent(value);
        case "ratio":
          return formatRatio(value);
        default:
          return formatCurrency(value); // Default fallback
      }
    }
    // Fallback for safety, though should ideally always find a kpiConfig
    // This fallback might need adjustment based on new KPI types
    return formatCurrency(value)
  }

  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="col-span-full">
          <CardContent className="p-6">
            <div className="text-center text-red-500">{error}</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Removed !brandName check as it's handled by filter context and loading/error states
  return (
    <div className="px-4 lg:px-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium">Overall KPI Summary</h3>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4"> {/* Adjusted grid for potentially 8 KPIs */}
        {kpiPairs.map((kpi) => (
          <Card key={kpi.id}>
            <CardHeader className="flex items-center justify-between pb-2">
              <div className="flex items-center space-x-1">
                <CardTitle className="text-sm font-medium">
                  {kpi.name}
                </CardTitle>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5 p-0"
                      >
                        <HelpCircle className="h-3 w-3" />
                        <span className="sr-only">KPI Definition</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">{kpi.definition}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              {kpi.hasPercentage && (
                <div className="flex items-center space-x-1">
                  <Button
                    variant={
                      displayModes[kpi.id] === "value" ? "default" : "outline"
                    }
                    size="icon"
                    className="h-6 w-6"
                    onClick={() =>
                      setDisplayModes((p) => ({ ...p, [kpi.id]: "value" }))
                    }
                    title="Show Values"
                  >
                    <DollarSign className="h-3 w-3" />
                  </Button>
                  <Button
                    variant={
                      displayModes[kpi.id] === "percent"
                        ? "default"
                        : "outline"
                    }
                    size="icon"
                    className="h-6 w-6"
                    onClick={() =>
                      setDisplayModes((p) => ({
                        ...p,
                        [kpi.id]: "percent",
                      }))
                    }
                    title="Show Percentages"
                  >
                    <Percent className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-2">
                {loading ? (
                  <Skeleton className="h-8 w-[120px]" />
                ) : kpi.hasPercentage && displayModes[kpi.id] === "percent" && kpi.percentKey ? (
                  // This branch is unlikely to be hit for marketing KPIs as hasPercentage is false
                  formatPercent(summaryKpis?.[kpi.percentKey as keyof CalculatedSummaryKpis] ?? null)
                ) : kpi.formatType === "number" ? (
                  formatNumber(summaryKpis?.[kpi.valueKey as keyof CalculatedSummaryKpis] ?? null)
                ) : kpi.formatType === "percent" ? (
                  // overallCTR is calculated as a ratio (e.g., 0.05)
                  // formatPercent expects a ratio and formats it as a percentage string (e.g., "5.0%")
                  formatPercent(summaryKpis?.[kpi.valueKey as keyof CalculatedSummaryKpis] ?? null)
                ) : kpi.formatType === "ratio" ? (
                  formatRatio(summaryKpis?.[kpi.valueKey as keyof CalculatedSummaryKpis] ?? null)
                ) : ( // Default to currency
                  formatCurrency(summaryKpis?.[kpi.valueKey as keyof CalculatedSummaryKpis] ?? null)
                )}
              </div>
              <div className="h-[120px]">
                {loading ? (
                  <Skeleton className="h-full w-full" />
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={chartTimeSeriesData} // Use the new state for chart data
                      margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
                    >
                      <XAxis
                        dataKey="date"
                        tickFormatter={rechartsDateFormatter}
                        minTickGap={30}
                        tick={{ fontSize: 10 }}
                        height={15}
                      />
                      <YAxis
                        tickFormatter={(val) => {
                          if (displayModes[kpi.id] === "percent" && kpi.hasPercentage) {
                            return new Intl.NumberFormat("en-US", {
                              style: "percent",
                              minimumFractionDigits: 0, // Adjusted for cleaner y-axis percentages
                              maximumFractionDigits: 1,
                            }).format(val); // This 'val' is likely 0-1 for percentages from Recharts
                          }
                          switch (kpi.formatType) {
                            case "currency":
                              if (Math.abs(val) >= 1000000) return `$${(val / 1000000).toFixed(val < 10000000 ? 1 : 0)}M`;
                              if (Math.abs(val) >= 1000) return `$${(val / 1000).toFixed(0)}K`;
                              return formatCurrencyUtil(val, filters.currency);
                            case "number":
                              if (Math.abs(val) >= 1000000) return `${(val / 1000000).toFixed(val < 10000000 ? 1 : 0)}M`;
                              if (Math.abs(val) >= 1000) return `${(val / 1000).toFixed(0)}K`;
                              return new Intl.NumberFormat("en-US", { maximumFractionDigits: 0 }).format(val);
                            case "percent": // For KPIs that are always percent (like CTR)
                               return new Intl.NumberFormat("en-US", {
                                style: "percent",
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 1, // val is 0-1 from recharts
                              }).format(val); // val is expected to be like 0.25 for 25%
                            case "ratio": // For ROAS
                                return `${val.toFixed(1)}x`; // e.g. 2.5x
                            default:
                              return new Intl.NumberFormat("en-US", { maximumFractionDigits: 0 }).format(val);
                          }
                        }}
                        tick={{ fontSize: 10 }}
                        width={kpi.formatType === 'currency' || kpi.formatType === 'ratio' ? 60 : 50}
                      />
                      <CartesianGrid strokeDasharray="3 3" />
                      <RechartsTooltip
                        formatter={customTooltipFormatter}
                        labelFormatter={rechartsLabelFormatter}
                      />
                      <Bar
                        dataKey={
                          kpi.hasPercentage &&
                          displayModes[kpi.id] === "percent" &&
                          kpi.percentKey
                            ? kpi.percentKey
                            : kpi.valueKey
                        }
                        fill={CHART_COLORS.orange}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
