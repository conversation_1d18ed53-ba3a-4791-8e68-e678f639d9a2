"use client"

import * as React from "react"

import { <PERSON><PERSON><PERSON>l<PERSON>oader, ChartLoader } from "@/components/ui/chart-loader"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  CartesianGrid,
  Line,
  LineChart,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from "recharts"
import { ChartConfig, ChartContainer } from "@/components/ui/chart"
import {
  FlexibleKpiQueryParams,
  GroupedKpiResponse,
  fetchKpiData,
} from '@/lib/api/dashboard-client'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  formatDate as formatDateUtil,
  formatKpiValue,
  getBrandColor as getUtilBrandColor,
} from "@/lib/chart-utils"

import { ALL_AVAILABLE_KPIS_LIST } from "@/lib/constants"
import { useFilters } from "@/lib/contexts/filter-context"

// Explicitly define the colors we need
const CHART_COLORS = {
  blue: "#2563EB",
  cyan: "#0EA5E9",
  azure: "#0284C7", // Azure blue for grid lines
  purple: "#9333EA",
  pink: "#EC4899",
  orange: "#F97316",
  yellow: "#EAB308",
  green: "#22C55E",
  red: "#EF4444",
  gray: "#6B7280",
  indigo: "#6366F1",
  teal: "#14B8A6",
  amber: "#F59E0B",
  emerald: "#10B981",
  lime: "#84CC16",
  rose: "#F43F5E",
}


// ————————————————————————————————————————————————————————————————
//  Utility
// ————————————————————————————————————————————————————————————————
function getBrandColor(brandName: string, index: number) {
  return getUtilBrandColor(brandName, index)
}

// ————————————————————————————————————————————————————————————————
//  Types
// ————————————————————————————————————————————————————————————————
type ChartDataPoint = {
  date: string
  [brandName: string]: number | string | null
}

interface TooltipPayloadItem {
  name: string
  value: number | null
  color: string
}

interface CustomTooltipProps {
  active?: boolean
  payload?: TooltipPayloadItem[]
  label?: string
}

// ————————————————————————————————————————————————————————————————
//  Component
// ————————————————————————————————————————————————————————————————
export function MasterKpiComparisonChart() {
  const { filters, getQueryParams } = useFilters()

  const [selectedKpi, setSelectedKpi] = React.useState<string>(
    ALL_AVAILABLE_KPIS_LIST[0],
  )
  const [chartData, setChartData] = React.useState<ChartDataPoint[]>([])
  const [allFetchedBrands, setAllFetchedBrands] = React.useState<string[]>([])
  const [visibleBrands, setVisibleBrands] = React.useState<string[]>([])
  const [sortedBrandsForPanel, setSortedBrandsForPanel] = React.useState<
    string[]
  >([])
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)
  const [chartConfig, setChartConfig] = React.useState<ChartConfig>({})

  // ————————————————————————————————————————————————————————————
  //  CSS-var → color resolver (unchanged)
  // ————————————————————————————————————————————————————————————
  function getResolvedColor(cssVariableString: string, depth = 0): string {
    const FALLBACK_COLOR = "#888888"
    const MAX_RECURSION_DEPTH = 5
    if (typeof window === "undefined") return cssVariableString
    if (depth > MAX_RECURSION_DEPTH) return FALLBACK_COLOR

    let currentString = cssVariableString
    const seenVars = new Set<string>()

    for (let i = 0; i < MAX_RECURSION_DEPTH; i++) {
      const variableMatch = currentString.match(/var\((--[^)]+)\)/)
      if (!variableMatch) break
      const variableName = variableMatch[1]
      if (seenVars.has(variableName)) return FALLBACK_COLOR
      seenVars.add(variableName)

      const style = getComputedStyle(document.documentElement)
      const resolvedValue = style.getPropertyValue(variableName).trim()
      if (!resolvedValue) return FALLBACK_COLOR

      currentString = currentString.replace(variableMatch[0], resolvedValue)
    }

    const value = currentString.trim()
    if (
      value.startsWith("oklch(") ||
      value.startsWith("rgb(") ||
      value.startsWith("rgba(") ||
      value.startsWith("#")
    )
      return value
    return FALLBACK_COLOR
  }

  // ————————————————————————————————————————————————————————————
  //  Data fetch
  // ————————————————————————————————————————————————————————————
  // Create a ref to store the AbortController
  const abortControllerRef = React.useRef<AbortController | null>(null);

  React.useEffect(() => {
    async function loadData() {
      if (!selectedKpi) return
      
      // Abort any in-flight requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      // Create a new AbortController for this request
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;
      
      setLoading(true)
      setError(null)

      try {
        // Directly use filter values from the context for clarity and correctness
        const params: FlexibleKpiQueryParams = {
          startDate: filters.startDate,
          endDate: filters.endDate,
          currency: filters.currency,
          kpis: [selectedKpi],
          groupByDimension: "brand",
          groupByTime: filters.groupBy, // This correctly uses the selected grouping
          // Include all filter parameters
          brands: filters.brands.length > 0 ? filters.brands : undefined,
          salesChannels: filters.salesChannels.length > 0 ? filters.salesChannels : undefined,
          countryNames: filters.countryNames.length > 0 ? filters.countryNames : undefined,
          brandGroups: filters.brandGroups.length > 0 ? filters.brandGroups : undefined
        };

        // Pass the abort signal to the fetch operation
        const rawData = await fetchKpiData(params, signal)
        const kpiDataForSelected = (rawData as GroupedKpiResponse)[selectedKpi]

        if (!kpiDataForSelected) {
          setChartData([])
          setAllFetchedBrands([])
          setVisibleBrands([])
          setSortedBrandsForPanel([])
          setChartConfig({})
          setLoading(false)
          return
        }

        const fetchedBrands = Object.keys(kpiDataForSelected)
        setAllFetchedBrands(fetchedBrands)
        
        const selectedBrandsFromFilter = filters.brands;
        
        let initialBrandsForDisplay: string[];
        if (selectedBrandsFromFilter.length === 0) {
          initialBrandsForDisplay = fetchedBrands;
        } else {
          initialBrandsForDisplay = fetchedBrands.filter(brand =>
            selectedBrandsFromFilter.includes(brand)
          );
        }
        setVisibleBrands(initialBrandsForDisplay); // Sets initial visibility for chart lines

        const newChartConfig: ChartConfig = {}
        fetchedBrands.forEach((brandName, idx) => {
          newChartConfig[brandName] = {
            label: brandName,
            color: getBrandColor(brandName, idx),
          }
        })
        setChartConfig(newChartConfig)

        const dateMap: { [date: string]: ChartDataPoint } = {}
        fetchedBrands.forEach((brand) => {
          kpiDataForSelected[brand]?.timeSeries?.forEach(
            (pt: { date: string; value: number | null }) => {
              if (!dateMap[pt.date]) dateMap[pt.date] = { date: pt.date }
              dateMap[pt.date][brand] = pt.value
            },
          )
        })

        const processed = Object.values(dateMap).sort(
          (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
        )

        const finalData = processed.map(({ date }) => {
          const point: ChartDataPoint = { date }
          fetchedBrands.forEach(
            (brand) =>
              (point[brand] =
                processed.find((p) => p.date === date)?.[brand] ?? null),
          )
          return point
        })

        setChartData(finalData)

        if (finalData.length) {
          const last = finalData[finalData.length - 1];
          const sorted = initialBrandsForDisplay // Use the filtered list
            .map((b) => ({
              name: b,
              value: typeof last[b] === "number" ? (last[b] as number) : -Infinity,
            }))
            .sort((a, b) => b.value - a.value)
            .map((b) => b.name);
          setSortedBrandsForPanel(sorted);
        } else {
          setSortedBrandsForPanel(initialBrandsForDisplay.slice().sort()); // Use the filtered list
        }
      } catch (err) {
        // Don't show errors for aborted requests
        if ((err as Error).name !== 'AbortError') {
          setError(err instanceof Error ? err.message : "Failed to load data")
          setChartData([])
          setAllFetchedBrands([])
          setVisibleBrands([])
          setSortedBrandsForPanel([])
          setChartConfig({})
        }
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [selectedKpi, filters, getQueryParams])

  // ————————————————————————————————————————————————————————————
  //  Helpers
  // ————————————————————————————————————————————————————————————
  // Create a chart-specific date formatter that adapts our utility to Recharts API
  const formatDate = (value: string | number) => {
    return formatDateUtil(String(value));
  }
  
  const formatValue = (val: number | null) =>
    formatKpiValue(val, selectedKpi, filters.currency)

  const yAxisTickFormatter = (val: number) => {
    const isPct = selectedKpi.startsWith("%")
    const opts: Intl.NumberFormatOptions = {
      style: isPct ? "percent" : "decimal",
      minimumFractionDigits: 0,
      maximumFractionDigits: isPct ? 1 : 0,
    }
    if (!isPct && Math.abs(val) >= 1000) {
      opts.notation = "compact"
      opts.compactDisplay = "short"
    }
    return new Intl.NumberFormat("en-CA", opts).format(val)
  }

  // Helper function to check if a brand is visible
  const isBrandVisible = (brand: string): boolean => {
    return visibleBrands.includes(brand)
  }

  // Helper function to toggle brand visibility
  const toggleBrandVisibility = (brand: string) => {
    setVisibleBrands((prev) => {
      if (prev.includes(brand)) {
        return prev.filter(b => b !== brand)
      } else {
        return [...prev, brand]
      }
    })
  }

  const CustomTooltipContent = ({
    active,
    payload,
    label,
  }: CustomTooltipProps) => {
    if (!active || !payload?.length || !chartData.length) return null

    const formattedLabel = label ? formatDate(label) : ""
    const items = payload
      .filter((p) => {
        // If no brands are selected in filters, show all visible brands
        if (filters.brands.length === 0) {
          return isBrandVisible(p.name);
        }
        // If brands are selected in filters, only show those brands
        return filters.brands.includes(p.name) && isBrandVisible(p.name);
      })
      .sort((a, b) => (Number(b.value ?? -Infinity) - Number(a.value ?? -Infinity)))
    if (!items.length) return null

    return (
      <Card className="pointer-events-none w-[300px] bg-background shadow-xl ring-1 ring-border">
        <CardContent className="p-3 space-y-[5px] text-sm">
          <p className="font-medium text-muted-foreground">{formattedLabel}</p>
          {items.map((it, idx) => (
            <div key={idx} className="flex items-center justify-between">
              <span className="flex items-center gap-1">
                <span
                  className="inline-block h-2 w-2 rounded-full"
                  style={{ backgroundColor: it.color }}
                />
                {it.name}
              </span>
              <span className="tabular-nums">{formatValue(it.value)}</span>
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }

  // ————————————————————————————————————————————————————————————
  //  Render
  // ————————————————————————————————————————————————————————————
  if (error)
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Master KPI Comparison</CardTitle>
          <CardDescription>Error loading data: {error}</CardDescription>
        </CardHeader>
      </Card>
    )

  if (loading) {
    return (
      <div className="flex w-full flex-col gap-3 sm:gap-4 xl:flex-row">
        <ChartLoader 
          title="Master KPI Comparison"
          description={`Compare '${selectedKpi}' across selected brands using a line chart.`}
          height="h-[300px] sm:h-[400px]"
          className="flex-grow min-w-0"
        />
        <BrandPanelLoader />
      </div>
    )
  }

  return (
    <div className="flex w-full flex-col gap-3 sm:gap-4 xl:flex-row">
      {/* ——— Chart ——— */}
      <Card className="flex-grow min-w-0">
        <CardHeader className="pb-3 sm:pb-6">
          <div className="flex flex-col items-start gap-3 sm:gap-4 lg:flex-row lg:items-center">
            <div className="flex-grow min-w-0">
              <CardTitle className="text-lg sm:text-xl">Master KPI Comparison</CardTitle>
              <CardDescription className="text-sm">
                Compare &apos;{selectedKpi}&apos; across selected brands using a
                line chart.
              </CardDescription>
            </div>

            <Select value={selectedKpi} onValueChange={setSelectedKpi}>
              <SelectTrigger className="w-full sm:w-[280px] lg:w-[240px] xl:w-[280px]" aria-label="Select KPI">
                <SelectValue placeholder="Select a KPI" />
              </SelectTrigger>
              <SelectContent>
                {ALL_AVAILABLE_KPIS_LIST.map((kpi) => (
                  <SelectItem key={kpi} value={kpi}>
                    {kpi}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>

        <CardContent className="h-[300px] sm:h-[400px] pt-3 sm:pt-6">
          {!chartData.length ? (
            <div className="flex h-full items-center justify-center text-muted-foreground">
              No data available for the selected KPI and filters.
            </div>
          ) : (
            <ChartContainer config={chartConfig} className="h-full w-full">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{ top: 5, right: 20, left: 10, bottom: 5 }}
                >
                  <CartesianGrid
                    strokeDasharray="3 3"
                    vertical={false}
                    stroke={CHART_COLORS.azure}
                    opacity={0.2}
                  />
                  <XAxis
                    dataKey="date"
                    tickFormatter={formatDate}
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    minTickGap={20}
                  />
                  <YAxis
                    tickFormatter={yAxisTickFormatter}
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    width={
                      filters.currency === "USD" || selectedKpi.startsWith("%")
                        ? 80
                        : 70
                    }
                  />

                  {/* Tooltip with 5 px spacing between items & width 300px */}
                  <RechartsTooltip
                    content={<CustomTooltipContent />}
                    cursor={{ strokeDasharray: "3 3" }}
                    wrapperStyle={{
                      backgroundColor: "white",
                      borderRadius: "4px",
                      width: 300,
                      maxWidth: 300,
                    }}
                  />

                  {allFetchedBrands
                    .filter((b) => {
                      // If no brands are selected in filters, show all visible brands
                      if (filters.brands.length === 0) {
                        return isBrandVisible(b);
                      }
                      // If brands are selected in filters, only show those brands
                      return filters.brands.includes(b) && isBrandVisible(b);
                    })
                    .map((brand) => (
                      <Line
                        key={brand}
                        type="monotone"
                        dataKey={brand}
                        stroke={
                          chartConfig[brand]?.color ||
                          getResolvedColor(
                            getBrandColor(
                              brand,
                              allFetchedBrands.indexOf(brand),
                            ),
                          )
                        }
                        strokeWidth={2}
                        dot={false}
                        activeDot={{ r: 6 }}
                        animationDuration={500}
                      />
                    ))}
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          )}
        </CardContent>
      </Card>

      {/* ——— Brand toggles ——— */}
      <Card className="w-full flex-shrink-0 xl:w-[280px]">
        <CardHeader className="pb-3 sm:pb-6">
          <CardTitle className="text-base sm:text-lg">Brands</CardTitle>
          <CardDescription className="text-sm">Click to toggle. Sorted by latest KPI.</CardDescription>
        </CardHeader>
        <CardContent className="h-[250px] sm:h-[300px] xl:h-[400px] overflow-y-auto pt-2">
          {!sortedBrandsForPanel.length ? (
            <div className="flex h-full items-center justify-center text-sm text-muted-foreground">
              No brands found or data available.
            </div>
          ) : (
            <ul className="space-y-1">
              {sortedBrandsForPanel.map((brand) => (
                <li
                  key={brand}
                  className={`flex cursor-pointer items-center gap-2 rounded-md p-1.5 text-sm hover:bg-muted ${
                    isBrandVisible(brand)
                      ? "font-semibold"
                      : "opacity-60 hover:opacity-100"
                  }`}
                  onClick={() => toggleBrandVisibility(brand)}
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault()
                      toggleBrandVisibility(brand)
                    }
                  }}
                  aria-pressed={isBrandVisible(brand)}
                  aria-label={`Toggle visibility for ${brand}`}
                >
                  <span
                    className="h-3.5 w-3.5 flex-shrink-0 rounded-sm"
                    style={{
                      backgroundColor:
                        chartConfig[brand]?.color ||
                        getResolvedColor(
                          getBrandColor(brand, allFetchedBrands.indexOf(brand)),
                        ),
                    }}
                  />
                  <span className="truncate flex-grow">
                    {chartConfig[brand]?.label || brand}
                  </span>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
