"use client"

import { BookmarkIcon, PencilIcon, PlusIcon, TrashIcon } from 'lucide-react';
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from './ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from './ui/dropdown-menu';
import { FilterState, useFilters } from '@/lib/contexts/filter-context';
import { SavedView, createSavedView, deleteSavedView, getSavedViews, updateSavedView } from '@/lib/api/saved-views-client';
import { useEffect, useRef, useState } from 'react';

import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { toast } from 'sonner';
import { useSession } from 'next-auth/react';

interface SavedViewsMenuProps {
  pageType: 'brand-deep-dive' | 'marketing-dashboard' | 'dashboard';
}

export function SavedViewsMenu({ pageType }: SavedViewsMenuProps) {
  const { filters, setFilters } = useFilters();
  const { data: session } = useSession();
  const [savedViews, setSavedViews] = useState<SavedView[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // State for save dialog
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [viewName, setViewName] = useState('');
  const [editingViewId, setEditingViewId] = useState<number | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  
  // State for delete confirmation
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [viewToDelete, setViewToDelete] = useState<SavedView | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch saved views on component mount and when pageType changes
  useEffect(() => {
    fetchSavedViews();
  }, [pageType]);

  // Function to fetch saved views
  const fetchSavedViews = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const views = await getSavedViews(pageType);
      setSavedViews(views);
    } catch (error) {
      console.error('Failed to fetch saved views:', error);
      setError('Failed to load saved views');
      toast.error('Failed to load saved views');
    } finally {
      setIsLoading(false);
    }
  };

  // References to the dialog close buttons
  const saveDialogCloseRef = useRef<HTMLButtonElement>(null);
  const deleteDialogCloseRef = useRef<HTMLButtonElement>(null);

  // Function to handle saving a view
  const handleSaveView = async () => {
    if (!viewName.trim()) {
      toast.error('Please enter a name for your view');
      return;
    }

    // Check if user is logged in
    if (!session?.user) {
      toast.error('Please log in to save views');
      return;
    }

    // Prevent multiple clicks
    if (isSaving) return;
    
    setIsSaving(true);

    try {
      let result;
      if (editingViewId) {
        // Update existing view
        result = await updateSavedView(editingViewId, {
          name: viewName,
          filter_data: filters
        });
        if (result) {
          // Reset form and refresh views
          setViewName('');
          setEditingViewId(null);
          
          // Close the dialog programmatically
          if (saveDialogCloseRef.current) {
            saveDialogCloseRef.current.click();
          }
          
          // Show success message and refresh views
          toast.success('View updated successfully');
          fetchSavedViews();
        } else {
          toast.error('Please log in to save views');
        }
      } else {
        // Create new view
        result = await createSavedView(viewName, pageType, filters);
        if (result) {
          // Reset form and refresh views
          setViewName('');
          setEditingViewId(null);
          
          // Close the dialog programmatically
          if (saveDialogCloseRef.current) {
            saveDialogCloseRef.current.click();
          }
          
          // Show success message and refresh views
          toast.success('View saved successfully');
          fetchSavedViews();
        } else {
          toast.error('Please log in to save views');
        }
      }
    } catch (error) {
      console.error('Failed to save view:', error);
      if (error instanceof Error && error.message.includes('already exists')) {
        toast.error('A view with this name already exists');
      } else {
        toast.error('Failed to save view');
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Function to handle loading a view
  const handleLoadView = (view: SavedView) => {
    try {
      const filterData = JSON.parse(view.filter_data) as FilterState;
      setFilters(filterData);
      toast.success(`Loaded view: ${view.name}`);
    } catch (error) {
      console.error('Failed to parse filter data:', error);
      toast.error('Failed to load view');
    }
  };

  // Function to handle editing a view
  const handleEditView = (view: SavedView) => {
    // Check if user is logged in
    if (!session?.user) {
      toast.error('Please log in to edit views');
      return;
    }
    
    setViewName(view.name);
    setEditingViewId(view.id);
    setSaveDialogOpen(true);
  };

  // Function to handle deleting a view
  const handleDeleteView = async () => {
    if (!viewToDelete) return;
    
    // Check if user is logged in
    if (!session?.user) {
      toast.error('Please log in to delete views');
      return;
    }
    
    // Prevent multiple clicks
    if (isDeleting) return;
    
    setIsDeleting(true);
    
    try {
      const success = await deleteSavedView(viewToDelete.id);
      if (success) {
        // Reset state
        setViewToDelete(null);
        
        // Close the dialog programmatically
        if (deleteDialogCloseRef.current) {
          deleteDialogCloseRef.current.click();
        }
        
        // Show success message and refresh views
        toast.success('View deleted successfully');
        fetchSavedViews();
      } else {
        toast.error('Please log in to delete views');
      }
    } catch (error) {
      console.error('Failed to delete view:', error);
      toast.error('Failed to delete view');
    } finally {
      setIsDeleting(false);
    }
  };

  // Function to open delete confirmation dialog
  const confirmDeleteView = (view: SavedView) => {
    // Check if user is logged in
    if (!session?.user) {
      toast.error('Please log in to delete views');
      return;
    }
    
    setViewToDelete(view);
    setDeleteDialogOpen(true);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="h-9">
            <BookmarkIcon className="h-4 w-4 mr-2" />
            Saved Views
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Saved Views</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {isLoading ? (
            <div className="px-2 py-1.5 text-sm text-center">Loading...</div>
          ) : error ? (
            <div className="px-2 py-1.5 text-sm text-center text-red-500">{error}</div>
          ) : savedViews.length === 0 ? (
            <div className="px-2 py-1.5 text-sm text-center text-muted-foreground">No saved views</div>
          ) : (
            savedViews.map((view) => (
              <div key={view.id} className="px-1">
                <div className="flex items-center justify-between rounded-sm px-2 py-1.5 hover:bg-accent hover:text-accent-foreground">
                  <button 
                    className="flex-1 text-left text-sm"
                    onClick={() => handleLoadView(view)}
                  >
                    {view.name}
                  </button>
                  <div className="flex items-center">
                    <button
                      className="h-8 w-8 p-0 rounded-sm hover:bg-primary/10 flex items-center justify-center"
                      onClick={() => handleEditView(view)}
                      title="Edit view"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      className="h-8 w-8 p-0 rounded-sm hover:bg-destructive/10 flex items-center justify-center"
                      onClick={() => confirmDeleteView(view)}
                      title="Delete view"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
          
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => {
            if (!session?.user) {
              toast.error('Please log in to save views');
              return;
            }
            setViewName('');
            setEditingViewId(null);
            setSaveDialogOpen(true);
          }}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Save Current View
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Save View Dialog */}
      <Dialog open={saveDialogOpen} onOpenChange={setSaveDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          {/* Hidden DialogClose button that we can trigger programmatically */}
          <DialogClose ref={saveDialogCloseRef} className="hidden" />
          <DialogHeader>
            <DialogTitle>
              {editingViewId ? 'Update Saved View' : 'Save Current View'}
            </DialogTitle>
            <DialogDescription>
              {editingViewId 
                ? 'Update the name of your saved view.' 
                : 'Save your current filter settings as a named view for quick access later.'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="view-name" className="text-right">
                Name
              </Label>
              <Input
                id="view-name"
                value={viewName}
                onChange={(e) => setViewName(e.target.value)}
                className="col-span-3"
                placeholder="My Custom View"
                autoFocus
              />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button 
              type="submit" 
              onClick={handleSaveView} 
              className="ml-2"
              disabled={isSaving}
            >
              {isSaving ? 'Saving...' : (editingViewId ? 'Update' : 'Save')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          {/* Hidden DialogClose button that we can trigger programmatically */}
          <DialogClose ref={deleteDialogCloseRef} className="hidden" />
          <DialogHeader>
            <DialogTitle>Delete Saved View</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this saved view? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {viewToDelete && (
              <div className="flex items-center justify-center p-4 border rounded-md bg-muted">
                <span className="font-medium">{viewToDelete.name}</span>
              </div>
            )}
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button 
              variant="destructive" 
              onClick={handleDeleteView}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
