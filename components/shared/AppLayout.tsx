"use client"

import * as React from "react"

import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"

import { AppSidebar } from "@/components/app-sidebar"
import { SiteHeader } from "@/components/site-header"

// Define types for navigation items
export type NavItem = {
  title: string;
  url: string;
  icon?: React.ElementType;
};

interface AppLayoutProps {
  children: React.ReactNode;
  navItems?: NavItem[];
  secondaryNavItems?: NavItem[];
  logoText?: string;
  logoHref?: string;
  pageTitle?: string;
  pageDescription?: string;
  showPageHeader?: boolean;
  contentClassName?: string;
}

export function AppLayout({ 
  children,
  navItems,
  secondaryNavItems,
  logoText = "NOLK",
  logoHref = "/dashboard",
  pageTitle,
  pageDescription,
  showPageHeader = false,
  contentClassName = "flex flex-col gap-3 py-3 sm:gap-4 sm:py-4 md:gap-6 md:py-6 px-3 sm:px-4 lg:px-6"
}: AppLayoutProps) {
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar 
        variant="inset" 
        navItems={navItems}
        secondaryNavItems={secondaryNavItems}
        logoText={logoText}
        logoHref={logoHref}
      />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col min-h-0">
          <div className="@container/main flex flex-1 flex-col gap-2 min-h-0">
            <main className="flex-1 overflow-auto">
              <div className={contentClassName}>
                {showPageHeader && pageTitle && (
                  <div>
                    <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">{pageTitle}</h1>
                    {pageDescription && (
                      <h2 className="text-sm sm:text-lg text-muted-foreground mt-1">{pageDescription}</h2>
                    )}
                  </div>
                )}
                {children}
              </div>
            </main>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
