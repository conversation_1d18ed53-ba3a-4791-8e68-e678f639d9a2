"use client"

import { CHART_COLORS, formatCurrency } from "@/lib/chart-utils"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card"
import {
  <PERSON>,
  <PERSON>,
  Pie,
  <PERSON><PERSON>,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
} from "recharts"
import React, { useEffect, useState } from "react"

import { Skeleton } from "./ui/skeleton"
import { useCampaignData } from "@/lib/api/campaign-data-service"
import { useFilters } from "@/lib/contexts/filter-context"

interface BreakdownChartDataPoint {
  name: string; // e.g., Sales Channel Type, Platform Name, Campaign Type Name
  value: number; // e.g., Total Spend for that category
}

// Helper to get a consistent color from the CHART_COLORS palette
const getColor = (index: number): string => {
  const colorKeys = Object.keys(CHART_COLORS);
  return CHART_COLORS[colorKeys[index % colorKeys.length] as keyof typeof CHART_COLORS];
};

export function SpendBreakdownChart() {
  const { filters, getQueryParams } = useFilters()
  const queryParamsString = getQueryParams().toString()
  const { data: campaignData, loading, error: apiError } = useCampaignData(queryParamsString)
  
  const [chartData, setChartData] = useState<BreakdownChartDataPoint[]>([])
  const [error, setError] = useState<string | null>(null)
  const currentCurrency = filters.currency

  // Process campaign data when it's loaded
  useEffect(() => {
    if (loading) {
      // Reset chart data while loading
      setChartData([])
      setError(null)
      return
    }

    if (apiError) {
      console.error("API Error:", apiError)
      setError(apiError)
      setChartData([])
      return
    }

    try {
      if (!Array.isArray(campaignData) || campaignData.length === 0) {
        console.warn("No campaign data returned from API or data is not an array.")
        setChartData([])
        return
      }

      // Aggregate spend by sales_channel_type for this example
      const spendByChannel: Record<string, number> = {}
      campaignData.forEach(item => {
        const channel = item.sales_channel_type || "Unknown Channel";
        const spend = item.totalSpend ?? 0;
        spendByChannel[channel] = (spendByChannel[channel] || 0) + spend;
      });

      const processedData: BreakdownChartDataPoint[] = Object.entries(spendByChannel).map(([name, value]) => ({
        name,
        value,
      })).sort((a,b) => b.value - a.value); // Sort by value descending

      setChartData(processedData)
    } catch (err) {
      console.error("Failed to process breakdown chart data:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
      setChartData([])
    }
  }, [campaignData, loading, apiError])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Spend Breakdown</CardTitle>
          <CardDescription>By Sales Channel</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] lg:h-[400px]">
          <Skeleton className="h-full w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Spend Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">Error loading breakdown data: {error}</p>
        </CardContent>
      </Card>
    )
  }
  
  if (!chartData.length && !loading) {
     return (
      <Card>
        <CardHeader>
          <CardTitle>Spend Breakdown</CardTitle>
           <CardDescription>By Sales Channel</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] lg:h-[400px] flex items-center justify-center">
          <p>No breakdown data available for the selected filters.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Spend Breakdown</CardTitle>
        <CardDescription>By Sales Channel (Aggregation: {filters.groupBy})</CardDescription>
      </CardHeader>
      <CardContent className="h-[300px] lg:h-[400px]">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              // label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`} // Example label
              outerRadius={"80%"} // Adjusted for better fit
              fill="#8884d8"
              dataKey="value"
              nameKey="name"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={getColor(index)} />
              ))}
            </Pie>
            <Tooltip formatter={(value: number) => formatCurrency(value, currentCurrency)} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
