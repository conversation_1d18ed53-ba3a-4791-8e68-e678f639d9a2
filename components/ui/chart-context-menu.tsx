"use client"

import * as React from "react"
import { Copy, Database, BarChart3, Info } from "lucide-react"
import { toast } from "sonner"
import * as PopoverPrimitive from "@radix-ui/react-popover" // Changed from ContextMenuPrimitive
import {
  // ContextMenu, // We'll use PopoverPrimitive.Root directly
  // ContextMenuContent, // Replaced by PopoverPrimitive.Content
  ContextMenuItem,
  ContextMenuSeparator,
} from "./context-menu" // Keep these local wrappers for items if they add value
import {
  ChartContextMenuData,
  copyChartElementData,
  copyChartTableData,
  createChartDataSummary,
  isValidChartData,
  type ChartContextMenuOptions,
  type ClipboardResult,
} from "@/lib/chart-context-menu-utils"

// Chart types supported by the context menu (might not be needed here anymore)
// export type ChartType = "bar" | "line" | "area" | "pie"

// Props interface for the ChartContextMenu component
export interface ChartContextMenuProps {
  children: React.ReactNode // The chart component itself
  chartData: ChartContextMenuData[]
  activeElement?: { // Information about the element under cursor/right-clicked
    dataKey?: string
    index?: number
    payload?: Record<string, unknown>
  } | null
  onCopySuccess?: (message: string) => void
  onCopyError?: (error: string) => void
  currency?: string
  className?: string // Will be applied to ContextMenuContent

  // New props for programmatic control
  isMenuOpen: boolean
  onOpenChange: (isOpen: boolean) => void // To sync state with parent
  menuPosition?: { x: number; y: number } // Position for the menu to appear
}

// Internal state to track right-click context
interface ContextMenuState {
  isOnElement: boolean
  elementData?: {
    index: number
    kpiName: string
  }
}

export function ChartContextMenu({
  children,
  chartData,
  activeElement,
  onCopySuccess,
  onCopyError,
  currency = "CAD",
  className, // This will now apply to ContextMenuContent
  isMenuOpen,
  onOpenChange,
  menuPosition,
}: ChartContextMenuProps) {
  const [contextState, setContextState] = React.useState<ContextMenuState>({
    isOnElement: false,
  })

  // Effect to update contextState when activeElement changes and menu is about to open
  React.useEffect(() => {
    if (isMenuOpen) { // Only update context when menu is opening or already open
      if (activeElement && activeElement.index !== undefined && activeElement.dataKey) {
        console.log('🐛 [ChartContextMenu] Updating context: Element focus.', activeElement);
        setContextState({
          isOnElement: true,
          elementData: {
            index: activeElement.index,
            kpiName: activeElement.dataKey,
          },
        });
      } else {
        console.log('🐛 [ChartContextMenu] Updating context: Background focus.');
        setContextState({
          isOnElement: false,
          elementData: undefined,
        });
      }
    }
  }, [isMenuOpen, activeElement]); // Rerun when isMenuOpen or activeElement changes

  const handleCopySuccess = React.useCallback((message: string) => {
    toast.success(message)
    onCopySuccess?.(message)
  }, [onCopySuccess])

  const handleCopyError = React.useCallback((error: string) => {
    toast.error(error)
    onCopyError?.(error)
  }, [onCopyError])

  const processClipboardResult = React.useCallback((
    result: ClipboardResult,
    successMessage: string
  ) => {
    if (result.success) {
      handleCopySuccess(successMessage)
    } else {
      handleCopyError(result.error || "Failed to copy to clipboard")
    }
  }, [handleCopySuccess, handleCopyError])

  const copyDataPoint = React.useCallback(async () => {
    if (!contextState.elementData || !isValidChartData(chartData)) {
      handleCopyError("No data point selected or chart data invalid")
      return
    }
    const { index, kpiName } = contextState.elementData
    const options: ChartContextMenuOptions = { currency, valueFormat: "formatted" }
    const result = await copyChartElementData(chartData, index, kpiName, options)
    processClipboardResult(result, "Data point copied to clipboard")
  }, [contextState.elementData, chartData, currency, processClipboardResult, handleCopyError])

  const copyAllData = React.useCallback(async () => {
    if (!isValidChartData(chartData)) {
      handleCopyError("No chart data available")
      return
    }
    const options: ChartContextMenuOptions = { currency, includeHeaders: true, valueFormat: "formatted" }
    const result = await copyChartTableData(chartData, options)
    processClipboardResult(result, "Chart data copied to clipboard")
  }, [chartData, currency, processClipboardResult, handleCopyError])

  const copyChartSummary = React.useCallback(async () => {
    if (!isValidChartData(chartData)) {
      handleCopyError("No chart data available")
      return
    }
    const summary = createChartDataSummary(chartData)
    try {
      await navigator.clipboard.writeText(summary)
      handleCopySuccess("Chart summary copied to clipboard")
    } catch {
      handleCopyError("Failed to copy chart summary")
    }
  }, [chartData, handleCopySuccess, handleCopyError])

  const hasValidData = isValidChartData(chartData)
  const hasElementData = contextState.isOnElement && contextState.elementData

  const contentStyle = menuPosition
    ? {
        position: 'fixed' as React.CSSProperties['position'],
        left: `${menuPosition.x}px`,
        top: `${menuPosition.y}px`,
      }
    : {}

  return (
    <>
      {children} {/* The chart is rendered directly */}
      <PopoverPrimitive.Root open={isMenuOpen} onOpenChange={onOpenChange}>
        {/* No explicit PopoverPrimitive.Trigger here, opening is controlled by `isMenuOpen` prop. */}
        <PopoverPrimitive.Portal>
          <PopoverPrimitive.Content
            className={className ? `${className} w-56` : "w-56"}
            style={contentStyle}
            onOpenAutoFocus={(e) => e.preventDefault()} // Prevent default focus stealing
            avoidCollisions={false} // Ensure menuPosition is strictly respected
            // sideOffset={0} // Default is fine
            // align="center" // Default is fine
          >
            {hasValidData ? (
              <>
                {hasElementData && (
                  <>
                    <ContextMenuItem onClick={copyDataPoint}>
                      <Copy className="mr-2 h-4 w-4" />
                      Copy Data Point
                    </ContextMenuItem>
                    <ContextMenuSeparator />
                  </>
                )}
                <ContextMenuItem onClick={copyAllData}>
                  <Database className="mr-2 h-4 w-4" />
                  Copy All Data
                </ContextMenuItem>
                {!contextState.isOnElement && ( // Show summary only if not on a specific element
                  <ContextMenuItem onClick={copyChartSummary}>
                    <Info className="mr-2 h-4 w-4" />
                    Copy Chart Summary
                  </ContextMenuItem>
                )}
              </>
            ) : (
              <ContextMenuItem disabled>
                <BarChart3 className="mr-2 h-4 w-4" />
                No chart data available
              </ContextMenuItem>
            )}
          </PopoverPrimitive.Content>
        </PopoverPrimitive.Portal>
      </PopoverPrimitive.Root>
    </>
  )
}

// Export additional types for consumers
export type { ChartContextMenuData, ChartContextMenuOptions } from "@/lib/chart-context-menu-utils"