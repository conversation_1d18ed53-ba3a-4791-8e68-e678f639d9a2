import { Card, CardContent, <PERSON>Header } from "./card"

import { Skeleton } from "./skeleton"

interface ChartLoaderProps {
  title?: string
  description?: string
  showHeader?: boolean
  height?: string
  className?: string
}

export function ChartLoader({ 
  title, 
  description, 
  showHeader = true, 
  height = "h-[400px]",
  className = ""
}: ChartLoaderProps) {
  return (
    <Card className={`w-full ${className}`}>
      {showHeader && (
        <CardHeader className="pb-3 sm:pb-6">
          <div className="space-y-2">
            {title && <Skeleton className="h-6 w-48" />}
            {description && <Skeleton className="h-4 w-64" />}
          </div>
        </CardHeader>
      )}
      <CardContent className={`${height} pt-3 sm:pt-6 flex items-center justify-center`}>
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">Loading chart data...</p>
        </div>
      </CardContent>
    </Card>
  )
}

interface KpiCardLoaderProps {
  className?: string
}

export function KpiCardLoader({ className = "" }: KpiCardLoaderProps) {
  return (
    <Card className={className}>
      <CardHeader className="flex items-center justify-between pb-2">
        <div className="flex items-center space-x-1">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-4 rounded-full" />
        </div>
        <div className="flex items-center space-x-1">
          <Skeleton className="h-6 w-6 rounded" />
          <Skeleton className="h-6 w-6 rounded" />
        </div>
      </CardHeader>
      <CardContent>
        <Skeleton className="h-8 w-32 mb-2" />
        <div className="h-[120px] flex items-center justify-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </div>
      </CardContent>
    </Card>
  )
}

interface BrandPanelLoaderProps {
  className?: string
}

export function BrandPanelLoader({ className = "" }: BrandPanelLoaderProps) {
  return (
    <Card className={`w-full flex-shrink-0 xl:w-[280px] ${className}`}>
      <CardHeader className="pb-3 sm:pb-6">
        <Skeleton className="h-5 w-16" />
        <Skeleton className="h-4 w-48" />
      </CardHeader>
      <CardContent className="h-[250px] sm:h-[300px] xl:h-[400px] pt-2">
        <div className="space-y-2">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="flex items-center gap-2">
              <Skeleton className="h-3.5 w-3.5 rounded-sm" />
              <Skeleton className="h-4 flex-1" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
