"use client"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useEffect, useState } from 'react';

// Client-side wrapper to prevent hydration issues with Radix UI Select components
function ClientOnlySelect({ children, ...props }: React.ComponentProps<typeof Select>) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null;
  }

  return <Select {...props}>{children}</Select>;
}

export {
  ClientOnlySelect as Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
};
