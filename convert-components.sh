#!/bin/bash

# Helper script to extract components from pages

# Function to extract admin component
extract_admin_component() {
    local page_name=$1
    local component_name=$(echo "$page_name" | sed 's/^./\U&/')
    
    echo "Creating component file for $component_name..."
    
    mkdir -p components/admin
    
    cat > "components/admin/${component_name}Component.tsx" << COMPONENT_EOF
'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';

// TODO: Import necessary components and utilities
// Check src/pages/admin/${page_name}.tsx for imports

export default function ${component_name}Component() {
  const { data: session, status } = useSession();
  const router = useRouter();
  
  // TODO: Copy state and logic from src/pages/admin/${page_name}.tsx
  // Remove: getServerSideProps, getStaticProps, NextPage type
  // Convert: next/router to next/navigation
  // Update: API calls to use new routes (/api/admin/...)
  
  if (status === 'loading') {
    return <div>Loading...</div>;
  }
  
  if (!session || session.user.role !== 'admin') {
    router.push('/auth/signin');
    return null;
  }
  
  return (
    <div>
      <h1>${component_name} Management</h1>
      {/* TODO: Copy JSX from src/pages/admin/${page_name}.tsx */}
    </div>
  );
}
COMPONENT_EOF
    
    echo "Component template created at components/admin/${component_name}Component.tsx"
    echo "Now manually copy the logic from src/pages/admin/${page_name}.tsx"
}

# Create auth component
create_auth_component() {
    mkdir -p components/auth
    
    cat > components/auth/SignInComponent.tsx << 'COMPONENT_EOF'
'use client';

import { signIn } from 'next-auth/react';
import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';

// TODO: Import UI components
// Check src/pages/auth/signin.tsx for imports

export default function SignInComponent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';
  
  const handleSignIn = async () => {
    await signIn('google', { callbackUrl });
  };
  
  return (
    <div className="flex min-h-screen items-center justify-center">
      {/* TODO: Copy JSX from src/pages/auth/signin.tsx */}
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold">Sign in to your account</h2>
        </div>
        <button
          onClick={handleSignIn}
          className="w-full rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
        >
          Sign in with Google
        </button>
      </div>
    </div>
  );
}
COMPONENT_EOF
    
    echo "Auth component template created at components/auth/SignInComponent.tsx"
}

# Create admin dashboard component
create_admin_dashboard() {
    mkdir -p components/admin
    
    cat > components/admin/AdminDashboard.tsx << 'COMPONENT_EOF'
'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';

// TODO: Import necessary components
// Check src/pages/admin/index.tsx for imports

export default function AdminDashboard() {
  const { data: session } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState(null);
  
  useEffect(() => {
    fetchDashboardStats();
  }, []);
  
  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/admin/dashboard-stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
    }
  };
  
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Admin Dashboard</h1>
      {/* TODO: Copy dashboard content from src/pages/admin/index.tsx */}
    </div>
  );
}
COMPONENT_EOF
    
    echo "Admin dashboard component created at components/admin/AdminDashboard.tsx"
}

# Main execution
case "$1" in
    "users"|"roles"|"permissions"|"groups"|"brands"|"settings"|"backups"|"db-structure")
        extract_admin_component "$1"
        ;;
    "auth")
        create_auth_component
        ;;
    "dashboard")
        create_admin_dashboard
        ;;
    "all")
        create_auth_component
        create_admin_dashboard
        for page in users roles permissions groups brands settings backups db-structure; do
            extract_admin_component "$page"
        done
        ;;
    *)
        echo "Usage: $0 {users|roles|permissions|groups|brands|settings|backups|db-structure|auth|dashboard|all}"
        exit 1
        ;;
esac
