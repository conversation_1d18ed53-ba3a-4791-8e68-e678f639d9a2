# NOLK Data Warehouse Documentation

## Database Overview

**Database Name:** dwh  
**User:** dwh_ai_reporting  
**Database Type:** PostgreSQL 8.0.2 (Redshift 1.0.111040)

## Schema: dwh_ai

The `dwh_ai` schema contains tables related to business intelligence and reporting for NOLK's multi-brand operations.

### Tables

#### 1. ai_reporting_brands

This table contains information about the different brands managed by NOLK.

**Row Count:** 23

| Column Name      | Data Type        | Nullable | Description                                  |
|------------------|------------------|----------|----------------------------------------------|
| brand_id         | character varying| YES      | Unique identifier for the brand              |
| code             | character varying| YES      | Short code or identifier for the brand       |
| name             | character varying| YES      | Full name of the brand                       |
| description      | character varying| YES      | Description of the brand                     |
| abbreviation     | character varying| YES      | Abbreviated name for the brand               |
| logo_image_url   | character varying| YES      | URL to the brand's logo image                |
| status           | character varying| YES      | Current status of the brand (e.g., active)   |
| stage            | character varying| YES      | Current stage of the brand (e.g., growth)    |
| group            | character varying| YES      | Group the brand belongs to                   |

**Usage:**
- Provides master data for brands.
- Used to link brand-specific data across other tables.

#### 2. ai_reporting_ds_kpis

This table stores key performance indicators (KPIs) for different brands across various dimensions.

**Row Count:** 92,456,498

| Column Name              | Data Type        | Nullable | Description                                               |
|--------------------------|------------------|----------|-----------------------------------------------------------|
| date                     | date             | YES      | Date of the KPI measurement                               |
| brand                    | character varying| YES      | Brand name                                                |
| kpi_name                 | character varying| YES      | Normalized/processed KPI name                             |
| raw_kpi_name             | character varying| YES      | Original KPI name before processing                       |
| unit                     | character varying| YES      | Unit of measurement for the KPI                           |
| sales_channel_type       | character varying| YES      | Channel through which sales occurred (e.g., direct, marketplace) |
| kpi_value                | double precision | YES      | Numerical value of the KPI                                |
| kpi_value_count_distinct | character varying| YES      | Count of distinct values (likely for certain KPI types)   |
| country_code             | character varying| YES      | Country code where the KPI was measured                   |
| country_name             | character varying| YES      | Full country name                                         |
| client_type              | character varying| YES      | Type of client/customer                                   |
| raw_kpi_unit             | character varying| YES      | Original unit of the KPI before processing                |

**Usage:**
- Primary table for brand performance analysis
- Supports multi-dimensional analysis by brand, country, sales channel, and time
- Stores both raw and processed KPI data

#### 3. ai_reporting_ds_marketing_advertisings

This table stores detailed data related to marketing and advertising campaigns and performance.

**Row Count:** 65,523,520

| Column Name                     | Data Type        | Nullable | Description                                                    |
|---------------------------------|------------------|----------|----------------------------------------------------------------|
| source_system                   | character varying| YES      | The source system from which the data originated               |
| advertising_platform            | character varying| YES      | Platform where the advertising occurred (e.g., Google Ads, Meta) |
| campaign_name                   | character varying| YES      | Name of the advertising campaign                               |
| advertising_group_name          | character varying| YES      | Name of the ad group within the campaign                       |
| advertising_account_name        | character varying| YES      | Name of the advertising account                                |
| advertising_name                | character varying| YES      | Name of the specific advertisement                             |
| brand                           | character varying| YES      | Brand associated with the advertising activity                 |
| date                            | date             | YES      | Date of the advertising data record                            |
| currency                        | character varying| YES      | Currency used for cost and sales figures                       |
| total_cost                      | double precision | YES      | Total cost of the advertising activity                         |
| total_clicks                    | bigint           | YES      | Total number of clicks on the ad                               |
| total_impressions               | bigint           | YES      | Total number of times the ad was displayed                     |
| total_interactions              | bigint           | YES      | Total number of interactions with the ad                       |
| brand_pre_acquisition           | boolean          | YES      | Flag indicating if activity was before brand acquisition       |
| number_of_sales_orders          | bigint           | YES      | Number of sales orders attributed to the advertising           |
| total_sales                     | double precision | YES      | Total sales value attributed to the advertising                |
| labels                          | character varying| YES      | Labels or tags associated with the campaign/ad                 |
| sales_channel_type              | character varying| YES      | Type of sales channel related to the advertising               |
| campaign_type                   | character varying| YES      | Type of advertising campaign (e.g., Search, Display)           |
| country_code                    | character varying| YES      | Country code relevant to the advertising activity              |
| country_name                    | character varying| YES      | Full country name                                              |
| sales_channel_name              | character varying| YES      | Specific name of the sales channel                             |
| asin                            | character varying| YES      | Amazon Standard Identification Number (if applicable)          |
| listing_sku                     | character varying| YES      | Stock Keeping Unit for the listing                             |
| product_sku                     | character varying| YES      | Stock Keeping Unit for the product                             |
| listing_name                    | character varying| YES      | Name of the product listing                                    |
| state_code                      | character varying| YES      | State code relevant to the advertising activity                |
| state_name                      | character varying| YES      | Full state name                                                |
| brand_acquisition_date          | date             | YES      | Date when the brand was acquired by NOLK                       |
| targeting_marketing_tactic      | character varying| YES      | Specific marketing tactic used for targeting                   |
| targeting_marketing_region      | character varying| YES      | Specific marketing region targeted                             |
| listing_status                  | character varying| YES      | Status of the product listing                                  |
| campaign_status                 | character varying| YES      | Status of the advertising campaign                             |
| advertising_creative_name       | character varying| YES      | Name of the ad creative used                                   |
| advertising_creative_image_url  | character varying| YES      | URL of the image used in the ad creative                       |

**Usage:**
- Detailed analysis of advertising spend and performance.
- Attribution of sales and orders to specific campaigns and ads.
- Supports analysis by platform, campaign type, targeting, and geography.

#### 4. ai_reporting_exchange_rates_history

This table maintains historical exchange rates between different currencies.

**Row Count:** 43,024

| Column Name     | Data Type        | Nullable | Description                                  |
|-----------------|------------------|----------|----------------------------------------------|
| snapshot_date   | date             | YES      | Date of the exchange rate record             |
| from_currency   | character varying| YES      | Source currency code                         |
| to_currency     | character varying| YES      | Target currency code                         |
| exchange_rate   | numeric          | YES      | Conversion rate from source to target currency |

**Usage:**
- Supports currency conversion for financial reporting
- Enables historical analysis in consistent currency units
- Allows for multi-currency operations across different markets

## KPIs and Dimensions

The data warehouse supports a comprehensive set of KPIs that can be analyzed across multiple dimensions. Below is a matrix showing which dimensions are available for each KPI:

| KPI Name | Date | Brand | Sales Channel Type | Compare With | Brand Group | KPI Name | Repeat Period | Display Currency As |
|----------|------|-------|-------------------|--------------|-------------|----------|---------------|---------------------|
| Net Revenue | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| D2C Net Revenue | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| D2C Gross Margin | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| D2C Contribution Margin | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| D2C Net AOV | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| D2C Orders | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Repeat Rate | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| LTV | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Brand LTV | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Website Traffic | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Conversion Rate | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Website Sale Order | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Organic Traffic | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Paid Traffic | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Adspend | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| ACOS | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| TACOS | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| TCAC | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Customers | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Existing Customers | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Contribution Margin | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| New Customers | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Cumulative Customers | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Cross-Sell Customer | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| % Cross-Sell Customer | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| Gross Margin | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| LTV:TCAC | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |


### Dimension Descriptions

- **Date**: Time dimension for trend analysis
- **Brand**: Individual brand within the NOLK portfolio
- **Sales Channel Type**: Distribution channel (e.g., D2C, Marketplace, Wholesale)
- **Compare With**: Comparison dimension for relative analysis
- **Brand Group**: Grouping of related brands
- **KPI Name**: The specific metric being measured
- **Repeat Period**: Time period for repeat purchase analysis
- **Display Currency As**: Currency conversion options

## Relationships and Usage

The database structure supports multi-brand analytics with the following capabilities:

1. **Brand Performance Tracking**
   - Track KPIs across multiple brands in the NOLK portfolio
   - Compare performance across different time periods
   - Analyze by geography (country)

2. **Multi-Currency Support**
   - Convert KPI values between currencies using the exchange rates table
   - Support for international operations and reporting

3. **Dimensional Analysis**
   - Break down performance by sales channel
   - Analyze by client type
   - Filter by country or region

## Recommendations

1. **Indexing Opportunities**
   - Add indexes on frequently queried columns like `date`, `brand`, and `country_code` in the `ai_reporting_ds_kpis` table
   
2. **Primary Keys**
   - Consider adding primary keys to the tables for better data integrity

3. **Documentation**
   - Maintain a data dictionary for the KPIs to ensure consistent interpretation
   - Document the business meaning of each KPI and its calculation method
