# NOLK Admin System Documentation

## Overview

The NOLK Admin System is a comprehensive administration interface built with Next.js that provides role-based access control and management capabilities for users, roles, permissions, groups, and brands. This document provides a detailed explanation of how the admin system works, its architecture, and its key components.

## Table of Contents

1. [Architecture](#architecture)
2. [Authentication and Authorization](#authentication-and-authorization)
3. [Admin Layout and Navigation](#admin-layout-and-navigation)
4. [User Management](#user-management)
5. [Role Management](#role-management)
6. [Permission Management](#permission-management)
7. [Group Management](#group-management)
8. [Brand Management](#brand-management)
9. [Database Structure](#database-structure)
10. [API Routes](#api-routes)
11. [Security Considerations](#security-considerations)

## Architecture

The admin system follows a client-server architecture with a clear separation between the frontend and backend:

- **Frontend**: Next.js with client-only components (no server components)
- **Backend**: API routes in Next.js Pages Router
- **Database**: SQLite database with tables for users, roles, permissions, groups, brands, and their relationships

The system adheres to the following architectural principles:

- Client-side components are in `/src/components/admin/`
- API routes are in `/src/pages/api/admin/`
- Admin pages are in `/src/pages/admin/`
- Authentication is handled by NextAuth.js
- Database access is restricted to API routes only

## Authentication and Authorization

### Authentication

The admin system uses NextAuth.js for authentication with the following features:

- Google OAuth provider for sign-in
- JWT-based session management
- Custom session handling to include user roles and permissions
- User impersonation capabilities for administrators

### Authorization

The system implements a comprehensive role-based access control (RBAC) system with:

1. **Roles**: Predefined sets of permissions (e.g., Super Admin, Admin, Brand Manager)
2. **Permissions**: Granular access controls for specific actions and resources
3. **Access Control Helpers**:
   - `hasPermission()`: Checks if a user has a specific permission
   - `hasRole()`: Checks if a user has a specific role
   - `hasAdminAccess()`: Checks if a user has admin-level access

### Role Hierarchy

The system has a hierarchical role structure:

1. **Super Admin**: Full system access with impersonation capabilities
2. **Admin**: Administrative access with some restrictions
3. **Brand Manager**: Limited access to manage specific brands
4. **General User**: Basic access to view content

### Permission System

Permissions follow a `{action}_{resource}` naming convention:

- `view_users`: Permission to view user information
- `manage_users`: Permission to create, edit, and delete users
- `view_roles`: Permission to view role information
- `manage_roles`: Permission to create, edit, and delete roles
- etc.

## Admin Layout and Navigation

### Layout Structure

The admin interface uses a consistent layout defined in `AdminLayout.tsx` with:

- Left sidebar navigation
- Main content area
- Impersonation warning banner (when active)

### Navigation

The sidebar navigation in `AdminLayout.tsx` dynamically renders menu items based on the user's roles:

- **Admin Overview**: Available to all users with admin access
- **Users**: Available to Super Admin and Admin
- **Roles**: Available to Super Admin and Admin
- **Permissions**: Available to Super Admin and Admin
- **Groups**: Available to Super Admin and Admin
- **Brands**: Available to Super Admin, Admin, and Brand Manager
- **DB Structure**: Available to Super Admin and Admin
- **Settings**: Available to Super Admin, Admin, and Brand Manager
- **Back to Dashboard**: Available to all users

The navigation items are filtered based on the user's roles to ensure they only see items they have permission to access.

## User Management

### User Interface

The user management interface (`/admin/users`) provides:

- A list of all users with their basic information and roles
- Ability to create new users
- Ability to edit existing users
- Ability to delete users
- Ability to impersonate users (Super Admin only)

### User Operations

1. **Create User**:
   - Form with fields for name, email, and password
   - Option to assign roles
   - API endpoint: `POST /api/admin/users`

2. **Edit User**:
   - Form with fields for name, email, and password (optional)
   - Option to modify roles
   - API endpoint: `PUT /api/admin/users/[id]`

3. **Delete User**:
   - Confirmation dialog before deletion
   - API endpoint: `DELETE /api/admin/users/[id]`

4. **Impersonate User**:
   - Available only to Super Admin
   - Allows admin to act as another user with complete data isolation
   - Comprehensive feature with localStorage isolation and visual indicators
   - **See detailed documentation**: [User Impersonation Feature](impersonation_feature.md)
   - API endpoints:
     - `POST /api/admin/impersonation/start`
     - `POST /api/admin/impersonation/stop`

### User-Role Relationship

Users can have multiple roles, and roles can be assigned to multiple users through the `UserRoles` junction table.

## Role Management

### Role Interface

The role management interface (`/admin/roles`) provides:

- A list of all roles with their descriptions
- Ability to create new roles
- Ability to edit existing roles
- Ability to delete roles
- Ability to assign permissions to roles

### Role Operations

1. **Create Role**:
   - Form with fields for name and description
   - Option to assign permissions
   - API endpoint: `POST /api/admin/roles`

2. **Edit Role**:
   - Form with fields for name and description
   - Option to modify permissions
   - API endpoint: `PUT /api/admin/roles/[id]`

3. **Delete Role**:
   - Confirmation dialog before deletion
   - API endpoint: `DELETE /api/admin/roles/[id]`

4. **Assign Permissions**:
   - Multi-select interface for permissions
   - API endpoint: `PUT /api/admin/roles/[id]/permissions`

### Role-Permission Relationship

Roles can have multiple permissions, and permissions can be assigned to multiple roles through the `RolePermissions` junction table.

## Permission Management

### Permission Interface

The permission management interface (`/admin/permissions`) provides:

- A list of all permissions with their descriptions
- Ability to create new permissions
- Ability to edit existing permissions
- Ability to delete permissions

### Permission Operations

1. **Create Permission**:
   - Form with fields for action, resource, and description
   - API endpoint: `POST /api/admin/permissions`

2. **Edit Permission**:
   - Form with fields for action, resource, and description
   - API endpoint: `PUT /api/admin/permissions/[id]`

3. **Delete Permission**:
   - Confirmation dialog before deletion
   - API endpoint: `DELETE /api/admin/permissions/[id]`

### Permission Structure

Permissions are structured as `{action}_{resource}` and stored in the `Permissions` table with:
- `action`: The operation (e.g., view, manage, create, update, delete)
- `resource`: The entity being acted upon (e.g., users, roles, brands)
- `description`: Human-readable description of the permission
- `allowed_sales_channels`: Optional JSON array of allowed sales channel IDs

## Group Management

### Group Interface

The group management interface (`/admin/groups`) provides:

- A list of all groups with their descriptions
- Ability to create new groups
- Ability to edit existing groups
- Ability to delete groups
- Tabs for managing users and brands associated with a group

### Group Operations

1. **Create Group**:
   - Form with fields for name and description
   - API endpoint: `POST /api/admin/groups`

2. **Edit Group**:
   - Form with fields for name and description
   - API endpoint: `PUT /api/admin/groups/[id]`

3. **Delete Group**:
   - Confirmation dialog before deletion
   - API endpoint: `DELETE /api/admin/groups/[id]`

4. **Manage Group Users**:
   - Interface to add/remove users from the group
   - API endpoints: 
     - `GET /api/admin/groups/[id]/users`
     - `PUT /api/admin/groups/[id]/users`

5. **Manage Group Brands**:
   - Interface to add/remove brands from the group
   - API endpoints: 
     - `GET /api/admin/groups/[id]/brands`
     - `PUT /api/admin/groups/[id]/brands`

### Group Relationships

- **User-Group**: Users can belong to multiple groups through the `UserGroups` junction table
- **Brand-Group**: Brands can belong to multiple groups through the `BrandGroups` junction table

## Brand Management

### Brand Interface

The brand management interface (`/admin/brands`) provides:

- A list of all brands with their descriptions
- Ability to create new brands
- Ability to edit existing brands
- Ability to delete brands
- Tabs for managing users and groups associated with a brand

### Brand Operations

1. **Create Brand**:
   - Form with fields for name, description, slug, and social media URLs
   - API endpoint: `POST /api/admin/brands`

2. **Edit Brand**:
   - Form with fields for name, description, slug, and social media URLs
   - API endpoint: `PUT /api/admin/brands/[id]`

3. **Delete Brand**:
   - Confirmation dialog before deletion
   - API endpoint: `DELETE /api/admin/brands/[id]`

4. **Manage Brand Users**:
   - Interface to add/remove users from the brand
   - API endpoints: 
     - `GET /api/admin/brands/[id]/users`
     - `PUT /api/admin/brands/[id]/users`

5. **Manage Brand Groups**:
   - Interface to add/remove groups from the brand
   - API endpoints: 
     - `GET /api/admin/brands/[id]/groups`
     - `PUT /api/admin/brands/[id]/groups`

6. **Sync Brand Slugs**:
   - Utility to generate and update slugs for all brands
   - API endpoint: `POST /api/admin/brands/sync-slugs`

### Brand Relationships

- **User-Brand**: Users can be associated with multiple brands through the `UserBrands` junction table
- **Brand-Group**: Brands can belong to multiple groups through the `BrandGroups` junction table

## Database Structure

The admin system uses a SQLite database with the following tables:

### Users Table

```sql
CREATE TABLE IF NOT EXISTS Users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    created_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL,
    updated_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL
);
```

### Roles Table

```sql
CREATE TABLE IF NOT EXISTS Roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    created_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL,
    updated_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL
);
```

### Permissions Table

```sql
CREATE TABLE IF NOT EXISTS Permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    action TEXT NOT NULL,
    resource TEXT NOT NULL,
    description TEXT,
    allowed_sales_channels TEXT,
    created_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL,
    updated_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL,
    UNIQUE (action, resource)
);
```

### UserRoles Junction Table

```sql
CREATE TABLE IF NOT EXISTS UserRoles (
    user_id INTEGER NOT NULL,
    role_id INTEGER NOT NULL,
    created_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES Roles(id) ON DELETE CASCADE
);
```

### RolePermissions Junction Table

```sql
CREATE TABLE IF NOT EXISTS RolePermissions (
    role_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    created_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES Roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES Permissions(id) ON DELETE CASCADE
);
```

### Groups Table

```sql
CREATE TABLE IF NOT EXISTS Groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    created_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL,
    updated_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL
);
```

### UserGroups Junction Table

```sql
CREATE TABLE IF NOT EXISTS UserGroups (
    user_id INTEGER NOT NULL,
    group_id INTEGER NOT NULL,
    created_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL,
    PRIMARY KEY (user_id, group_id),
    FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES Groups(id) ON DELETE CASCADE
);
```

### Brands Table

```sql
CREATE TABLE IF NOT EXISTS Brands (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    slug TEXT UNIQUE,
    website TEXT,
    amazon_url TEXT,
    facebook_url TEXT,
    instagram_url TEXT,
    twitter_url TEXT,
    created_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL,
    updated_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL
);
```

### UserBrands Junction Table

```sql
CREATE TABLE IF NOT EXISTS UserBrands (
    user_id INTEGER NOT NULL,
    brand_id INTEGER NOT NULL,
    created_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL,
    PRIMARY KEY (user_id, brand_id),
    FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE,
    FOREIGN KEY (brand_id) REFERENCES Brands(id) ON DELETE CASCADE
);
```

### BrandGroups Junction Table

```sql
CREATE TABLE IF NOT EXISTS BrandGroups (
    brand_id INTEGER NOT NULL,
    group_id INTEGER NOT NULL,
    created_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL,
    updated_at INTEGER DEFAULT (strftime('%s', 'now')) NOT NULL,
    PRIMARY KEY (brand_id, group_id),
    FOREIGN KEY (brand_id) REFERENCES Brands(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES Groups(id) ON DELETE CASCADE
);
```

## API Routes

The admin system provides a comprehensive set of API routes for managing users, roles, permissions, groups, and brands:

### User API Routes

- `GET /api/admin/users`: List all users
- `POST /api/admin/users`: Create a new user
- `GET /api/admin/users/[id]`: Get a specific user
- `PUT /api/admin/users/[id]`: Update a user
- `DELETE /api/admin/users/[id]`: Delete a user
- `GET /api/admin/users/[id]/roles`: Get roles for a user
- `PUT /api/admin/users/[id]/roles`: Update roles for a user
- `GET /api/admin/users/[id]/groups`: Get groups for a user
- `PUT /api/admin/users/[id]/groups`: Update groups for a user
- `GET /api/admin/users/[id]/brands`: Get brands for a user
- `PUT /api/admin/users/[id]/brands`: Update brands for a user

### Role API Routes

- `GET /api/admin/roles`: List all roles
- `POST /api/admin/roles`: Create a new role
- `GET /api/admin/roles/[id]`: Get a specific role
- `PUT /api/admin/roles/[id]`: Update a role
- `DELETE /api/admin/roles/[id]`: Delete a role
- `GET /api/admin/roles/[id]/permissions`: Get permissions for a role
- `PUT /api/admin/roles/[id]/permissions`: Update permissions for a role

### Permission API Routes

- `GET /api/admin/permissions`: List all permissions
- `POST /api/admin/permissions`: Create a new permission
- `GET /api/admin/permissions/[id]`: Get a specific permission
- `PUT /api/admin/permissions/[id]`: Update a permission
- `DELETE /api/admin/permissions/[id]`: Delete a permission

### Group API Routes

- `GET /api/admin/groups`: List all groups
- `POST /api/admin/groups`: Create a new group
- `GET /api/admin/groups/[id]`: Get a specific group
- `PUT /api/admin/groups/[id]`: Update a group
- `DELETE /api/admin/groups/[id]`: Delete a group
- `GET /api/admin/groups/[id]/users`: Get users in a group
- `PUT /api/admin/groups/[id]/users`: Update users in a group
- `GET /api/admin/groups/[id]/brands`: Get brands in a group
- `PUT /api/admin/groups/[id]/brands`: Update brands in a group

### Brand API Routes

- `GET /api/admin/brands`: List all brands
- `POST /api/admin/brands`: Create a new brand
- `GET /api/admin/brands/[id]`: Get a specific brand
- `PUT /api/admin/brands/[id]`: Update a brand
- `DELETE /api/admin/brands/[id]`: Delete a brand
- `GET /api/admin/brands/[id]/users`: Get users for a brand
- `PUT /api/admin/brands/[id]/users`: Update users for a brand
- `GET /api/admin/brands/[id]/groups`: Get groups for a brand
- `PUT /api/admin/brands/[id]/groups`: Update groups for a brand
- `POST /api/admin/brands/sync-slugs`: Sync slugs for all brands

### Impersonation API Routes

- `POST /api/admin/impersonation/start`: Start impersonating a user
- `POST /api/admin/impersonation/stop`: Stop impersonating a user

**Note**: For comprehensive impersonation feature documentation including security considerations, localStorage isolation, and troubleshooting, see [User Impersonation Feature Documentation](impersonation_feature.md).

### Database Structure API Route

- `GET /api/admin/database-structure`: Get database structure information

### Backup API Routes

- `GET /api/admin/backup/list`: List all backups
- `POST /api/admin/backup`: Create a new backup
- `GET /api/admin/backup/download/[filename]`: Download a backup
- `DELETE /api/admin/backup/delete/[filename]`: Delete a backup

## Security Considerations

The admin system implements several security measures:

### Authentication Security

- JWT-based sessions with secure cookies
- Password hashing using bcrypt
- OAuth integration with Google

### Authorization Security

- Permission checks on all API routes
- Role-based access control for UI elements
- Special handling for Super Admin role

### API Security

- Input validation using Zod schemas
- Error handling with appropriate HTTP status codes
- CSRF protection through NextAuth.js

### Impersonation Security

- Limited to Super Admin role
- Clear visual indication when impersonating
- Original user information preserved
- Easy way to revert to original user

### Database Security

- Parameterized queries to prevent SQL injection
- Foreign key constraints with ON DELETE CASCADE
- Timestamps for auditing purposes

## Conclusion

The NOLK Admin System provides a comprehensive solution for managing users, roles, permissions, groups, and brands with a secure, role-based access control system. The architecture follows best practices for Next.js applications with a clear separation between client and server code, and the database schema is designed to support complex relationships between entities.
