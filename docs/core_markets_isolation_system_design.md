# Core Markets Isolation System - Technical Design Document

## Executive Summary

The core markets isolation system will provide easy access to the 4 most important markets (USA, Canada, UK, France) through:
1. **Quick-select buttons** for instant selection of core market countries
2. **Core markets aggregation** as a separate API parameter that treats the 4 countries as a single entity
3. **Dual display mode** showing both aggregated "Core Markets" data and individual country breakdowns
4. **Seamless integration** with existing country filtering and permissions

## System Architecture

```mermaid
graph TB
    subgraph "UI Layer"
        A[Country Filter Dialog] --> B[Quick Select Buttons]
        A --> C[Individual Country Checkboxes]
        B --> D[Core Markets Toggle]
    end
    
    subgraph "State Management"
        E[FilterContext] --> F[countryNames: string[]]
        E --> G[coreMarketsEnabled: boolean]
        E --> H[getQueryParams()]
    end
    
    subgraph "API Layer"
        I[API Endpoints] --> J[countryNames parameter]
        I --> K[coreMarkets parameter]
        L[Query Builder] --> M[Country Filtering Logic]
        L --> N[Core Markets Aggregation]
    end
    
    subgraph "Database Layer"
        O[Redshift Queries] --> P[Individual Country Filters]
        O --> Q[Core Markets Union Query]
    end
    
    A --> E
    E --> I
    I --> O
```

## 1. Core Markets Definition & Constants

**File: `lib/constants.ts`**
```typescript
// Add to existing constants
export const CORE_MARKETS = {
  countries: ['United States', 'Canada', 'United Kingdom', 'France'],
  displayName: 'Core Markets',
  id: 'core-markets'
} as const;

export const CORE_MARKETS_COUNTRY_MAPPING = {
  'USA': 'United States',
  'US': 'United States', 
  'UK': 'United Kingdom',
  'France': 'France',
  'Canada': 'Canada'
} as const;
```

## 2. State Management Updates

**File: `lib/contexts/filter-context.tsx`**

### Enhanced FilterState Type
```typescript
export type FilterState = {
  startDate: string;
  endDate: string;
  currency: 'CAD' | 'USD';
  groupBy: GroupingOption;
  brands: string[];
  brandGroups: string[];
  salesChannels: string[];
  countryNames: string[];
  coreMarketsEnabled: boolean; // NEW: Track if core markets is enabled
};
```

### Enhanced Context Type
```typescript
type FilterContextType = {
  filters: FilterState;
  setFilters: React.Dispatch<React.SetStateAction<FilterState>>;
  availableBrands: string[];
  availableBrandGroups: string[];
  availableSalesChannels: string[];
  availableCountries: CountryData[];
  isLoading: boolean;
  error: string | null;
  getQueryParams: () => string;
  // NEW: Core markets helpers
  toggleCoreMarkets: () => void;
  isCoreMarketsSelected: boolean;
};
```

### Enhanced Query Parameters Function
```typescript
const getQueryParams = (): string => {
  const params = new URLSearchParams();
  
  // ... existing parameters ...
  
  if (filters.countryNames && filters.countryNames.length > 0) {
    params.append('countryNames', filters.countryNames.join(','));
  }
  
  // NEW: Core markets parameter
  if (filters.coreMarketsEnabled) {
    params.append('coreMarkets', 'true');
  }
  
  return params.toString();
};
```

## 3. UI Component Enhancements

**File: `components/filters/country-filter-dialog.tsx`**

### Enhanced Component Structure
```mermaid
graph TB
    A[Country Filter Dialog] --> B[Core Markets Section]
    A --> C[Individual Countries Section]
    
    B --> D[Quick Select Button]
    B --> E[Core Markets Toggle]
    
    C --> F[Search Input]
    C --> G[Select All/Clear All]
    C --> H[Country Checkboxes Grid]
    
    D --> I[Auto-select 4 countries]
    E --> J[Enable aggregation mode]
```

### New Props Interface
```typescript
interface CountryFilterDialogProps {
  // ... existing props ...
  coreMarketsEnabled: boolean;
  onCoreMarketsToggle: (enabled: boolean) => void;
  onQuickSelectCoreMarkets: () => void;
}
```

### Core Markets UI Section
```typescript
// New section in the dialog
<div className="border-b pb-4 mb-4">
  <div className="flex items-center justify-between mb-2">
    <Label className="text-sm font-medium">Core Markets</Label>
    <Switch
      checked={coreMarketsEnabled}
      onCheckedChange={onCoreMarketsToggle}
    />
  </div>
  <Button
    variant="outline"
    size="sm"
    onClick={onQuickSelectCoreMarkets}
    className="w-full"
  >
    <Globe className="h-4 w-4 mr-2" />
    Select Core Markets (USA, Canada, UK, France)
  </Button>
  {coreMarketsEnabled && (
    <p className="text-xs text-muted-foreground mt-2">
      Core markets data will be aggregated and displayed alongside individual countries
    </p>
  )}
</div>
```

## 4. API Enhancements

### Enhanced Parameter Types
```typescript
// Add to existing query parameter types
type CampaignQueryParams = {
  // ... existing params ...
  countryNames?: string | string[];
  coreMarkets?: string | boolean; // NEW
};

type FlexibleKpiQueryParams = {
  // ... existing params ...
  countryNames?: string | string[];
  coreMarkets?: string | boolean; // NEW
};
```

### Enhanced Query Builder Logic

**File: `app/api/marketing/campaign-data/lib/query-builder.ts`**

```typescript
// Enhanced country filtering logic
if (params.countryNames.length > 0) {
  const countryPlaceholders = params.countryNames.map(() => `$${paramIndex++}`).join(',');
  conditions.push(`m.country_name IN (${countryPlaceholders})`);
  queryParams.push(...params.countryNames);
}

// NEW: Core markets aggregation
if (params.coreMarkets) {
  // Add core markets as a computed column
  selectList.push(`
    CASE 
      WHEN m.country_name IN ('United States', 'Canada', 'United Kingdom', 'France')
      THEN 'Core Markets'
      ELSE m.country_name
    END AS market_segment
  `);
  
  // Modify groupBy to include market segmentation
  groupByClause += `, market_segment`;
}
```

### Enhanced Flexible KPIs API

**File: `app/api/dashboard/flexible-kpis/route.ts`**

```typescript
// Add core markets support to buildSqlQuery function
if (params.coreMarkets) {
  // Add market segmentation to SELECT
  selectList.push(`
    CASE 
      WHEN k.country_name IN ('United States', 'Canada', 'United Kingdom', 'France')
      THEN 'Core Markets'
      ELSE k.country_name
    END AS market_segment
  `);
  groupByList.push("market_segment");
}
```

## 5. Database Query Patterns

### Core Markets Aggregation Query Pattern
```sql
SELECT
  DATE_TRUNC('month', k.date)::DATE::VARCHAR AS date,
  k.kpi_name,
  CASE 
    WHEN k.country_name IN ('United States', 'Canada', 'United Kingdom', 'France')
    THEN 'Core Markets'
    ELSE k.country_name
  END AS market_segment,
  SUM(k.kpi_value) AS value
FROM dwh_ai.ai_reporting_ds_kpis k
LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
WHERE k.date BETWEEN $1 AND $2
  AND (
    k.country_name IN ('United States', 'Canada', 'United Kingdom', 'France')
    OR k.country_name IN ($3, $4, $5) -- Additional selected countries
  )
GROUP BY 1, 2, 3
ORDER BY 1, 2, 3;
```

### Dual Display Query Strategy
```mermaid
graph LR
    A[Core Markets Enabled] --> B{Query Strategy}
    B --> C[Union Query]
    C --> D[Individual Countries]
    C --> E[Aggregated Core Markets]
    D --> F[USA: $100k]
    D --> G[Canada: $80k]
    D --> H[UK: $120k]
    D --> I[France: $90k]
    E --> J[Core Markets: $390k]
```

## 6. Component Integration Points

### Filter Badges Enhancement
**File: `components/filters/filter-badges.tsx`**
```typescript
// Add core markets badge
{filters.coreMarketsEnabled && (
  <Badge variant="secondary" className="gap-1">
    <Globe className="h-3 w-3" />
    Core Markets Enabled
    <Button
      variant="ghost"
      size="sm"
      className="h-4 w-4 p-0 hover:bg-transparent"
      onClick={() => setFilters(prev => ({ ...prev, coreMarketsEnabled: false }))}
    >
      <X className="h-3 w-3" />
    </Button>
  </Badge>
)}
```

### Dashboard Integration
```typescript
// Charts will automatically receive core markets data
// through existing country filtering mechanisms
// No changes needed to chart components
```

## 7. Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Country Filter Dialog
    participant C as FilterContext
    participant API as API Endpoints
    participant DB as Redshift Database
    
    U->>UI: Click "Select Core Markets"
    UI->>C: Update countryNames + coreMarketsEnabled
    C->>API: Send countryNames + coreMarkets=true
    API->>DB: Execute enhanced query with CASE statement
    DB->>API: Return individual + aggregated data
    API->>C: Processed data with "Core Markets" entity
    C->>UI: Update charts with dual display
    UI->>U: Show Core Markets + individual countries
```

## 8. Implementation Phases

### Phase 1: Core Infrastructure
1. Add core markets constants to `lib/constants.ts`
2. Enhance FilterState type in `lib/contexts/filter-context.tsx`
3. Add core markets toggle logic to FilterContext

### Phase 2: UI Components
1. Enhance `components/filters/country-filter-dialog.tsx`
2. Add core markets section with quick-select button
3. Update `components/filters/filter-badges.tsx`

### Phase 3: API Integration
1. Update parameter types in API endpoints
2. Enhance query builders for core markets aggregation
3. Modify `app/api/marketing/campaign-data/lib/query-builder.ts`
4. Update `app/api/dashboard/flexible-kpis/route.ts`

### Phase 4: Testing & Optimization
1. Test dual display functionality
2. Verify data aggregation accuracy
3. Performance testing for core markets queries
4. User acceptance testing

## 9. Technical Requirements

### Dependencies
- No new external dependencies required
- Uses existing UI components (Switch, Button, Badge)
- Leverages current Redshift connection pool
- Integrates with existing authentication system

### Performance Considerations
- Core markets queries use CASE statements for efficient aggregation
- Existing indexes on country_name columns will optimize performance
- Query results cached through existing mechanisms
- Minimal impact on existing query performance

### Security & Permissions
- Core markets respect existing brand-based permissions
- No additional permission levels required
- Data filtering follows current user access patterns
- Audit trails maintained through existing logging

## 10. Benefits & Impact

- **User Experience**: One-click access to core markets data
- **Data Insights**: Aggregated view of most important markets
- **Flexibility**: Maintains individual country access
- **Performance**: Optimized queries for core markets
- **Scalability**: Easy to extend to other market groups

## 11. Testing Strategy

### Unit Tests
- FilterContext core markets logic
- Query parameter generation
- Country mapping functions

### Integration Tests
- API endpoint parameter handling
- Database query execution
- UI component interactions

### End-to-End Tests
- Complete user workflow
- Data accuracy verification
- Performance benchmarks

## 12. Rollout Plan

### Development Environment
1. Implement core infrastructure
2. Add UI components
3. Integrate API changes
4. Comprehensive testing

### Staging Environment
1. Deploy complete feature
2. User acceptance testing
3. Performance validation
4. Bug fixes and refinements

### Production Environment
1. Feature flag controlled rollout
2. Monitor performance metrics
3. Gather user feedback
4. Full deployment

This design maintains backward compatibility while adding powerful new core markets functionality that enhances the analytics dashboard's strategic value.