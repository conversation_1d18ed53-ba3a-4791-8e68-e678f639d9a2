# Executive Summary Documentation

This document provides a comprehensive overview of the Executive Summary feature, detailing its components, data flow, hooks, types, and utility functions. The feature is located within the [`src/components/executive-summary/`](src/components/executive-summary/) directory.

## Table of Contents

1.  [Overview](#overview)
2.  [Components](#components)
    *   [`AdditionalMetrics.tsx`](#additionalmetricstsx)
    *   [`DataInsights.tsx`](#datainsightstsx)
    *   [`KpiTable.tsx`](#kpitabletsx)
    *   [`NotesAndMethodology.tsx`](#notesandmethodologytsx)
    *   [`Overview.tsx` (Component)](#overviewtsx-component)
    *   [`PdfExport.tsx`](#pdfexporttsx)
    *   [`PeriodSelector.tsx`](#periodselectortsx)
    *   [`ReportHeader.tsx`](#reportheadertsx)
    *   [`TrendCard.tsx`](#trendcardtsx)
    *   [`TrendSection.tsx`](#trendsectiontsx)
3.  [Data Handling](#data-handling)
    *   [`dataPrep.ts`](#dataprepts)
    *   [`hooks.ts`](#hooksts)
4.  [Utility Functions](#utility-functions)
    *   [`utils.ts`](#utilsts)
5.  [Type Definitions](#type-definitions)
    *   [`types.ts`](#typests)
6.  [Entry Point](#entry-point)
    *   [`index.ts`](#indexts)
7.  [Interactions and Data Flow](#interactions-and-data-flow)
8.  [Notable Features](#notable-features)

## Overview

The Executive Summary module is designed to provide a high-level snapshot of key business performance indicators. It aggregates data from various sources, presents it in an easily digestible format, and offers insights into trends and overall performance. The main page for this feature is likely [`src/pages/executive-summary.tsx`](src/pages/executive-summary.tsx:0), which would compose these components.

## Components

### [`AdditionalMetrics.tsx`](src/components/executive-summary/AdditionalMetrics.tsx:0)

*   **Purpose:** This component displays a section for additional metrics that are planned for future integration. It currently serves as a placeholder, indicating that supply metrics (like inventory turnover, inventory value) and customer satisfaction metrics (NPS, CSAT, retention rate) will be shown here once data sources are connected.
*   **Functionality:**
    *   Renders a card layout.
    *   Clearly states that data integration is required for this section.
    *   Lists examples of "Supply Metrics" (Inventory Turnover, Inventory Value, Supply Chain Efficiency) and "Customer Metrics" (Net Promoter Score, Customer Satisfaction Score, Customer Retention Rate) that are "Coming soon".
    *   Includes a "Learn more about data integration" button, though its functionality is not defined within this component.
*   **Notable Features:** Uses `lucide-react` icons for visual cues. Styled with Tailwind CSS and custom theme variables (e.g., `bg-primary`, `text-muted-foreground`).

### [`DataInsights.tsx`](src/components/executive-summary/DataInsights.tsx:0)

*   **Purpose:** This component is responsible for displaying an analysis of revenue, key observations, and strategic recommendations based on the provided Key Performance Indicator (KPI) data and trend information.
*   **Functionality:**
    *   Receives `kpiData`, `weekTrend`, `monthTrend`, `yearTrend`, `selectedBrand`, and `currency` as props.
    *   **Revenue Analysis:**
        *   Displays the net revenue for the selected brand for the current month.
        *   Shows weekly and monthly percentage changes in revenue, with visual indicators (icons and colors) for positive, negative, or neutral trends.
        *   Presents "Key Observations" based on monthly revenue trends and gross margin relative to net revenue.
    *   **Strategic Recommendations:**
        *   Provides "Focus Areas" such as reviewing advertising efficiency if ad spend is high relative to revenue, investigating revenue decline, or building on annual growth.
        *   Suggests "Growth Opportunities" like analyzing top-performing products, reviewing marketing channel performance, and considering cross-brand promotions.
    *   Uses helper functions `formatCurrency` and `formatPercentage` from [`utils.ts`](src/components/executive-summary/utils.ts:0).
    *   `getTrendIndicator` internal helper function determines the appropriate icon (`TrendingUp`, `TrendingDown`, `LineChart`) and color styling based on the trend value.
*   **Data Flow:** Consumes processed KPI data and pre-calculated trend data.
*   **Notable Features:**
    *   Conditional rendering based on data availability and trend values.
    *   Uses `lucide-react` icons and custom SVG icons for visual elements.
    *   Styling is theme-aware, adjusting colors for dark/light modes (e.g., `text-green-500`, `bg-green-700/20`).

### [`KpiTable.tsx`](src/components/executive-summary/KpiTable.tsx:0)

*   **Purpose:** This component renders a table displaying key performance metrics, comparing Month-to-Date (MTD) or selected period values against budget, Year-to-Date (YTD) values, and last year's values.
*   **Functionality:**
    *   Receives `kpiSummaryData`, `currency`, `isLoading`, `error`, `onRetry` callback, and `periodType` as props.
    *   **Column Headers:** Dynamically sets column headers based on the `periodType` (Month, Quarter, Year). For example, "Month-to-Date" vs. "Quarter-to-Date".
    *   **Data Display:**
        *   Iterates through `kpiSummaryData` to render rows for each KPI.
        *   Formats currency values using `formatCurrency` from [`utils.ts`](src/components/executive-summary/utils.ts:0).
        *   Displays budget variance as a percentage with a trend indicator.
    *   **Trend Indicators:** Uses `renderTrendIndicator` and `getTrendColorClass` helpers to show visual cues (icons and colors) for budget variance.
    *   **Loading State:** Shows a skeleton/loading UI with animated pulse effects while data is being fetched (`isLoading` is true).
    *   **Error State:** Displays an error message and a retry button if `error` is present.
    *   **Client-Side Time:** Includes a `ClientTime` sub-component to display the "Last updated" time, which updates every minute on the client-side to avoid SSR hydration errors.
    *   Uses [`KpiWithTooltip.tsx`](src/components/kpi/KpiWithTooltip.tsx:0) to display KPI names with informational tooltips.
*   **Notable Features:**
    *   Responsive column headers based on the selected time period.
    *   Clear loading and error states for better user experience.
    *   Alternating row background colors for readability (`getRowBgClass`).
    *   Theme-aware styling for trend indicators and KPI emphasis.

### [`NotesAndMethodology.tsx`](src/components/executive-summary/NotesAndMethodology.tsx:0)

*   **Purpose:** This component provides supplementary information about the executive summary report, including data sources, methodology, and future plans.
*   **Functionality:**
    *   Uses a tabbed interface (`Tabs`, `TabsList`, `TabsTrigger`, `TabsContent` from `shadcn/ui`) to organize information.
    *   **Data Sources Tab:**
        *   Explains that the report uses real-time data from a centralized API.
        *   States the currency in which financial figures are presented (passed as a `currency` prop).
        *   Mentions daily data refresh and standardized JSON format.
    *   **Methodology Tab:**
        *   Describes the approach for trend analysis (Week, Month, Year trends) and percentage calculations.
    *   **Future Plans Tab:**
        *   Outlines planned enhancements, such as integrating supply chain analytics, customer satisfaction scores, and detailed budget comparisons.
        *   Provides an estimated timeline for these enhancements (e.g., "Expected Q3 2025").
    *   Includes a general contact point for questions.
*   **Notable Features:**
    *   Well-organized information using tabs.
    *   Uses `lucide-react` icons to enhance tab labels and section headers.
    *   Distinct styling for each tab's content using gradient backgrounds and theme colors.

### [`Overview.tsx` (Component)](src/components/executive-summary/Overview.tsx:0)

*   **Purpose:** This component renders the main overview section of the executive summary, providing a brief introduction and highlighting the key areas covered in the report.
*   **Functionality:**
    *   Receives `selectedBrand` as a prop.
    *   Displays a title "Performance Overview".
    *   Includes a paragraph summarizing the report's purpose, mentioning the `selectedBrand`.
    *   Presents three highlighted areas:
        *   Revenue Trends
        *   Profitability Metrics
        *   Customer Acquisition
    *   Each highlighted area has an icon and a brief description.
*   **Notable Features:**
    *   Uses `lucide-react` icons for visual appeal.
    *   Card-based layout with distinct styling for the header and content sections.

### [`PdfExport.tsx`](src/components/executive-summary/PdfExport.tsx:0)

*   **Purpose:** This component is responsible for generating a PDF version of the executive summary report. It does not render any visible UI itself but triggers a PDF download.
*   **Functionality:**
    *   Receives various data props: `selectedBrand`, `selectedPeriod`, `currency`, `kpiSummaryData`, `weekTrend`, `monthTrend`, `yearTrend`.
    *   Also accepts `onSuccess` and `onError` callbacks.
    *   Uses the `jsPDF` library and the `jspdf-autotable` plugin to construct the PDF.
    *   **PDF Structure:**
        *   Sets document properties (title, subject, author).
        *   Adds a header with a gradient background and a placeholder NOLK logo.
        *   Includes a title "Executive Summary".
        *   Displays report information: Brand, Period, Currency, Generated Date/Time.
        *   Adds a "Performance Overview" section.
        *   Renders trend cards for Weekly, Monthly, and Yearly trends using a `drawTrendCard` helper function. This function styles the cards with titles, values, and change indicators.
        *   Includes a "Key Performance Indicators" section with a table generated by `jspdf-autotable`.
            *   The table displays KPIs, current period values, budget variance, YTD, and last year values.
            *   Custom styling is applied to table headers, rows, and cells, including conditional text color for budget variance (green for positive, red for negative).
        *   Optionally adds a "Data Insights" section with placeholder text if space permits on the first page.
        *   Adds a footer to each page with "NOLK Analytics - Confidential", page number, and generation timestamp.
    *   **Filename Generation:** Creates a clean filename for the PDF based on the brand and period.
    *   **Automatic Generation:** The `generatePdf` function is called via `useEffect` when the component mounts or its dependencies change. The `generatePdf` function itself is memoized with `useCallback`.
*   **Data Flow:** Consumes nearly all the key data points of the executive summary to replicate the report in PDF format.
*   **Notable Features:**
    *   Comprehensive PDF generation with custom styling, headers, footers, and tables.
    *   Theme-aware color definitions for PDF elements, mapped from CSS variables (though used as static RGB values in the code).
    *   Error handling and success/error callbacks.
    *   The component itself is hidden (`className="hidden"`) as its sole purpose is to trigger the PDF download.

### [`PeriodSelector.tsx`](src/components/executive-summary/PeriodSelector.tsx:0)

*   **Purpose:** Allows the user to select the time period (Month, Quarter, Year) and the specific period instance (e.g., January 2023, Q1 2023) for the report.
*   **Functionality:**
    *   Receives `selectedPeriodType`, `selectedPeriod`, `availablePeriods`, `onPeriodTypeChange` callback, `onPeriodChange` callback, and `isLoading` as props.
    *   **Period Type Selection:** Displays buttons for "Month", "Quarter", and "Year". Clicking a button calls `onPeriodTypeChange`.
    *   **Specific Period Selection:** Uses a `Select` dropdown (from `shadcn/ui`) to list `availablePeriods`. Changing the selection calls `onPeriodChange`.
        *   Each item in the dropdown displays the period `label` and an asterisk (`*`) if `isComplete` is false.
    *   **Date Range Display:** Shows the start and end dates of the `selectedPeriod` (e.g., "Jan 1, 2023 - Jan 31, 2023").
    *   **Incomplete Period Warning:** If `selectedPeriod.isComplete` is false, it displays a warning message: "* Incomplete period - data may be partial".
    *   **Tooltip:** An info icon next to the "Time Period" label provides a tooltip explaining the period selection and the meaning of the asterisk.
    *   **Loading State:** Disables controls when `isLoading` is true.
*   **Notable Features:**
    *   User-friendly interface for selecting complex time periods.
    *   Clear indication of incomplete periods.
    *   Uses `date-fns` for formatting dates.

### [`ReportHeader.tsx`](src/components/executive-summary/ReportHeader.tsx:0)

*   **Purpose:** Displays the main header for the executive summary report, including the report title, selected brand, currency, and the selected period with its date range.
*   **Functionality:**
    *   Receives `selectedBrand`, `currency`, and `selectedPeriod` as props.
    *   Displays "Nolk Executive Report" as the main title.
    *   Shows the `selectedPeriod.label` (e.g., "January 2023").
    *   Displays key information cards for:
        *   Brand: `selectedBrand` (or "All Brands" if none selected).
        *   Period: Formatted start and end dates of `selectedPeriod` (e.g., "Jan 1 - Jan 31, 2023").
        *   Currency: The `currency` (CAD or USD).
    *   Indicates if a period is incomplete with an asterisk (`*`).
    *   Includes a "Updated daily" status indicator (a green dot).
    *   Generates and displays a `Report ID` based on the period type and start date.
*   **Notable Features:**
    *   Clear and concise presentation of essential report context.
    *   Uses `lucide-react` icons for visual distinction.
    *   Uses `date-fns` for formatting dates.

### [`TrendCard.tsx`](src/components/executive-summary/TrendCard.tsx:0)

*   **Purpose:** Displays a single trend, typically for a specific period (Week, Month, Year) and KPI. It shows the current value, percentage change from the previous period, and a visual indicator of the trend.
*   **Functionality:**
    *   Receives `trend` (a `TrendData` object), `currency`, and `isLoading` as props.
    *   **Trend Colors:** `getTrendColors` helper determines background, text, border, and icon colors based on whether the `trend.change` is positive, negative, or null. These colors are theme-aware for dark mode.
    *   **Trend Indicator:** `renderTrendIndicator` displays an appropriate icon (`TrendingUp`, `TrendingDown`, `Minus`, or `RefreshCw` for loading) based on `trend.change` and `isLoading`.
    *   **Value Display:** Formats `trend.value` using `formatCurrency` from [`utils.ts`](src/components/executive-summary/utils.ts:0). Shows a skeleton loader if `isLoading`.
    *   **Percentage Display:** Formats `trend.change` as a percentage (e.g., "+5.0%", "-2.1%"). Shows "N/A" if null, or "Loading..." if `isLoading`.
    *   **Visual Bar:** Includes a horizontal bar whose width (`getPercentageWidth`) and color (`trendColors.progressBg`) visually represent the magnitude and direction of the trend.
    *   Displays the `trend.period` (e.g., "Week Trend") and `trend.kpiName` (defaults to "Net Revenue").
*   **Notable Features:**
    *   Visually appealing and informative card design.
    *   Dynamic styling based on trend data.
    *   Clear loading state.

### [`TrendSection.tsx`](src/components/executive-summary/TrendSection.tsx:0)

*   **Purpose:** This component groups and displays multiple `TrendCard` components for weekly, monthly, and yearly trends. It also allows the user to select a KPI to view trends for.
*   **Functionality:**
    *   Receives `initialWeekTrend`, `initialMonthTrend`, `initialYearTrend`, `currency`, `isLoading`, and raw `weekTrendData`, `monthTrendData`, `yearTrendData` as props.
    *   **KPI Selection:**
        *   Provides a `Select` dropdown (from `shadcn/ui`) allowing users to choose a KPI from a predefined list (`kpiOptions`: Net Revenue, Gross Revenue, Gross Margin, Adspend, Contribution Margin).
        *   The default selected KPI is "Net Revenue".
    *   **Trend Data Calculation:**
        *   Uses `useEffect` to recalculate `weekTrend`, `monthTrend`, and `yearTrend` using `calculateTrendData` from [`utils.ts`](src/components/executive-summary/utils.ts:0) whenever the `selectedKpi` or the raw trend data props change.
    *   **Display:** Renders three `TrendCard` components:
        *   One for the calculated `weekTrend`.
        *   One for the calculated `monthTrend`.
        *   One for the calculated `yearTrend` (label overridden to "Last Twelve Months").
    *   Passes `isLoading` and `currency` to child `TrendCard` components.
*   **Data Flow:**
    *   Receives initial trend data (likely based on a default KPI).
    *   Receives raw data (`weekTrendData`, `monthTrendData`, `yearTrendData`) which contains time series for multiple KPIs.
    *   When the user selects a new KPI, it re-processes the raw data to calculate and display trends for that specific KPI.
*   **Notable Features:**
    *   Interactive KPI selection for trend analysis.
    *   Dynamic updates to trend cards based on user selection.
    *   Uses a grid layout for displaying the trend cards.

## Data Handling

### [`dataPrep.ts`](src/components/executive-summary/dataPrep.ts:0)

*   **Purpose:** This file contains functions for preparing data specifically for the `KpiTable` component.
*   **Key Functions:**
    *   **`prepareKpiSummaryData(kpiData: FlexibleKpiResponse | null, ytdData: FlexibleKpiResponse | null): KpiSummaryData[]`**
        *   Takes raw KPI data for the current period (`kpiData`) and year-to-date (`ytdData`).
        *   Extracts values for specific KPIs: 'Net Revenue', 'Gross Margin', 'Contribution Margin', 'Adspend'.
        *   **Simulates** budget and last year values based on the current MTD/YTD values. *This is a crucial point: budget and last year data are not directly fetched but derived/mocked in this function.*
        *   Calculates `vsbudget` percentage variance using an internal `calculateBudgetVariance` helper.
        *   Returns an array of `KpiSummaryData` objects, structured for easy rendering in the `KpiTable`. The KPIs are hardcoded as 'Total Revenue', 'Gross Profit', 'Net Profit', and 'Adspend'.
*   **Data Flow:** Transforms raw API responses into a structured format suitable for the `KpiTable`.

### [`hooks.ts`](src/components/executive-summary/hooks.ts:0)

*   **Purpose:** This file defines custom React hooks to manage state and data fetching for the executive summary feature.
*   **Key Hooks:**
    *   **`useFilterData()`:**
        *   Fetches filter data (brands, brand groups) from the `/api/dashboard/filters` endpoint.
        *   Manages loading and error states for this fetch.
        *   Sets a default `selectedBrand` from the fetched data.
        *   Returns `filterData`, `loadingFilters`, `filterError`, `selectedBrand`, and `setSelectedBrand`.
        *   Includes abort controller logic to cancel fetches if the component unmounts or a new request is made.
    *   **`useTimePeriod()`:**
        *   Manages the state for time period selection (`selectedPeriodType`, `selectedPeriod`, `availablePeriods`).
        *   Initializes with a default period type (Month) and the last complete month.
        *   Provides `changePeriodType` and `selectPeriod` callbacks to update the state.
        *   Uses `generateAvailablePeriods` and `getLastCompletePeriod` from [`utils.ts`](src/components/executive-summary/utils.ts:0).
    *   **`useKpiData(currency, selectedBrand, selectedPeriod)`:**
        *   Fetches all necessary KPI data based on the provided `currency`, `selectedBrand`, and `selectedPeriod`.
        *   Manages loading (`loadingData`) and error (`apiError`) states.
        *   **Data Fetches:**
            *   `fetchPeriodData`: Fetches data for the `selectedPeriod` and also fetches YTD data in parallel using `getPeriodParams` and `getYearTrendParams` from [`utils.ts`](src/components/executive-summary/utils.ts:0). Stores results in `kpiData` and `ytdData`.
            *   `fetchWeekTrendData`: Fetches data for the weekly trend using `getWeekTrendParams`. Stores result in `weekTrendData`.
            *   `fetchMonthTrendData`: Fetches data for the monthly trend using `getMonthTrendParams`. Stores result in `monthTrendData`.
            *   `fetchYearTrendData`: Fetches data for the yearly trend using `getYearTrendParams`. Stores result in `yearTrendData`.
            *   All fetches hit the `/api/dashboard/flexible-kpis` endpoint with different parameters.
        *   **Trend Calculation:** Calculates `weekTrend`, `monthTrend`, and `yearTrend` (for 'Net Revenue' by default) using `calculateTrendData` from [`utils.ts`](src/components/executive-summary/utils.ts:0) based on the fetched `weekTrendData`, `monthTrendData`, `yearTrendData`.
        *   `useEffect` triggers `fetchKpiData` when dependencies change.
        *   Includes abort controller logic for all fetches.
        *   Returns all fetched data, calculated trends, loading/error states, and the `fetchKpiData` callback.
    *   **`useCurrencyPreference()`:**
        *   Manages the user's preferred currency (`CAD` or `USD`).
        *   Initializes currency from `localStorage` or defaults to 'CAD'.
        *   Saves the currency to `localStorage` whenever it changes.
        *   Returns `currency` and `setCurrency`.
*   **Data Flow:** These hooks are central to fetching data from the API, managing user selections (brand, period, currency), and providing processed data to the components.

## Utility Functions

### [`utils.ts`](src/components/executive-summary/utils.ts:0)

*   **Purpose:** Provides helper functions for data calculation, formatting, and API parameter generation.
*   **Key Functions:**
    *   **`calculateTrendData(data, period, kpiName = 'Net Revenue'): TrendData`:**
        *   Calculates trend data (current value and percentage change) for a given KPI from a `FlexibleKpiResponse`.
        *   Sorts time series data.
        *   Splits data into current and previous periods (divides the time series in half).
        *   Calculates total values for both periods and then the percentage change.
        *   Handles cases with insufficient data.
    *   **`formatCurrency(value, currency): string`:** Formats a number as a currency string (e.g., "$1,234"). Handles null values by returning "N/A".
    *   **`formatPercentage(value): string`:** Formats a number as a percentage string (e.g., "12.34%"). Handles null values by returning "N/A".
    *   **API Parameter Generators:**
        *   `getMtdParams(currency, selectedBrand)`: Generates URLSearchParams for Month-to-Date data.
        *   `getYtdParams(currency, selectedBrand)`: Generates URLSearchParams for Year-to-Date data.
        *   `getWeekTrendParams(currency, selectedBrand)`: Params for data spanning the last 14 days, grouped by day.
        *   `getMonthTrendParams(currency, selectedBrand)`: Params for data spanning the last 2 months, grouped by week.
        *   `getYearTrendParams(currency, selectedBrand)`: Params for data spanning the last year, grouped by month.
        *   `getPeriodParams(period, currency, selectedBrand)`: Generates URLSearchParams based on a `Period` object, setting `groupByTime` according to `period.type`.
        *   All these functions request a standard set of KPIs: 'Gross Revenue', 'Net Revenue', 'Gross Margin', 'Adspend', 'Contribution Margin', '% Gross Margin', '% Contribution Margin'.
    *   **Period Generation & Handling:**
        *   `isPeriodComplete(period: Period): boolean`: Checks if a period's end date is in the past.
        *   `generateAvailablePeriods(periodType: PeriodType, count = 12): Period[]`: Generates an array of past `Period` objects (Month, Quarter, or Year) up to `count`. Each period includes its type, year, value, label, start/end dates, and `isComplete` status.
        *   `getLastCompletePeriod(periodType: PeriodType): Period`: Returns the most recent `Period` of the given type that is complete. If none are complete, it returns the oldest available period.
*   **Dependencies:** Uses `date-fns` extensively for date manipulations.

## Type Definitions

### [`types.ts`](src/components/executive-summary/types.ts:0)

*   **Purpose:** Defines TypeScript types and enums used throughout the executive summary feature.
*   **Key Types:**
    *   **`KpiResultData`**: Defines the structure for a single KPI's data, including a `summary` (with a `value`) and `timeSeries` data.
    *   **`SimpleKpiResponse`**, **`GroupedKpiResponse`**, **`FlexibleKpiResponse`**: Define possible structures for API responses containing KPI data.
    *   **`BrandInfo`**: Structure for brand name and group.
    *   **`FilterData`**: Structure for filter data (list of `BrandInfo` and `brandGroups`).
    *   **`PeriodType` (enum)**: `MONTH`, `QUARTER`, `YEAR`.
    *   **`Period` (interface)**: Defines a time period with its type, year, value (month/quarter number), label, start/end dates, and `isComplete` flag.
    *   **`TimePeriodState` (interface)**: Defines the state managed by the `useTimePeriod` hook.
    *   **`KpiSummaryData`**: Structure for data rows in the `KpiTable` (KPI name, MTD, vs budget, YTD, last year).
    *   **`TrendData`**: Structure for data used by `TrendCard` (period label, current value, percentage change, optional KPI name).
    *   **`ValidCurrency`**: A union type for supported currencies: `'CAD' | 'USD'`.

## Entry Point

### [`index.ts`](src/components/executive-summary/index.ts:0)

*   **Purpose:** Serves as the main export file for the executive summary module.
*   **Functionality:** It re-exports all components, types, hooks, and utility functions from the other files within the [`src/components/executive-summary/`](src/components/executive-summary/) directory. This allows other parts of the application to import executive summary features from a single entry point.

## Interactions and Data Flow

1.  **Initialization & User Selections:**
    *   The main page (e.g., [`src/pages/executive-summary.tsx`](src/pages/executive-summary.tsx:0)) would likely use the custom hooks:
        *   `useCurrencyPreference()` to get/set the currency.
        *   `useFilterData()` to fetch brand filters and allow brand selection.
        *   `useTimePeriod()` to manage period type and specific period selection via the `PeriodSelector` component.
2.  **Data Fetching:**
    *   `useKpiData()` is the core data-fetching hook. It takes the selected `currency`, `brand`, and `period` as inputs.
    *   It makes multiple API calls to `/api/dashboard/flexible-kpis` with different parameters (generated by functions in [`utils.ts`](src/components/executive-summary/utils.ts:0)) to get:
        *   Data for the selected period (`kpiData`).
        *   Year-to-Date data (`ytdData`).
        *   Data for week, month, and year trends (`weekTrendData`, `monthTrendData`, `yearTrendData`).
3.  **Data Preparation & Processing:**
    *   `useKpiData()` also performs initial trend calculations (for 'Net Revenue' by default) using `calculateTrendData` from [`utils.ts`](src/components/executive-summary/utils.ts:0).
    *   [`dataPrep.ts`](src/components/executive-summary/dataPrep.ts:0) (`prepareKpiSummaryData`) takes `kpiData` and `ytdData` to structure data for the `KpiTable`, including *simulating* budget and last year figures.
    *   The `TrendSection` component further processes `weekTrendData`, `monthTrendData`, `yearTrendData` using `calculateTrendData` if the user selects a different KPI for trend viewing.
4.  **Component Rendering:**
    *   `ReportHeader` displays the selected brand, period, and currency.
    *   `Overview` (component) provides a static introduction.
    *   `PeriodSelector` allows users to change the `selectedPeriod` and `selectedPeriodType`, which triggers data refetching via `useKpiData`.
    *   `TrendSection` displays `TrendCard`s, which update based on the selected KPI and fetched trend data.
    *   `KpiTable` displays the summary metrics from `prepareKpiSummaryData`.
    *   `DataInsights` uses `kpiData` and the initially calculated trends from `useKpiData` to show revenue analysis and recommendations.
    *   `AdditionalMetrics` is a placeholder for future data.
    *   `NotesAndMethodology` provides static context.
    *   `PdfExport` consumes much of this data to generate a downloadable PDF report.

## Notable Features

*   **Modular Design:** Components are well-defined and handle specific parts of the UI.
*   **Custom Hooks:** State logic and data fetching are encapsulated in custom hooks, promoting reusability and separation of concerns.
*   **Dynamic Data Display:** UI updates dynamically based on user selections (brand, period, currency, KPI for trends).
*   **Comprehensive Data Fetching:** Fetches data for various periods and granularities to support different views.
*   **Trend Analysis:** Calculates and visualizes weekly, monthly, and yearly trends.
*   **PDF Export:** Provides functionality to download the report as a styled PDF.
*   **Loading and Error States:** Most data-driven components have clear loading and error handling.
*   **Theme-Aware Styling:** Components are styled with Tailwind CSS and appear to consider theming (e.g., comments about dark mode colors).
*   **Client-Side Rendering Considerations:** `ClientTime` in `KpiTable.tsx` and the 'use client' directive indicate awareness of Next.js client/server rendering.
*   **Data Simulation:** Budget and "last year" data in the `KpiTable` are currently simulated in `dataPrep.ts`. This is a significant detail for understanding data accuracy.
*   **AbortController Usage:** Asynchronous fetch operations in hooks use `AbortController` to cancel requests if dependencies change or components unmount, preventing unnecessary processing and potential memory leaks.

This documentation should provide a clear understanding of the Executive Summary feature's architecture and functionality.