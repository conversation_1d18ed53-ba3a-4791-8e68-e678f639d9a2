import { FlatCompat } from "@eslint/eslintrc";
import { dirname } from "path";
import { fileURLToPath } from "url";
import nextPlugin from "@next/eslint-plugin-next"; // Import the plugin directly
import process from "process"; // Import process

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
  resolvePluginsRelativeTo: __dirname,
});

// Get the base configurations, which should include TypeScript setup
const baseConfigs = compat.extends("next/core-web-vitals", "next/typescript");

const eslintConfig = [
  ...baseConfigs,
  // Manually add the Next.js plugin and its recommended rules
  // This is an attempt to bypass potential issues in how eslint-config-next structures its exports for FlatCompat
  {
    plugins: {
      "@next/next": nextPlugin,
    },
    rules: {
      // Add recommended Next.js rules manually.
      // This list might need adjustment based on the specific version of @next/eslint-plugin-next
      // and what 'eslint-config-next' typically enables.
      // For example:
      "@next/next/no-html-link-for-pages": "error",
      "@next/next/no-sync-scripts": "error",
      // ... other rules from 'plugin:@next/next/recommended' or 'plugin:@next/next/core-web-vitals'
      // This is a simplified example; a full list would be more extensive.
    },
  },
  // Ensure parserOptions for TypeScript are correctly set, as this is crucial
  {
    files: ["**/*.ts", "**/*.tsx"],
    languageOptions: {
      parserOptions: {
        project: true, // or './jsconfig.json'
        tsconfigRootDir: process.cwd(), // Make sure process is imported if not already
      },
    },
    settings: {
      next: {
        rootDir: process.cwd(),
      },
    },
  }
];

// Ensure process is imported if used (e.g., process.cwd())
// import process from "process"; // Uncomment if not already imported globally or if needed

export default eslintConfig;
