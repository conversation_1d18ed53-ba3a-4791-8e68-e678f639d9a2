// Import Jest DOM extensions for DOM element assertions
import '@testing-library/jest-dom';

// Import TextEncoder/TextDecoder from util
import { TextDecoder, TextEncoder } from 'util';
// Import node-fetch for Fetch API polyfill
import fetch, { Headers, Request, Response } from 'node-fetch';

// Polyfill for TextEncoder/TextDecoder
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Polyfill for Fetch API
global.fetch = fetch;
global.Request = Request;
global.Headers = Headers;

// Mock NextResponse for API route testing
jest.mock('next/server', () => {
  return {
    NextRequest: jest.fn(),
    NextResponse: {
      json: jest.fn((data, init) => {
        const mockResponse = {
          status: init?.status || 200,
          headers: {
            'Content-Type': 'application/json',
            ...init?.headers,
          },
          json: async () => data,
          text: async () => JSON.stringify(data),
          ok: (init?.status || 200) >= 200 && (init?.status || 200) < 300,
        };
        return mockResponse;
      }),
      redirect: jest.fn((url, status) => {
        const mockResponse = {
          status: status || 302,
          headers: { Location: url },
          json: async () => ({}),
          text: async () => '',
          ok: false,
        };
        return mockResponse;
      }),
    },
  };
});

// Create a custom Response class with json() method for testing
class TestResponse extends Response {
  constructor(body, init) {
    super(body, init);
    this._body = body;
  }

  async json() {
    if (typeof this._body === 'string') {
      return JSON.parse(this._body);
    }
    return this._body;
  }
}

global.Response = TestResponse;

// Set up any global mocks or configurations here

// Mock the next/router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    pathname: '/',
    query: {},
  }),
}));

// Mock next-auth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: {
      user: { name: 'Test User', email: '<EMAIL>' },
      expires: new Date(Date.now() + 2 * 86400).toISOString(),
    },
    status: 'authenticated',
  })),
  signIn: jest.fn(),
  signOut: jest.fn(),
  getSession: jest.fn(),
}));

// Mock next/headers for API route testing
jest.mock('next/headers', () => ({
  cookies: jest.fn(() => ({
    get: jest.fn(),
    getAll: jest.fn(),
    set: jest.fn(),
  })),
  headers: jest.fn(() => ({
    get: jest.fn(),
    has: jest.fn(),
  })),
}));

// Global timeout for tests
jest.setTimeout(30000);
