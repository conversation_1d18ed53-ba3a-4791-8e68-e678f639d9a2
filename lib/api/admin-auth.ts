import { Session } from 'next-auth';

/**
 * Checks if a user has admin privileges, considering impersonation scenarios.
 * 
 * During impersonation:
 * - If the original user is a Super Admin, they retain admin access
 * - This allows admins to access admin functionality while impersonating other users
 * 
 * @param session - The NextAuth session object
 * @returns boolean - true if user has admin access, false otherwise
 */
export function hasAdminAccess(session: Session | null): boolean {
  if (!session?.user) {
    return false;
  }

  const user = session.user;
  
  // Check if current user has admin roles
  const hasCurrentAdminRole = !!(user.roles?.includes('Admin') || user.roles?.includes('Super Admin'));
  
  // Check if original user (during impersonation) has admin roles
  const hasOriginalAdminRole = !!(user.isImpersonating &&
    (user.originalUser?.roles?.includes('Admin') || user.originalUser?.roles?.includes('Super Admin')));
  
  return hasCurrentAdminRole || hasOriginalAdminRole;
}

/**
 * Checks if a user has Super Admin privileges, considering impersonation scenarios.
 * 
 * @param session - The NextAuth session object
 * @returns boolean - true if user has Super Admin access, false otherwise
 */
export function hasSuperAdminAccess(session: Session | null): boolean {
  if (!session?.user) {
    return false;
  }

  const user = session.user;
  
  // Check if current user has Super Admin role
  const hasCurrentSuperAdminRole = !!(user.roles?.includes('Super Admin'));
  
  // Check if original user (during impersonation) has Super Admin role
  const hasOriginalSuperAdminRole = !!(user.isImpersonating &&
    user.originalUser?.roles?.includes('Super Admin'));
  
  return hasCurrentSuperAdminRole || hasOriginalSuperAdminRole;
}