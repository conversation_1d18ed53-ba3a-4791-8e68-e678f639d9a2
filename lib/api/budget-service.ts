import fs from 'fs';
import { getDb } from './db';
import { parse } from 'csv-parse/sync';
import path from 'path';

export interface BudgetRecord {
  brand: string;
  month: string;
  account: string;
  budget_value: number;
}

export async function importBudgetsFromCsv() {
  try {
    const db = await getDb();
    
    // Path to the CSV file
    const csvPath = path.resolve(process.cwd(), 'data/budget.csv');
    
    // Read the CSV file
    const fileContent = fs.readFileSync(csvPath, 'utf-8');
    
    // Parse the CSV content
    const records = parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true
    });
    
    // Begin a transaction
    await db.exec('BEGIN TRANSACTION');
    
    // Clear existing data (optional)
    await db.exec('DELETE FROM Budgets');
    
    // Prepare the insert statement
    const stmt = await db.prepare(
      'INSERT OR REPLACE INTO Budgets (brand, month, account, budget_value) VALUES (?, ?, ?, ?)'
    );
    
    // Process each record
    for (const record of records) {
      const brand = record.Brand;
      const month = record.Month;
      const account = record.Account;
      
      // Parse the budget value, handling commas and currency symbols
      let budgetValue = record['Budget 2025'];
      if (typeof budgetValue === 'string') {
        budgetValue = budgetValue.replace(/[^\d.-]/g, '');
      }
      budgetValue = parseFloat(budgetValue);
      
      // Skip if any required field is missing or budget value is invalid
      if (!brand || !month || !account || isNaN(budgetValue)) {
        console.warn('Skipping invalid record:', record);
        continue;
      }
      
      // Insert the record
      await stmt.run(brand, month, account, budgetValue);
    }
    
    // Finalize the statement
    await stmt.finalize();
    
    // Commit the transaction
    await db.exec('COMMIT');
    
    console.log('Budget data imported successfully');
    return { success: true, message: 'Budget data imported successfully' };
  } catch (error: unknown) {
    console.error('Error importing budget data:', error);
    
    // Attempt to rollback the transaction if an error occurred
    try {
      const db = await getDb();
      await db.exec('ROLLBACK');
    } catch (rollbackError) {
      console.error('Error rolling back transaction:', rollbackError);
    }
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { success: false, message: `Error importing budget data: ${errorMessage}` };
  }
}

export async function getBudgetsByBrand(brand?: string) {
  try {
    const db = await getDb();
    
    let query: string;
    const params: string[] = [];
    
    if (brand) {
      // Single brand - return individual records
      query = 'SELECT brand, month, account, budget_value FROM Budgets WHERE brand = ? ORDER BY brand, month, account';
      params.push(brand);
    } else {
      // All brands - aggregate by month and account across all brands
      query = `
        SELECT
          'All Brands' as brand,
          month,
          account,
          SUM(budget_value) as budget_value
        FROM Budgets
        GROUP BY month, account
        ORDER BY month, account
      `;
    }
    
    const budgets = await db.all(query, ...params);
    return budgets;
  } catch (error: unknown) {
    console.error('Error fetching budgets:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to fetch budgets: ${errorMessage}`);
  }
}

export async function getAllBrands() {
  try {
    const db = await getDb();
    const brands = await db.all('SELECT DISTINCT brand FROM Budgets ORDER BY brand');
    return brands.map(b => b.brand);
  } catch (error: unknown) {
    console.error('Error fetching brands:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to fetch brands: ${errorMessage}`);
  }
}

export async function updateBudget(
  brand: string,
  month: string,
  account: string,
  budgetValue: number,
  updatedBy?: string
): Promise<{ success: boolean; message: string }> {
  try {
    // Validate input
    if (!brand || !month || !account) {
      return { success: false, message: 'Brand, month, and account are required' };
    }

    if (typeof budgetValue !== 'number' || isNaN(budgetValue)) {
      return { success: false, message: 'Budget value must be a valid number' };
    }

    // Reasonable range validation (adjust as needed)
    if (budgetValue < -********** || budgetValue > **********) {
      return { success: false, message: 'Budget value must be between -$1B and $1B' };
    }

    const db = await getDb();
    
    // Update or insert the budget record
    const result = await db.run(
      `INSERT OR REPLACE INTO Budgets (brand, month, account, budget_value, updated_at, updated_by)
       VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, ?)`,
      [brand, month, account, budgetValue, updatedBy || null]
    );

    if (result.changes && result.changes > 0) {
      return { success: true, message: 'Budget updated successfully' };
    } else {
      return { success: false, message: 'No changes were made to the budget' };
    }
  } catch (error: unknown) {
    console.error('Error updating budget:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return { success: false, message: `Failed to update budget: ${errorMessage}` };
  }
}

export async function getBudgetRecord(
  brand: string,
  month: string,
  account: string
): Promise<BudgetRecord | null> {
  try {
    const db = await getDb();
    const record = await db.get(
      'SELECT brand, month, account, budget_value FROM Budgets WHERE brand = ? AND month = ? AND account = ?',
      [brand, month, account]
    );
    return record || null;
  } catch (error: unknown) {
    console.error('Error fetching budget record:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to fetch budget record: ${errorMessage}`);
  }
}
