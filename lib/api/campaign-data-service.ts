"use client"

import { useEffect, useState } from 'react';

// Define the types for our campaign data
export interface CampaignDataResponse {
  date?: string;
  campaign_name?: string;
  brand_name?: string;
  sales_channel_type?: string;
  totalSpend: number;
  totalImpressions?: number;
  totalClicks?: number;
  totalConversions?: number;
  totalConversionValue?: number;
  roas?: number;
  cpa?: number;
  [key: string]: string | number | undefined;
}

// Tracking ongoing requests to avoid duplicate fetches
const ongoingRequests: Record<string, Promise<CampaignDataResponse[]>> = {};

/**
 * Fetches campaign data from the API without caching to ensure fresh data
 * @param queryParams The query parameters string
 * @returns The campaign data response
 */
export async function fetchCampaignData(queryParams: string): Promise<CampaignDataResponse[]> {
  // If there's already an ongoing request for these exact params, return that promise
  // This prevents duplicate network requests for the same data during rapid component mounts/re-renders
  if (queryParams in ongoingRequests) {
    return ongoingRequests[queryParams];
  }

  // Create a new promise for this request
  const fetchPromise = new Promise<CampaignDataResponse[]>(async (resolve, reject) => {
    try {
      console.log(`Fetching campaign data with params: ${queryParams}`);
      const response = await fetch(`/api/marketing/campaign-data?${queryParams}`);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error("API Error Response:", errorText);
        throw new Error(
          `API request failed with status ${response.status}: ${response.statusText}`
        );
      }
      
      const data = await response.json();
      
      // Remove this request from ongoing requests
      delete ongoingRequests[queryParams];
      
      resolve(data);
    } catch (error) {
      // Remove this request from ongoing requests on error
      delete ongoingRequests[queryParams];
      reject(error);
    }
  });
  
  // Store the promise in ongoing requests
  ongoingRequests[queryParams] = fetchPromise;
  
  return fetchPromise;
}

/**
 * React hook to fetch campaign data
 * @param queryParams The query parameters string
 * @returns The campaign data, loading state, and error
 */
export function useCampaignData(queryParams: string) {
  const [data, setData] = useState<CampaignDataResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);
        
        const result = await fetchCampaignData(queryParams);
        
        if (isMounted) {
          setData(result);
        }
      } catch (err) {
        console.error("Failed to load campaign data:", err);
        if (isMounted) {
          setError(err instanceof Error ? err.message : "An unknown error occurred");
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    fetchData();
    
    return () => {
      isMounted = false;
    };
  }, [queryParams]);

  return { data, loading, error };
}

/**
 * Cancels any ongoing requests
 * @param queryParams Optional query parameters to cancel specific requests
 */
export function cancelOngoingRequests(queryParams?: string) {
  if (queryParams) {
    delete ongoingRequests[queryParams];
  } else {
    Object.keys(ongoingRequests).forEach(key => {
      delete ongoingRequests[key];
    });
  }
}
