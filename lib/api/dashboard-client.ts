// Dashboard client for fetching and processing KPI data

// Query Parameter Types
type ValidGroupByTime = 'day' | 'week' | 'month' | 'quarter' | 'year';
type ValidGroupByDimension = 'brand' | 'brandGroup';
type ValidCurrency = 'CAD' | 'USD';

export type FlexibleKpiQueryParams = {
  startDate?: string; // YYYY-MM-DD
  endDate?: string;   // YYYY-MM-DD
  currency?: ValidCurrency;
  brands?: string | string[];
  brand?: string;     // Support for brand-deep-dive context which uses singular 'brand'
  brandGroups?: string | string[];
  kpis?: string | string[];
  groupBy?: ValidGroupByTime; // New simplified groupBy parameter
  groupByTime?: ValidGroupByTime; // Keeping for backward compatibility
  groupByDimension?: ValidGroupByDimension;
  salesChannels?: string | string[];
  countryNames?: string | string[];
};

// Time Series Data Point
export type TimeSeriesDataPoint = { date: string; value: number | null };

// KPI Summary
export type KpiSummary = { value: number | null };

// KPI Result Data
export type KpiResultData = {
  summary: KpiSummary;
  timeSeries: TimeSeriesDataPoint[];
};

// Response type when NOT grouping by dimension
export type SimpleKpiResponse = {
  [kpiName: string]: KpiResultData;
};

// Response type WHEN grouping by dimension
export type GroupedKpiResponse = {
  [kpiName: string]: {
    [dimensionValue: string]: KpiResultData;
  };
};

// Union type for the final response
export type FlexibleKpiResponse = SimpleKpiResponse | GroupedKpiResponse;

// Error response
export type ErrorResponse = {
  error: string;
  details?: string;
};

// Function to fetch KPI data with timeout and retry logic
export async function fetchKpiData(
  params: FlexibleKpiQueryParams,
  signal?: AbortSignal,
  timeoutMs: number = 30000 // 30 second default timeout
): Promise<FlexibleKpiResponse> {
  // Convert params to URL search params
  const searchParams = new URLSearchParams();
  
  // Add simple params
  if (params.startDate) searchParams.append('startDate', params.startDate);
  if (params.endDate) searchParams.append('endDate', params.endDate);
  if (params.currency) searchParams.append('currency', params.currency);
  if (params.groupBy) searchParams.append('groupByTime', params.groupBy); // Use groupBy as groupByTime
  else if (params.groupByTime) searchParams.append('groupByTime', params.groupByTime);
  if (params.groupByDimension) searchParams.append('groupByDimension', params.groupByDimension);
  
  // Add array params
  if (params.kpis) {
    const kpisArray = Array.isArray(params.kpis) ? params.kpis : [params.kpis];
    kpisArray.forEach(kpi => searchParams.append('kpis', kpi));
  }
  
  // Handle both 'brand' (singular from brand-deep-dive) and 'brands' (plural from dashboard) parameters
  if (params.brands) {
    const brandsArray = Array.isArray(params.brands) ? params.brands : [params.brands];
    brandsArray.forEach(brand => searchParams.append('brands', brand));
  }
  // Support for brand-deep-dive context which uses 'brand' parameter (singular)
  else if (params.brand) {
    const brandValue = String(params.brand);
    searchParams.append('brands', brandValue); // Convert to 'brands' for API consistency
  }
  
  if (params.brandGroups) {
    const brandGroupsArray = Array.isArray(params.brandGroups) ? params.brandGroups : [params.brandGroups];
    brandGroupsArray.forEach(group => searchParams.append('brandGroups', group));
  }
  
  if (params.salesChannels) {
    const salesChannelsArray = Array.isArray(params.salesChannels) ? params.salesChannels : [params.salesChannels];
    salesChannelsArray.forEach(channel => searchParams.append('salesChannels', channel));
  }
  
  if (params.countryNames) {
    const countryNamesArray = Array.isArray(params.countryNames) ? params.countryNames : [params.countryNames];
    countryNamesArray.forEach(country => searchParams.append('countryNames', country));
  }
  
  // Create timeout promise
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Request timeout after ${timeoutMs}ms`));
    }, timeoutMs);
  });

  // Make the API request with abort signal if provided
  const startTime = Date.now();
  console.log('[Dashboard Client] Starting API request:', `/api/dashboard/flexible-kpis?${searchParams.toString()}`);
  console.log('[Dashboard Client] Request params:', Object.fromEntries(searchParams.entries()));
  console.log(`[Dashboard Client] Request timeout set to ${timeoutMs}ms`);
  
  const fetchPromise = fetch(`/api/dashboard/flexible-kpis?${searchParams.toString()}`, {
    signal: signal
  });

  // Race between fetch and timeout
  const response = await Promise.race([fetchPromise, timeoutPromise]);
  
  const requestDuration = Date.now() - startTime;
  console.log(`[Dashboard Client] API request completed in ${requestDuration}ms`);

  if (requestDuration > 10000) {
    console.warn(`[Dashboard Client] SLOW REQUEST WARNING: Request took ${requestDuration}ms (over 10 seconds)`);
  }

  if (!response.ok) {
    console.error(`[Dashboard Client] API request failed with status ${response.status}: ${response.statusText}`);
    try {
      const errorData = await response.json() as ErrorResponse;
      console.error('[Dashboard Client] Error details:', errorData);
      throw new Error(errorData.details || errorData.error || 'Failed to fetch KPI data');
    } catch (parseError) {
      console.error('[Dashboard Client] Failed to parse error response:', parseError);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }

  return response.json() as Promise<FlexibleKpiResponse>;
}

// Helper function to format currency values
export function formatCurrency(value: number | null, currency: ValidCurrency = 'CAD'): string {
  if (value === null) return '-';
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
}

// Helper function to format percentage values
export function formatPercentage(value: number | null, decimals: number = 2): string {
  if (value === null) return '-';
  
  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(value);
}

// Helper function to get trend percentage from KPI data
export function getTrendPercentage(kpiData: KpiResultData): number | null {
  const timeSeries = kpiData.timeSeries;
  if (!timeSeries || timeSeries.length < 2) return null;
  
  // Get the first and last non-null values
  const nonNullPoints = timeSeries.filter(point => point.value !== null);
  if (nonNullPoints.length < 2) return null;
  
  const firstPoint = nonNullPoints[0];
  const lastPoint = nonNullPoints[nonNullPoints.length - 1];
  
  if (firstPoint.value === 0) return null;
  
  return (lastPoint.value! - firstPoint.value!) / firstPoint.value!;
}

// Helper function to determine if a trend is positive (true) or negative (false)
export function isTrendPositive(kpiName: string, trendPercentage: number | null): boolean | null {
  if (trendPercentage === null) return null;
  
  // For most metrics, higher is better
  const negativeMetrics = ['Adspend', '% Adspend', 'Landed Cost', '% Landed Cost', 
                          'Fulfillment Cost', '% Fulfillment Cost', 'Transaction Cost', 
                          '% Transaction Cost', 'Discount', '% Discount', 'Refund', '% Refund'];
  
  const isNegativeMetric = negativeMetrics.includes(kpiName);
  
  // For negative metrics, a decrease (negative trend) is actually good
  if (isNegativeMetric) {
    return trendPercentage < 0;
  }
  
  // For positive metrics, an increase (positive trend) is good
  return trendPercentage > 0;
}
