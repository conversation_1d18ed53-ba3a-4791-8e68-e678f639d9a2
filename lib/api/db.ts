import { open } from 'sqlite';
import path from 'path';
import sqlite3 from 'sqlite3';

// Define the path to the database file
const dbPath = path.resolve(process.cwd(), 'data.db');

let db: Awaited<ReturnType<typeof open>> | null = null;

export async function getDb() {
  if (!db) {
    try {
      console.log(`Attempting to connect to database at: ${dbPath}`);
      db = await open({
        filename: dbPath,
        driver: sqlite3.Database,
      });
      console.log('Successfully connected to the SQLite database.');

      // Create Users table if it doesn't exist
      await db.exec(`
        CREATE TABLE IF NOT EXISTS Users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          email TEXT NOT NULL UNIQUE,
          password_hash TEXT NOT NULL,
          createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
        );
      `);
      console.log('Users table checked/created successfully.');

      // Create Roles table if it doesn't exist
      await db.exec(`
        CREATE TABLE IF NOT EXISTS Roles (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          description TEXT
        );
      `);
      console.log('Roles table checked/created successfully.');

      // Create Permissions table if it doesn't exist
      await db.exec(`
        CREATE TABLE IF NOT EXISTS Permissions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          action TEXT NOT NULL,
          resource TEXT NOT NULL,
          description TEXT,
          allowed_sales_channels TEXT, -- Storing as JSON string or comma-separated
          UNIQUE (action, resource)
        );
      `);
      console.log('Permissions table checked/created successfully.');

      // Create RolePermissions junction table if it doesn't exist
      await db.exec(`
        CREATE TABLE IF NOT EXISTS RolePermissions (
          role_id INTEGER NOT NULL,
          permission_id INTEGER NOT NULL,
          PRIMARY KEY (role_id, permission_id),
          FOREIGN KEY (role_id) REFERENCES Roles(id) ON DELETE CASCADE,
          FOREIGN KEY (permission_id) REFERENCES Permissions(id) ON DELETE CASCADE
        );
      `);
      console.log('RolePermissions table checked/created successfully.');

      // Create Brands table
      await db.exec(`
        CREATE TABLE IF NOT EXISTS Brands (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          slug TEXT NOT NULL UNIQUE,
          social_media_urls TEXT -- Storing as JSON string
        );
      `);
      console.log('Brands table checked/created successfully.');

      // Create Groups table if it doesn't exist
      await db.exec(`
        CREATE TABLE IF NOT EXISTS Groups (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          description TEXT
        );
      `);
      console.log('Groups table checked/created successfully.');

      // Create UserGroups junction table if it doesn't exist
      await db.exec(`
        CREATE TABLE IF NOT EXISTS UserGroups (
          user_id INTEGER NOT NULL,
          group_id INTEGER NOT NULL,
          PRIMARY KEY (user_id, group_id),
          FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE,
          FOREIGN KEY (group_id) REFERENCES Groups(id) ON DELETE CASCADE
        );
      `);
      console.log('UserGroups table checked/created successfully.');

      // Create BrandGroups junction table if it doesn't exist
      await db.exec(`
        CREATE TABLE IF NOT EXISTS BrandGroups (
          brand_id INTEGER NOT NULL,
          group_id INTEGER NOT NULL,
          PRIMARY KEY (brand_id, group_id),
          FOREIGN KEY (brand_id) REFERENCES Brands(id) ON DELETE CASCADE,
          FOREIGN KEY (group_id) REFERENCES Groups(id) ON DELETE CASCADE
        );
      `);
      console.log('BrandGroups table checked/created successfully.');

      // Create UserBrands junction table if it doesn't exist
      await db.exec(`
        CREATE TABLE IF NOT EXISTS UserBrands (
          user_id INTEGER NOT NULL,
          brand_id INTEGER NOT NULL,
          PRIMARY KEY (user_id, brand_id),
          FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE,
          FOREIGN KEY (brand_id) REFERENCES Brands(id) ON DELETE CASCADE
        );
      `);
      console.log('UserBrands table checked/created successfully.');

      // Create UserRoles junction table if it doesn't exist
      await db.exec(`
        CREATE TABLE IF NOT EXISTS UserRoles (
          user_id INTEGER NOT NULL,
          role_id INTEGER NOT NULL,
          PRIMARY KEY (user_id, role_id),
          FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE,
          FOREIGN KEY (role_id) REFERENCES Roles(id) ON DELETE CASCADE
        );
      `);
      console.log('UserRoles table checked/created successfully.');

      // Create ApplicationSettings table if it doesn't exist
      await db.exec(`
        CREATE TABLE IF NOT EXISTS ApplicationSettings (
          id INTEGER PRIMARY KEY CHECK (id = 1), -- Enforce only one row with id = 1
          site_title TEXT DEFAULT 'My Application',
          default_language TEXT DEFAULT 'en',
          maintenance_mode INTEGER DEFAULT 0 -- 0 for false, 1 for true
        );
      `);
      console.log('ApplicationSettings table checked/created successfully.');

      // Initialize ApplicationSettings with default values if the table is empty
      // Using INSERT OR IGNORE to prevent error if the row (id=1) already exists.
      await db.run(`
        INSERT OR IGNORE INTO ApplicationSettings (id, site_title, default_language, maintenance_mode)
        VALUES (1, 'NOLK Analytics', 'en', 0);
      `);
      console.log('ApplicationSettings initialized with default values if necessary.');

      // Create SavedViews table if it doesn't exist
      await db.exec(`
        CREATE TABLE IF NOT EXISTS SavedViews (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          page_type TEXT NOT NULL, -- 'brand-deep-dive' or 'marketing-dashboard'
          filter_data TEXT NOT NULL, -- JSON string of filter state
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(user_id, name, page_type),
          FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE
        );
      `);
      console.log('SavedViews table checked/created successfully.');

      // Create Budgets table if it doesn't exist
      await db.exec(`
        CREATE TABLE IF NOT EXISTS Budgets (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          brand TEXT NOT NULL,
          month TEXT NOT NULL,
          account TEXT NOT NULL,
          budget_value REAL NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_by TEXT,
          UNIQUE(brand, month, account)
        );
      `);
      console.log('Budgets table checked/created successfully.');

    } catch (error) {
      console.error('Failed to connect to the SQLite database or create tables:', error);
      throw new Error('Failed to connect to the database.');
    }
  }
  return db;
}

// Optional: Add a function to close the database connection if needed,
// though for serverless functions, persistent connections are often managed differently.
export async function closeDb() {
  if (db) {
    await db.close();
    db = null;
    console.log('Database connection closed.');
  }
}
