/**
 * Centralized Error Handling Utilities
 * 
 * Provides consistent error handling, logging, and response formatting
 * across all API endpoints.
 */

import { NextResponse } from 'next/server';
import { ZodError } from 'zod';

// Error types for classification
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMIT = 'RATE_LIMIT',
  DATABASE = 'DATABASE',
  EXTERNAL_API = 'EXTERNAL_API',
  INTERNAL = 'INTERNAL'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Custom error class with additional context
export class APIError extends Error {
  public readonly type: ErrorType;
  public readonly severity: ErrorSeverity;
  public readonly statusCode: number;
  public readonly context?: Record<string, any>;
  public readonly timestamp: number;
  public readonly requestId?: string;

  constructor(
    message: string,
    type: ErrorType,
    statusCode: number,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: Record<string, any>,
    requestId?: string
  ) {
    super(message);
    this.name = 'APIError';
    this.type = type;
    this.severity = severity;
    this.statusCode = statusCode;
    this.context = context;
    this.timestamp = Date.now();
    this.requestId = requestId;
  }
}

// Predefined error creators
export const ErrorCreators = {
  validation: (message: string, context?: Record<string, any>) =>
    new APIError(message, ErrorType.VALIDATION, 400, ErrorSeverity.LOW, context),

  authentication: (message: string = 'Authentication required', context?: Record<string, any>) =>
    new APIError(message, ErrorType.AUTHENTICATION, 401, ErrorSeverity.MEDIUM, context),

  authorization: (message: string = 'Insufficient permissions', context?: Record<string, any>) =>
    new APIError(message, ErrorType.AUTHORIZATION, 403, ErrorSeverity.MEDIUM, context),

  notFound: (resource: string, context?: Record<string, any>) =>
    new APIError(`${resource} not found`, ErrorType.NOT_FOUND, 404, ErrorSeverity.LOW, context),

  rateLimit: (message: string = 'Rate limit exceeded', context?: Record<string, any>) =>
    new APIError(message, ErrorType.RATE_LIMIT, 429, ErrorSeverity.MEDIUM, context),

  database: (message: string, context?: Record<string, any>) =>
    new APIError(message, ErrorType.DATABASE, 500, ErrorSeverity.HIGH, context),

  externalAPI: (service: string, context?: Record<string, any>) =>
    new APIError(`External service error: ${service}`, ErrorType.EXTERNAL_API, 502, ErrorSeverity.HIGH, context),

  internal: (message: string = 'Internal server error', context?: Record<string, any>) =>
    new APIError(message, ErrorType.INTERNAL, 500, ErrorSeverity.CRITICAL, context)
};

// Error response format
interface ErrorResponse {
  error: string;
  type: ErrorType;
  timestamp: number;
  requestId?: string;
  details?: string;
  context?: Record<string, any>;
}

// Development-only error response with stack trace
interface DevErrorResponse extends ErrorResponse {
  stack?: string;
  originalError?: any;
}

/**
 * Central error handler for API routes
 */
export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorCounts: Map<string, number> = new Map();
  private lastErrorTime: Map<string, number> = new Map();

  private constructor() {}

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handles and formats errors for API responses
   */
  public handleError(error: unknown, requestId?: string): NextResponse {
    const apiError = this.normalizeError(error, requestId);
    
    // Log the error
    this.logError(apiError);
    
    // Track error frequency
    this.trackError(apiError);
    
    // Create response
    const response = this.createErrorResponse(apiError);
    
    return NextResponse.json(response, { 
      status: apiError.statusCode,
      headers: {
        'X-Error-Type': apiError.type,
        'X-Error-Severity': apiError.severity,
        ...(requestId && { 'X-Request-ID': requestId })
      }
    });
  }

  /**
   * Normalizes different error types into APIError
   */
  private normalizeError(error: unknown, requestId?: string): APIError {
    if (error instanceof APIError) {
      return error;
    }

    if (error instanceof ZodError) {
      const message = error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ');
      return new APIError(
        `Validation failed: ${message}`,
        ErrorType.VALIDATION,
        400,
        ErrorSeverity.LOW,
        { zodErrors: error.errors },
        requestId
      );
    }

    if (error instanceof Error) {
      // Check for specific error patterns
      if (error.message.includes('ECONNREFUSED') || error.message.includes('connection')) {
        return new APIError(
          'Database connection failed',
          ErrorType.DATABASE,
          503,
          ErrorSeverity.HIGH,
          { originalMessage: error.message },
          requestId
        );
      }

      if (error.message.includes('timeout')) {
        return new APIError(
          'Request timeout',
          ErrorType.EXTERNAL_API,
          504,
          ErrorSeverity.MEDIUM,
          { originalMessage: error.message },
          requestId
        );
      }

      if (error.message.includes('permission') || error.message.includes('unauthorized')) {
        return new APIError(
          error.message,
          ErrorType.AUTHORIZATION,
          403,
          ErrorSeverity.MEDIUM,
          undefined,
          requestId
        );
      }

      // Generic error
      return new APIError(
        error.message,
        ErrorType.INTERNAL,
        500,
        ErrorSeverity.HIGH,
        { originalMessage: error.message },
        requestId
      );
    }

    // Unknown error type
    return new APIError(
      'An unexpected error occurred',
      ErrorType.INTERNAL,
      500,
      ErrorSeverity.CRITICAL,
      { originalError: String(error) },
      requestId
    );
  }

  /**
   * Logs errors with appropriate level based on severity
   */
  private logError(error: APIError): void {
    const logData = {
      message: error.message,
      type: error.type,
      severity: error.severity,
      statusCode: error.statusCode,
      timestamp: new Date(error.timestamp).toISOString(),
      requestId: error.requestId,
      context: error.context
    };

    switch (error.severity) {
      case ErrorSeverity.LOW:
        console.info('[API Error - Low]', logData);
        break;
      case ErrorSeverity.MEDIUM:
        console.warn('[API Error - Medium]', logData);
        break;
      case ErrorSeverity.HIGH:
        console.error('[API Error - High]', logData);
        break;
      case ErrorSeverity.CRITICAL:
        console.error('[API Error - CRITICAL]', logData);
        // In production, you might want to send alerts here
        break;
    }
  }

  /**
   * Tracks error frequency for monitoring
   */
  private trackError(error: APIError): void {
    const errorKey = `${error.type}:${error.statusCode}`;
    const currentCount = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, currentCount + 1);
    this.lastErrorTime.set(errorKey, error.timestamp);

    // Alert on high error frequency (more than 10 errors of same type in 5 minutes)
    if (currentCount > 10) {
      const firstErrorTime = this.lastErrorTime.get(errorKey) || 0;
      if (error.timestamp - firstErrorTime < 300000) { // 5 minutes
        console.error('[High Error Frequency Alert]', {
          errorType: error.type,
          count: currentCount,
          timeWindow: '5 minutes'
        });
      }
    }
  }

  /**
   * Creates the error response object
   */
  private createErrorResponse(error: APIError): ErrorResponse | DevErrorResponse {
    const baseResponse: ErrorResponse = {
      error: error.message,
      type: error.type,
      timestamp: error.timestamp,
      requestId: error.requestId
    };

    // Add context for validation errors
    if (error.type === ErrorType.VALIDATION && error.context) {
      baseResponse.context = error.context;
    }

    // Add details for certain error types
    if (error.type === ErrorType.RATE_LIMIT) {
      baseResponse.details = 'Please try again later';
    }

    // In development, include more details
    if (process.env.NODE_ENV === 'development') {
      const devResponse: DevErrorResponse = {
        ...baseResponse,
        stack: error.stack,
        originalError: error.context
      };
      return devResponse;
    }

    // In production, sanitize sensitive information
    if (error.severity === ErrorSeverity.CRITICAL || error.type === ErrorType.INTERNAL) {
      baseResponse.error = 'An internal error occurred';
      delete baseResponse.context;
    }

    return baseResponse;
  }

  /**
   * Gets error statistics for monitoring
   */
  public getErrorStats(): {
    totalErrors: number;
    errorsByType: Record<string, number>;
    recentErrors: Array<{ type: ErrorType; count: number; lastOccurrence: number }>;
  } {
    const totalErrors = Array.from(this.errorCounts.values()).reduce((sum, count) => sum + count, 0);
    
    const errorsByType: Record<string, number> = {};
    const recentErrors: Array<{ type: ErrorType; count: number; lastOccurrence: number }> = [];

    this.errorCounts.forEach((count, key) => {
      const [type] = key.split(':');
      errorsByType[type] = (errorsByType[type] || 0) + count;
      
      const lastOccurrence = this.lastErrorTime.get(key) || 0;
      recentErrors.push({
        type: type as ErrorType,
        count,
        lastOccurrence
      });
    });

    // Sort by most recent
    recentErrors.sort((a, b) => b.lastOccurrence - a.lastOccurrence);

    return {
      totalErrors,
      errorsByType,
      recentErrors: recentErrors.slice(0, 10) // Top 10 most recent
    };
  }

  /**
   * Clears error statistics (useful for testing)
   */
  public clearStats(): void {
    this.errorCounts.clear();
    this.lastErrorTime.clear();
  }
}

/**
 * Utility function for wrapping API route handlers with error handling
 */
export function withErrorHandler<T extends any[], R>(
  handler: (...args: T) => Promise<R>,
  requestId?: string
): (...args: T) => Promise<R | NextResponse> {
  return async (...args: T): Promise<R | NextResponse> => {
    try {
      return await handler(...args);
    } catch (error) {
      return ErrorHandler.getInstance().handleError(error, requestId);
    }
  };
}

/**
 * Middleware for generating request IDs
 */
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();
