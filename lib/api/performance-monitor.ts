/**
 * Performance Monitoring Utilities
 *
 * Provides utilities for monitoring API performance, database query times,
 * and system resource usage in production.
 */

interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface QueryMetric extends PerformanceMetric {
  query: string;
  rowCount?: number;
  cached?: boolean;
}

interface APIMetric extends PerformanceMetric {
  endpoint: string;
  method: string;
  statusCode: number;
  userId?: string;
}

interface SystemMetric {
  timestamp: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage?: NodeJS.CpuUsage;
  activeConnections?: number;
}

/**
 * Performance Monitor Class
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private queryMetrics: QueryMetric[] = [];
  private apiMetrics: APIMetric[] = [];
  private systemMetrics: SystemMetric[] = [];
  private maxMetrics: number = 1000; // Keep last 1000 metrics

  private constructor() {
    // Start system monitoring
    this.startSystemMonitoring();
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Records a database query performance metric
   */
  recordQuery(query: string, duration: number, rowCount?: number, cached?: boolean): void {
    const metric: QueryMetric = {
      name: 'database_query',
      query: this.sanitizeQuery(query),
      duration,
      timestamp: Date.now(),
      rowCount,
      cached,
      metadata: {
        queryType: this.getQueryType(query),
        tableCount: this.getTableCount(query)
      }
    };

    this.queryMetrics.push(metric);
    this.trimMetrics(this.queryMetrics);

    // Log slow queries
    if (duration > 1000) { // Queries over 1 second
      console.warn(`Slow query detected (${duration}ms):`, {
        query: metric.query.substring(0, 100) + '...',
        duration,
        rowCount
      });
    }
  }

  /**
   * Records an API endpoint performance metric
   */
  recordAPI(endpoint: string, method: string, duration: number, statusCode: number, userId?: string): void {
    const metric: APIMetric = {
      name: 'api_request',
      endpoint,
      method,
      duration,
      timestamp: Date.now(),
      statusCode,
      userId,
      metadata: {
        isError: statusCode >= 400,
        isSlowRequest: duration > 2000
      }
    };

    this.apiMetrics.push(metric);
    this.trimMetrics(this.apiMetrics);

    // Log slow API requests
    if (duration > 2000) { // Requests over 2 seconds
      console.warn(`Slow API request detected (${duration}ms):`, {
        endpoint,
        method,
        statusCode,
        userId
      });
    }
  }

  /**
   * Records a general performance metric
   */
  recordMetric(name: string, duration: number, metadata?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      duration,
      timestamp: Date.now(),
      metadata
    };

    this.metrics.push(metric);
    this.trimMetrics(this.metrics);
  }

  /**
   * Gets performance statistics for a specific metric type
   */
  getStats(metricName: string, timeWindowMs: number = 300000): {
    count: number;
    avgDuration: number;
    minDuration: number;
    maxDuration: number;
    p95Duration: number;
    p99Duration: number;
  } {
    const now = Date.now();
    const relevantMetrics = this.metrics
      .filter(m => m.name === metricName && (now - m.timestamp) <= timeWindowMs)
      .map(m => m.duration)
      .sort((a, b) => a - b);

    if (relevantMetrics.length === 0) {
      return {
        count: 0,
        avgDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        p95Duration: 0,
        p99Duration: 0
      };
    }

    const sum = relevantMetrics.reduce((a, b) => a + b, 0);
    const p95Index = Math.floor(relevantMetrics.length * 0.95);
    const p99Index = Math.floor(relevantMetrics.length * 0.99);

    return {
      count: relevantMetrics.length,
      avgDuration: sum / relevantMetrics.length,
      minDuration: relevantMetrics[0],
      maxDuration: relevantMetrics[relevantMetrics.length - 1],
      p95Duration: relevantMetrics[p95Index] || 0,
      p99Duration: relevantMetrics[p99Index] || 0
    };
  }

  /**
   * Gets query performance statistics
   */
  getQueryStats(timeWindowMs: number = 300000): {
    totalQueries: number;
    slowQueries: number;
    avgDuration: number;
    cacheHitRate: number;
    topSlowQueries: Array<{ query: string; duration: number; timestamp: number }>;
  } {
    const now = Date.now();
    const recentQueries = this.queryMetrics.filter(q => (now - q.timestamp) <= timeWindowMs);

    const slowQueries = recentQueries.filter(q => q.duration > 1000);
    const cachedQueries = recentQueries.filter(q => q.cached === true);
    const totalDuration = recentQueries.reduce((sum, q) => sum + q.duration, 0);

    const topSlowQueries = recentQueries
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10)
      .map(q => ({
        query: q.query.substring(0, 100) + '...',
        duration: q.duration,
        timestamp: q.timestamp
      }));

    return {
      totalQueries: recentQueries.length,
      slowQueries: slowQueries.length,
      avgDuration: recentQueries.length > 0 ? totalDuration / recentQueries.length : 0,
      cacheHitRate: recentQueries.length > 0 ? cachedQueries.length / recentQueries.length : 0,
      topSlowQueries
    };
  }

  /**
   * Gets API performance statistics
   */
  getAPIStats(timeWindowMs: number = 300000): {
    totalRequests: number;
    errorRequests: number;
    slowRequests: number;
    avgDuration: number;
    errorRate: number;
    topSlowEndpoints: Array<{ endpoint: string; method: string; duration: number }>;
  } {
    const now = Date.now();
    const recentRequests = this.apiMetrics.filter(a => (now - a.timestamp) <= timeWindowMs);

    const errorRequests = recentRequests.filter(a => a.statusCode >= 400);
    const slowRequests = recentRequests.filter(a => a.duration > 2000);
    const totalDuration = recentRequests.reduce((sum, a) => sum + a.duration, 0);

    const topSlowEndpoints = recentRequests
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10)
      .map(a => ({
        endpoint: a.endpoint,
        method: a.method,
        duration: a.duration
      }));

    return {
      totalRequests: recentRequests.length,
      errorRequests: errorRequests.length,
      slowRequests: slowRequests.length,
      avgDuration: recentRequests.length > 0 ? totalDuration / recentRequests.length : 0,
      errorRate: recentRequests.length > 0 ? errorRequests.length / recentRequests.length : 0,
      topSlowEndpoints
    };
  }

  /**
   * Gets current system metrics
   */
  getCurrentSystemMetrics(): SystemMetric {
    return {
      timestamp: Date.now(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    };
  }

  /**
   * Gets system performance trends
   */
  getSystemTrends(timeWindowMs: number = 300000): {
    avgMemoryUsage: number;
    maxMemoryUsage: number;
    memoryTrend: 'increasing' | 'decreasing' | 'stable';
  } {
    const now = Date.now();
    const recentMetrics = this.systemMetrics.filter(s => (now - s.timestamp) <= timeWindowMs);

    if (recentMetrics.length === 0) {
      return {
        avgMemoryUsage: 0,
        maxMemoryUsage: 0,
        memoryTrend: 'stable'
      };
    }

    const memoryUsages = recentMetrics.map(s => s.memoryUsage.heapUsed);
    const avgMemory = memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length;
    const maxMemory = Math.max(...memoryUsages);

    // Simple trend calculation
    const firstHalf = memoryUsages.slice(0, Math.floor(memoryUsages.length / 2));
    const secondHalf = memoryUsages.slice(Math.floor(memoryUsages.length / 2));

    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;

    let memoryTrend: 'increasing' | 'decreasing' | 'stable' = 'stable';
    const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;

    if (changePercent > 10) {
      memoryTrend = 'increasing';
    } else if (changePercent < -10) {
      memoryTrend = 'decreasing';
    }

    return {
      avgMemoryUsage: avgMemory,
      maxMemoryUsage: maxMemory,
      memoryTrend
    };
  }

  /**
   * Exports performance data for external monitoring
   */
  exportMetrics(): {
    queries: QueryMetric[];
    apis: APIMetric[];
    system: SystemMetric[];
    summary: {
      queryStats: {
        totalQueries: number;
        slowQueries: number;
        avgDuration: number;
        cacheHitRate: number;
        topSlowQueries: Array<{ query: string; duration: number; timestamp: number }>;
      };
      apiStats: {
        totalRequests: number;
        errorRequests: number;
        slowRequests: number;
        avgDuration: number;
        errorRate: number;
        topSlowEndpoints: Array<{ endpoint: string; method: string; duration: number }>;
      };
      systemTrends: {
        avgMemoryUsage: number;
        maxMemoryUsage: number;
        memoryTrend: 'increasing' | 'decreasing' | 'stable';
      };
    };
  } {
    return {
      queries: this.queryMetrics.slice(-100), // Last 100 queries
      apis: this.apiMetrics.slice(-100), // Last 100 API calls
      system: this.systemMetrics.slice(-100), // Last 100 system snapshots
      summary: {
        queryStats: this.getQueryStats(),
        apiStats: this.getAPIStats(),
        systemTrends: this.getSystemTrends()
      }
    };
  }

  private startSystemMonitoring(): void {
    // Record system metrics every 30 seconds
    setInterval(() => {
      this.systemMetrics.push(this.getCurrentSystemMetrics());
      this.trimMetrics(this.systemMetrics);
    }, 30000);
  }

  private trimMetrics(metricsArray: any[]): void {
    if (metricsArray.length > this.maxMetrics) {
      metricsArray.splice(0, metricsArray.length - this.maxMetrics);
    }
  }

  private sanitizeQuery(query: string): string {
    // Remove sensitive data from queries for logging
    return query
      .replace(/password\s*=\s*'[^']*'/gi, "password='***'")
      .replace(/password\s*=\s*"[^"]*"/gi, 'password="***"')
      .substring(0, 500); // Limit query length in logs
  }

  private getQueryType(query: string): string {
    const upperQuery = query.toUpperCase().trim();
    if (upperQuery.startsWith('SELECT')) return 'SELECT';
    if (upperQuery.startsWith('INSERT')) return 'INSERT';
    if (upperQuery.startsWith('UPDATE')) return 'UPDATE';
    if (upperQuery.startsWith('DELETE')) return 'DELETE';
    if (upperQuery.startsWith('WITH')) return 'CTE';
    return 'OTHER';
  }

  private getTableCount(query: string): number {
    const tableMatches = query.match(/FROM\s+[\w.]+|JOIN\s+[\w.]+/gi);
    return tableMatches ? tableMatches.length : 0;
  }
}

/**
 * Decorator for measuring function execution time
 */
export function measurePerformance(metricName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const start = Date.now();
      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - start;
        PerformanceMonitor.getInstance().recordMetric(metricName, duration);
        return result;
      } catch (error) {
        const duration = Date.now() - start;
        PerformanceMonitor.getInstance().recordMetric(`${metricName}_error`, duration);
        throw error;
      }
    };
  };
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();
