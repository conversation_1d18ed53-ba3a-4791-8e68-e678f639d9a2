/**
 * Query Security Utilities
 * 
 * Provides utilities for validating and securing database queries
 * to prevent SQL injection and other security vulnerabilities.
 */

import { z } from 'zod';

// Allowed table names for queries (whitelist approach)
const ALLOWED_TABLES = [
  'dwh_ai.ai_reporting_ds_kpis',
  'dwh_ai.ai_reporting_brands',
  'dwh_ai.ai_reporting_ds_marketing_advertisings',
  'dwh_ai.ai_reporting_exchange_rates_history',
  'Users',
  'Roles',
  'Permissions',
  'Groups',
  'Brands',
  'UserRoles',
  'UserGroups',
  'UserBrands',
  'RolePermissions',
  'Budgets',
  'ApplicationSettings'
] as const;

// Allowed column patterns (for basic validation)
const ALLOWED_COLUMN_PATTERNS = [
  /^[a-zA-Z_][a-zA-Z0-9_]*$/, // Standard column names
  /^[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*$/, // Table.column format
  /^"[a-zA-Z_][a-zA-Z0-9_\s]*"$/, // Quoted column names
] as const;

// Dangerous SQL keywords that should be carefully monitored
const DANGEROUS_KEYWORDS = [
  'DROP',
  'DELETE',
  'INSERT',
  'UPDATE',
  'ALTER',
  'CREATE',
  'TRUNCATE',
  'EXEC',
  'EXECUTE',
  'UNION',
  'SCRIPT',
  'DECLARE',
  'CURSOR'
] as const;

// Schema for validating query parameters
export const QueryParamsSchema = z.object({
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
  brands: z.array(z.string().min(1).max(100)).optional(),
  brandGroups: z.array(z.string().min(1).max(100)).optional(),
  salesChannels: z.array(z.string().min(1).max(100)).optional(),
  countryNames: z.array(z.string().min(1).max(100)).optional(),
  currency: z.enum(['USD', 'CAD', 'EUR', 'GBP']).optional(),
  groupByTime: z.enum(['day', 'week', 'month', 'quarter', 'year']).optional(),
  groupByDimension: z.enum(['brand', 'brandGroup', 'salesChannel', 'country']).optional(),
  limit: z.number().min(1).max(10000).optional(),
  offset: z.number().min(0).optional()
});

export type ValidatedQueryParams = z.infer<typeof QueryParamsSchema>;

/**
 * Validates query parameters against the schema
 */
export function validateQueryParams(params: unknown): ValidatedQueryParams {
  try {
    return QueryParamsSchema.parse(params);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new Error(`Query parameter validation failed: ${errorMessages.join(', ')}`);
    }
    throw new Error('Query parameter validation failed');
  }
}

/**
 * Sanitizes a string value for use in SQL queries
 */
export function sanitizeStringValue(value: string): string {
  if (typeof value !== 'string') {
    throw new Error('Value must be a string');
  }
  
  // Remove null bytes and control characters
  const sanitized = value.replace(/[\x00-\x1F\x7F]/g, '');
  
  // Limit length to prevent DoS attacks
  if (sanitized.length > 1000) {
    throw new Error('String value too long');
  }
  
  return sanitized;
}

/**
 * Validates that a table name is in the allowed list
 */
export function validateTableName(tableName: string): boolean {
  return ALLOWED_TABLES.includes(tableName as any);
}

/**
 * Validates column names against allowed patterns
 */
export function validateColumnName(columnName: string): boolean {
  return ALLOWED_COLUMN_PATTERNS.some(pattern => pattern.test(columnName));
}

/**
 * Checks if a query contains dangerous keywords
 */
export function containsDangerousKeywords(query: string): string[] {
  const upperQuery = query.toUpperCase();
  return DANGEROUS_KEYWORDS.filter(keyword => upperQuery.includes(keyword));
}

/**
 * Validates a complete SQL query for security issues
 */
export function validateQuery(query: string): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check for dangerous keywords
  const dangerousKeywords = containsDangerousKeywords(query);
  if (dangerousKeywords.length > 0) {
    errors.push(`Query contains dangerous keywords: ${dangerousKeywords.join(', ')}`);
  }
  
  // Check for SQL injection patterns
  const injectionPatterns = [
    /['"][\s]*;[\s]*--/i, // Quote followed by semicolon and comment
    /['"][\s]*;[\s]*\/\*/i, // Quote followed by semicolon and block comment
    /union[\s]+select/i, // UNION SELECT attacks
    /or[\s]+1[\s]*=[\s]*1/i, // OR 1=1 attacks
    /and[\s]+1[\s]*=[\s]*1/i, // AND 1=1 attacks
  ];
  
  injectionPatterns.forEach((pattern, index) => {
    if (pattern.test(query)) {
      errors.push(`Query matches SQL injection pattern ${index + 1}`);
    }
  });
  
  // Check query length
  if (query.length > 50000) {
    errors.push('Query is too long');
  }
  
  // Check for multiple statements
  const statements = query.split(';').filter(s => s.trim().length > 0);
  if (statements.length > 1) {
    warnings.push('Query contains multiple statements');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Creates a safe parameterized query builder
 */
export class SafeQueryBuilder {
  private query: string = '';
  private params: any[] = [];
  private paramIndex: number = 1;
  
  constructor(baseQuery: string = '') {
    this.query = baseQuery;
  }
  
  /**
   * Adds a WHERE condition with a parameterized value
   */
  addWhereCondition(column: string, operator: string, value: any): this {
    if (!validateColumnName(column)) {
      throw new Error(`Invalid column name: ${column}`);
    }
    
    const allowedOperators = ['=', '!=', '<>', '<', '>', '<=', '>=', 'LIKE', 'ILIKE', 'IN', 'NOT IN'];
    if (!allowedOperators.includes(operator.toUpperCase())) {
      throw new Error(`Invalid operator: ${operator}`);
    }
    
    const condition = this.query.includes('WHERE') ? ' AND ' : ' WHERE ';
    this.query += `${condition}${column} ${operator} $${this.paramIndex++}`;
    this.params.push(value);
    
    return this;
  }
  
  /**
   * Adds an ORDER BY clause
   */
  addOrderBy(column: string, direction: 'ASC' | 'DESC' = 'ASC'): this {
    if (!validateColumnName(column)) {
      throw new Error(`Invalid column name: ${column}`);
    }
    
    if (!['ASC', 'DESC'].includes(direction)) {
      throw new Error(`Invalid sort direction: ${direction}`);
    }
    
    const orderClause = this.query.includes('ORDER BY') ? ', ' : ' ORDER BY ';
    this.query += `${orderClause}${column} ${direction}`;
    
    return this;
  }
  
  /**
   * Adds a LIMIT clause
   */
  addLimit(limit: number): this {
    if (!Number.isInteger(limit) || limit < 1 || limit > 10000) {
      throw new Error('Limit must be an integer between 1 and 10000');
    }
    
    this.query += ` LIMIT $${this.paramIndex++}`;
    this.params.push(limit);
    
    return this;
  }
  
  /**
   * Returns the built query and parameters
   */
  build(): { query: string; params: any[] } {
    const validation = validateQuery(this.query);
    if (!validation.isValid) {
      throw new Error(`Query validation failed: ${validation.errors.join(', ')}`);
    }
    
    return {
      query: this.query,
      params: this.params
    };
  }
}

/**
 * Rate limiting for query execution
 */
export class QueryRateLimiter {
  private requests: Map<string, number[]> = new Map();
  private readonly maxRequests: number;
  private readonly windowMs: number;
  
  constructor(maxRequests: number = 100, windowMs: number = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }
  
  /**
   * Checks if a request is allowed for the given identifier
   */
  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(identifier) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    // Add current request
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return true;
  }
  
  /**
   * Gets the remaining requests for an identifier
   */
  getRemainingRequests(identifier: string): number {
    const now = Date.now();
    const requests = this.requests.get(identifier) || [];
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    return Math.max(0, this.maxRequests - validRequests.length);
  }
}
