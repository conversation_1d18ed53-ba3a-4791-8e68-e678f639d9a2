import { FilterState } from '../contexts/filter-context';

export type SavedView = {
  id: number;
  user_id: number;
  name: string;
  page_type: 'brand-deep-dive' | 'marketing-dashboard' | 'dashboard';
  filter_data: string; // JSON string of filter state
  created_at: string;
  updated_at: string;
};

/**
 * Get all saved views for a specific page type
 */
export async function getSavedViews(pageType: string): Promise<SavedView[]> {
  try {
    const response = await fetch(`/api/dashboard/saved-views?pageType=${encodeURIComponent(pageType)}`);
    
    if (!response.ok) {
      // If unauthorized, return empty array instead of throwing
      if (response.status === 401) {
        console.warn('User not authenticated, returning empty saved views array');
        return [];
      }
      
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch saved views');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching saved views:', error);
    // Return empty array for any errors to prevent UI from breaking
    return [];
  }
}

/**
 * Get a specific saved view by ID
 */
export async function getSavedViewById(id: number): Promise<SavedView | null> {
  try {
    const response = await fetch(`/api/dashboard/saved-views/${id}`);
    
    if (!response.ok) {
      // If unauthorized or not found, return null instead of throwing
      if (response.status === 401 || response.status === 404) {
        console.warn(`View with ID ${id} not found or user not authorized`);
        return null;
      }
      
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch saved view');
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error fetching saved view with ID ${id}:`, error);
    return null;
  }
}

/**
 * Create a new saved view
 */
export async function createSavedView(
  name: string, 
  pageType: 'brand-deep-dive' | 'marketing-dashboard' | 'dashboard', 
  filterData: FilterState
): Promise<SavedView | null> {
  try {
    const response = await fetch('/api/dashboard/saved-views', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name,
        page_type: pageType,
        filter_data: JSON.stringify(filterData),
      }),
    });
    
    if (!response.ok) {
      // If unauthorized, return null instead of throwing
      if (response.status === 401) {
        console.warn('User not authenticated, cannot create saved view');
        return null;
      }
      
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create saved view');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error creating saved view:', error);
    // Show the error message but don't throw
    if (error instanceof Error) {
      console.error(error.message);
    }
    return null;
  }
}

/**
 * Update an existing saved view
 */
export async function updateSavedView(
  id: number, 
  data: { name?: string; filter_data?: FilterState }
): Promise<SavedView | null> {
  try {
    const updateData: { name?: string; filter_data?: string } = {};
    
    if (data.name !== undefined) {
      updateData.name = data.name;
    }
    
    if (data.filter_data !== undefined) {
      updateData.filter_data = JSON.stringify(data.filter_data);
    }
    
    const response = await fetch(`/api/dashboard/saved-views/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });
    
    if (!response.ok) {
      // If unauthorized or not found, return null instead of throwing
      if (response.status === 401 || response.status === 404) {
        console.warn(`User not authenticated or view not found, cannot update view`);
        return null;
      }
      
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update saved view');
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error updating saved view with ID ${id}:`, error);
    // Show the error message but don't throw
    if (error instanceof Error) {
      console.error(error.message);
    }
    return null;
  }
}

/**
 * Delete a saved view
 */
export async function deleteSavedView(id: number): Promise<boolean> {
  try {
    const response = await fetch(`/api/dashboard/saved-views/${id}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      // If unauthorized or not found, return false instead of throwing
      if (response.status === 401 || response.status === 404) {
        console.warn(`User not authenticated or view not found, cannot delete view`);
        return false;
      }
      
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete saved view');
    }
    
    const result = await response.json();
    return result.success;
  } catch (error) {
    console.error(`Error deleting saved view with ID ${id}:`, error);
    // Return false instead of throwing
    return false;
  }
}
