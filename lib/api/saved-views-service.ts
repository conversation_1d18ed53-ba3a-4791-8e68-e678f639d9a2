import { getDb } from './db';

export type SavedView = {
  id: number;
  user_id: number;
  name: string;
  page_type: 'brand-deep-dive' | 'marketing-dashboard' | 'dashboard';
  filter_data: string; // JSON string of filter state
  created_at: string;
  updated_at: string;
};

export type SavedViewInput = Omit<SavedView, 'id' | 'created_at' | 'updated_at'>;

/**
 * Get all saved views for a specific user and page type
 */
export async function getSavedViews(userId: number, pageType: string): Promise<SavedView[]> {
  const db = await getDb();
  
  const views = await db.all<SavedView[]>(
    `SELECT * FROM SavedViews 
     WHERE user_id = ? AND page_type = ?
     ORDER BY updated_at DESC`,
    [userId, pageType]
  );
  
  return views || [];
}

/**
 * Get a specific saved view by ID
 */
export async function getSavedViewById(id: number, userId: number): Promise<SavedView | null> {
  const db = await getDb();
  
  const view = await db.get<SavedView>(
    `SELECT * FROM SavedViews 
     WHERE id = ? AND user_id = ?`,
    [id, userId]
  );
  
  return view || null;
}

/**
 * Create a new saved view
 */
export async function createSavedView(viewData: SavedViewInput): Promise<SavedView> {
  const db = await getDb();
  
  try {
    const result = await db.run(
      `INSERT INTO SavedViews (user_id, name, page_type, filter_data)
       VALUES (?, ?, ?, ?)`,
      [viewData.user_id, viewData.name, viewData.page_type, viewData.filter_data]
    );
    
    if (result.lastID) {
      const newView = await getSavedViewById(result.lastID, viewData.user_id);
      if (newView) {
        return newView;
      }
    }
    
    throw new Error('Failed to create saved view');
  } catch (error: unknown) {
    const err = error as Error;
    // Handle unique constraint violation
    if (err.message.includes('UNIQUE constraint failed')) {
      throw new Error(`A view with the name "${viewData.name}" already exists for this page`);
    }
    throw error;
  }
}

/**
 * Update an existing saved view
 */
export async function updateSavedView(
  id: number, 
  userId: number, 
  viewData: Partial<SavedViewInput>
): Promise<SavedView> {
  const db = await getDb();
  
  // First check if the view exists and belongs to the user
  const existingView = await getSavedViewById(id, userId);
  if (!existingView) {
    throw new Error('Saved view not found or you do not have permission to update it');
  }
  
  // Build the update query dynamically based on provided fields
  const updateFields: string[] = [];
  const params: (string | number)[] = [];
  
  if (viewData.name !== undefined) {
    updateFields.push('name = ?');
    params.push(viewData.name);
  }
  
  if (viewData.filter_data !== undefined) {
    updateFields.push('filter_data = ?');
    params.push(viewData.filter_data);
  }
  
  // Always update the updated_at timestamp
  updateFields.push('updated_at = CURRENT_TIMESTAMP');
  
  // Add the WHERE clause parameters
  params.push(id);
  params.push(userId);
  
  try {
    await db.run(
      `UPDATE SavedViews 
       SET ${updateFields.join(', ')}
       WHERE id = ? AND user_id = ?`,
      params
    );
    
    // Return the updated view
    const updatedView = await getSavedViewById(id, userId);
    if (updatedView) {
      return updatedView;
    }
    
    throw new Error('Failed to retrieve updated view');
  } catch (error: unknown) {
    const err = error as Error;
    // Handle unique constraint violation
    if (err.message.includes('UNIQUE constraint failed')) {
      throw new Error(`A view with the name "${viewData.name}" already exists for this page`);
    }
    throw error;
  }
}

/**
 * Delete a saved view
 */
export async function deleteSavedView(id: number, userId: number): Promise<boolean> {
  const db = await getDb();
  
  // First check if the view exists and belongs to the user
  const existingView = await getSavedViewById(id, userId);
  if (!existingView) {
    throw new Error('Saved view not found or you do not have permission to delete it');
  }
  
  const result = await db.run(
    `DELETE FROM SavedViews 
     WHERE id = ? AND user_id = ?`,
    [id, userId]
  );
  
  return result.changes !== undefined && result.changes > 0;
}
