import {
  Archive,
  ArrowLeft,
  Database,
  Factory,
  KeyRound,
  LayoutDashboard,
  Settings,
  ShieldCheck,
  Users,
  UsersRound
} from 'lucide-react';

import React from 'react';

export interface NavItem {
  label: string;
  href: string;
  icon?: React.ElementType; // Changed to React.ElementType to be more flexible
  requiredRoles?: string[];
  requiredPermissions?: string[];
  children?: NavItem[]; // For potential future sub-menus
}

export const adminNavItems: NavItem[] = [
  {
    label: 'Back to Dashboard',
    href: '/dashboard',
    icon: ArrowLeft,
  },
  {
    label: 'Admin Overview',
    href: '/admin',
    icon: LayoutDashboard,
  },
  {
    label: 'User Management',
    href: '/admin/users',
    icon: Users,
    requiredRoles: ['Super Admin', 'Admin'],
    requiredPermissions: ['view_users', 'manage_users'],
  },
  {
    label: 'Role Management',
    href: '/admin/roles',
    icon: ShieldCheck,
    requiredRoles: ['Super Admin', 'Admin'],
    requiredPermissions: ['view_roles', 'manage_roles'],
  },
  {
    label: 'Permission Management',
    href: '/admin/permissions',
    icon: KeyRound,
    requiredRoles: ['Super Admin'],
    requiredPermissions: ['view_permissions', 'manage_permissions'],
  },
  {
    label: 'Group Management',
    href: '/admin/groups',
    icon: UsersRound,
    requiredRoles: ['Super Admin', 'Admin'],
    requiredPermissions: ['view_groups', 'manage_groups'],
  },
  {
    label: 'Brand Management',
    href: '/admin/brands',
    icon: Factory,
    requiredRoles: ['Super Admin', 'Admin', 'Brand Manager'],
    requiredPermissions: ['view_brands', 'manage_brands'],
  },
  {
    label: 'Settings',
    href: '/admin/settings',
    icon: Settings,
    requiredRoles: ['Super Admin'],
  },
  {
    label: 'DB Structure',
    href: '/admin/db-structure',
    icon: Database,
    requiredRoles: ['Super Admin'],
  },
  {
    label: 'Backup Management',
    href: '/admin/backups',
    icon: Archive,
    requiredRoles: ['Super Admin'],
  },
];

// Helper function to check if user has at least one of the required roles
export const userHasRole = (userRoles: string[] | undefined, requiredRoles: string[] | undefined): boolean => {
  if (!requiredRoles || requiredRoles.length === 0) {
    return true; // No roles required, accessible to all
  }
  if (!userRoles || userRoles.length === 0) {
    return false; // User has no roles, but roles are required
  }
  return requiredRoles.some(role => userRoles.includes(role));
};

// Helper function to check if user has at least one of the required permissions
export const userHasPermission = (userPermissions: string[] | undefined, requiredPermissions: string[] | undefined): boolean => {
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true; // No permissions required
  }
  if (!userPermissions || userPermissions.length === 0) {
    return false; // User has no permissions, but permissions are required
  }
  return requiredPermissions.some(permission => userPermissions.includes(permission));
};
