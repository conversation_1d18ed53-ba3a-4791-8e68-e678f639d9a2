"use client"

import React, { ReactNode, createContext, useContext, useEffect, useState } from 'react';

import { useSession } from 'next-auth/react';

// Define the grouping options
export type GroupingOption = 'day' | 'week' | 'month' | 'quarter' | 'year';

// Define the filter state type
export type BrandDeepDiveState = {
  startDate: string;
  endDate: string;
  currency: 'CAD' | 'USD';
  groupBy: GroupingOption;
  selectedBrand: string | null;
  salesChannels: string[];
  countryNames: string[]; // Stores selected country names
};

// Type for individual country data from API
export type CountryData = {
  name: string;
  hasSales: boolean;
};

// Define the context type
type BrandDeepDiveContextType = {
  state: BrandDeepDiveState;
  setState: React.Dispatch<React.SetStateAction<BrandDeepDiveState>>;
  availableBrands: string[];
  availableSalesChannels: string[];
  availableCountries: CountryData[]; // Changed to store CountryData
  isLoading: boolean; // For filter options
  error: string | null;
  getQueryParams: () => string;
  isStateInitialized: boolean; // To track if initial state (from localStorage or default) is set
};

// Create the context with a default value
const BrandDeepDiveContext = createContext<BrandDeepDiveContextType | undefined>(undefined);

// Base LocalStorage key for saving filter state
const BRAND_DEEP_DIVE_STORAGE_KEY_BASE = 'brand_deep_dive_state';

// Smart groupBy logic based on date range
const getSmartGroupByTime = (startDate: string, endDate: string): GroupingOption => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffInDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  
  // Calculate difference in months for 6+ month threshold
  const diffInMonths = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());
  
  console.log(`[DEBUG] Smart grouping calculation:`, {
    startDate,
    endDate,
    diffInDays,
    diffInMonths,
    logic: diffInDays < 30 ? 'day (< 30 days)' : diffInMonths >= 6 ? 'month (>= 6 months)' : 'week (30 days to 6 months)'
  });
  
  // Less than 30 days -> group by day
  if (diffInDays < 30) {
    return 'day';
  }
  // 6 months or more -> group by month
  else if (diffInMonths >= 6) {
    return 'month';
  }
  // 30 days to 6 months -> group by week
  else {
    return 'week';
  }
};

// Default filter state
const getDefaultState = (): BrandDeepDiveState => {
  // Get dates for the last 90 days by default
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 90);

  // Format dates as YYYY-MM-DD
  const formattedStartDate = startDate.toISOString().split('T')[0];
  const formattedEndDate = endDate.toISOString().split('T')[0];

  // Use smart grouping based on the default date range
  const smartGroupBy = getSmartGroupByTime(formattedStartDate, formattedEndDate);

  return {
    startDate: formattedStartDate,
    endDate: formattedEndDate,
    currency: 'CAD',
    groupBy: smartGroupBy,
    selectedBrand: null,
    salesChannels: [],
    countryNames: [],
  };
};

// Provider component
export const BrandDeepDiveProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Get the current user session
  const { data: session } = useSession();
  const userId = session?.user?.id;
  const isImpersonating = session?.user?.isImpersonating;
  const originalUserId = session?.user?.originalUser?.id;
  
  // Initialize with default state
  const [state, setState] = useState<BrandDeepDiveState>(getDefaultState());
  const [hasUserModifiedState, setHasUserModifiedState] = useState(false);
  
  // Generate user-specific storage key with impersonation awareness
  const getUserStorageKey = () => {
    if (!userId) return null;
    
    if (isImpersonating && originalUserId) {
      // During impersonation, use isolated key to prevent contamination
      return `${BRAND_DEEP_DIVE_STORAGE_KEY_BASE}_${originalUserId}_impersonating_${userId}`;
    }
    
    return `${BRAND_DEEP_DIVE_STORAGE_KEY_BASE}_${userId}`;
  };
  
  // Load saved state from localStorage after component mounts and when user changes
  useEffect(() => {
    if (userId) {
      try {
        const userStorageKey = getUserStorageKey();
        if (userStorageKey) {
          const savedState = localStorage.getItem(userStorageKey);
          if (savedState) {
            setState(JSON.parse(savedState) as BrandDeepDiveState);
            console.log('[BrandDeepDiveContext] Loaded saved state for key:', userStorageKey);
          } else {
            // If no saved state for this user, use default state
            setState(getDefaultState());
            console.log('[BrandDeepDiveContext] No saved state found, using defaults for key:', userStorageKey);
          }
        }
        // Reset the modification flag when loading a new user's filters
        setHasUserModifiedState(false);
      } catch (loadError) { // Changed variable name
        console.error('Failed to load saved brand deep dive state:', loadError);
      } finally {
        setIsStateInitialized(true); // Mark state as initialized
      }
    } else {
      // If no userId, still mark as initialized with default state
      setState(getDefaultState());
      setIsStateInitialized(true);
    }
  }, [userId, isImpersonating, originalUserId]); // Re-run when userId or impersonation state changes
  
  const [availableBrands, setAvailableBrands] = useState<string[]>([]);
  const [availableSalesChannels, setAvailableSalesChannels] = useState<string[]>([]);
  const [availableCountries, setAvailableCountries] = useState<CountryData[]>([]); // Changed to store CountryData[]
  const [isLoading, setIsLoading] = useState(true); // For filter options
  const [error, setError] = useState<string | null>(null);
  const [isStateInitialized, setIsStateInitialized] = useState(false); // New state

  // Custom state setter that also marks state as modified
  const setStateWithModificationFlag = (newState: React.SetStateAction<BrandDeepDiveState>) => {
    setState(newState);
    setHasUserModifiedState(true);
  };
  
  // Auto-update groupBy when date range changes
  useEffect(() => {
    if (isStateInitialized) {
      const smartGroupBy = getSmartGroupByTime(state.startDate, state.endDate);
      console.log(`[DEBUG] BrandDeepDiveContext - Smart grouping calculation:`);
      console.log(`[DEBUG] - Date range: ${state.startDate} to ${state.endDate}`);
      console.log(`[DEBUG] - Current groupBy: ${state.groupBy}`);
      console.log(`[DEBUG] - Calculated smart groupBy: ${smartGroupBy}`);
      
      if (smartGroupBy !== state.groupBy) {
        console.log(`[BrandDeepDiveContext] Auto-updating groupBy from ${state.groupBy} to ${smartGroupBy} based on date range`);
        setState(prev => ({
          ...prev,
          groupBy: smartGroupBy
        }));
      } else {
        console.log(`[DEBUG] - No groupBy change needed`);
      }
    }
  }, [state.startDate, state.endDate, isStateInitialized]);

  // Save state to localStorage whenever it changes and user has modified filters
  useEffect(() => {
    if (typeof window !== 'undefined' && userId && hasUserModifiedState) {
      const userStorageKey = getUserStorageKey();
      if (userStorageKey) {
        localStorage.setItem(userStorageKey, JSON.stringify(state));
        console.log('[BrandDeepDiveContext] Saved state to key:', userStorageKey);
      }
    }
  }, [state, userId, hasUserModifiedState, isImpersonating, originalUserId]);

  // Fetch available filter options
  useEffect(() => {
    async function fetchFilterOptions() {
      try {
        setIsLoading(true);
        setError(null);

        // Set default values first in case API calls fail
        const defaultBrands = ['Brand A', 'Brand B', 'Brand C'];
        const defaultSalesChannels = ['Direct', 'Marketplace', 'Wholesale'];
        const defaultCountries: CountryData[] = [ // Updated default countries to match new type
          { name: 'Canada', hasSales: true },
          { name: 'United States', hasSales: true },
          { name: 'Mexico', hasSales: false }
        ];

        try {
          // Fetch brands
          const brandsResponse = await fetch('/api/dashboard/brands');
          if (!brandsResponse.ok) {
            console.warn(`Failed to fetch brands: ${brandsResponse.statusText}`);
            // Don't throw, continue with other fetches
            setAvailableBrands(defaultBrands);
          } else {
            // Define a type for the brand data
            type BrandData = {
              name: string;
              group?: string;
              brand_id: string;
            };
            
            // Parse the response and handle possible error responses
            try {
              const responseData = await brandsResponse.json();
              
              // Check if the response is an error object
              if (responseData.error) {
                console.warn(`Error in brands response: ${responseData.error}`);
                setAvailableBrands(defaultBrands);
              } else {
                // Successfully received brands data
                const brandsData = responseData as BrandData[];
                
                // Filter out any brands that aren't in the database or the user doesn't have access to
                // (handled by the API, but adding this check for clarity)
                setAvailableBrands(brandsData.map(brand => brand.name));
                
                // If the currently selected brand is not in the available brands list,
                // reset the selected brand to null
                if (state.selectedBrand && !brandsData.some(brand => brand.name === state.selectedBrand)) {
                  setState(prev => ({
                    ...prev,
                    selectedBrand: null
                  }));
                }
              }
            } catch (parseError) {
              console.error('Error parsing brands response:', parseError);
              setAvailableBrands(defaultBrands);
            }
          }
        } catch (brandError) {
          console.error('Error fetching brands:', brandError);
          setAvailableBrands(defaultBrands);
        }

        try {
          // Fetch sales channels
          const channelsResponse = await fetch('/api/dashboard/sales-channels');
          if (!channelsResponse.ok) {
            console.warn(`Failed to fetch sales channels: ${channelsResponse.statusText}`);
            setAvailableSalesChannels(defaultSalesChannels);
          } else {
            const channelsData = await channelsResponse.json() as string[];
            setAvailableSalesChannels(channelsData);
          }
        } catch (channelsError) {
          console.error('Error fetching sales channels:', channelsError);
          setAvailableSalesChannels(defaultSalesChannels);
        }

        try {
          // Fetch countries
          const countriesResponse = await fetch('/api/dashboard/countries');
          if (!countriesResponse.ok) {
            console.warn(`Failed to fetch countries: ${countriesResponse.statusText}`);
            setAvailableCountries(defaultCountries);
          } else {
            const countriesData = await countriesResponse.json() as CountryData[]; // Expect CountryData[]
            setAvailableCountries(countriesData);
          }
        } catch (countriesError) {
          console.error('Error fetching countries:', countriesError);
          setAvailableCountries(defaultCountries); // Use updated default
        }
      } catch (err) {
        console.error('Failed to load filter options:', err);
        setError('Failed to load filter options');
        
        // Set default values if the entire process fails
        setAvailableBrands(['Brand A', 'Brand B', 'Brand C']);
        setAvailableSalesChannels(['Direct', 'Marketplace', 'Wholesale']);
        setAvailableCountries([ // Use updated default
            { name: 'Canada', hasSales: true },
            { name: 'United States', hasSales: true },
            { name: 'Mexico', hasSales: false }
        ]);
      } finally {
        setIsLoading(false);
      }
    }

    fetchFilterOptions();
  }, []);

  // Helper function to convert state to query params
  const getQueryParams = (): string => {
    const params = new URLSearchParams();

    if (state.startDate) {
      params.append('startDate', state.startDate);
    }
    if (state.endDate) {
      params.append('endDate', state.endDate);
    }
    if (state.currency) {
      params.append('currency', state.currency);
    }
    if (state.groupBy) {
      // The API expects groupByTime
      params.append('groupByTime', state.groupBy);
    }

    if (state.selectedBrand) {
      // Use 'brands' parameter for consistency across all contexts
      params.append('brands', state.selectedBrand);
      // Keep 'brand' param for backwards compatibility
      params.append('brand', state.selectedBrand);
    }

    if (state.salesChannels && state.salesChannels.length > 0) {
      params.append('salesChannels', state.salesChannels.join(','));
    }

    if (state.countryNames && state.countryNames.length > 0) {
      params.append('countryNames', state.countryNames.join(','));
    }

    return params.toString();
  };

  return (
    <BrandDeepDiveContext.Provider
      value={{
        state,
        setState: setStateWithModificationFlag,
        availableBrands,
        availableSalesChannels,
        availableCountries,
        isLoading,
        error,
        getQueryParams,
        isStateInitialized, // Provide in context
      }}
    >
      {children}
    </BrandDeepDiveContext.Provider>
  );
};

// Custom hook to use the brand deep dive context
export const useBrandDeepDive = () => {
  const context = useContext(BrandDeepDiveContext);
  if (context === undefined) {
    throw new Error('useBrandDeepDive must be used within a BrandDeepDiveProvider');
  }
  return context;
};
