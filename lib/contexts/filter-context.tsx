"use client"

import React, { ReactNode, createContext, useContext, useEffect, useState } from 'react';

import { useSession } from 'next-auth/react';

// Define the grouping options
export type GroupingOption = 'day' | 'week' | 'month' | 'quarter' | 'year';

// Define the filter state type
export type FilterState = {
  startDate: string;
  endDate: string;
  currency: 'CAD' | 'USD';
  groupBy: GroupingOption;
  brands: string[];
  brandGroups: string[];
  salesChannels: string[];
  countryNames: string[]; // Stores selected country names
};

// Type for individual country data from API
export type CountryData = {
  name: string;
  hasSales: boolean;
};

// Define the context type
type FilterContextType = {
  filters: FilterState;
  setFilters: React.Dispatch<React.SetStateAction<FilterState>>;
  availableBrands: string[];
  availableBrandGroups: string[];
  availableSalesChannels: string[];
  availableCountries: CountryData[]; // Changed to store CountryData
  isLoading: boolean;
  error: string | null;
  getQueryParams: () => string;
};

// Create the context with a default value
const FilterContext = createContext<FilterContextType | undefined>(undefined);

// Base LocalStorage key for saving filter state
const FILTER_STATE_STORAGE_KEY_BASE = 'dashboard_filter_state';

// Default filter state
const getDefaultFilterState = (): FilterState => {
  // Get dates for the last 90 days by default
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 90);

  // Format dates as YYYY-MM-DD
  const formattedStartDate = startDate.toISOString().split('T')[0];
  const formattedEndDate = endDate.toISOString().split('T')[0];

  return {
    startDate: formattedStartDate,
    endDate: formattedEndDate,
    currency: 'CAD',
    groupBy: 'month',
    brands: [],
    brandGroups: [],
    salesChannels: [],
    countryNames: [],
  };
};

// Provider component
export const FilterProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Get the current user session
  const { data: session } = useSession();
  const userId = session?.user?.id;
  const isImpersonating = session?.user?.isImpersonating;
  const originalUserId = session?.user?.originalUser?.id;
  
  // Initialize with default state
  const [filters, setFilters] = useState<FilterState>(getDefaultFilterState());
  const [hasUserModifiedFilters, setHasUserModifiedFilters] = useState(false);
  
  // Generate user-specific storage key with impersonation awareness
  const getUserStorageKey = () => {
    if (!userId) return null;
    
    if (isImpersonating && originalUserId) {
      // During impersonation, use isolated key to prevent contamination
      return `${FILTER_STATE_STORAGE_KEY_BASE}_${originalUserId}_impersonating_${userId}`;
    }
    
    return `${FILTER_STATE_STORAGE_KEY_BASE}_${userId}`;
  };
  
  // Load saved state from localStorage after component mounts and when user changes
  useEffect(() => {
    console.log('[FilterContext] useEffect triggered for user state loading:', {
      userId,
      isImpersonating,
      originalUserId,
      hasUserId: !!userId
    });

    if (userId) {
      try {
        const userStorageKey = getUserStorageKey();
        if (userStorageKey) {
          const savedState = localStorage.getItem(userStorageKey);
          if (savedState) {
            const parsedState = JSON.parse(savedState) as FilterState;
            setFilters(parsedState);
            console.log('[FilterContext] Loaded saved filters for key:', userStorageKey, {
              brands: parsedState.brands,
              brandsCount: parsedState.brands.length
            });
          } else {
            // If no saved state for this user, use default state
            const defaultState = getDefaultFilterState();
            setFilters(defaultState);
            console.log('[FilterContext] No saved state found, using defaults for key:', userStorageKey, {
              brands: defaultState.brands,
              brandsCount: defaultState.brands.length
            });
          }
        }
        // Reset the modification flag when loading a new user's filters
        setHasUserModifiedFilters(false);
      } catch (error) {
        console.error('Failed to load saved filter state:', error);
      }
    } else {
      console.log('[FilterContext] No userId available, not loading filters');
    }
  }, [userId, isImpersonating, originalUserId]); // Re-run when userId or impersonation state changes
  const [availableBrands, setAvailableBrands] = useState<string[]>([]);
  const [availableBrandGroups, setAvailableBrandGroups] = useState<string[]>([]);
  const [availableSalesChannels, setAvailableSalesChannels] = useState<string[]>([]);
  const [availableCountries, setAvailableCountries] = useState<CountryData[]>([]); // Changed to store CountryData[]
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Custom filter setter that also marks filters as modified
  const setFiltersWithModificationFlag = (newFilters: React.SetStateAction<FilterState>) => {
    setFilters(newFilters);
    setHasUserModifiedFilters(true);
  };
  
  // Save filter state to localStorage whenever it changes and user has modified filters
  useEffect(() => {
    if (typeof window !== 'undefined' && userId && hasUserModifiedFilters) {
      const userStorageKey = getUserStorageKey();
      if (userStorageKey) {
        localStorage.setItem(userStorageKey, JSON.stringify(filters));
        console.log('[FilterContext] Saved filters to key:', userStorageKey);
      }
    }
  }, [filters, userId, hasUserModifiedFilters, isImpersonating, originalUserId]);

  // Fetch available filter options
  useEffect(() => {
    async function fetchFilterOptions() {
      try {
        setIsLoading(true);
        setError(null);

        // Set default values first in case API calls fail
        const defaultBrands = ['Brand A', 'Brand B', 'Brand C'];
        const defaultBrandGroups = ['Group 1', 'Group 2'];
        const defaultSalesChannels = ['Direct', 'Marketplace', 'Wholesale'];
        const defaultCountries: CountryData[] = [ // Updated default countries to match new type
          { name: 'Canada', hasSales: true },
          { name: 'United States', hasSales: true },
          { name: 'Mexico', hasSales: false }
        ];

        try {
          // Fetch brands
          console.log('[DEBUG FILTER CONTEXT] Starting brands fetch...');
          const brandsResponse = await fetch('/api/dashboard/brands');
          console.log('[DEBUG FILTER CONTEXT] Brands response status:', brandsResponse.status, brandsResponse.statusText);
          
          if (!brandsResponse.ok) {
            console.warn(`Failed to fetch brands: ${brandsResponse.statusText}`);
            // Don't throw, continue with other fetches
            setAvailableBrands(defaultBrands);
            setAvailableBrandGroups(defaultBrandGroups);
          } else {
            // Define a type for the brand data
            type BrandData = {
              name: string;
              group?: string;
              brand_id: string;
            };
            
            // Parse the response and handle possible error responses
            try {
              const responseData = await brandsResponse.json();
              console.log('[DEBUG FILTER CONTEXT] Brands response data:', responseData);
              
              // Check if the response is an error object
              if (responseData.error) {
                console.warn(`Error in brands response: ${responseData.error}`, responseData);
                setAvailableBrands(defaultBrands);
                setAvailableBrandGroups(defaultBrandGroups);
              } else {
                // Successfully received brands data
                const brandsData = responseData as BrandData[];
                console.log('[DEBUG FILTER CONTEXT] Processing brands data:', brandsData);
                const brandNames = brandsData.map(brand => brand.name);
                const brandGroups = [...new Set(brandsData.map(brand => brand.group).filter(Boolean) as string[])];
                console.log('[DEBUG FILTER CONTEXT] Setting available brands:', brandNames);
                console.log('[DEBUG FILTER CONTEXT] Setting available brand groups:', brandGroups);
                setAvailableBrands(brandNames);
                setAvailableBrandGroups(brandGroups);
              }
            } catch (parseError) {
              console.error('Error parsing brands response:', parseError);
              setAvailableBrands(defaultBrands);
              setAvailableBrandGroups(defaultBrandGroups);
            }
          }
        } catch (brandError) {
          console.error('Error fetching brands:', brandError);
          setAvailableBrands(defaultBrands);
          setAvailableBrandGroups(defaultBrandGroups);
        }

        try {
          // Fetch sales channels
          const channelsResponse = await fetch('/api/dashboard/sales-channels');
          if (!channelsResponse.ok) {
            console.warn(`Failed to fetch sales channels: ${channelsResponse.statusText}`);
            setAvailableSalesChannels(defaultSalesChannels);
          } else {
            const channelsData = await channelsResponse.json() as string[];
            setAvailableSalesChannels(channelsData);
          }
        } catch (channelsError) {
          console.error('Error fetching sales channels:', channelsError);
          setAvailableSalesChannels(defaultSalesChannels);
        }

        try {
          // Fetch countries
          const countriesResponse = await fetch('/api/dashboard/countries');
          if (!countriesResponse.ok) {
            console.warn(`Failed to fetch countries: ${countriesResponse.statusText}`);
            setAvailableCountries(defaultCountries);
          } else {
            const countriesData = await countriesResponse.json() as CountryData[]; // Expect CountryData[]
            setAvailableCountries(countriesData);
          }
        } catch (countriesError) {
          console.error('Error fetching countries:', countriesError);
          setAvailableCountries(defaultCountries); // Use updated default
        }
      } catch (err) {
        console.error('Failed to load filter options:', err);
        setError('Failed to load filter options');
        
        // Set default values if the entire process fails
        setAvailableBrands(['Brand A', 'Brand B', 'Brand C']);
        setAvailableBrandGroups(['Group 1', 'Group 2']);
        setAvailableSalesChannels(['Direct', 'Marketplace', 'Wholesale']);
        setAvailableCountries([ // Use updated default
            { name: 'Canada', hasSales: true },
            { name: 'United States', hasSales: true },
            { name: 'Mexico', hasSales: false }
        ]);
      } finally {
        setIsLoading(false);
      }
    }

    fetchFilterOptions();
  }, []);



  // Helper function to convert filter state to query params
  const getQueryParams = (): string => {
    const params = new URLSearchParams();

    if (filters.startDate) {
      params.append('startDate', filters.startDate);
    }
    if (filters.endDate) {
      params.append('endDate', filters.endDate);
    }
    if (filters.currency) {
      params.append('currency', filters.currency);
    }
    if (filters.groupBy) {
      // The API expects groupByTime
      params.append('groupByTime', filters.groupBy);
    }

    if (filters.brands && filters.brands.length > 0) {
      params.append('brands', filters.brands.join(','));
    }

    // Note: brandGroups is not included as per the task description for /api/marketing/campaign-data

    if (filters.salesChannels && filters.salesChannels.length > 0) {
      params.append('salesChannels', filters.salesChannels.join(','));
    }

    if (filters.countryNames && filters.countryNames.length > 0) {
      params.append('countryNames', filters.countryNames.join(','));
    }

    return params.toString();
  };

  return (
    <FilterContext.Provider
      value={{
        filters,
        setFilters: setFiltersWithModificationFlag,
        availableBrands,
        availableBrandGroups,
        availableSalesChannels,
        availableCountries,
        isLoading,
        error,
        getQueryParams,
      }}
    >
      {children}
    </FilterContext.Provider>
  );
};

// Custom hook to use the filter context
export const useFilters = () => {
  const context = useContext(FilterContext);
  if (context === undefined) {
    throw new Error('useFilters must be used within a FilterProvider');
  }
  return context;
};
