#!/bin/bash

# Pages Router to App Router Migration Script
# This script automates the migration process from Next.js Pages Router to App Router

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to capitalize first letter
capitalize() {
    echo "$1" | sed 's/^./\U&/'
}

# Create backup
create_backup() {
    log_info "Creating backup of current project..."
    BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    cp -r src components lib app "$BACKUP_DIR/" 2>/dev/null || true
    cp next.config.ts tsconfig.json package.json "$BACKUP_DIR/" 2>/dev/null || true
    log_success "Backup created in $BACKUP_DIR"
}

# Phase 1: Preparation and Cleanup
phase1_preparation() {
    log_info "=== PHASE 1: PREPARATION AND CLEANUP ==="
    
    # 1.1 Consolidate Component Structure
    log_info "Consolidating component structure..."
    
    # Create necessary directories
    mkdir -p components/admin
    mkdir -p components/ui
    mkdir -p components/campaign-detail
    
    # Move admin components
    if [ -d "src/components/admin" ]; then
        log_info "Moving admin components..."
        cp -r src/components/admin/* components/admin/ 2>/dev/null || true
    fi
    
    # Merge UI components (check for conflicts)
    if [ -d "src/components/ui" ]; then
        log_info "Merging UI components..."
        for file in src/components/ui/*; do
            filename=$(basename "$file")
            if [ -f "components/ui/$filename" ]; then
                log_warning "Conflict found: $filename exists in both locations"
                # Keep the newer file
                if [ "src/components/ui/$filename" -nt "components/ui/$filename" ]; then
                    cp "$file" "components/ui/"
                    log_info "Updated $filename with newer version from src/"
                fi
            else
                cp "$file" "components/ui/" 2>/dev/null || true
            fi
        done
    fi
    
    # Move campaign detail components
    if [ -d "src/components/campaign-detail" ]; then
        log_info "Moving campaign detail components..."
        cp -r src/components/campaign-detail/* components/campaign-detail/ 2>/dev/null || true
    fi
    
    # 1.2 Consolidate Library Code
    log_info "Consolidating library code..."
    if [ -d "src/lib" ]; then
        cp -r src/lib/* lib/ 2>/dev/null || true
    fi
    
    if [ -d "src/config" ]; then
        mkdir -p lib/config
        cp -r src/config/* lib/config/ 2>/dev/null || true
    fi
    
    log_success "Phase 1 completed"
}

# Phase 2: Migrate Authentication
phase2_auth_migration() {
    log_info "=== PHASE 2: MIGRATE AUTHENTICATION ==="
    
    # 2.1 Move NextAuth Configuration
    log_info "Creating new auth configuration..."
    mkdir -p app/api/auth/\[...nextauth\]
    
    # Create route.ts for NextAuth
    if [ -f "src/pages/api/auth/[...nextauth].ts" ]; then
        cat > app/api/auth/\[...nextauth\]/route.ts << 'EOF'
import { authOptions } from '@/lib/auth-options';
import NextAuth from 'next-auth';

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
EOF
        
        # Extract authOptions to separate file
        log_info "Extracting auth options..."
        mkdir -p lib
        # This would need manual intervention to properly extract authOptions
        log_warning "Manual intervention needed: Extract authOptions from src/pages/api/auth/[...nextauth].ts to lib/auth-options.ts"
    fi
    
    # 2.2 Move auth pages
    log_info "Moving auth pages..."
    mkdir -p app/auth/signin
    
    if [ -f "src/pages/auth/signin.tsx" ]; then
        # Convert signin page to App Router format
        cat > app/auth/signin/page.tsx << 'EOF'
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth-options';
import SignInComponent from '@/components/auth/SignInComponent';

export default async function SignInPage() {
  const session = await getServerSession(authOptions);
  
  if (session) {
    redirect('/dashboard');
  }
  
  return <SignInComponent />;
}
EOF
        log_warning "Manual intervention needed: Extract SignIn component logic from src/pages/auth/signin.tsx to components/auth/SignInComponent.tsx"
    fi
    
    log_success "Phase 2 completed"
}

# Phase 3: Migrate Admin Functionality
phase3_admin_migration() {
    log_info "=== PHASE 3: MIGRATE ADMIN FUNCTIONALITY ==="
    
    # 3.1 Create Admin Layout
    log_info "Creating admin layout..."
    mkdir -p app/admin
    
    cat > app/admin/layout.tsx << 'EOF'
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { authOptions } from '@/lib/auth-options';
import AdminLayout from '@/components/admin/AdminLayout';

export default async function AdminRootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession(authOptions);
  
  if (!session || session.user.role !== 'admin') {
    redirect('/auth/signin');
  }
  
  return <AdminLayout>{children}</AdminLayout>;
}
EOF
    
    # 3.2 Migrate Admin Pages
    log_info "Migrating admin pages..."
    
    # Create admin page directories
    admin_pages=("users" "roles" "permissions" "groups" "brands" "settings" "backups" "db-structure")
    
    for page in "${admin_pages[@]}"; do
        mkdir -p "app/admin/$page"
    done
    
    # Convert admin index page
    if [ -f "src/pages/admin/index.tsx" ]; then
        log_info "Converting admin dashboard page..."
        # Extract the page content and convert to App Router format
        # This is a template - actual content needs to be extracted
        cat > app/admin/page.tsx << 'EOF'
import AdminDashboard from '@/components/admin/AdminDashboard';

export default function AdminPage() {
  return <AdminDashboard />;
}
EOF
    fi
    
    # Convert other admin pages
    for page in "${admin_pages[@]}"; do
        if [ -f "src/pages/admin/$page.tsx" ]; then
            log_info "Converting admin/$page page..."
            # Capitalize the component name
            component_name=$(capitalize "$page")
            
            # Create a basic template for each page
            cat > "app/admin/$page/page.tsx" << EOF
import ${component_name}Component from '@/components/admin/${component_name}Component';

export default function ${component_name}Page() {
  return <${component_name}Component />;
}
EOF
            log_warning "Manual intervention needed: Extract component logic from src/pages/admin/$page.tsx to components/admin/${component_name}Component.tsx"
        fi
    done
    
    # 3.3 Migrate Admin API Routes
    log_info "Migrating admin API routes..."
    
    # Create API route structure
    api_routes=("users" "roles" "permissions" "groups" "brands" "settings" "backups" "dashboard-stats" "db-structure" "impersonation")
    
    for route in "${api_routes[@]}"; do
        mkdir -p "app/api/admin/$route"
    done
    
    # Create route templates for each API
    for route in "${api_routes[@]}"; do
        if [ -f "src/pages/api/admin/$route.ts" ] || [ -f "src/pages/api/admin/$route/index.ts" ]; then
            log_info "Converting admin/$route API route..."
            
            # Create main route.ts
            cat > "app/api/admin/$route/route.ts" << 'EOF'
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || session.user.role !== 'admin') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // TODO: Implement GET logic from Pages Router
  return NextResponse.json({ message: 'GET not implemented' });
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || session.user.role !== 'admin') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // TODO: Implement POST logic from Pages Router
  return NextResponse.json({ message: 'POST not implemented' });
}
EOF
            
            # Create [id] routes where needed
            if [ "$route" != "settings" ] && [ "$route" != "dashboard-stats" ] && [ "$route" != "db-structure" ]; then
                mkdir -p "app/api/admin/$route/\[id\]"
                cat > "app/api/admin/$route/[id]/route.ts" << 'EOF'
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-options';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session || session.user.role !== 'admin') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // TODO: Implement GET by ID logic
  return NextResponse.json({ id: params.id });
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session || session.user.role !== 'admin') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // TODO: Implement PUT logic
  return NextResponse.json({ id: params.id });
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session || session.user.role !== 'admin') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // TODO: Implement DELETE logic
  return NextResponse.json({ id: params.id });
}
EOF
            fi
        fi
    done
    
    # Special routes
    mkdir -p app/api/admin/brands/sync-slugs
    mkdir -p app/api/admin/impersonation/start
    mkdir -p app/api/admin/impersonation/stop
    
    log_success "Phase 3 completed"
}

# Update import paths
update_imports() {
    log_info "Updating import paths..."
    
    # Find all TypeScript/JavaScript files
    find . -type f \( -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" \) \
        -not -path "./node_modules/*" \
        -not -path "./.next/*" \
        -not -path "./backup_*/*" \
        -exec sed -i.bak \
        -e 's|@/src/lib/|@/lib/|g' \
        -e 's|@/src/components/|@/components/|g' \
        -e 's|@/src/config/|@/lib/config/|g' \
        -e 's|from ["'\'']src/|from ["'\'']@/|g' \
        -e 's|import ["'\'']src/|import ["'\'']@/|g' \
        {} \;
    
    # Remove backup files
    find . -name "*.bak" -type f -delete
    
    log_success "Import paths updated"
}

# Phase 5: Clean Up
phase5_cleanup() {
    log_info "=== PHASE 5: CLEANUP ==="
    
    read -p "Are you sure you want to remove the src/ directory? This cannot be undone! (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "Removing Pages Router files..."
        rm -rf src/
        log_success "Pages Router files removed"
    else
        log_warning "Skipping cleanup - src/ directory preserved"
    fi
}

# Update configuration files
update_configs() {
    log_info "Updating configuration files..."
    
    # Update next.config.ts
    cat > next.config.ts << 'EOF'
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    serverActions: true,
  },
};

export default nextConfig;
EOF
    
    # Update tsconfig.json paths
    # This requires careful JSON manipulation, so we'll just provide instructions
    log_warning "Manual intervention needed: Update tsconfig.json paths to remove src/ references"
    
    log_success "Configuration files updated"
}

# Create manual conversion helper script
create_conversion_helper() {
    log_info "Creating conversion helper script..."
    
    cat > convert-components.sh << 'EOF'
#!/bin/bash

# Helper script to extract components from pages

# Function to extract admin component
extract_admin_component() {
    local page_name=$1
    local component_name=$(echo "$page_name" | sed 's/^./\U&/')
    
    echo "Creating component file for $component_name..."
    
    mkdir -p components/admin
    
    cat > "components/admin/${component_name}Component.tsx" << COMPONENT_EOF
'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';

// TODO: Import necessary components and utilities
// Check src/pages/admin/${page_name}.tsx for imports

export default function ${component_name}Component() {
  const { data: session, status } = useSession();
  const router = useRouter();
  
  // TODO: Copy state and logic from src/pages/admin/${page_name}.tsx
  // Remove: getServerSideProps, getStaticProps, NextPage type
  // Convert: next/router to next/navigation
  // Update: API calls to use new routes (/api/admin/...)
  
  if (status === 'loading') {
    return <div>Loading...</div>;
  }
  
  if (!session || session.user.role !== 'admin') {
    router.push('/auth/signin');
    return null;
  }
  
  return (
    <div>
      <h1>${component_name} Management</h1>
      {/* TODO: Copy JSX from src/pages/admin/${page_name}.tsx */}
    </div>
  );
}
COMPONENT_EOF
    
    echo "Component template created at components/admin/${component_name}Component.tsx"
    echo "Now manually copy the logic from src/pages/admin/${page_name}.tsx"
}

# Create auth component
create_auth_component() {
    mkdir -p components/auth
    
    cat > components/auth/SignInComponent.tsx << 'COMPONENT_EOF'
'use client';

import { signIn } from 'next-auth/react';
import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';

// TODO: Import UI components
// Check src/pages/auth/signin.tsx for imports

export default function SignInComponent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';
  
  const handleSignIn = async () => {
    await signIn('google', { callbackUrl });
  };
  
  return (
    <div className="flex min-h-screen items-center justify-center">
      {/* TODO: Copy JSX from src/pages/auth/signin.tsx */}
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold">Sign in to your account</h2>
        </div>
        <button
          onClick={handleSignIn}
          className="w-full rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
        >
          Sign in with Google
        </button>
      </div>
    </div>
  );
}
COMPONENT_EOF
    
    echo "Auth component template created at components/auth/SignInComponent.tsx"
}

# Create admin dashboard component
create_admin_dashboard() {
    mkdir -p components/admin
    
    cat > components/admin/AdminDashboard.tsx << 'COMPONENT_EOF'
'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';

// TODO: Import necessary components
// Check src/pages/admin/index.tsx for imports

export default function AdminDashboard() {
  const { data: session } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState(null);
  
  useEffect(() => {
    fetchDashboardStats();
  }, []);
  
  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/admin/dashboard-stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
    }
  };
  
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Admin Dashboard</h1>
      {/* TODO: Copy dashboard content from src/pages/admin/index.tsx */}
    </div>
  );
}
COMPONENT_EOF
    
    echo "Admin dashboard component created at components/admin/AdminDashboard.tsx"
}

# Main execution
case "$1" in
    "users"|"roles"|"permissions"|"groups"|"brands"|"settings"|"backups"|"db-structure")
        extract_admin_component "$1"
        ;;
    "auth")
        create_auth_component
        ;;
    "dashboard")
        create_admin_dashboard
        ;;
    "all")
        create_auth_component
        create_admin_dashboard
        for page in users roles permissions groups brands settings backups db-structure; do
            extract_admin_component "$page"
        done
        ;;
    *)
        echo "Usage: $0 {users|roles|permissions|groups|brands|settings|backups|db-structure|auth|dashboard|all}"
        exit 1
        ;;
esac
EOF
    
    chmod +x convert-components.sh
    log_success "Created convert-components.sh helper script"
}

# Main execution
main() {
    log_info "Starting Pages Router to App Router migration..."
    
    # Create backup first
    create_backup
    
    # Execute phases
    phase1_preparation
    update_imports  # Update imports after moving files
    
    phase2_auth_migration
    phase3_admin_migration
    
    # Create helper script
    create_conversion_helper
    
    # Update configurations
    update_configs
    
    # Final cleanup
    phase5_cleanup
    
    log_success "Migration script completed!"
    log_warning "Manual intervention required for:"
    echo "1. Extract component logic from pages to separate components"
    echo "   Run: ./convert-components.sh all"
    echo "2. Convert API route logic from Pages Router to App Router format"
    echo "3. Update tsconfig.json paths"
    echo "4. Test all functionality thoroughly"
    echo "5. Update any hardcoded paths or routes in components"
}

# Run main function
main