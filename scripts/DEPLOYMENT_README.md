# NOLK v4 Deployment Scripts

This directory contains scripts for deploying and managing the NOLK v4 application on your production server.

## 📋 Prerequisites

Before using these scripts, ensure you have:

1. **SSH Key**: The `ai_reporting_server_key.pem` file in the project root
2. **Git Repository**: Project must be a git repository with remote origin set
3. **Server Access**: SSH access to the EC2 server
4. **Node.js**: Node.js and npm installed on the server
5. **PM2**: PM2 process manager (will be installed automatically if missing)

## 🚀 Deployment Scripts

### 1. Full Production Deployment (`deploy-production.sh`)

**Purpose**: Complete deployment with all checks and validations

**Usage**:
```bash
# Full deployment with all checks
./scripts/deploy-production.sh

# Force deployment (skip local checks)
./scripts/deploy-production.sh --force
```

**What it does**:
- ✅ Checks prerequisites (SSH key, git repo, etc.)
- ✅ Runs type checking and linting
- ✅ Runs unit tests
- ✅ Tests local build
- ✅ Commits and pushes changes to git
- ✅ Deploys to server
- ✅ Installs dependencies
- ✅ Builds application on server
- ✅ Configures PM2
- ✅ Restarts application
- ✅ Verifies deployment
- ✅ Shows deployment summary

**When to use**: For production releases and when you want full validation

### 2. Quick Deployment (`quick-deploy.sh`)

**Purpose**: Fast deployment for minor changes

**Usage**:
```bash
./scripts/quick-deploy.sh
```

**What it does**:
- ⚡ Quick git push
- ⚡ Pulls latest code on server
- ⚡ Installs dependencies
- ⚡ Builds application
- ⚡ Restarts application
- ⚡ Basic health check

**When to use**: For quick fixes, minor updates, or when you're confident in your changes

### 3. Server Management (`server-manage.sh`)

**Purpose**: Common server operations and monitoring

**Usage**:
```bash
# Show application status
./scripts/server-manage.sh status

# Show logs (last 50 lines)
./scripts/server-manage.sh logs

# Show logs (last 100 lines)
./scripts/server-manage.sh logs 100

# Show real-time logs
./scripts/server-manage.sh tail

# Restart application
./scripts/server-manage.sh restart

# Stop application
./scripts/server-manage.sh stop

# Start application
./scripts/server-manage.sh start

# Monitor with PM2
./scripts/server-manage.sh monitor

# Connect via SSH
./scripts/server-manage.sh ssh

# Show environment info
./scripts/server-manage.sh env

# Create backup
./scripts/server-manage.sh backup

# Show help
./scripts/server-manage.sh help
```

## 🔧 Server Configuration

The scripts are configured for:

- **Server**: `ec2-54-234-11-118.compute-1.amazonaws.com`
- **User**: `ec2-user`
- **App Path**: `/var/www/apps/nolk-insights`
- **App Name**: `nolk-insights`
- **Port**: `6699`
- **Repository**: `https://github.com/nolkco/ai-reporting.git`

## 📁 File Structure on Server

After deployment, the server will have:

```
/var/www/apps/nolk-insights/
├── .git/                   # Git repository
├── .next/                  # Next.js build output
├── app/                    # Application source
├── components/             # React components
├── lib/                    # Utilities
├── node_modules/           # Dependencies
├── logs/                   # Application logs
├── ecosystem.config.js     # PM2 configuration
├── package.json           # Package configuration
└── .env.production        # Environment variables
```

## 🔍 Monitoring and Logs

### View Application Status
```bash
./scripts/server-manage.sh status
```

### View Logs
```bash
# Last 50 lines
./scripts/server-manage.sh logs

# Last 100 lines
./scripts/server-manage.sh logs 100

# Real-time logs
./scripts/server-manage.sh tail
```

### Monitor Resources
```bash
./scripts/server-manage.sh monitor
```

## 🚨 Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   ```bash
   # Check SSH key permissions
   chmod 600 ai_reporting_server_key.pem
   
   # Test SSH connection
   ssh -i ai_reporting_server_key.pem <EMAIL>
   ```

2. **Application Won't Start**
   ```bash
   # Check logs
   ./scripts/server-manage.sh logs
   
   # Check PM2 status
   ./scripts/server-manage.sh status
   
   # Restart application
   ./scripts/server-manage.sh restart
   ```

3. **Build Failures**
   ```bash
   # Connect to server and check
   ./scripts/server-manage.sh ssh
   cd /var/www/apps/nolk-insights
   npm run build
   ```

4. **Port Already in Use**
   ```bash
   # Connect to server
   ./scripts/server-manage.sh ssh
   
   # Check what's using port 6699
   sudo lsof -i :6699
   
   # Kill process if needed
   sudo kill -9 <PID>
   ```

### Emergency Commands

```bash
# Stop application immediately
./scripts/server-manage.sh stop

# Start application
./scripts/server-manage.sh start

# Force restart
./scripts/server-manage.sh restart

# Connect to server for manual intervention
./scripts/server-manage.sh ssh
```

## 🔒 Security Notes

- SSH key permissions are automatically set to 600
- StrictHostKeyChecking is disabled for automation
- All connections use the provided SSH key
- Environment variables should be stored in `.env.production` on the server

## 📞 Support

If you encounter issues:

1. Check the logs: `./scripts/server-manage.sh logs`
2. Verify server status: `./scripts/server-manage.sh status`
3. Test SSH connection: `./scripts/server-manage.sh ssh`
4. Create a backup before major changes: `./scripts/server-manage.sh backup`

## 🔄 Workflow Examples

### Daily Development Workflow
```bash
# Make your changes
# ...

# Quick deployment
./scripts/quick-deploy.sh

# Check if everything is working
./scripts/server-manage.sh status
```

### Production Release Workflow
```bash
# Make your changes
# ...

# Full deployment with all checks
./scripts/deploy-production.sh

# Monitor the deployment
./scripts/server-manage.sh tail
```

### Maintenance Workflow
```bash
# Create backup
./scripts/server-manage.sh backup

# Check current status
./scripts/server-manage.sh status

# Perform maintenance
# ...

# Restart if needed
./scripts/server-manage.sh restart
```
