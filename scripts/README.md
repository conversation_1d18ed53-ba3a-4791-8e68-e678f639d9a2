# Admin User Management Scripts

## add-admin-users.mjs

This script adds a predefined list of users with Admin role to the SQLite database.

### Usage

```bash
node scripts/add-admin-users.mjs
```

### What it does

1. Connects to the SQLite database (`data.db`)
2. Ensures the "Admin" role exists (creates it if not found)
3. Processes each user in the predefined list:
   - If user already exists: adds Admin role if not already assigned
   - If user doesn't exist: creates new user with Admin role
4. Generates temporary passwords for new users: `TempPassword123!`
5. Provides a summary of all users with Admin role

### Users Added

The script adds the following users with Admin role:
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>

### Security Notes

- All new users are created with the temporary password: `TempPassword123!`
- Users should change their password on first login
- The script is idempotent - running it multiple times won't create duplicates
- Existing users will only have the Admin role added if they don't already have it

### Database Schema

The script works with the following tables:
- `Users` - stores user information
- `Roles` - stores role definitions
- `UserRoles` - junction table linking users to roles
