#!/usr/bin/env node

import bcrypt from 'bcrypt';
import { open } from 'sqlite';
import path from 'path';
import sqlite3 from 'sqlite3';

// Define the path to the database file
const dbPath = path.resolve(process.cwd(), 'data.db');

// List of users to add with Admin role
const adminUsers = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

async function addAdminUsers() {
  let db;
  
  try {
    console.log(`Connecting to database at: ${dbPath}`);
    db = await open({
      filename: dbPath,
      driver: sqlite3.Database,
    });
    console.log('Successfully connected to the SQLite database.');

    // First, ensure the Admin role exists
    let adminRole = await db.get('SELECT id FROM Roles WHERE name = ?', ['Admin']);
    
    if (!adminRole) {
      console.log('Admin role not found, creating it...');
      const result = await db.run(
        'INSERT INTO Roles (name, description) VALUES (?, ?)',
        ['Admin', 'Administrator role with full system access']
      );
      adminRole = { id: result.lastID };
      console.log(`Created Admin role with ID: ${adminRole.id}`);
    } else {
      console.log(`Found existing Admin role with ID: ${adminRole.id}`);
    }

    // Process each user
    for (const email of adminUsers) {
      try {
        console.log(`\nProcessing user: ${email}`);
        
        // Check if user already exists
        const existingUser = await db.get('SELECT id, name FROM Users WHERE email = ?', [email]);
        
        if (existingUser) {
          console.log(`User ${email} already exists with ID: ${existingUser.id}`);
          
          // Check if user already has Admin role
          const existingRole = await db.get(
            'SELECT 1 FROM UserRoles WHERE user_id = ? AND role_id = ?',
            [existingUser.id, adminRole.id]
          );
          
          if (existingRole) {
            console.log(`User ${email} already has Admin role`);
          } else {
            // Add Admin role to existing user
            await db.run(
              'INSERT INTO UserRoles (user_id, role_id) VALUES (?, ?)',
              [existingUser.id, adminRole.id]
            );
            console.log(`Added Admin role to existing user ${email}`);
          }
        } else {
          // Create new user
          console.log(`Creating new user: ${email}`);
          
          // Extract name from email (part before @)
          const name = email.split('@')[0];
          
          // Generate a temporary password (should be changed on first login)
          const tempPassword = 'TempPassword123!';
          const passwordHash = await bcrypt.hash(tempPassword, 10);
          
          // Insert user
          const userResult = await db.run(
            'INSERT INTO Users (name, email, password_hash) VALUES (?, ?, ?)',
            [name, email, passwordHash]
          );
          
          const userId = userResult.lastID;
          console.log(`Created user ${email} with ID: ${userId}`);
          
          // Add Admin role to new user
          await db.run(
            'INSERT INTO UserRoles (user_id, role_id) VALUES (?, ?)',
            [userId, adminRole.id]
          );
          console.log(`Added Admin role to new user ${email}`);
          console.log(`Temporary password for ${email}: ${tempPassword}`);
        }
      } catch (userError) {
        console.error(`Error processing user ${email}:`, userError);
      }
    }

    console.log('\n=== Summary ===');
    
    // Get final count of users with Admin role
    const adminUserCount = await db.get(`
      SELECT COUNT(*) as count 
      FROM UserRoles ur 
      INNER JOIN Roles r ON ur.role_id = r.id 
      WHERE r.name = 'Admin'
    `);
    
    console.log(`Total users with Admin role: ${adminUserCount.count}`);
    
    // List all users with Admin role
    const adminUsersList = await db.all(`
      SELECT u.name, u.email 
      FROM Users u
      INNER JOIN UserRoles ur ON u.id = ur.user_id
      INNER JOIN Roles r ON ur.role_id = r.id
      WHERE r.name = 'Admin'
      ORDER BY u.email
    `);
    
    console.log('\nUsers with Admin role:');
    adminUsersList.forEach(user => {
      console.log(`- ${user.name} (${user.email})`);
    });

  } catch (error) {
    console.error('Error adding admin users:', error);
    process.exit(1);
  } finally {
    if (db) {
      await db.close();
      console.log('\nDatabase connection closed.');
    }
  }
}

// Run the script
addAdminUsers()
  .then(() => {
    console.log('\nScript completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });

export { addAdminUsers };
