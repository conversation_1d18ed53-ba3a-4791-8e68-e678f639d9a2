#!/usr/bin/env node

import bcrypt from 'bcrypt';
import { open } from 'sqlite';
import path from 'path';
import sqlite3 from 'sqlite3';

// Define the path to the database file
const dbPath = path.resolve(process.cwd(), 'data.db');

async function addFarbourAdmin() {
  let db;
  
  try {
    // Open database connection
    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });
    
    console.log('Connected to the SQLite database.');
    
    const email = 'farbour';
    const password = 'admin';
    const name = 'François Arbour';
    
    // Check if user already exists
    const existingUser = await db.get('SELECT id FROM Users WHERE email = ?', [email]);
    
    if (existingUser) {
      console.log(`User ${email} already exists with ID: ${existingUser.id}`);
      
      // Update password for existing user
      const passwordHash = await bcrypt.hash(password, 10);
      await db.run('UPDATE Users SET password_hash = ? WHERE email = ?', [passwordHash, email]);
      console.log(`Updated password for existing user: ${email}`);
    } else {
      // Create new user
      console.log(`Creating new user: ${email}`);
      
      const passwordHash = await bcrypt.hash(password, 10);
      
      // Insert user
      const userResult = await db.run(
        'INSERT INTO Users (name, email, password_hash) VALUES (?, ?, ?)',
        [name, email, passwordHash]
      );
      
      const userId = userResult.lastID;
      console.log(`Created user ${email} with ID: ${userId}`);
      
      // Check if Super Admin role exists, if not create it
      let superAdminRole = await db.get('SELECT id FROM Roles WHERE name = ?', ['Super Admin']);
      
      if (!superAdminRole) {
        console.log('Creating Super Admin role...');
        const roleResult = await db.run(
          'INSERT INTO Roles (name, description) VALUES (?, ?)',
          ['Super Admin', 'Full system access with all permissions']
        );
        superAdminRole = { id: roleResult.lastID };
        console.log(`Created Super Admin role with ID: ${superAdminRole.id}`);
      }
      
      // Add Super Admin role to user
      const existingRole = await db.get(
        'SELECT * FROM UserRoles WHERE user_id = ? AND role_id = ?',
        [userId, superAdminRole.id]
      );
      
      if (!existingRole) {
        await db.run(
          'INSERT INTO UserRoles (user_id, role_id) VALUES (?, ?)',
          [userId, superAdminRole.id]
        );
        console.log(`Added Super Admin role to user: ${email}`);
      } else {
        console.log(`User ${email} already has Super Admin role`);
      }
    }
    
    console.log('\n✅ Admin user setup completed successfully!');
    console.log(`Username: ${email}`);
    console.log(`Password: ${password}`);
    console.log('\nYou can now log in using these credentials.');
    
  } catch (error) {
    console.error('Error setting up admin user:', error);
    process.exit(1);
  } finally {
    if (db) {
      await db.close();
      console.log('Database connection closed.');
    }
  }
}

// Run the script
addFarbourAdmin();
