#!/bin/bash

# NOLK v4 Production Deployment Script
# This script deploys the application to the production server and restarts it

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVER_HOST="ec2-54-234-11-118.compute-1.amazonaws.com"
SERVER_USER="ec2-user"
SERVER_KEY="./ai_reporting_server_key.pem"
APP_PATH="/var/www/apps/nolk-insights"
APP_NAME="nolk-insights"
REPO_URL="https://github.com/nolkco/ai-reporting.git"
BRANCH="main"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required files exist
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if [ ! -f "$SERVER_KEY" ]; then
        print_error "SSH key not found: $SERVER_KEY"
        exit 1
    fi
    
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Are you in the project root?"
        exit 1
    fi
    
    # Check if git is initialized
    if [ ! -d ".git" ]; then
        print_error "Git repository not found. Please initialize git first."
        exit 1
    fi
    
    # Set correct permissions for SSH key
    chmod 600 "$SERVER_KEY"
    
    print_success "Prerequisites check passed"
}

# Function to run local tests and build
run_local_checks() {
    print_status "Running local checks..."
    
    # Type checking
    print_status "Running type check..."
    npm run type-check
    
    # Linting
    print_status "Running linter..."
    npm run lint
    
    # Unit tests
    print_status "Running unit tests..."
    npm run test:ci
    
    # Build locally to catch any build errors
    print_status "Testing local build..."
    npm run build
    
    print_success "Local checks passed"
}

# Function to commit and push changes
push_to_repository() {
    print_status "Pushing changes to repository..."
    
    # Check if there are any changes to commit
    if git diff --quiet && git diff --staged --quiet; then
        print_warning "No changes to commit"
    else
        # Add all changes
        git add .
        
        # Commit with timestamp
        COMMIT_MSG="Production deployment $(date '+%Y-%m-%d %H:%M:%S')"
        git commit -m "$COMMIT_MSG" || print_warning "Nothing to commit"
    fi
    
    # Push to remote
    git push origin "$BRANCH"
    
    print_success "Changes pushed to repository"
}

# Function to execute commands on remote server
execute_remote() {
    local command="$1"
    ssh -i "$SERVER_KEY" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" "$command"
}

# Function to deploy to server
deploy_to_server() {
    print_status "Deploying to production server..."
    
    # Test SSH connection
    print_status "Testing SSH connection..."
    execute_remote "echo 'SSH connection successful'"
    
    # Create app directory if it doesn't exist
    print_status "Setting up application directory..."
    execute_remote "sudo mkdir -p $APP_PATH && sudo chown $SERVER_USER:$SERVER_USER $APP_PATH"
    
    # Clone or pull repository
    print_status "Updating code on server..."
    execute_remote "
        if [ -d '$APP_PATH/.git' ]; then
            cd $APP_PATH && git fetch origin && git reset --hard origin/$BRANCH
        else
            git clone $REPO_URL $APP_PATH && cd $APP_PATH && git checkout $BRANCH
        fi
    "
    
    # Install dependencies
    print_status "Installing dependencies on server..."
    execute_remote "cd $APP_PATH && npm ci --production=false"
    
    # Build application
    print_status "Building application on server..."
    execute_remote "cd $APP_PATH && npm run build"
    
    print_success "Application deployed to server"
}

# Function to restart application
restart_application() {
    print_status "Restarting application..."
    
    # Check if PM2 is installed
    execute_remote "which pm2 > /dev/null || npm install -g pm2"
    
    # Create PM2 ecosystem file if it doesn't exist
    print_status "Setting up PM2 configuration..."
    execute_remote "cd $APP_PATH && cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: '$APP_NAME',
    script: 'npm',
    args: 'start',
    cwd: '$APP_PATH',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 6699
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=4096',
    watch: false,
    ignore_watch: ['node_modules', 'logs', '.next'],
    restart_delay: 1000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
EOF"
    
    # Create logs directory
    execute_remote "cd $APP_PATH && mkdir -p logs"
    
    # Stop existing application (if running)
    print_status "Stopping existing application..."
    execute_remote "cd $APP_PATH && pm2 stop $APP_NAME || true"
    
    # Start application
    print_status "Starting application..."
    execute_remote "cd $APP_PATH && pm2 start ecosystem.config.js"
    
    # Save PM2 configuration
    execute_remote "pm2 save"
    
    # Setup PM2 startup script
    execute_remote "pm2 startup systemd -u $SERVER_USER --hp /home/<USER>"
    
    print_success "Application restarted successfully"
}

# Function to verify deployment
verify_deployment() {
    print_status "Verifying deployment..."
    
    # Wait a moment for the application to start
    sleep 10
    
    # Check if application is running
    execute_remote "pm2 status $APP_NAME"
    
    # Test health endpoint
    print_status "Testing application health..."
    if execute_remote "curl -f http://localhost:6699/api/monitoring/health"; then
        print_success "Health check passed"
    else
        print_warning "Health check failed - application may still be starting"
    fi
    
    # Show application logs
    print_status "Recent application logs:"
    execute_remote "cd $APP_PATH && pm2 logs $APP_NAME --lines 10 --nostream"
    
    print_success "Deployment verification completed"
}

# Function to show deployment summary
show_summary() {
    echo ""
    echo "=================================="
    echo "   DEPLOYMENT SUMMARY"
    echo "=================================="
    echo "Server: $SERVER_HOST"
    echo "Application: $APP_NAME"
    echo "Path: $APP_PATH"
    echo "Branch: $BRANCH"
    echo "Status: $(execute_remote "pm2 jlist | jq -r '.[] | select(.name==\"$APP_NAME\") | .pm2_env.status'")"
    echo ""
    echo "Useful commands:"
    echo "  View logs: ssh -i $SERVER_KEY $SERVER_USER@$SERVER_HOST 'cd $APP_PATH && pm2 logs $APP_NAME'"
    echo "  Restart app: ssh -i $SERVER_KEY $SERVER_USER@$SERVER_HOST 'cd $APP_PATH && pm2 restart $APP_NAME'"
    echo "  Stop app: ssh -i $SERVER_KEY $SERVER_USER@$SERVER_HOST 'cd $APP_PATH && pm2 stop $APP_NAME'"
    echo "=================================="
}

# Main deployment function
main() {
    echo ""
    echo "🚀 Starting NOLK v4 Production Deployment"
    echo "=========================================="
    
    # Check if force flag is provided
    FORCE_DEPLOY=false
    if [ "$1" = "--force" ] || [ "$1" = "-f" ]; then
        FORCE_DEPLOY=true
        print_warning "Force deployment enabled - skipping local checks"
    fi
    
    # Run deployment steps
    check_prerequisites
    
    if [ "$FORCE_DEPLOY" = false ]; then
        run_local_checks
    fi
    
    push_to_repository
    deploy_to_server
    restart_application
    verify_deployment
    show_summary
    
    print_success "🎉 Deployment completed successfully!"
    echo ""
}

# Handle script interruption
trap 'print_error "Deployment interrupted"; exit 1' INT TERM

# Run main function with all arguments
main "$@"
