#!/bin/bash

# NOLK v4 Quick Deployment Script
# This script provides a fast deployment without extensive checks

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVER_HOST="ec2-54-234-11-118.compute-1.amazonaws.com"
SERVER_USER="ec2-user"
SERVER_KEY="./ai_reporting_server_key.pem"
APP_PATH="/var/www/apps/nolk-insights"
APP_NAME="nolk-insights"
BRANCH="main"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to execute commands on remote server
execute_remote() {
    local command="$1"
    ssh -i "$SERVER_KEY" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" "$command"
}

# Main quick deployment function
main() {
    echo ""
    echo "⚡ Quick NOLK v4 Deployment"
    echo "=========================="
    
    # Check SSH key exists
    if [ ! -f "$SERVER_KEY" ]; then
        print_error "SSH key not found: $SERVER_KEY"
        exit 1
    fi
    
    # Set correct permissions for SSH key
    chmod 600 "$SERVER_KEY"
    
    # Quick git push
    print_status "Pushing changes to repository..."
    git add . && git commit -m "Quick deployment $(date '+%Y-%m-%d %H:%M:%S')" || true
    git push origin "$BRANCH"
    
    # Quick server update
    print_status "Updating server..."
    execute_remote "cd $APP_PATH && git pull origin $BRANCH"
    
    # Install dependencies (only if package.json changed)
    print_status "Checking dependencies..."
    execute_remote "cd $APP_PATH && npm ci --production=false"
    
    # Build application
    print_status "Building application..."
    execute_remote "cd $APP_PATH && npm run build"
    
    # Restart application
    print_status "Restarting application..."
    execute_remote "cd $APP_PATH && pm2 restart $APP_NAME || pm2 start ecosystem.config.js"
    
    # Quick health check
    sleep 5
    print_status "Health check..."
    if execute_remote "curl -f http://localhost:6699/api/monitoring/health"; then
        print_success "✅ Quick deployment completed successfully!"
    else
        print_error "❌ Health check failed"
        exit 1
    fi
    
    echo ""
    echo "🎉 Application is running at: http://$SERVER_HOST:6699"
    echo ""
}

# Handle script interruption
trap 'print_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
