#!/bin/bash

# NOLK v4 Server Management Script
# This script provides common server management operations

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVER_HOST="ec2-54-234-11-118.compute-1.amazonaws.com"
SERVER_USER="ec2-user"
SERVER_KEY="./ai_reporting_server_key.pem"
APP_PATH="/var/www/apps/nolk-insights"
APP_NAME="nolk-insights"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Function to execute commands on remote server
execute_remote() {
    local command="$1"
    ssh -i "$SERVER_KEY" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" "$command"
}

# Function to show application status
show_status() {
    print_status "Application Status:"
    execute_remote "cd $APP_PATH && pm2 status $APP_NAME"
    echo ""
    print_status "System Resources:"
    execute_remote "free -h && echo '' && df -h /"
}

# Function to show application logs
show_logs() {
    local lines=${1:-50}
    print_status "Showing last $lines lines of logs:"
    execute_remote "cd $APP_PATH && pm2 logs $APP_NAME --lines $lines --nostream"
}

# Function to restart application
restart_app() {
    print_status "Restarting application..."
    execute_remote "cd $APP_PATH && pm2 restart $APP_NAME"
    sleep 5
    print_status "Testing health..."
    if execute_remote "curl -f http://localhost:6699/api/monitoring/health"; then
        print_success "Application restarted successfully"
    else
        print_error "Health check failed after restart"
    fi
}

# Function to stop application
stop_app() {
    print_status "Stopping application..."
    execute_remote "cd $APP_PATH && pm2 stop $APP_NAME"
    print_success "Application stopped"
}

# Function to start application
start_app() {
    print_status "Starting application..."
    execute_remote "cd $APP_PATH && pm2 start $APP_NAME"
    sleep 5
    print_status "Testing health..."
    if execute_remote "curl -f http://localhost:6699/api/monitoring/health"; then
        print_success "Application started successfully"
    else
        print_error "Health check failed after start"
    fi
}

# Function to connect to server via SSH
connect_ssh() {
    print_status "Connecting to server via SSH..."
    ssh -i "$SERVER_KEY" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST"
}

# Function to monitor application in real-time
monitor_app() {
    print_status "Monitoring application (Press Ctrl+C to exit)..."
    execute_remote "cd $APP_PATH && pm2 monit"
}

# Function to show real-time logs
tail_logs() {
    print_status "Showing real-time logs (Press Ctrl+C to exit)..."
    execute_remote "cd $APP_PATH && pm2 logs $APP_NAME --follow"
}

# Function to update environment variables
update_env() {
    print_status "Current environment file:"
    execute_remote "cd $APP_PATH && ls -la .env* || echo 'No .env files found'"
    echo ""
    print_warning "To update environment variables:"
    echo "1. Connect to server: $0 ssh"
    echo "2. Edit the .env file: nano $APP_PATH/.env.production"
    echo "3. Restart the application: $0 restart"
}

# Function to backup application
backup_app() {
    local backup_name="nolk-backup-$(date '+%Y%m%d-%H%M%S')"
    print_status "Creating backup: $backup_name"
    execute_remote "
        cd /tmp && 
        tar -czf $backup_name.tar.gz -C $APP_PATH . --exclude=node_modules --exclude=.next --exclude=logs &&
        echo 'Backup created: /tmp/$backup_name.tar.gz'
    "
    print_success "Backup completed"
}

# Function to show help
show_help() {
    echo "NOLK v4 Server Management Script"
    echo "================================"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  status      Show application and system status"
    echo "  logs [n]    Show last n lines of logs (default: 50)"
    echo "  tail        Show real-time logs"
    echo "  restart     Restart the application"
    echo "  stop        Stop the application"
    echo "  start       Start the application"
    echo "  monitor     Monitor application with PM2"
    echo "  ssh         Connect to server via SSH"
    echo "  env         Show environment configuration info"
    echo "  backup      Create a backup of the application"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 status"
    echo "  $0 logs 100"
    echo "  $0 restart"
    echo ""
}

# Main function
main() {
    # Check if SSH key exists
    if [ ! -f "$SERVER_KEY" ]; then
        print_error "SSH key not found: $SERVER_KEY"
        exit 1
    fi
    
    # Set correct permissions for SSH key
    chmod 600 "$SERVER_KEY"
    
    # Parse command
    case "${1:-help}" in
        "status")
            show_status
            ;;
        "logs")
            show_logs "${2:-50}"
            ;;
        "tail")
            tail_logs
            ;;
        "restart")
            restart_app
            ;;
        "stop")
            stop_app
            ;;
        "start")
            start_app
            ;;
        "monitor")
            monitor_app
            ;;
        "ssh")
            connect_ssh
            ;;
        "env")
            update_env
            ;;
        "backup")
            backup_app
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Handle script interruption
trap 'print_error "Operation interrupted"; exit 1' INT TERM

# Run main function with all arguments
main "$@"
