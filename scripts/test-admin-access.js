#!/usr/bin/env node

/**
 * Test script to verify admin access diagnostic endpoints
 * Run with: node scripts/test-admin-access.js
 */

const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';

async function testDiagnosticEndpoints() {
  console.log('🔍 Testing Admin Access Diagnostic Endpoints\n');
  
  try {
    // Test session diagnostic endpoint
    console.log('📊 Testing session diagnostic endpoint...');
    const sessionResponse = await fetch(`${baseUrl}/api/debug/session`);
    const sessionData = await sessionResponse.json();
    
    console.log(`Status: ${sessionResponse.status}`);
    if (sessionResponse.ok) {
      console.log('✅ Session diagnostic endpoint working');
      console.log(`User: ${sessionData.userEmail || 'Not authenticated'}`);
      console.log(`Roles: ${JSON.stringify(sessionData.roles || [])}`);
      console.log(`Admin Access: ${sessionData.hasAdminAccess || false}`);
    } else {
      console.log('❌ Session diagnostic failed:', sessionData.error);
    }
    
    console.log('\n---\n');
    
    // Test admin endpoints diagnostic
    console.log('🔧 Testing admin endpoints diagnostic...');
    const endpointsResponse = await fetch(`${baseUrl}/api/debug/admin-endpoints`);
    const endpointsData = await endpointsResponse.json();
    
    console.log(`Status: ${endpointsResponse.status}`);
    if (endpointsResponse.ok) {
      console.log('✅ Admin endpoints diagnostic working');
      console.log(`All endpoints would return 401: ${endpointsData.summary?.allEndpointsWouldReturn401 || 'Unknown'}`);
      console.log(`User Email: ${endpointsData.summary?.userEmail || 'Not authenticated'}`);
      console.log(`User Roles: ${JSON.stringify(endpointsData.summary?.userRoles || [])}`);
    } else {
      console.log('❌ Admin endpoints diagnostic failed:', endpointsData.error);
    }
    
    console.log('\n---\n');
    
    // Test actual admin endpoint
    console.log('🏛️ Testing actual admin groups endpoint...');
    const groupsResponse = await fetch(`${baseUrl}/api/admin/groups`);
    const groupsData = await groupsResponse.json();
    
    console.log(`Status: ${groupsResponse.status}`);
    if (groupsResponse.ok) {
      console.log('✅ Admin groups endpoint accessible');
      console.log(`Groups found: ${groupsData.data?.length || 0}`);
    } else {
      console.log('❌ Admin groups endpoint failed:', groupsData.error);
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Instructions for manual testing
console.log('🚀 Admin Access Diagnostic Test');
console.log('================================\n');
console.log('To test these endpoints manually:');
console.log(`1. Open your browser and navigate to: ${baseUrl}/api/debug/session`);
console.log(`2. Check the session data and look for roles/permissions`);
console.log(`3. Navigate to: ${baseUrl}/api/debug/admin-endpoints`);
console.log(`4. Check if admin endpoints would return 401`);
console.log(`5. Try accessing: ${baseUrl}/api/admin/groups`);
console.log(`6. Check browser console and server logs for diagnostic messages\n`);

console.log('📋 What to look for:');
console.log('- User email should be "<EMAIL>" for super admin');
console.log('- Roles should include ["Admin", "Super Admin"]');
console.log('- Permissions should include "view_groups", "manage_groups", etc.');
console.log('- hasAdminAccess should be true');
console.log('- Admin endpoints should NOT return 401\n');

// Run the test if this script is executed directly
if (require.main === module) {
  testDiagnosticEndpoints();
}

module.exports = { testDiagnosticEndpoints };