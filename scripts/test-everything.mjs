#!/usr/bin/env node

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';

/**
 * Comprehensive test script for NOLK v4
 * Tests everything using the farbour/admin credentials
 */

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    log(`\n${colors.cyan}Running: ${command} ${args.join(' ')}${colors.reset}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function checkPrerequisites() {
  log('\n🔍 Checking prerequisites...', colors.blue);
  
  try {
    // Check if Node.js is available
    await runCommand('node', ['--version']);
    
    // Check if npm is available
    await runCommand('npm', ['--version']);
    
    // Check if dependencies are installed
    const packageJsonPath = path.resolve(process.cwd(), 'package.json');
    const nodeModulesPath = path.resolve(process.cwd(), 'node_modules');
    
    try {
      await fs.access(nodeModulesPath);
      log('✅ Node modules found', colors.green);
    } catch {
      log('📦 Installing dependencies...', colors.yellow);
      await runCommand('npm', ['install']);
    }
    
    log('✅ Prerequisites check passed', colors.green);
  } catch (error) {
    log(`❌ Prerequisites check failed: ${error.message}`, colors.red);
    throw error;
  }
}

async function setupTestUser() {
  log('\n👤 Setting up test user (farbour/admin)...', colors.blue);
  
  try {
    await runCommand('node', ['scripts/add-farbour-admin.mjs']);
    log('✅ Test user setup completed', colors.green);
  } catch (error) {
    log(`❌ Test user setup failed: ${error.message}`, colors.red);
    throw error;
  }
}

async function runUnitTests() {
  log('\n🧪 Running unit tests...', colors.blue);
  
  try {
    await runCommand('npm', ['run', 'test:unit']);
    log('✅ Unit tests passed', colors.green);
    return true;
  } catch (error) {
    log(`❌ Unit tests failed: ${error.message}`, colors.red);
    return false;
  }
}

async function runIntegrationTests() {
  log('\n🔗 Running integration tests...', colors.blue);
  
  try {
    await runCommand('npm', ['run', 'test:integration']);
    log('✅ Integration tests passed', colors.green);
    return true;
  } catch (error) {
    log(`❌ Integration tests failed: ${error.message}`, colors.red);
    return false;
  }
}

async function startDevServer() {
  log('\n🚀 Starting development server...', colors.blue);
  
  return new Promise((resolve, reject) => {
    const server = spawn('npm', ['run', 'dev'], {
      stdio: 'pipe',
      shell: true
    });

    let serverReady = false;
    
    server.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Ready') || output.includes('localhost:6699') || output.includes('Local:')) {
        if (!serverReady) {
          serverReady = true;
          log('✅ Development server is ready', colors.green);
          resolve(server);
        }
      }
    });

    server.stderr.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Ready') || output.includes('localhost:6699')) {
        if (!serverReady) {
          serverReady = true;
          log('✅ Development server is ready', colors.green);
          resolve(server);
        }
      }
    });

    server.on('error', (error) => {
      reject(error);
    });

    // Timeout after 2 minutes
    setTimeout(() => {
      if (!serverReady) {
        reject(new Error('Server startup timeout'));
      }
    }, 120000);
  });
}

async function runE2ETests() {
  log('\n🎭 Running E2E tests with Playwright...', colors.blue);
  
  try {
    await runCommand('npm', ['run', 'test:e2e']);
    log('✅ E2E tests passed', colors.green);
    return true;
  } catch (error) {
    log(`❌ E2E tests failed: ${error.message}`, colors.red);
    return false;
  }
}

async function generateTestReport(results) {
  log('\n📊 Generating test report...', colors.blue);
  
  const report = {
    timestamp: new Date().toISOString(),
    testUser: 'farbour/admin',
    results: results,
    summary: {
      total: Object.keys(results).length,
      passed: Object.values(results).filter(r => r === true).length,
      failed: Object.values(results).filter(r => r === false).length
    }
  };

  const reportPath = path.resolve(process.cwd(), 'test-report.json');
  await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
  
  log(`📄 Test report saved to: ${reportPath}`, colors.cyan);
  
  // Print summary
  log('\n📋 Test Summary:', colors.bright);
  log(`Total test suites: ${report.summary.total}`);
  log(`Passed: ${report.summary.passed}`, colors.green);
  log(`Failed: ${report.summary.failed}`, colors.red);
  
  if (report.summary.failed === 0) {
    log('\n🎉 All tests passed!', colors.green);
  } else {
    log('\n⚠️  Some tests failed. Check the details above.', colors.yellow);
  }
}

async function main() {
  log('🚀 Starting comprehensive test suite for NOLK v4', colors.bright);
  log('Testing with user: farbour/admin', colors.cyan);
  
  const results = {};
  let devServer = null;
  
  try {
    // Step 1: Check prerequisites
    await checkPrerequisites();
    
    // Step 2: Setup test user
    await setupTestUser();
    
    // Step 3: Run unit tests
    results.unitTests = await runUnitTests();
    
    // Step 4: Run integration tests
    results.integrationTests = await runIntegrationTests();
    
    // Step 5: Start dev server for E2E tests
    devServer = await startDevServer();
    
    // Wait a bit for server to fully initialize
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Step 6: Run E2E tests
    results.e2eTests = await runE2ETests();
    
    // Step 7: Generate report
    await generateTestReport(results);
    
  } catch (error) {
    log(`\n💥 Test suite failed: ${error.message}`, colors.red);
    process.exit(1);
  } finally {
    // Clean up: Stop dev server
    if (devServer) {
      log('\n🛑 Stopping development server...', colors.yellow);
      devServer.kill('SIGTERM');
      
      // Give it a moment to shut down gracefully
      setTimeout(() => {
        if (!devServer.killed) {
          devServer.kill('SIGKILL');
        }
      }, 5000);
    }
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('\n🛑 Test suite interrupted by user', colors.yellow);
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('\n🛑 Test suite terminated', colors.yellow);
  process.exit(0);
});

// Run the main function
main().catch((error) => {
  log(`\n💥 Unexpected error: ${error.message}`, colors.red);
  process.exit(1);
});
