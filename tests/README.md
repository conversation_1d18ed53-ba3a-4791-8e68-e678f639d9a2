# Testing Strategy for Next.js Application with Redshift Integration

This directory contains the test suite for the Next.js application with Redshift database integration. The tests are organized into different categories to ensure comprehensive coverage of the application.

## Test Structure

```
/tests
├── /unit                    # Unit tests for individual functions
│   ├── /lib
│   │   ├── /api             # Tests for API utility functions
│   │   └── /utils           # Tests for utility functions
├── /integration             # Integration tests combining multiple units
│   ├── /redshift            # Tests for Redshift integration
│   ├── /api                 # Tests for API routes with Redshift
├── /performance             # Performance and load tests
├── /fixtures                # Test data and fixtures (future use)
└── /utils                   # Test utilities and helpers
    ├── redshift-test-utils.ts  # Utilities for Redshift testing
    ├── test-setup.ts           # Test setup and teardown
    └── test-types.ts           # Shared type definitions
```

## Priority Areas

1. **Flexible KPIs Endpoint** - This has been identified as a problematic area with calculation errors and performance issues
2. **Redshift Connection Management** - Testing pool creation, connection handling, and error recovery
3. **Query Building Logic** - Ensuring SQL queries are correctly constructed based on parameters
4. **Data Processing and Calculations** - Verifying the accuracy of KPI calculations and data transformations
5. **Error Handling and Edge Cases** - Testing how the system handles invalid inputs, missing data, etc.

## Running Tests

The test suite uses Jest as the testing framework. The following scripts are available in `package.json`:

```bash
# Run all tests
npm test

# Run tests in watch mode (useful during development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Run only unit tests
npm run test:unit

# Run only integration tests
npm run test:integration

# Run only performance tests
npm run test:performance
```

## Test Environment

The tests require a connection to a Redshift database. The test suite is designed to work with a dedicated test Redshift database with the same schema as production.

Environment variables needed for testing:
- `REDSHIFT_HOST`
- `REDSHIFT_PORT`
- `REDSHIFT_DATABASE`
- `REDSHIFT_USER`
- `REDSHIFT_PASSWORD`

## Test Categories

### Unit Tests

Unit tests focus on testing individual functions in isolation:

- **Query Building Functions**: Tests for functions that construct SQL queries based on parameters
- **Data Processing Functions**: Tests for functions that transform data
- **Utility Functions**: Tests for helper functions

### Integration Tests

Integration tests verify how components work together:

- **Redshift Connection**: Tests for connection pooling, error handling, and reconnection logic
- **Database Queries**: Tests for actual queries against the test Redshift database
- **API Flow**: Tests for the complete flow from API request to database query to response

### Performance Tests

Performance tests measure the response time and resource usage of the application:

- **Query Performance**: Tests for query execution time
- **Load Testing**: Tests for system behavior under load
- **Concurrency**: Tests for multiple simultaneous requests

## Important Notes

1. According to project rules, we should NEVER use mock data and should always load directly from the Redshift database.
2. Tests are designed to be skipped if the Redshift connection is not available, allowing for partial test runs in environments without database access.
3. Performance tests have configurable thresholds that may need adjustment based on the specific environment.