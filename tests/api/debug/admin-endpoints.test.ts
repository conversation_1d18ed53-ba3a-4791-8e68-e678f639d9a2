/**
 * API Route Tests for Debug Admin Endpoints
 * 
 * Tests the /api/debug/admin-endpoints route which provides diagnostics
 * for admin endpoint accessibility and permission validation.
 */

import { hasAdminAccess, hasSuperAdminAccess } from '@/lib/api/admin-auth';

import { GET } from '@/app/api/debug/admin-endpoints/route';
import { getServerSession } from 'next-auth';

// Mock next-auth functions
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}));

// Mock admin-auth functions
jest.mock('@/lib/api/admin-auth', () => ({
  hasAdminAccess: jest.fn(),
  hasSuperAdminAccess: jest.fn(),
}));

// Mock auth options
jest.mock('@/lib/auth-options', () => ({
  authOptions: {
    providers: [],
    callbacks: {},
  },
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockHasAdminAccess = hasAdminAccess as jest.MockedFunction<typeof hasAdminAccess>;
const mockHasSuperAdminAccess = hasSuperAdminAccess as jest.MockedFunction<typeof hasSuperAdminAccess>;

describe('/api/debug/admin-endpoints', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('GET /api/debug/admin-endpoints', () => {
    it('should return 401 when no session exists', async () => {
      // Mock no session
      mockGetServerSession.mockResolvedValue(null);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
      expect(data.message).toBe('No session found');
    });

    it('should return 403 when user has no admin access', async () => {
      // Mock regular user session without admin access
      const mockSession = {
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          roles: ['User'],
          permissions: ['view_dashboard'],
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockReturnValue(false);
      mockHasSuperAdminAccess.mockReturnValue(false);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Forbidden');
      expect(data.message).toBe('Admin access required');
    });

    it('should return comprehensive admin endpoint diagnostics for admin user', async () => {
      // Mock admin user session
      const mockSession = {
        user: {
          id: 'admin-123',
          email: '<EMAIL>',
          roles: ['Admin'],
          permissions: [
            'view_users', 'manage_users',
            'view_roles', 'manage_roles',
            'view_permissions', 'manage_permissions',
            'view_groups', 'manage_groups',
            'view_brands', 'manage_brands'
          ],
          isSuperAdmin: false,
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockReturnValue(true);
      mockHasSuperAdminAccess.mockReturnValue(false);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify session info
      expect(data.session.userId).toBe('admin-123');
      expect(data.session.email).toBe('<EMAIL>');
      expect(data.session.roles).toEqual(['Admin']);
      expect(data.session.hasAdminAccess).toBe(true);
      expect(data.session.hasSuperAdminAccess).toBe(false);
      
      // Verify admin endpoints accessibility
      expect(data.adminEndpoints).toBeDefined();
      expect(data.adminEndpoints.users).toBeDefined();
      expect(data.adminEndpoints.users.accessible).toBe(true);
      expect(data.adminEndpoints.users.hasViewPermission).toBe(true);
      expect(data.adminEndpoints.users.hasManagePermission).toBe(true);
      
      expect(data.adminEndpoints.roles).toBeDefined();
      expect(data.adminEndpoints.roles.accessible).toBe(true);
      expect(data.adminEndpoints.roles.hasViewPermission).toBe(true);
      expect(data.adminEndpoints.roles.hasManagePermission).toBe(true);
      
      expect(data.adminEndpoints.permissions).toBeDefined();
      expect(data.adminEndpoints.permissions.accessible).toBe(true);
      expect(data.adminEndpoints.permissions.hasViewPermission).toBe(true);
      expect(data.adminEndpoints.permissions.hasManagePermission).toBe(true);
      
      expect(data.adminEndpoints.groups).toBeDefined();
      expect(data.adminEndpoints.groups.accessible).toBe(true);
      expect(data.adminEndpoints.groups.hasViewPermission).toBe(true);
      expect(data.adminEndpoints.groups.hasManagePermission).toBe(true);
      
      expect(data.adminEndpoints.brands).toBeDefined();
      expect(data.adminEndpoints.brands.accessible).toBe(true);
      expect(data.adminEndpoints.brands.hasViewPermission).toBe(true);
      expect(data.adminEndpoints.brands.hasManagePermission).toBe(true);
    });

    it('should return diagnostics for super admin user with all access', async () => {
      // Mock super admin user session
      const mockSession = {
        user: {
          id: 'superadmin-123',
          email: '<EMAIL>',
          roles: ['Admin', 'Super Admin'],
          permissions: [
            'view_users', 'manage_users',
            'view_roles', 'manage_roles',
            'view_permissions', 'manage_permissions',
            'view_groups', 'manage_groups',
            'view_brands', 'manage_brands',
            'view_backups', 'manage_backups',
            'view_settings', 'manage_settings'
          ],
          isSuperAdmin: true,
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockReturnValue(true);
      mockHasSuperAdminAccess.mockReturnValue(true);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify session info
      expect(data.session.userId).toBe('superadmin-123');
      expect(data.session.hasAdminAccess).toBe(true);
      expect(data.session.hasSuperAdminAccess).toBe(true);
      
      // Verify all admin endpoints are accessible
      Object.values(data.adminEndpoints).forEach((endpoint: any) => {
        expect(endpoint.accessible).toBe(true);
      });
      
      // Verify super admin specific endpoints
      expect(data.adminEndpoints.backups).toBeDefined();
      expect(data.adminEndpoints.backups.accessible).toBe(true);
      expect(data.adminEndpoints.settings).toBeDefined();
      expect(data.adminEndpoints.settings.accessible).toBe(true);
    });

    it('should handle partial permissions correctly', async () => {
      // Mock admin user with partial permissions
      const mockSession = {
        user: {
          id: 'partial-admin-123',
          email: '<EMAIL>',
          roles: ['Admin'],
          permissions: [
            'view_users', // Has view but not manage
            'view_roles',
            'manage_permissions', // Has manage but not view
            'view_groups',
            'manage_groups'
          ],
          isSuperAdmin: false,
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockReturnValue(true);
      mockHasSuperAdminAccess.mockReturnValue(false);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify partial permissions
      expect(data.adminEndpoints.users.hasViewPermission).toBe(true);
      expect(data.adminEndpoints.users.hasManagePermission).toBe(false);
      expect(data.adminEndpoints.users.accessible).toBe(true); // Should be accessible with view permission
      
      expect(data.adminEndpoints.roles.hasViewPermission).toBe(true);
      expect(data.adminEndpoints.roles.hasManagePermission).toBe(false);
      
      expect(data.adminEndpoints.permissions.hasViewPermission).toBe(false);
      expect(data.adminEndpoints.permissions.hasManagePermission).toBe(true);
      expect(data.adminEndpoints.permissions.accessible).toBe(true); // Should be accessible with manage permission
      
      expect(data.adminEndpoints.groups.hasViewPermission).toBe(true);
      expect(data.adminEndpoints.groups.hasManagePermission).toBe(true);
      expect(data.adminEndpoints.groups.accessible).toBe(true);
      
      expect(data.adminEndpoints.brands.hasViewPermission).toBe(false);
      expect(data.adminEndpoints.brands.hasManagePermission).toBe(false);
      expect(data.adminEndpoints.brands.accessible).toBe(false); // Should not be accessible without any permissions
    });

    it('should handle impersonating admin user', async () => {
      // Mock impersonating admin session
      const mockSession = {
        user: {
          id: 'impersonated-user-456',
          email: '<EMAIL>',
          roles: ['User'],
          permissions: ['view_dashboard'],
          isImpersonating: true,
          originalUser: {
            id: 'admin-123',
            email: '<EMAIL>',
            roles: ['Admin', 'Super Admin'],
            permissions: ['view_users', 'manage_users'],
          },
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockReturnValue(true); // Admin access through original user
      mockHasSuperAdminAccess.mockReturnValue(true);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify impersonation info
      expect(data.session.isImpersonating).toBe(true);
      expect(data.session.originalUser).toBeDefined();
      expect(data.session.originalUser.id).toBe('admin-123');
      expect(data.session.originalUser.email).toBe('<EMAIL>');
      
      // Should have admin access through original user
      expect(data.session.hasAdminAccess).toBe(true);
      expect(data.session.hasSuperAdminAccess).toBe(true);
    });

    it('should handle user with no permissions', async () => {
      // Mock admin user with no specific permissions
      const mockSession = {
        user: {
          id: 'admin-no-perms-123',
          email: '<EMAIL>',
          roles: ['Admin'],
          permissions: [], // No specific permissions
          isSuperAdmin: false,
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockReturnValue(true);
      mockHasSuperAdminAccess.mockReturnValue(false);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // All endpoints should be inaccessible due to lack of permissions
      Object.values(data.adminEndpoints).forEach((endpoint: any) => {
        expect(endpoint.hasViewPermission).toBe(false);
        expect(endpoint.hasManagePermission).toBe(false);
        expect(endpoint.accessible).toBe(false);
      });
    });

    it('should handle session retrieval errors', async () => {
      // Mock session error
      mockGetServerSession.mockRejectedValue(new Error('Database connection failed'));

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Internal Server Error');
      expect(data.message).toBe('Failed to analyze admin endpoints');
      expect(data.details).toBe('Database connection failed');
    });

    it('should handle admin access check errors', async () => {
      // Mock valid session but admin access error
      const mockSession = {
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          roles: ['Admin'],
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockImplementation(() => {
        throw new Error('Admin access check failed');
      });

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Internal Server Error');
      expect(data.message).toBe('Failed to analyze admin endpoints');
      expect(data.details).toBe('Admin access check failed');
    });

    it('should provide comprehensive endpoint coverage', async () => {
      // Mock super admin session
      const mockSession = {
        user: {
          id: 'superadmin-123',
          email: '<EMAIL>',
          roles: ['Admin', 'Super Admin'],
          permissions: [
            'view_users', 'manage_users',
            'view_roles', 'manage_roles',
            'view_permissions', 'manage_permissions',
            'view_groups', 'manage_groups',
            'view_brands', 'manage_brands',
            'view_backups', 'manage_backups',
            'view_settings', 'manage_settings'
          ],
          isSuperAdmin: true,
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockReturnValue(true);
      mockHasSuperAdminAccess.mockReturnValue(true);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      
      // Verify all expected admin endpoints are covered
      const expectedEndpoints = [
        'users', 'roles', 'permissions', 'groups', 'brands', 
        'backups', 'settings', 'dashboardStats', 'dbStructure'
      ];
      
      expectedEndpoints.forEach(endpoint => {
        expect(data.adminEndpoints[endpoint]).toBeDefined();
        expect(data.adminEndpoints[endpoint]).toHaveProperty('accessible');
        expect(data.adminEndpoints[endpoint]).toHaveProperty('hasViewPermission');
        expect(data.adminEndpoints[endpoint]).toHaveProperty('hasManagePermission');
      });
    });
  });
});