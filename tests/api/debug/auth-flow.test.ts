/**
 * API Route Tests for Debug Auth Flow Endpoint
 * 
 * Tests the /api/debug/auth-flow route which provides comprehensive
 * authentication flow diagnostics including session, JWT, cookies, and environment checks.
 */

import { GET } from '@/app/api/debug/auth-flow/route';
import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { getToken } from 'next-auth/jwt';

// Mock NextResponse
jest.mock('next/server', () => {
  const actual = jest.requireActual('next/server');
  return {
    ...actual,
    NextRequest: actual.NextRequest,
    NextResponse: {
      json: jest.fn((data, init) => {
        const response = {
          json: jest.fn().mockResolvedValue(data),
          status: init?.status || 200,
          headers: new Headers(),
          ok: (init?.status || 200) >= 200 && (init?.status || 200) < 300,
          statusText: 'OK',
          url: '',
          redirected: false,
          type: 'basic',
          body: null,
          bodyUsed: false,
          clone: jest.fn(),
          arrayBuffer: jest.fn(),
          blob: jest.fn(),
          formData: jest.fn(),
          text: jest.fn(),
        };
        return response;
      }),
    },
  };
});

// Mock next-auth functions
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}));

jest.mock('next-auth/jwt', () => ({
  getToken: jest.fn(),
  decode: jest.fn(),
}));

// Mock auth options
jest.mock('@/lib/auth-options', () => ({
  authOptions: {
    providers: [],
    callbacks: {},
  },
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockGetToken = getToken as jest.MockedFunction<typeof getToken>;

describe('/api/debug/auth-flow', () => {
  let mockRequest: NextRequest;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Create a mock NextRequest
    mockRequest = new NextRequest('http://localhost:3000/api/debug/auth-flow', {
      method: 'GET',
      headers: {
        'user-agent': 'test-agent',
        'host': 'localhost:3000',
        'origin': 'http://localhost:3000',
      },
    });

    // Set up environment variables
    process.env.NEXTAUTH_URL = 'http://localhost:3000';
    process.env.NEXTAUTH_SECRET = 'test-secret';
    process.env.GOOGLE_CLIENT_ID = 'test-client-id';
    process.env.GOOGLE_CLIENT_SECRET = 'test-client-secret';
    Object.defineProperty(process.env, 'NODE_ENV', { value: 'test', writable: true });
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.NEXTAUTH_URL;
    delete process.env.NEXTAUTH_SECRET;
    delete process.env.GOOGLE_CLIENT_ID;
    delete process.env.GOOGLE_CLIENT_SECRET;
  });

  describe('GET /api/debug/auth-flow', () => {
    it('should return comprehensive auth diagnostics with valid session', async () => {
      // Mock valid session
      const mockSession = {
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          roles: ['user'],
          permissions: ['read', 'write'],
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);

      // Mock valid token
      const mockToken = {
        sub: 'user-123',
        email: '<EMAIL>',
        roles: ['user'],
        permissions: ['read', 'write'],
      };
      mockGetToken.mockResolvedValue(mockToken);

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify server session test
      expect(data.tests.serverSession.exists).toBe(true);
      expect(data.tests.serverSession.userEmail).toBe('<EMAIL>');
      expect(data.tests.serverSession.userId).toBe('user-123');
      expect(data.tests.serverSession.roles).toEqual(['user']);
      expect(data.tests.serverSession.permissions).toBe(2);

      // Verify JWT token test
      expect(data.tests.jwtToken.exists).toBe(true);
      expect(data.tests.jwtToken.userEmail).toBe('<EMAIL>');
      expect(data.tests.jwtToken.userId).toBe('user-123');
      expect(data.tests.jwtToken.roles).toEqual(['user']);
      expect(data.tests.jwtToken.permissions).toBe(2);

      // Verify environment check
      expect(data.tests.environment.nextauthUrl).toBe('http://localhost:3000');
      expect(data.tests.environment.nextauthSecret).toBe(true);
      expect(data.tests.environment.googleClientId).toBe(true);
      expect(data.tests.environment.googleClientSecret).toBe(true);
      expect(data.tests.environment.nodeEnv).toBe('test');

      // Verify headers
      expect(data.headers.userAgent).toBe('test-agent');
      expect(data.headers.host).toBe('localhost:3000');
      expect(data.headers.origin).toBe('http://localhost:3000');
    });

    it('should handle missing session gracefully', async () => {
      // Mock no session
      mockGetServerSession.mockResolvedValue(null);
      mockGetToken.mockResolvedValue(null);

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify server session test
      expect(data.tests.serverSession.exists).toBe(false);
      expect(data.tests.serverSession.userEmail).toBeUndefined();
      expect(data.tests.serverSession.userId).toBeUndefined();

      // Verify JWT token test
      expect(data.tests.jwtToken.exists).toBe(false);
      expect(data.tests.jwtToken.userEmail).toBeUndefined();
      expect(data.tests.jwtToken.userId).toBeUndefined();
    });

    it('should handle missing environment variables', async () => {
      // Remove environment variables
      delete process.env.NEXTAUTH_SECRET;
      delete process.env.GOOGLE_CLIENT_ID;
      delete process.env.GOOGLE_CLIENT_SECRET;

      mockGetServerSession.mockResolvedValue(null);
      mockGetToken.mockResolvedValue(null);

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify environment check
      expect(data.tests.environment.nextauthSecret).toBe(false);
      expect(data.tests.environment.googleClientId).toBe(false);
      expect(data.tests.environment.googleClientSecret).toBe(false);
    });

    it('should handle session errors gracefully', async () => {
      // Mock session error
      mockGetServerSession.mockRejectedValue(new Error('Session error'));
      mockGetToken.mockResolvedValue(null);

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.message).toBe('Session error');
      expect(data.error.name).toBe('Error');
    });

    it('should handle JWT errors gracefully', async () => {
      // Mock JWT error
      mockGetServerSession.mockResolvedValue(null);
      mockGetToken.mockRejectedValue(new Error('JWT verification failed'));

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.message).toBe('JWT verification failed');
      expect(data.isJwtError).toBe(true);
    });

    it('should detect cryptographic errors', async () => {
      // Mock cryptographic error
      mockGetServerSession.mockRejectedValue(new Error('Encryption key not found'));
      mockGetToken.mockResolvedValue(null);

      const response = await GET(mockRequest);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.message).toBe('Encryption key not found');
      expect(data.isCryptographicError).toBe(true);
    });

    it('should process cookies correctly', async () => {
      // Create request with cookies
      const requestWithCookies = new NextRequest('http://localhost:3000/api/debug/auth-flow', {
        method: 'GET',
        headers: {
          'cookie': 'next-auth.session-token=test-token; next-auth.csrf-token=csrf-token; next-auth.callback-url=http://localhost:3000',
        },
      });

      mockGetServerSession.mockResolvedValue(null);
      mockGetToken.mockResolvedValue(null);

      const response = await GET(requestWithCookies);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify cookie detection
      expect(data.tests.cookies.available).toContain('next-auth.session-token');
      expect(data.tests.cookies.available).toContain('next-auth.csrf-token');
      expect(data.tests.cookies.available).toContain('next-auth.callback-url');
      expect(data.tests.cookies.sessionToken).toBe(true);
      expect(data.tests.cookies.csrfToken).toBe(true);
      expect(data.tests.cookies.callbackUrl).toBe(true);
    });

    it('should handle secure cookies', async () => {
      // Create request with secure cookies
      const requestWithSecureCookies = new NextRequest('http://localhost:3000/api/debug/auth-flow', {
        method: 'GET',
        headers: {
          'cookie': '__Secure-next-auth.session-token=secure-token',
        },
      });

      mockGetServerSession.mockResolvedValue(null);
      mockGetToken.mockResolvedValue(null);

      const response = await GET(requestWithSecureCookies);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify secure cookie detection
      expect(data.tests.cookies.sessionTokenSecure).toBe(true);
    });
  });
});
