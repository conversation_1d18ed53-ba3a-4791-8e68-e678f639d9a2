/**
 * API Route Tests for Debug JWT Test Endpoint
 * 
 * Tests the /api/debug/jwt-test route which provides JWT encoding/decoding
 * diagnostics and tests JWT functionality with various token types.
 */

import { decode, encode } from 'next-auth/jwt';

import { GET } from '@/app/api/debug/jwt-test/route';

// Mock next-auth/jwt functions
jest.mock('next-auth/jwt', () => ({
  encode: jest.fn(),
  decode: jest.fn(),
}));

const mockEncode = encode as jest.MockedFunction<typeof encode>;
const mockDecode = decode as jest.MockedFunction<typeof decode>;

describe('/api/debug/jwt-test', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Set up environment variables
    process.env.NEXTAUTH_SECRET = 'test-secret-key-for-jwt-testing';
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.NEXTAUTH_SECRET;
  });

  describe('GET /api/debug/jwt-test', () => {
    it('should return error when NEXTAUTH_SECRET is missing', async () => {
      // Remove NEXTAUTH_SECRET
      delete process.env.NEXTAUTH_SECRET;

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(false);
      expect(data.error).toBe('NEXTAUTH_SECRET not found');
    });

    it('should successfully test JWT encoding and decoding with all token types', async () => {
      // Mock successful encoding and decoding for all token types
      mockEncode
        .mockResolvedValueOnce('encoded-simple-token')  // Simple token
        .mockResolvedValueOnce('encoded-complex-token') // Complex token
        .mockResolvedValueOnce('encoded-large-token');  // Large token

      mockDecode
        .mockResolvedValueOnce({
          sub: 'test-user-123',
          email: '<EMAIL>',
          name: 'Test User',
        })
        .mockResolvedValueOnce({
          sub: 'test-user-123',
          email: '<EMAIL>',
          name: 'Test User',
          roles: ['Admin', 'Super Admin'],
          permissions: ['view_users', 'manage_users', 'view_roles', 'manage_roles', 'view_permissions', 'manage_permissions'],
          groups: [1, 2, 3],
          brands: [1, 2, 3, 4, 5],
          isSuperAdmin: true,
          isImpersonating: false,
        })
        .mockResolvedValueOnce({
          sub: 'test-user-123',
          permissions: Array.from({ length: 50 }, (_, i) => `permission_${i}`),
          groups: Array.from({ length: 20 }, (_, i) => i + 1),
          brands: Array.from({ length: 100 }, (_, i) => i + 1),
        });

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify secret information
      expect(data.tests.secretAvailable).toBe(true);
      expect(data.tests.secretLength).toBe(32); // Length of test secret
      
      // Verify simple token test
      expect(data.tests.simpleToken.encoded).toBe(true);
      expect(data.tests.simpleToken.decoded).toBe(true);
      expect(data.tests.simpleToken.matches).toBe(true);
      
      // Verify complex token test
      expect(data.tests.complexToken.encoded).toBe(true);
      expect(data.tests.complexToken.decoded).toBe(true);
      expect(data.tests.complexToken.matches).toBe(true);
      expect(data.tests.complexToken.rolesMatch).toBe(true);
      
      // Verify large token test
      expect(data.tests.largeToken.encoded).toBe(true);
      expect(data.tests.largeToken.decoded).toBe(true);
      expect(data.tests.largeToken.matches).toBe(true);
      expect(data.tests.largeToken.tokenSize).toBe(18); // Length of 'encoded-large-token'
    });

    it('should handle encoding failures gracefully', async () => {
      // Mock encoding failure
      mockEncode.mockRejectedValueOnce(new Error('Encoding failed'));

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.message).toBe('Encoding failed');
      expect(data.error.name).toBe('Error');
    });

    it('should handle decoding failures gracefully', async () => {
      // Mock successful encoding but failed decoding
      mockEncode.mockResolvedValueOnce('encoded-token');
      mockDecode.mockRejectedValueOnce(new Error('Invalid token format'));

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.message).toBe('Invalid token format');
    });

    it('should detect JWT-specific errors', async () => {
      // Mock JWT-specific error
      mockEncode.mockRejectedValueOnce(new Error('JWT signature verification failed'));

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.message).toBe('JWT signature verification failed');
      expect(data.isJwtError).toBe(true);
    });

    it('should detect cryptographic errors', async () => {
      // Mock cryptographic error
      mockEncode.mockRejectedValueOnce(new Error('Encryption key invalid'));

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.message).toBe('Encryption key invalid');
      expect(data.isCryptographicError).toBe(true);
    });

    it('should handle partial encoding/decoding success', async () => {
      // Mock partial success - simple works, complex fails, large works
      mockEncode
        .mockResolvedValueOnce('encoded-simple-token')
        .mockRejectedValueOnce(new Error('Complex token too large'))
        .mockResolvedValueOnce('encoded-large-token');

      mockDecode
        .mockResolvedValueOnce({ sub: 'test-user-123' })
        .mockResolvedValueOnce({ sub: 'test-user-123' });

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error.message).toBe('Complex token too large');
    });

    it('should validate token data integrity', async () => {
      // Mock encoding and decoding with data mismatch
      mockEncode
        .mockResolvedValueOnce('encoded-simple-token')
        .mockResolvedValueOnce('encoded-complex-token')
        .mockResolvedValueOnce('encoded-large-token');

      mockDecode
        .mockResolvedValueOnce({
          sub: 'different-user-456', // Mismatch
          email: '<EMAIL>',
        })
        .mockResolvedValueOnce({
          sub: 'test-user-123',
          roles: ['User'], // Different roles
        })
        .mockResolvedValueOnce({
          sub: 'test-user-123',
        });

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify data integrity checks
      expect(data.tests.simpleToken.matches).toBe(false); // Subject mismatch
      expect(data.tests.complexToken.matches).toBe(true);
      expect(data.tests.complexToken.rolesMatch).toBe(false); // Roles mismatch
      expect(data.tests.largeToken.matches).toBe(true);
    });

    it('should handle very large tokens correctly', async () => {
      // Mock large token encoding with realistic size
      const largeTokenString = 'x'.repeat(5000); // Simulate large token
      mockEncode
        .mockResolvedValueOnce('encoded-simple-token')
        .mockResolvedValueOnce('encoded-complex-token')
        .mockResolvedValueOnce(largeTokenString);

      mockDecode
        .mockResolvedValueOnce({ sub: 'test-user-123' })
        .mockResolvedValueOnce({ sub: 'test-user-123', roles: ['Admin', 'Super Admin'] })
        .mockResolvedValueOnce({ sub: 'test-user-123' });

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.tests.largeToken.tokenSize).toBe(5000);
    });

    it('should test with different secret formats', async () => {
      // Test with base64-like secret
      process.env.NEXTAUTH_SECRET = '=base64encodedkey';
      
      mockEncode.mockResolvedValueOnce('encoded-token');
      mockDecode.mockResolvedValueOnce({ sub: 'test-user-123' });

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.tests.secretAvailable).toBe(true);
      expect(data.tests.secretLength).toBe(17);
    });

    it('should handle null/undefined decode results', async () => {
      // Mock encoding success but decode returns null
      mockEncode
        .mockResolvedValueOnce('encoded-simple-token')
        .mockResolvedValueOnce('encoded-complex-token')
        .mockResolvedValueOnce('encoded-large-token');

      mockDecode
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce({ sub: 'test-user-123' });

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // Verify handling of null/undefined results
      expect(data.tests.simpleToken.decoded).toBe(false);
      expect(data.tests.simpleToken.matches).toBe(false);
      expect(data.tests.complexToken.decoded).toBe(false);
      expect(data.tests.complexToken.matches).toBe(false);
      expect(data.tests.largeToken.decoded).toBe(true);
      expect(data.tests.largeToken.matches).toBe(true);
    });

    it('should verify encode/decode function calls with correct parameters', async () => {
      mockEncode.mockResolvedValue('encoded-token');
      mockDecode.mockResolvedValue({ sub: 'test-user-123' });

      await GET();

      // Verify encode was called with correct parameters
      expect(mockEncode).toHaveBeenCalledTimes(3);
      expect(mockEncode).toHaveBeenCalledWith({
        secret: 'test-secret-key-for-jwt-testing',
        token: expect.objectContaining({
          sub: 'test-user-123',
          email: '<EMAIL>',
          name: 'Test User',
        })
      });

      // Verify decode was called with correct parameters
      expect(mockDecode).toHaveBeenCalledTimes(3);
      expect(mockDecode).toHaveBeenCalledWith({
        secret: 'test-secret-key-for-jwt-testing',
        token: 'encoded-token'
      });
    });
  });
});