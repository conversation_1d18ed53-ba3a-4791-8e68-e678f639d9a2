/**
 * API Route Tests for Debug Session Endpoint
 * 
 * Tests the /api/debug/session route which provides session debugging information
 * including session data, admin access checks, and user permissions validation.
 */

import { hasAdminAccess, hasSuperAdminAccess } from '@/lib/api/admin-auth';

import { GET } from '@/app/api/debug/session/route';
import { getServerSession } from 'next-auth';

// Mock next-auth functions
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}));

// Mock admin-auth functions
jest.mock('@/lib/api/admin-auth', () => ({
  hasAdminAccess: jest.fn(),
  hasSuperAdminAccess: jest.fn(),
}));

// Mock auth options
jest.mock('@/lib/auth-options', () => ({
  authOptions: {
    providers: [],
    callbacks: {},
  },
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockHasAdminAccess = hasAdminAccess as jest.MockedFunction<typeof hasAdminAccess>;
const mockHasSuperAdminAccess = hasSuperAdminAccess as jest.MockedFunction<typeof hasSuperAdminAccess>;

describe('/api/debug/session', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('GET /api/debug/session', () => {
    it('should return 401 when no session exists', async () => {
      // Mock no session
      mockGetServerSession.mockResolvedValue(null);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('No session found');
      expect(data.authenticated).toBe(false);
    });

    it('should return comprehensive session diagnostics for super admin user', async () => {
      // Mock super admin session
      const mockSession = {
        user: {
          id: 'user-123',
          name: 'Super Admin User',
          email: '<EMAIL>',
          roles: ['Admin', 'Super Admin'],
          permissions: [
            'view_groups', 'manage_groups',
            'view_permissions', 'manage_permissions',
            'view_brands', 'manage_brands',
            'view_users', 'manage_users',
            'view_roles', 'manage_roles'
          ],
          groups: [1, 2],
          brands: [10, 20, 30],
          isSuperAdmin: true,
          isImpersonating: false,
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockReturnValue(true);
      mockHasSuperAdminAccess.mockReturnValue(true);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.authenticated).toBe(true);
      expect(data.sessionExists).toBe(true);
      expect(data.userExists).toBe(true);
      
      // Verify user info
      expect(data.userId).toBe('user-123');
      expect(data.userEmail).toBe('<EMAIL>');
      expect(data.userName).toBe('Super Admin User');
      
      // Verify super admin flags
      expect(data.isSuperAdmin).toBe(true);
      expect(data.expectedSuperAdmin).toBe(true);
      
      // Verify roles and permissions
      expect(data.roles).toEqual(['Admin', 'Super Admin']);
      expect(data.permissions).toEqual([
        'view_groups', 'manage_groups',
        'view_permissions', 'manage_permissions',
        'view_brands', 'manage_brands',
        'view_users', 'manage_users',
        'view_roles', 'manage_roles'
      ]);
      
      // Verify access checks
      expect(data.hasAdminAccess).toBe(true);
      expect(data.hasSuperAdminAccess).toBe(true);
      
      // Verify groups and brands
      expect(data.groups).toEqual([1, 2]);
      expect(data.brands).toEqual([10, 20, 30]);
      
      // Verify impersonation status
      expect(data.isImpersonating).toBe(false);
      expect(data.originalUser).toBeNull();
      expect(data.sessionIsImpersonating).toBe(false);
      expect(data.sessionOriginalUserId).toBeNull();
      
      // Verify validation checks
      expect(data.validationChecks.hasExpectedEmail).toBe(true);
      expect(data.validationChecks.hasAdminRole).toBe(true);
      expect(data.validationChecks.hasSuperAdminRole).toBe(true);
      expect(data.validationChecks.hasViewGroupsPermission).toBe(true);
      expect(data.validationChecks.hasManageGroupsPermission).toBe(true);
      expect(data.validationChecks.hasViewPermissionsPermission).toBe(true);
      expect(data.validationChecks.hasManagePermissionsPermission).toBe(true);
      expect(data.validationChecks.hasViewBrandsPermission).toBe(true);
      expect(data.validationChecks.hasManageBrandsPermission).toBe(true);
    });

    it('should return diagnostics for regular user without admin access', async () => {
      // Mock regular user session
      const mockSession = {
        user: {
          id: 'user-456',
          name: 'Regular User',
          email: '<EMAIL>',
          roles: ['User'],
          permissions: ['view_dashboard'],
          groups: [1],
          brands: [10],
          isSuperAdmin: false,
          isImpersonating: false,
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockReturnValue(false);
      mockHasSuperAdminAccess.mockReturnValue(false);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.authenticated).toBe(true);
      
      // Verify user info
      expect(data.userId).toBe('user-456');
      expect(data.userEmail).toBe('<EMAIL>');
      expect(data.userName).toBe('Regular User');
      
      // Verify super admin flags
      expect(data.isSuperAdmin).toBe(false);
      expect(data.expectedSuperAdmin).toBe(false);
      
      // Verify roles and permissions
      expect(data.roles).toEqual(['User']);
      expect(data.permissions).toEqual(['view_dashboard']);
      
      // Verify access checks
      expect(data.hasAdminAccess).toBe(false);
      expect(data.hasSuperAdminAccess).toBe(false);
      
      // Verify validation checks
      expect(data.validationChecks.hasExpectedEmail).toBe(false);
      expect(data.validationChecks.hasAdminRole).toBe(false);
      expect(data.validationChecks.hasSuperAdminRole).toBe(false);
      expect(data.validationChecks.hasViewGroupsPermission).toBe(false);
      expect(data.validationChecks.hasManageGroupsPermission).toBe(false);
    });

    it('should handle impersonating user correctly', async () => {
      // Mock impersonating session
      const mockSession = {
        user: {
          id: 'impersonated-user-789',
          name: 'Impersonated User',
          email: '<EMAIL>',
          roles: ['User'],
          permissions: ['view_dashboard'],
          groups: [1],
          brands: [10],
          isSuperAdmin: false,
          isImpersonating: true,
          originalUser: {
            id: 'admin-123',
            name: 'Admin User',
            email: '<EMAIL>',
            roles: ['Admin', 'Super Admin'],
            permissions: ['view_groups', 'manage_groups'],
          },
        },
        isImpersonating: true,
        originalUserId: 'admin-123',
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockReturnValue(true); // Admin access through original user
      mockHasSuperAdminAccess.mockReturnValue(true);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.authenticated).toBe(true);
      
      // Verify impersonation status
      expect(data.isImpersonating).toBe(true);
      expect(data.originalUser).toEqual({
        id: 'admin-123',
        name: 'Admin User',
        email: '<EMAIL>',
        roles: ['Admin', 'Super Admin'],
        permissions: ['view_groups', 'manage_groups'],
      });
      expect(data.sessionIsImpersonating).toBe(true);
      expect(data.sessionOriginalUserId).toBe('admin-123');
      
      // Verify access checks (should have admin access through original user)
      expect(data.hasAdminAccess).toBe(true);
      expect(data.hasSuperAdminAccess).toBe(true);
    });

    it('should handle user with minimal data', async () => {
      // Mock session with minimal user data
      const mockSession = {
        user: {
          id: 'user-minimal',
          email: '<EMAIL>',
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockReturnValue(false);
      mockHasSuperAdminAccess.mockReturnValue(false);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.authenticated).toBe(true);
      
      // Verify user info
      expect(data.userId).toBe('user-minimal');
      expect(data.userEmail).toBe('<EMAIL>');
      expect(data.userName).toBeUndefined();
      
      // Verify defaults for missing data
      expect(data.roles).toEqual([]);
      expect(data.permissions).toEqual([]);
      expect(data.groups).toEqual([]);
      expect(data.brands).toEqual([]);
      expect(data.isSuperAdmin).toBeUndefined();
      expect(data.isImpersonating).toBe(false);
      
      // Verify access checks
      expect(data.hasAdminAccess).toBe(false);
      expect(data.hasSuperAdminAccess).toBe(false);
    });

    it('should handle session retrieval errors', async () => {
      // Mock session error
      mockGetServerSession.mockRejectedValue(new Error('Database connection failed'));

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to analyze session');
      expect(data.details).toBe('Database connection failed');
    });

    it('should handle admin access check errors', async () => {
      // Mock valid session but admin access error
      const mockSession = {
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          roles: ['User'],
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockImplementation(() => {
        throw new Error('Admin access check failed');
      });

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to analyze session');
      expect(data.details).toBe('Admin access check failed');
    });

    it('should validate all expected permissions correctly', async () => {
      // Mock session with all expected permissions
      const mockSession = {
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          roles: ['Admin', 'Super Admin'],
          permissions: [
            'view_groups', 'manage_groups',
            'view_permissions', 'manage_permissions',
            'view_brands', 'manage_brands',
            'view_users', 'manage_users',
            'view_roles', 'manage_roles'
          ],
          isSuperAdmin: true,
        },
      };
      mockGetServerSession.mockResolvedValue(mockSession);
      mockHasAdminAccess.mockReturnValue(true);
      mockHasSuperAdminAccess.mockReturnValue(true);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      
      // Verify all expected permissions are present
      expect(data.permissions).toContain('view_groups');
      expect(data.permissions).toContain('manage_groups');
      expect(data.permissions).toContain('view_permissions');
      expect(data.permissions).toContain('manage_permissions');
      expect(data.permissions).toContain('view_brands');
      expect(data.permissions).toContain('manage_brands');
      expect(data.permissions).toContain('view_users');
      expect(data.permissions).toContain('manage_users');
      expect(data.permissions).toContain('view_roles');
      expect(data.permissions).toContain('manage_roles');
      
      // Verify expected permissions array
      expect(data.expectedPermissions).toEqual([
        'view_groups', 'manage_groups',
        'view_permissions', 'manage_permissions',
        'view_brands', 'manage_brands',
        'view_users', 'manage_users',
        'view_roles', 'manage_roles'
      ]);
    });
  });
});