import { test, expect } from '@playwright/test';
import {
  waitForPageLoad,
  signInAsAdmin,
  takeScreenshot,
  getMainContent
} from './utils/test-helpers';

test.describe('Simple Authentication Test', () => {
  test('should sign in with farbour/admin credentials', async ({ page }) => {
    console.log('🔐 Testing authentication with farbour/admin...');
    
    try {
      // Sign in as admin
      await signInAsAdmin(page);
      
      // Verify we're on the dashboard
      expect(page.url()).toContain('/dashboard');
      
      // Verify main content is visible
      const mainContent = await getMainContent(page);
      await expect(mainContent).toBeVisible();
      
      console.log('✅ Authentication successful!');
      await takeScreenshot(page, 'auth-success');
      
    } catch (error) {
      console.log('❌ Authentication failed:', error);
      await takeScreenshot(page, 'auth-failed');
      throw error;
    }
  });

  test('should navigate to admin dashboard', async ({ page }) => {
    console.log('🔐 Testing admin access...');
    
    try {
      // Sign in as admin
      await signInAsAdmin(page);
      
      // Navigate to admin
      await page.goto('/admin');
      await waitForPageLoad(page);
      
      // Verify we're on admin page
      expect(page.url()).toContain('/admin');
      expect(page.url()).not.toContain('/auth/signin');
      
      // Verify main content is visible
      const mainContent = await getMainContent(page);
      await expect(mainContent).toBeVisible();
      
      console.log('✅ Admin access successful!');
      await takeScreenshot(page, 'admin-access-success');
      
    } catch (error) {
      console.log('❌ Admin access failed:', error);
      await takeScreenshot(page, 'admin-access-failed');
      throw error;
    }
  });
});
