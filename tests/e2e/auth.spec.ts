import { test, expect } from '@playwright/test';
import {
  waitForPageLoad,
  signInWithGoogle,
  signInWithCredentials,
  signInAsAdmin,
  signOut,
  isAuthenticated,
  verifyAdminAccess,
  navigateToAdminDashboard,
  takeScreenshot
} from './utils/test-helpers';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Start each test from the home page
    await page.goto('/');
  });

  test('should redirect unauthenticated users to sign-in page', async ({ page }) => {
    // When visiting the root page without authentication
    await page.goto('/');

    // Should be redirected to sign-in page
    await page.waitForURL('**/auth/signin');
    await waitForPageLoad(page);

    // Verify sign-in page elements
    await expect(page.locator('h1')).toContainText('NOLK');
    await expect(page.locator('h2')).toContainText('Sign in to your account');
    await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();

    // Take screenshot for verification
    await takeScreenshot(page, 'sign-in-page');
  });

  test('should display sign-in page correctly', async ({ page }) => {
    await page.goto('/auth/signin');
    await waitForPageLoad(page);

    // Check page title
    await expect(page).toHaveTitle(/NOLK|Create Next App/);

    // Check main elements
    await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
    await expect(page.locator('h2:has-text("Sign in to your account")')).toBeVisible();
    await expect(page.locator('p:has-text("Please sign in with your Google account")')).toBeVisible();

    // Check Google sign-in button
    const googleButton = page.locator('button:has-text("Continue with Google")');
    await expect(googleButton).toBeVisible();
    await expect(googleButton).toBeEnabled();

    // Check NOLK logo
    await expect(page.locator('svg')).toBeVisible();
  });

  test('should handle sign-in button click', async ({ page }) => {
    await page.goto('/auth/signin');
    await waitForPageLoad(page);

    // Click the Google sign-in button
    const googleButton = page.locator('button:has-text("Continue with Google")');
    await googleButton.click();

    // In a real environment, this would redirect to Google OAuth
    // For testing, we'll check that the button responds to clicks
    await expect(googleButton).toHaveClass(/.*loading.*|.*disabled.*/);
  });

  test('should redirect authenticated users away from sign-in page', async ({ page }) => {
    // Mock authentication
    await page.addInitScript(() => {
      // Mock NextAuth session
      window.localStorage.setItem('nextauth.session-token', 'mock-token');
    });

    await page.goto('/auth/signin');

    // Should be redirected away from sign-in page
    // Note: This test might need adjustment based on actual auth implementation
    await page.waitForTimeout(2000);

    // Check if we're redirected (URL should not contain signin)
    const currentUrl = page.url();
    if (!currentUrl.includes('/auth/signin')) {
      expect(currentUrl).toContain('/dashboard');
    }
  });

  test('should handle authentication errors gracefully', async ({ page }) => {
    await page.goto('/auth/signin?error=OAuthSignin');
    await waitForPageLoad(page);

    // Should still show the sign-in page
    await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
    await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();

    // Could check for error message if implemented
    // await expect(page.locator('.error-message')).toBeVisible();
  });

  test('should handle callback URL parameter', async ({ page }) => {
    const callbackUrl = encodeURIComponent('/brand-deep-dive');
    await page.goto(`/auth/signin?callbackUrl=${callbackUrl}`);
    await waitForPageLoad(page);

    // Should show sign-in page with callback URL preserved
    await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();

    // URL should contain the callback parameter
    expect(page.url()).toContain(`callbackUrl=${callbackUrl}`);
  });

  test('should be responsive on different screen sizes', async ({ page }) => {
    await page.goto('/auth/signin');
    await waitForPageLoad(page);

    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);

    // Elements should still be visible and properly arranged
    await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
    await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();

    await takeScreenshot(page, 'sign-in-mobile');

    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500);

    await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
    await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();

    await takeScreenshot(page, 'sign-in-tablet');

    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(500);

    await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
    await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();

    await takeScreenshot(page, 'sign-in-desktop');
  });

  test('should not have console errors on sign-in page', async ({ page }) => {
    const consoleErrors: string[] = [];

    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    await page.goto('/auth/signin');
    await waitForPageLoad(page);

    // Filter out known acceptable errors (if any)
    const criticalErrors = consoleErrors.filter(error =>
      !error.includes('favicon') &&
      !error.includes('404') &&
      !error.includes('net::ERR_FAILED')
    );

    expect(criticalErrors).toHaveLength(0);
  });
});

test.describe('Credentials Authentication', () => {
  test.beforeEach(async ({ page }) => {
    // Ensure we start from a clean state
    await page.goto('/auth/signin');
    await waitForPageLoad(page);
  });

  test('should successfully sign in with farbour/admin credentials', async ({ page }) => {
    // Take screenshot of sign-in page
    await takeScreenshot(page, 'signin-page-before-credentials');

    // Sign in with admin credentials
    await signInAsAdmin(page);

    // Verify we're on the dashboard
    expect(page.url()).toContain('/dashboard');

    // Verify dashboard elements are present
    const dashboardHeading = page.locator('h1, h2, [data-testid="dashboard-title"]');
    await expect(dashboardHeading).toBeVisible({ timeout: 10000 });

    // Take screenshot of successful login
    await takeScreenshot(page, 'successful-admin-login');
  });

  test('should reject invalid credentials', async ({ page }) => {
    // Navigate to sign-in page
    await page.goto('/auth/signin');
    await waitForPageLoad(page);

    // Switch to credentials tab if available
    const credentialsTab = page.locator('button:has-text("Username/Password")');
    if (await credentialsTab.isVisible()) {
      await credentialsTab.click();
      await page.waitForTimeout(500);
    }

    // Fill in invalid credentials
    await page.fill('input[name="username"]', 'invalid');
    await page.fill('input[name="password"]', 'wrongpassword');

    // Click sign in button
    await page.click('button[type="submit"]:has-text("Sign In")');

    // Wait a moment for the response
    await page.waitForTimeout(2000);

    // Verify we're still on sign-in page
    expect(page.url()).toContain('/auth/signin');

    // Look for error message
    const errorMessage = page.locator('text=/invalid|error|failed|incorrect/i');
    if (await errorMessage.isVisible()) {
      await takeScreenshot(page, 'invalid-credentials-error');
    }
  });

  test('should verify admin access after login', async ({ page }) => {
    // Sign in as admin
    await signInAsAdmin(page);

    // Verify admin access
    const hasAdminAccess = await verifyAdminAccess(page);
    expect(hasAdminAccess).toBe(true);

    // Navigate to admin dashboard
    await navigateToAdminDashboard(page);

    // Take screenshot of admin dashboard
    await takeScreenshot(page, 'admin-dashboard-access');
  });

  test('should maintain session across page navigation', async ({ page }) => {
    // Sign in as admin
    await signInAsAdmin(page);

    // Navigate to different pages and verify we stay authenticated
    const pages = ['/dashboard', '/marketing', '/admin'];

    for (const pagePath of pages) {
      await page.goto(pagePath);
      await waitForPageLoad(page);

      // Verify we're not redirected to sign-in
      expect(page.url()).not.toContain('/auth/signin');
      expect(page.url()).toContain(pagePath);

      // Take screenshot
      const pageName = pagePath.replace('/', '') || 'root';
      await takeScreenshot(page, `authenticated-${pageName}`);
    }
  });

  test('should successfully sign out', async ({ page }) => {
    // Sign in as admin
    await signInAsAdmin(page);

    // Sign out
    await signOut(page);

    // Verify we're redirected to sign-in page
    await page.waitForURL('**/auth/signin', { timeout: 10000 });

    // Verify sign-in page elements are visible
    await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();

    // Take screenshot of sign-out state
    await takeScreenshot(page, 'after-signout');
  });
});
