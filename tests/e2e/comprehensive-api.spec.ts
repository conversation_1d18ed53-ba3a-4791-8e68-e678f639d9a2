import { test, expect } from '@playwright/test';
import {
  signInAsAdmin,
  testApiEndpoint
} from './utils/test-helpers';

test.describe('Comprehensive API Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Sign in as admin for all tests to get proper session
    await signInAsAdmin(page);
  });

  test('should test dashboard API endpoints', async ({ page }) => {
    const dashboardEndpoints = [
      { path: '/api/dashboard/flexible-kpis', expectedStatus: 200 },
      { path: '/api/dashboard/kpi-data', expectedStatus: 200 },
      { path: '/api/dashboard/chart-data', expectedStatus: 200 }
    ];

    for (const endpoint of dashboardEndpoints) {
      try {
        const response = await page.request.get(endpoint.path);
        console.log(`${endpoint.path}: ${response.status()}`);
        
        if (response.status() === 200) {
          const data = await response.json();
          console.log(`${endpoint.path} returned data:`, Object.keys(data));
        }
      } catch (error) {
        console.log(`${endpoint.path}: Failed - ${error}`);
      }
    }
  });

  test('should test marketing API endpoints', async ({ page }) => {
    const marketingEndpoints = [
      { path: '/api/marketing/campaigns', expectedStatus: 200 },
      { path: '/api/marketing/kpis', expectedStatus: 200 },
      { path: '/api/marketing/spend-breakdown', expectedStatus: 200 }
    ];

    for (const endpoint of marketingEndpoints) {
      try {
        const response = await page.request.get(endpoint.path);
        console.log(`${endpoint.path}: ${response.status()}`);
        
        if (response.status() === 200) {
          const data = await response.json();
          console.log(`${endpoint.path} returned data:`, Object.keys(data));
        }
      } catch (error) {
        console.log(`${endpoint.path}: Failed - ${error}`);
      }
    }
  });

  test('should test admin API endpoints with proper authorization', async ({ page }) => {
    const adminEndpoints = [
      { path: '/api/admin/users', expectedStatus: 200 },
      { path: '/api/admin/roles', expectedStatus: 200 },
      { path: '/api/admin/permissions', expectedStatus: 200 },
      { path: '/api/admin/groups', expectedStatus: 200 },
      { path: '/api/admin/brands', expectedStatus: 200 },
      { path: '/api/admin/settings', expectedStatus: 200 },
      { path: '/api/admin/backups', expectedStatus: 200 },
      { path: '/api/admin/db-structure', expectedStatus: 200 }
    ];

    for (const endpoint of adminEndpoints) {
      try {
        const response = await page.request.get(endpoint.path);
        console.log(`${endpoint.path}: ${response.status()}`);
        
        // Admin endpoints should return 200 for authorized users
        if (response.status() === 200) {
          const data = await response.json();
          console.log(`${endpoint.path} returned data:`, Object.keys(data));
        } else if (response.status() === 401) {
          console.log(`${endpoint.path}: Unauthorized (expected for non-admin users)`);
        }
      } catch (error) {
        console.log(`${endpoint.path}: Failed - ${error}`);
      }
    }
  });

  test('should test budget API endpoint', async ({ page }) => {
    try {
      const response = await page.request.get('/api/budget');
      console.log(`Budget API: ${response.status()}`);
      
      if (response.status() === 200) {
        const data = await response.json();
        console.log('Budget API returned data:', Object.keys(data));
      }
    } catch (error) {
      console.log(`Budget API failed: ${error}`);
    }
  });

  test('should test AI assistant API endpoint', async ({ page }) => {
    try {
      // Test GET request
      const getResponse = await page.request.get('/api/ai-assistant');
      console.log(`AI Assistant GET: ${getResponse.status()}`);
      
      // Test POST request with sample data
      const postResponse = await page.request.post('/api/ai-assistant', {
        data: {
          message: 'What is the total revenue?',
          context: 'dashboard'
        }
      });
      console.log(`AI Assistant POST: ${postResponse.status()}`);
      
      if (postResponse.status() === 200) {
        const data = await postResponse.json();
        console.log('AI Assistant response:', data);
      }
    } catch (error) {
      console.log(`AI Assistant API failed: ${error}`);
    }
  });

  test('should test API error handling', async ({ page }) => {
    // Test non-existent endpoints
    const invalidEndpoints = [
      '/api/nonexistent',
      '/api/dashboard/invalid',
      '/api/admin/invalid'
    ];

    for (const endpoint of invalidEndpoints) {
      try {
        const response = await page.request.get(endpoint);
        console.log(`${endpoint}: ${response.status()}`);
        
        // Should return 404 for non-existent endpoints
        expect([404, 405]).toContain(response.status());
      } catch (error) {
        console.log(`${endpoint}: Failed as expected - ${error}`);
      }
    }
  });

  test('should test API with query parameters', async ({ page }) => {
    const endpointsWithParams = [
      '/api/dashboard/flexible-kpis?startDate=2024-01-01&endDate=2024-12-31',
      '/api/marketing/campaigns?brand=test&currency=USD',
      '/api/budget?brand=test&brands=true'
    ];

    for (const endpoint of endpointsWithParams) {
      try {
        const response = await page.request.get(endpoint);
        console.log(`${endpoint}: ${response.status()}`);
        
        if (response.status() === 200) {
          const data = await response.json();
          console.log(`${endpoint} with params returned:`, Object.keys(data));
        }
      } catch (error) {
        console.log(`${endpoint}: Failed - ${error}`);
      }
    }
  });

  test('should test API response times', async ({ page }) => {
    const performanceEndpoints = [
      '/api/dashboard/flexible-kpis',
      '/api/marketing/campaigns',
      '/api/budget'
    ];

    for (const endpoint of performanceEndpoints) {
      const startTime = Date.now();
      
      try {
        const response = await page.request.get(endpoint);
        const responseTime = Date.now() - startTime;
        
        console.log(`${endpoint}: ${response.status()} in ${responseTime}ms`);
        
        // API should respond within 5 seconds
        expect(responseTime).toBeLessThan(5000);
      } catch (error) {
        console.log(`${endpoint}: Failed - ${error}`);
      }
    }
  });

  test('should test API data validation', async ({ page }) => {
    // Test dashboard API data structure
    try {
      const response = await page.request.get('/api/dashboard/flexible-kpis');
      
      if (response.status() === 200) {
        const data = await response.json();
        
        // Validate expected data structure
        expect(data).toBeDefined();
        
        if (data.kpis) {
          expect(Array.isArray(data.kpis)).toBe(true);
          console.log(`Dashboard API returned ${data.kpis.length} KPIs`);
        }
        
        if (data.data) {
          expect(Array.isArray(data.data)).toBe(true);
          console.log(`Dashboard API returned ${data.data.length} data points`);
        }
      }
    } catch (error) {
      console.log(`Dashboard API validation failed: ${error}`);
    }
  });

  test('should test API authentication requirements', async ({ page }) => {
    // Create a new page without authentication
    const unauthenticatedPage = await page.context().newPage();
    
    const protectedEndpoints = [
      '/api/admin/users',
      '/api/admin/roles',
      '/api/dashboard/flexible-kpis'
    ];

    for (const endpoint of protectedEndpoints) {
      try {
        const response = await unauthenticatedPage.request.get(endpoint);
        console.log(`${endpoint} (unauthenticated): ${response.status()}`);
        
        // Should return 401 or redirect for protected endpoints
        expect([401, 302, 403]).toContain(response.status());
      } catch (error) {
        console.log(`${endpoint} (unauthenticated): Failed as expected - ${error}`);
      }
    }
    
    await unauthenticatedPage.close();
  });

  test('should test API CORS headers', async ({ page }) => {
    try {
      const response = await page.request.get('/api/dashboard/flexible-kpis');
      
      if (response.status() === 200) {
        const headers = response.headers();
        
        console.log('API Response Headers:');
        console.log('Content-Type:', headers['content-type']);
        console.log('Cache-Control:', headers['cache-control']);
        
        // Verify JSON content type
        expect(headers['content-type']).toContain('application/json');
      }
    } catch (error) {
      console.log(`API headers test failed: ${error}`);
    }
  });

  test('should test API rate limiting (if implemented)', async ({ page }) => {
    const endpoint = '/api/dashboard/flexible-kpis';
    const requests = [];
    
    // Make multiple rapid requests
    for (let i = 0; i < 10; i++) {
      requests.push(page.request.get(endpoint));
    }
    
    try {
      const responses = await Promise.all(requests);
      
      let successCount = 0;
      let rateLimitedCount = 0;
      
      responses.forEach((response, index) => {
        console.log(`Request ${index + 1}: ${response.status()}`);
        
        if (response.status() === 200) {
          successCount++;
        } else if (response.status() === 429) {
          rateLimitedCount++;
        }
      });
      
      console.log(`Successful requests: ${successCount}, Rate limited: ${rateLimitedCount}`);
    } catch (error) {
      console.log(`Rate limiting test failed: ${error}`);
    }
  });

  test('should test API monitoring endpoints', async ({ page }) => {
    const monitoringEndpoints = [
      '/api/monitoring/health',
      '/api/monitoring/status'
    ];

    for (const endpoint of monitoringEndpoints) {
      try {
        const response = await page.request.get(endpoint);
        console.log(`${endpoint}: ${response.status()}`);
        
        if (response.status() === 200) {
          const data = await response.json();
          console.log(`${endpoint} health check:`, data);
        }
      } catch (error) {
        console.log(`${endpoint}: Failed - ${error}`);
      }
    }
  });
});
