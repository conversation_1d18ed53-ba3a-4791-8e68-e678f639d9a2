import { test, expect } from '@playwright/test';
import {
  waitForPageLoad,
  signInAsAdmin,
  takeScreenshot,
  waitForLoadingToComplete,
  testAccessibility
} from './utils/test-helpers';

test.describe('Comprehensive Performance Testing', () => {
  test.beforeEach(async ({ page }) => {
    // Sign in as admin for all tests
    await signInAsAdmin(page);
  });

  test('should measure page load performance across all main pages', async ({ page }) => {
    const pages = [
      { name: 'Dashboard', url: '/dashboard', maxLoadTime: 8000 },
      { name: 'Brand Deep Dive', url: '/brand-deep-dive', maxLoadTime: 12000 },
      { name: 'Marketing Dashboard', url: '/marketing-dashboard', maxLoadTime: 15000 },
      { name: 'Executive Summary', url: '/executive-summary', maxLoadTime: 10000 },
      { name: 'Budget', url: '/budget', maxLoadTime: 8000 },
      { name: 'AI Assistant', url: '/ai-assistant', maxLoadTime: 6000 },
      { name: 'Admin Dashboard', url: '/admin', maxLoadTime: 8000 }
    ];

    const performanceResults = [];

    for (const pageInfo of pages) {
      const startTime = Date.now();

      try {
        await page.goto(pageInfo.url);
        await waitForPageLoad(page);
        await waitForLoadingToComplete(page);

        const loadTime = Date.now() - startTime;

        performanceResults.push({
          page: pageInfo.name,
          url: pageInfo.url,
          loadTime,
          maxAllowed: pageInfo.maxLoadTime,
          passed: loadTime < pageInfo.maxLoadTime
        });

        console.log(`${pageInfo.name}: ${loadTime}ms (max: ${pageInfo.maxLoadTime}ms)`);

        // Verify page loaded within threshold
        expect(loadTime).toBeLessThan(pageInfo.maxLoadTime);

      } catch (error) {
        console.log(`${pageInfo.name}: Failed to load - ${error}`);
        performanceResults.push({
          page: pageInfo.name,
          url: pageInfo.url,
          loadTime: -1,
          maxAllowed: pageInfo.maxLoadTime,
          passed: false,
          error: error.toString()
        });
      }
    }

    // Log performance summary
    console.log('\n=== Performance Summary ===');
    performanceResults.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.page}: ${result.loadTime}ms`);
    });
  });

  test('should measure Time to First Contentful Paint (FCP)', async ({ page }) => {
    const pages = ['/dashboard', '/marketing-dashboard', '/brand-deep-dive'];

    for (const url of pages) {
      await page.goto(url);

      // Measure FCP using Performance API
      const fcpTime = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
            if (fcpEntry) {
              resolve(fcpEntry.startTime);
            }
          }).observe({ entryTypes: ['paint'] });

          // Fallback timeout
          setTimeout(() => resolve(-1), 5000);
        });
      });

      if (fcpTime > 0) {
        console.log(`${url} FCP: ${fcpTime.toFixed(2)}ms`);
        // FCP should be under 2.5 seconds for good performance
        expect(fcpTime).toBeLessThan(2500);
      }
    }
  });

  test('should measure Largest Contentful Paint (LCP)', async ({ page }) => {
    const pages = ['/dashboard', '/marketing-dashboard'];

    for (const url of pages) {
      await page.goto(url);
      await waitForPageLoad(page);

      // Measure LCP using Performance API
      const lcpTime = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            resolve(lastEntry.startTime);
          }).observe({ entryTypes: ['largest-contentful-paint'] });

          // Fallback timeout
          setTimeout(() => resolve(-1), 10000);
        });
      });

      if (lcpTime > 0) {
        console.log(`${url} LCP: ${lcpTime.toFixed(2)}ms`);
        // LCP should be under 4 seconds for good performance
        expect(lcpTime).toBeLessThan(4000);
      }
    }
  });

  test('should measure JavaScript bundle sizes', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);

    // Get resource sizes
    const resourceSizes = await page.evaluate(() => {
      const resources = performance.getEntriesByType('resource');
      const jsResources = resources.filter(resource =>
        resource.name.includes('.js') && resource.transferSize
      );

      return jsResources.map(resource => ({
        name: resource.name.split('/').pop(),
        size: resource.transferSize,
        decodedSize: resource.decodedBodySize
      }));
    });

    let totalJSSize = 0;
    resourceSizes.forEach(resource => {
      totalJSSize += resource.size;
      console.log(`JS Bundle: ${resource.name} - ${(resource.size / 1024).toFixed(2)}KB`);
    });

    console.log(`Total JS Bundle Size: ${(totalJSSize / 1024).toFixed(2)}KB`);

    // Total JS should be under 2MB for good performance
    expect(totalJSSize).toBeLessThan(2 * 1024 * 1024);
  });

  test('should test memory usage during navigation', async ({ page }) => {
    const pages = ['/dashboard', '/brand-deep-dive', '/marketing-dashboard', '/executive-summary'];
    const memoryUsage = [];

    for (const url of pages) {
      await page.goto(url);
      await waitForPageLoad(page);
      await waitForLoadingToComplete(page);

      // Get memory usage
      const memory = await page.evaluate(() => {
        if ('memory' in performance) {
          return {
            usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
            totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
            jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
          };
        }
        return null;
      });

      if (memory) {
        memoryUsage.push({
          page: url,
          usedMB: (memory.usedJSHeapSize / 1024 / 1024).toFixed(2),
          totalMB: (memory.totalJSHeapSize / 1024 / 1024).toFixed(2)
        });

        console.log(`${url} Memory: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB used`);
      }
    }

    // Log memory usage summary
    console.log('\n=== Memory Usage Summary ===');
    memoryUsage.forEach(usage => {
      console.log(`${usage.page}: ${usage.usedMB}MB used, ${usage.totalMB}MB total`);
    });
  });

  test('should test network performance and resource loading', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);

    // Get network timing information
    const networkTiming = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const resources = performance.getEntriesByType('resource');

      return {
        navigation: {
          domainLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
          connect: navigation.connectEnd - navigation.connectStart,
          request: navigation.responseStart - navigation.requestStart,
          response: navigation.responseEnd - navigation.responseStart,
          domProcessing: navigation.domContentLoadedEventStart - navigation.responseEnd
        },
        resourceCount: resources.length,
        slowResources: resources.filter(r => r.duration > 1000).length
      };
    });

    console.log('Network Performance:');
    console.log(`Domain Lookup: ${networkTiming.navigation.domainLookup.toFixed(2)}ms`);
    console.log(`Connection: ${networkTiming.navigation.connect.toFixed(2)}ms`);
    console.log(`Request: ${networkTiming.navigation.request.toFixed(2)}ms`);
    console.log(`Response: ${networkTiming.navigation.response.toFixed(2)}ms`);
    console.log(`DOM Processing: ${networkTiming.navigation.domProcessing.toFixed(2)}ms`);
    console.log(`Total Resources: ${networkTiming.resourceCount}`);
    console.log(`Slow Resources (>1s): ${networkTiming.slowResources}`);

    // Verify reasonable network performance
    expect(networkTiming.navigation.domainLookup).toBeLessThan(500);
    expect(networkTiming.navigation.connect).toBeLessThan(1000);
    expect(networkTiming.slowResources).toBeLessThan(5);
  });

  test('should test performance under different network conditions', async ({ page }) => {
    // Test with slow 3G network simulation
    await page.context().route('**/*', async (route) => {
      // Add artificial delay to simulate slow network
      await new Promise(resolve => setTimeout(resolve, 100));
      await route.continue();
    });

    const startTime = Date.now();
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    const loadTimeSlowNetwork = Date.now() - startTime;

    console.log(`Dashboard load time with slow network: ${loadTimeSlowNetwork}ms`);

    // Should still load within reasonable time even on slow network
    expect(loadTimeSlowNetwork).toBeLessThan(20000);
  });

  test('should test performance with large datasets', async ({ page }) => {
    // Navigate to data-heavy pages
    const dataHeavyPages = [
      '/marketing-dashboard',
      '/brand-deep-dive'
    ];

    for (const url of dataHeavyPages) {
      const startTime = Date.now();

      await page.goto(url);
      await waitForPageLoad(page);
      await waitForLoadingToComplete(page);

      // Wait for charts and tables to render
      await page.waitForTimeout(3000);

      const loadTime = Date.now() - startTime;
      console.log(`${url} with data rendering: ${loadTime}ms`);

      // Data-heavy pages should load within 20 seconds
      expect(loadTime).toBeLessThan(20000);
    }
  });

  test('should test scroll performance on long pages', async ({ page }) => {
    await page.goto('/marketing-dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);

    // Measure scroll performance
    const scrollStartTime = Date.now();

    // Scroll to bottom of page
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });

    await page.waitForTimeout(1000);

    // Scroll back to top
    await page.evaluate(() => {
      window.scrollTo(0, 0);
    });

    const scrollTime = Date.now() - scrollStartTime;
    console.log(`Scroll performance: ${scrollTime}ms`);

    // Scrolling should be smooth and fast
    expect(scrollTime).toBeLessThan(2000);
  });

  test('should test chart rendering performance', async ({ page }) => {
    await page.goto('/dashboard');
    await waitForPageLoad(page);

    // Measure chart rendering time
    const chartRenderTime = await page.evaluate(() => {
      const startTime = Date.now();

      return new Promise((resolve) => {
        const checkCharts = () => {
          const charts = document.querySelectorAll('svg, canvas, .recharts-wrapper');
          if (charts.length > 0) {
            resolve(Date.now() - startTime);
          } else {
            setTimeout(checkCharts, 100);
          }
        };

        checkCharts();

        // Timeout after 10 seconds
        setTimeout(() => resolve(-1), 10000);
      });
    });

    if (chartRenderTime > 0) {
      console.log(`Chart rendering time: ${chartRenderTime}ms`);
      // Charts should render within 5 seconds
      expect(chartRenderTime).toBeLessThan(5000);
    }
  });

  test('should test accessibility performance', async ({ page }) => {
    const pages = ['/dashboard', '/marketing-dashboard', '/admin'];

    for (const url of pages) {
      const startTime = Date.now();

      await page.goto(url);
      await waitForPageLoad(page);

      const accessibilityInfo = await testAccessibility(page);
      const accessibilityTime = Date.now() - startTime;

      console.log(`${url} accessibility scan: ${accessibilityTime}ms`);
      console.log(`Accessibility elements found:`, accessibilityInfo);

      // Accessibility scan should complete quickly
      expect(accessibilityTime).toBeLessThan(3000);

      // Verify minimum accessibility requirements
      expect(accessibilityInfo.headings).toBeGreaterThan(0);
      // Log landmarks but don't require them
      console.log(`${url} landmarks: ${accessibilityInfo.landmarks}`);
    }
  });
});
