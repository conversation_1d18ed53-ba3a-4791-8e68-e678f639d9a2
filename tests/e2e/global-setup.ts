import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E Test Global Setup...');
  
  // Start browser for setup
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto(config.projects[0].use.baseURL || 'http://localhost:6699');
    
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
    
    // Check if the application is responding
    const title = await page.title();
    console.log(`✅ Application is ready. Page title: ${title}`);
    
    // You can add more setup logic here, such as:
    // - Database seeding
    // - Creating test users
    // - Setting up test data
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }
  
  console.log('✅ E2E Test Global Setup completed successfully');
}

export default globalSetup;
