import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E Test Global Teardown...');
  
  try {
    // Add any cleanup logic here, such as:
    // - Cleaning up test data
    // - Resetting database state
    // - Cleaning up uploaded files
    // - Stopping external services
    
    console.log('✅ E2E Test Global Teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw here to avoid masking test failures
  }
}

export default globalTeardown;
