import { test, expect } from '@playwright/test';

test.describe('Smoke Tests - Basic Application Health', () => {
  test('application should be running and accessible', async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Check that we get a response (not a connection error)
    const title = await page.title();
    expect(title).toBeTruthy();
    
    // Check that the page has some content
    const bodyText = await page.locator('body').textContent();
    expect(bodyText).toBeTruthy();
    expect(bodyText!.length).toBeGreaterThan(0);
    
    console.log(`✅ Application is accessible. Page title: "${title}"`);
  });

  test('should handle basic navigation', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Try to navigate to sign-in page
    await page.goto('/auth/signin');
    await page.waitForLoadState('networkidle');
    
    // Should load without errors
    const currentUrl = page.url();
    expect(currentUrl).toContain('/auth/signin');
    
    console.log(`✅ Navigation works. Current URL: ${currentUrl}`);
  });

  test('should not have critical JavaScript errors', async ({ page }) => {
    const jsErrors: string[] = [];
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        jsErrors.push(msg.text());
      }
    });
    
    page.on('pageerror', (error) => {
      jsErrors.push(`Page Error: ${error.message}`);
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Filter out non-critical errors
    const criticalErrors = jsErrors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('net::ERR_FAILED') &&
      !error.includes('ChunkLoadError') &&
      !error.includes('Loading chunk')
    );
    
    if (criticalErrors.length > 0) {
      console.log('JavaScript errors found:', criticalErrors);
    }
    
    // For smoke test, we'll be lenient and just log errors
    console.log(`✅ JavaScript error check completed. Found ${criticalErrors.length} critical errors.`);
  });

  test('should respond within reasonable time', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 10 seconds for smoke test
    expect(loadTime).toBeLessThan(10000);
    
    console.log(`✅ Application loaded in ${loadTime}ms`);
  });

  test('should have basic HTML structure', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check for basic HTML elements
    await expect(page.locator('html')).toBeVisible();
    await expect(page.locator('head')).toBeAttached();
    await expect(page.locator('body')).toBeVisible();
    
    // Check for React root or Next.js structure
    const reactRoot = page.locator('#__next, #root, [data-reactroot]');
    const hasReactRoot = await reactRoot.count() > 0;
    
    if (hasReactRoot) {
      console.log('✅ React/Next.js structure detected');
    } else {
      console.log('ℹ️ No React root detected, but page structure is valid');
    }
  });
});
