import { test, expect } from '@playwright/test';
import {
  waitForPageLoad,
  navigateToPage,
  takeScreenshot,
  testSidebarNavigation,
  signInAsAdmin,
  testMainNavigation,
  verifyPageElements
} from './utils/test-helpers';

test.describe('Complete User Journeys', () => {
  test.beforeEach(async ({ page }) => {
    // Sign in as admin for all tests
    await signInAsAdmin(page);
  });

  test('Complete Dashboard Journey: Dashboard → Brand Deep Dive → Marketing → Executive Summary', async ({ page }) => {
    // Start at dashboard
    await navigateToPage(page, '/dashboard');
    await expect(page.locator('h1:has-text("Dashboard")')).toBeVisible();
    await takeScreenshot(page, 'journey-1-dashboard');

    // Navigate to Brand Deep Dive
    const brandLink = page.locator('nav a:has-text("Brand Deep Dive")');
    if (await brandLink.isVisible()) {
      await brandLink.click();
      await page.waitForURL('**/brand-deep-dive');
      await waitForPageLoad(page);
      await takeScreenshot(page, 'journey-1-brand-deep-dive');
    }

    // Navigate to Marketing Dashboard
    const marketingLink = page.locator('nav a:has-text("Marketing Dashboard")');
    if (await marketingLink.isVisible()) {
      await marketingLink.click();
      await page.waitForURL('**/marketing-dashboard');
      await waitForPageLoad(page);
      await takeScreenshot(page, 'journey-1-marketing');
    }

    // Navigate to Executive Summary
    const executiveLink = page.locator('nav a:has-text("Executive Summary")');
    if (await executiveLink.isVisible()) {
      await executiveLink.click();
      await page.waitForURL('**/executive-summary');
      await waitForPageLoad(page);
      await takeScreenshot(page, 'journey-1-executive');
    }

    // Return to Dashboard
    const dashboardLink = page.locator('nav a:has-text("Dashboard")');
    if (await dashboardLink.isVisible()) {
      await dashboardLink.click();
      await page.waitForURL('**/dashboard');
      await waitForPageLoad(page);
      await takeScreenshot(page, 'journey-1-back-to-dashboard');
    }
  });

  test('Analytics Journey: Dashboard → Apply Filters → View Charts → Export Data', async ({ page }) => {
    await navigateToPage(page, '/dashboard');

    // Look for filter controls
    const filterElements = page.locator('[data-testid*="filter"], .filter, [class*="filter"], select, input[type="date"]');
    const filterCount = await filterElements.count();

    if (filterCount > 0) {
      console.log(`Found ${filterCount} filter elements`);

      // Try to interact with first filter
      const firstFilter = filterElements.first();
      if (await firstFilter.isVisible()) {
        await firstFilter.click();
        await page.waitForTimeout(1000);
        await takeScreenshot(page, 'journey-2-filters-applied');
      }
    }

    // Look for chart elements
    const chartElements = page.locator('svg, canvas, .chart, [data-testid*="chart"]');
    const chartCount = await chartElements.count();

    if (chartCount > 0) {
      console.log(`Found ${chartCount} chart elements`);
      await takeScreenshot(page, 'journey-2-charts-visible');
    }

    // Look for export buttons
    const exportButtons = page.locator('button:has-text("Export"), button:has-text("Download"), [data-testid*="export"]');
    const exportCount = await exportButtons.count();

    if (exportCount > 0) {
      console.log(`Found ${exportCount} export elements`);
      // Don't actually click export to avoid file downloads in tests
      await takeScreenshot(page, 'journey-2-export-available');
    }
  });

  test('Mobile User Journey: Navigation and Responsiveness', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    await navigateToPage(page, '/dashboard');
    await takeScreenshot(page, 'journey-3-mobile-dashboard');

    // Check if mobile navigation works
    const mobileMenuButton = page.locator('button[aria-label*="menu"], button[aria-label*="Menu"], .mobile-menu-button');

    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click();
      await page.waitForTimeout(500);
      await takeScreenshot(page, 'journey-3-mobile-menu-open');

      // Try to navigate to another page
      const brandLink = page.locator('a:has-text("Brand Deep Dive")');
      if (await brandLink.isVisible()) {
        await brandLink.click();
        await page.waitForURL('**/brand-deep-dive');
        await waitForPageLoad(page);
        await takeScreenshot(page, 'journey-3-mobile-brand-deep-dive');
      }
    } else {
      // If no mobile menu, test regular navigation
      await testSidebarNavigation(page);
    }
  });

  test('Error Handling Journey: Invalid URLs and Error Recovery', async ({ page }) => {
    // Test invalid URL
    await page.goto('/invalid-page-that-does-not-exist');
    await waitForPageLoad(page);

    // Should either show 404 page or redirect
    const currentUrl = page.url();
    console.log(`Invalid URL redirected to: ${currentUrl}`);
    await takeScreenshot(page, 'journey-4-invalid-url');

    // Navigate to a valid page
    await navigateToPage(page, '/dashboard');
    await expect(page.locator('main')).toBeVisible();
    await takeScreenshot(page, 'journey-4-recovery');
  });

  test('Performance Journey: Page Load Times Across Application', async ({ page }) => {
    const pages = [
      { name: 'Dashboard', url: '/dashboard' },
      { name: 'Brand Deep Dive', url: '/brand-deep-dive' },
      { name: 'Marketing Dashboard', url: '/marketing-dashboard' },
      { name: 'Executive Summary', url: '/executive-summary' },
      { name: 'Budget', url: '/budget' },
      { name: 'AI Assistant', url: '/ai-assistant' }
    ];

    const loadTimes: { [key: string]: number } = {};

    for (const pageInfo of pages) {
      const startTime = Date.now();

      try {
        await navigateToPage(page, pageInfo.url);
        const loadTime = Date.now() - startTime;
        loadTimes[pageInfo.name] = loadTime;

        console.log(`${pageInfo.name} loaded in ${loadTime}ms`);

        // Each page should load within 10 seconds
        expect(loadTime).toBeLessThan(10000);

        await takeScreenshot(page, `journey-5-${pageInfo.name.toLowerCase().replace(' ', '-')}`);
      } catch (error) {
        console.log(`Failed to load ${pageInfo.name}: ${error}`);
        loadTimes[pageInfo.name] = -1;
      }
    }

    // Log performance summary
    console.log('Page Load Performance Summary:', loadTimes);
  });

  test('Data Flow Journey: Filter Changes Affect All Components', async ({ page }) => {
    await navigateToPage(page, '/dashboard');

    // Take initial screenshot
    await takeScreenshot(page, 'journey-6-initial-state');

    // Look for date filters
    const dateInputs = page.locator('input[type="date"], input[placeholder*="date"]');
    const dateCount = await dateInputs.count();

    if (dateCount > 0) {
      const firstDateInput = dateInputs.first();
      await firstDateInput.fill('2024-01-01');
      await page.waitForTimeout(2000); // Wait for data to update
      await takeScreenshot(page, 'journey-6-date-filter-applied');
    }

    // Look for dropdown filters
    const dropdowns = page.locator('select, [role="combobox"]');
    const dropdownCount = await dropdowns.count();

    if (dropdownCount > 0) {
      const firstDropdown = dropdowns.first();
      if (await firstDropdown.isVisible()) {
        await firstDropdown.click();
        await page.waitForTimeout(1000);

        // Try to select an option
        const options = page.locator('option, [role="option"]');
        const optionCount = await options.count();

        if (optionCount > 1) {
          await options.nth(1).click();
          await page.waitForTimeout(2000);
          await takeScreenshot(page, 'journey-6-dropdown-filter-applied');
        }
      }
    }

    // Verify that components updated (this would need specific selectors based on actual implementation)
    await takeScreenshot(page, 'journey-6-final-state');
  });

  test('Accessibility Journey: Keyboard Navigation and Screen Reader Support', async ({ page }) => {
    await navigateToPage(page, '/dashboard');

    // Test keyboard navigation
    await page.keyboard.press('Tab');
    await page.waitForTimeout(500);
    await takeScreenshot(page, 'journey-7-first-tab');

    // Continue tabbing through elements
    for (let i = 0; i < 5; i++) {
      await page.keyboard.press('Tab');
      await page.waitForTimeout(300);
    }

    await takeScreenshot(page, 'journey-7-keyboard-navigation');

    // Test Enter key on focused element
    await page.keyboard.press('Enter');
    await page.waitForTimeout(1000);
    await takeScreenshot(page, 'journey-7-enter-pressed');

    // Check for accessibility attributes
    const elementsWithAriaLabels = page.locator('[aria-label]');
    const ariaLabelCount = await elementsWithAriaLabels.count();
    console.log(`Found ${ariaLabelCount} elements with aria-label`);

    const elementsWithRoles = page.locator('[role]');
    const roleCount = await elementsWithRoles.count();
    console.log(`Found ${roleCount} elements with role attributes`);
  });

  test('should test complete admin workflow with farbour user', async ({ page }) => {
    // Test admin dashboard access
    await navigateToPage(page, '/admin');

    // Verify admin page elements
    await verifyPageElements(page, [
      'h1, h2, [data-testid="admin-title"]',
      'main, [role="main"], .main-content'
    ]);

    // Take screenshot of admin dashboard
    await takeScreenshot(page, 'admin-dashboard-complete');

    // Test navigation back to main dashboard
    await navigateToPage(page, '/dashboard');
    expect(page.url()).toContain('/dashboard');

    // Test marketing section
    await navigateToPage(page, '/marketing');
    expect(page.url()).toContain('/marketing');

    await takeScreenshot(page, 'marketing-dashboard-complete');
  });

  test('should test all main navigation with authenticated user', async ({ page }) => {
    // Test navigation to each main section
    await testMainNavigation(page);
  });

  test('should test dashboard functionality with real authentication', async ({ page }) => {
    // Start at dashboard
    await navigateToPage(page, '/dashboard');

    // Verify dashboard elements are present
    const dashboardElements = [
      'h1, h2, [data-testid="dashboard-title"]',
      'main, [role="main"], .main-content'
    ];

    await verifyPageElements(page, dashboardElements);

    // Take screenshot of dashboard
    await takeScreenshot(page, 'dashboard-functionality-test');

    // Test sidebar navigation if present
    await testSidebarNavigation(page);
  });
});
