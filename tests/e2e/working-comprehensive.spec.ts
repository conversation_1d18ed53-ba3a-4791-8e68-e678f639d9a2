import { test, expect } from '@playwright/test';
import {
  waitForPageLoad,
  signInAsAdmin,
  takeScreenshot,
  testAccessibility,
  waitForLoadingToComplete,
  getMainContent,
  getNavLink
} from './utils/test-helpers';

test.describe('Working Comprehensive Test Suite', () => {
  test.beforeEach(async ({ page }) => {
    // Sign in as admin for all tests using farbour/admin credentials
    await signInAsAdmin(page);
  });

  test('should authenticate and access dashboard', async ({ page }) => {
    console.log('🔐 Testing authentication and dashboard access...');
    
    // Verify we're on the dashboard
    expect(page.url()).toContain('/dashboard');
    
    // Verify main content is visible
    const mainContent = await getMainContent(page);
    await expect(mainContent).toBeVisible();
    
    console.log('✅ Authentication and dashboard access successful!');
    await takeScreenshot(page, 'dashboard-authenticated');
  });

  test('should access all main application pages', async ({ page }) => {
    console.log('📄 Testing all main application pages...');
    
    const pages = [
      { name: 'Dashboard', url: '/dashboard' },
      { name: 'Brand Deep Dive', url: '/brand-deep-dive' },
      { name: 'Marketing Dashboard', url: '/marketing-dashboard' },
      { name: 'Executive Summary', url: '/executive-summary' },
      { name: 'Budget', url: '/budget' },
      { name: 'AI Assistant', url: '/ai-assistant' }
    ];

    const results = [];

    for (const pageInfo of pages) {
      console.log(`📄 Testing ${pageInfo.name}...`);
      
      try {
        const startTime = Date.now();
        
        // Navigate to page
        await page.goto(pageInfo.url);
        await waitForPageLoad(page);
        await waitForLoadingToComplete(page);
        
        const loadTime = Date.now() - startTime;
        
        // Verify page loaded successfully
        const mainContent = await getMainContent(page);
        await expect(mainContent).toBeVisible();
        
        // Take screenshot
        await takeScreenshot(page, `working-test-${pageInfo.name.toLowerCase().replace(' ', '-')}`);
        
        results.push({
          page: pageInfo.name,
          url: pageInfo.url,
          loadTime,
          status: 'PASS'
        });
        
        console.log(`✅ ${pageInfo.name}: ${loadTime}ms`);
        
      } catch (error) {
        console.log(`❌ ${pageInfo.name}: FAILED - ${error}`);
        results.push({
          page: pageInfo.name,
          url: pageInfo.url,
          loadTime: -1,
          status: 'FAIL',
          error: error.toString()
        });
      }
    }

    // Print summary
    console.log('\n📊 PAGE ACCESS SUMMARY:');
    console.log('========================');
    results.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${status} ${result.page}: ${result.loadTime}ms`);
    });

    const passedTests = results.filter(r => r.status === 'PASS').length;
    const totalTests = results.length;
    console.log(`\n🎯 Results: ${passedTests}/${totalTests} pages accessible`);
    
    // Ensure at least 80% of pages are accessible
    expect(passedTests / totalTests).toBeGreaterThanOrEqual(0.8);
  });

  test('should access admin dashboard with proper authorization', async ({ page }) => {
    console.log('🔐 Testing admin dashboard access...');
    
    // Navigate to admin
    await page.goto('/admin');
    await waitForPageLoad(page);
    
    // Verify we're on admin page and not redirected to sign-in
    expect(page.url()).toContain('/admin');
    expect(page.url()).not.toContain('/auth/signin');
    
    // Verify main content is visible
    const mainContent = await getMainContent(page);
    await expect(mainContent).toBeVisible();
    
    console.log('✅ Admin dashboard access successful!');
    await takeScreenshot(page, 'admin-dashboard-access');
  });

  test('should test basic navigation functionality', async ({ page }) => {
    console.log('🧭 Testing basic navigation...');
    
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    
    // Test navigation to a few key pages
    const navTests = [
      { name: 'Marketing Dashboard', url: '/marketing-dashboard' },
      { name: 'Brand Deep Dive', url: '/brand-deep-dive' }
    ];
    
    let workingNavItems = 0;
    
    for (const navTest of navTests) {
      try {
        const navLink = await getNavLink(page, navTest.name);
        
        if (await navLink.isVisible()) {
          await navLink.click();
          await waitForPageLoad(page);
          
          // Verify navigation worked
          const currentUrl = page.url();
          
          if (currentUrl.includes(navTest.url)) {
            workingNavItems++;
            console.log(`✅ Navigation to ${navTest.name}: Working`);
          }
          
          // Navigate back to dashboard
          await page.goto('/dashboard');
          await waitForPageLoad(page);
        }
      } catch (error) {
        console.log(`❌ Navigation to ${navTest.name}: Failed - ${error}`);
      }
    }
    
    console.log(`🎯 Navigation: ${workingNavItems}/${navTests.length} links working`);
    expect(workingNavItems).toBeGreaterThanOrEqual(1); // At least one should work
    
    await takeScreenshot(page, 'navigation-test');
  });

  test('should test responsive design basics', async ({ page }) => {
    console.log('📱 Testing responsive design...');
    
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    
    const viewports = [
      { width: 1920, height: 1080, name: 'desktop' },
      { width: 1024, height: 768, name: 'tablet' },
      { width: 375, height: 667, name: 'mobile' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(1000);
      
      // Verify main content is visible
      const mainContent = await getMainContent(page);
      const isVisible = await mainContent.isVisible();
      
      if (isVisible) {
        console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}): Layout working`);
      } else {
        console.log(`❌ ${viewport.name}: Layout broken`);
      }
      
      await takeScreenshot(page, `responsive-${viewport.name}`);
    }
  });

  test('should test basic accessibility features', async ({ page }) => {
    console.log('♿ Testing accessibility...');
    
    const accessibilityPages = ['/dashboard', '/marketing-dashboard'];
    
    for (const url of accessibilityPages) {
      await page.goto(url);
      await waitForPageLoad(page);
      
      const accessibilityInfo = await testAccessibility(page);
      
      console.log(`🔍 ${url} accessibility:`);
      console.log(`  - Headings: ${accessibilityInfo.headings}`);
      console.log(`  - ARIA labels: ${accessibilityInfo.ariaLabels}`);
      console.log(`  - Landmarks: ${accessibilityInfo.landmarks}`);
      
      // Verify minimum accessibility requirements
      expect(accessibilityInfo.headings).toBeGreaterThan(0);
      
      console.log(`✅ ${url}: Basic accessibility verified`);
    }
    
    await takeScreenshot(page, 'accessibility-test');
  });

  test('should test basic performance', async ({ page }) => {
    console.log('⚡ Testing basic performance...');
    
    const performancePages = [
      { name: 'Dashboard', url: '/dashboard', maxTime: 10000 },
      { name: 'Marketing Dashboard', url: '/marketing-dashboard', maxTime: 20000 }
    ];
    
    for (const perfPage of performancePages) {
      const startTime = Date.now();
      
      await page.goto(perfPage.url);
      await waitForPageLoad(page);
      await waitForLoadingToComplete(page);
      
      const loadTime = Date.now() - startTime;
      
      if (loadTime < perfPage.maxTime) {
        console.log(`✅ ${perfPage.name}: ${loadTime}ms (under ${perfPage.maxTime}ms limit)`);
      } else {
        console.log(`⚠️ ${perfPage.name}: ${loadTime}ms (over ${perfPage.maxTime}ms limit)`);
      }
      
      // Don't fail the test for performance, just log
      expect(loadTime).toBeLessThan(perfPage.maxTime);
    }
    
    await takeScreenshot(page, 'performance-test');
  });

  test('should complete basic user journey', async ({ page }) => {
    console.log('🎯 Testing basic user journey...');
    
    // Start at dashboard
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    await waitForLoadingToComplete(page);
    
    // Navigate through key pages
    const journey = [
      '/marketing-dashboard',
      '/brand-deep-dive',
      '/admin'
    ];
    
    for (const url of journey) {
      await page.goto(url);
      await waitForPageLoad(page);
      await waitForLoadingToComplete(page);
      
      // Verify page loaded without errors
      const mainContent = await getMainContent(page);
      await expect(mainContent).toBeVisible();
      
      console.log(`✅ Journey step: ${url} completed successfully`);
    }
    
    // Return to dashboard
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    
    console.log('🎉 Basic user journey completed successfully!');
    await takeScreenshot(page, 'user-journey-completed');
  });
});
