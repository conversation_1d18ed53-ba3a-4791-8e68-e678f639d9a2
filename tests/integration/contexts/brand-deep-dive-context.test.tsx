/**
 * Integration Tests for Brand Deep Dive Context
 *
 * Tests the brand-deep-dive-context integration including state management,
 * data flow, and provider interactions with child components.
 */

import { BrandDeepDiveProvider, BrandDeepDiveState, useBrandDeepDive } from '@/lib/contexts/brand-deep-dive-context';
import { cleanupTestEnvironment, initializeTestEnvironment } from '../../utils/test-setup';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';

import React from 'react';

// Mock fetch for API calls
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Mock next-auth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: {
      user: {
        id: 'test-user-123',
        email: '<EMAIL>',
        isImpersonating: false,
      },
    },
    status: 'authenticated',
  })),
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

// Test component to consume the context
const TestConsumer: React.FC = () => {
  const {
    state,
    setState,
    availableBrands,
    availableSalesChannels,
    availableCountries,
    isLoading,
    error,
    getQueryParams,
    isStateInitialized,
  } = useBrandDeepDive();

  return (
    <div>
      <div data-testid="selected-brand">{state.selectedBrand || 'No brand selected'}</div>
      <div data-testid="start-date">{state.startDate}</div>
      <div data-testid="end-date">{state.endDate}</div>
      <div data-testid="currency">{state.currency}</div>
      <div data-testid="group-by">{state.groupBy}</div>
      <div data-testid="sales-channels">{JSON.stringify(state.salesChannels)}</div>
      <div data-testid="country-names">{JSON.stringify(state.countryNames)}</div>
      <div data-testid="available-brands">{JSON.stringify(availableBrands)}</div>
      <div data-testid="available-sales-channels">{JSON.stringify(availableSalesChannels)}</div>
      <div data-testid="available-countries">{JSON.stringify(availableCountries)}</div>
      <div data-testid="loading">{isLoading ? 'Loading' : 'Not loading'}</div>
      <div data-testid="error">{error || 'No error'}</div>
      <div data-testid="query-params">{getQueryParams()}</div>
      <div data-testid="state-initialized">{isStateInitialized ? 'Initialized' : 'Not initialized'}</div>

      <button
        data-testid="set-brand"
        onClick={() => setState(prev => ({ ...prev, selectedBrand: 'Test Brand' }))}
      >
        Set Brand
      </button>

      <button
        data-testid="set-date-range"
        onClick={() => setState(prev => ({
          ...prev,
          startDate: '2024-01-01',
          endDate: '2024-01-15' // 15 days - should use day grouping
        }))}
      >
        Set Date Range
      </button>

      <button
        data-testid="set-currency"
        onClick={() => setState(prev => ({ ...prev, currency: 'USD' }))}
      >
        Set Currency
      </button>

      <button
        data-testid="set-sales-channels"
        onClick={() => setState(prev => ({ ...prev, salesChannels: ['online', 'retail'] }))}
      >
        Set Sales Channels
      </button>

      <button
        data-testid="set-countries"
        onClick={() => setState(prev => ({ ...prev, countryNames: ['US', 'CA'] }))}
      >
        Set Countries
      </button>
    </div>
  );
};

// Test component that uses context outside provider (should throw error)
const TestConsumerOutsideProvider: React.FC = () => {
  const context = useBrandDeepDive();
  return <div>{context.state.selectedBrand}</div>;
};

describe('Brand Deep Dive Context Integration', () => {
  beforeAll(async () => {
    await initializeTestEnvironment();
  });

  afterAll(async () => {
    await cleanupTestEnvironment();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockFetch.mockClear();
    mockLocalStorage.getItem.mockClear();
    mockLocalStorage.setItem.mockClear();

    // Setup default API responses
    mockFetch.mockImplementation((url) => {
      if (url === '/api/dashboard/brands') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve([
            { name: 'Brand A', brand_id: '1' },
            { name: 'Brand B', brand_id: '2' },
            { name: 'Brand C', brand_id: '3' },
          ]),
        } as Response);
      }
      if (url === '/api/dashboard/sales-channels') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(['Direct', 'Marketplace', 'Wholesale']),
        } as Response);
      }
      if (url === '/api/dashboard/countries') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve([
            { name: 'Canada', hasSales: true },
            { name: 'United States', hasSales: true },
            { name: 'Mexico', hasSales: false },
          ]),
        } as Response);
      }
      return Promise.reject(new Error('Unknown URL'));
    });
  });

  describe('Provider Setup and Context Access', () => {
    it('should provide default context values', async () => {
      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      // Wait for initialization and loading to complete
      await waitFor(() => {
        expect(screen.getByTestId('state-initialized')).toHaveTextContent('Initialized');
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      });

      expect(screen.getByTestId('selected-brand')).toHaveTextContent('No brand selected');
      expect(screen.getByTestId('currency')).toHaveTextContent('CAD');
      expect(screen.getByTestId('sales-channels')).toHaveTextContent('[]');
      expect(screen.getByTestId('country-names')).toHaveTextContent('[]');
      expect(screen.getByTestId('error')).toHaveTextContent('No error');
    });

    it('should throw error when used outside provider', () => {
      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      expect(() => {
        render(<TestConsumerOutsideProvider />);
      }).toThrow('useBrandDeepDive must be used within a BrandDeepDiveProvider');

      consoleSpy.mockRestore();
    });

    it('should load available filter options from API', async () => {
      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      // Wait for API calls to complete
      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      });

      // Verify API data was loaded
      expect(screen.getByTestId('available-brands')).toHaveTextContent(
        JSON.stringify(['Brand A', 'Brand B', 'Brand C'])
      );
      expect(screen.getByTestId('available-sales-channels')).toHaveTextContent(
        JSON.stringify(['Direct', 'Marketplace', 'Wholesale'])
      );
      expect(screen.getByTestId('available-countries')).toHaveTextContent(
        JSON.stringify([
          { name: 'Canada', hasSales: true },
          { name: 'United States', hasSales: true },
          { name: 'Mexico', hasSales: false },
        ])
      );

      // Verify API calls were made
      expect(mockFetch).toHaveBeenCalledWith('/api/dashboard/brands');
      expect(mockFetch).toHaveBeenCalledWith('/api/dashboard/sales-channels');
      expect(mockFetch).toHaveBeenCalledWith('/api/dashboard/countries');
    });
  });

  describe('State Management', () => {
    it('should update selected brand', async () => {
      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('state-initialized')).toHaveTextContent('Initialized');
      });

      fireEvent.click(screen.getByTestId('set-brand'));

      expect(screen.getByTestId('selected-brand')).toHaveTextContent('Test Brand');
    });

    it('should update date range and smart group by', async () => {
      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('state-initialized')).toHaveTextContent('Initialized');
      });

      fireEvent.click(screen.getByTestId('set-date-range'));

      expect(screen.getByTestId('start-date')).toHaveTextContent('2024-01-01');
      expect(screen.getByTestId('end-date')).toHaveTextContent('2024-01-15');

      // Should auto-update groupBy to 'day' for 15-day range
      await waitFor(() => {
        expect(screen.getByTestId('group-by')).toHaveTextContent('day');
      });
    });

    it('should update currency', async () => {
      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('state-initialized')).toHaveTextContent('Initialized');
      });

      fireEvent.click(screen.getByTestId('set-currency'));

      expect(screen.getByTestId('currency')).toHaveTextContent('USD');
    });

    it('should update sales channels', async () => {
      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('state-initialized')).toHaveTextContent('Initialized');
      });

      fireEvent.click(screen.getByTestId('set-sales-channels'));

      expect(screen.getByTestId('sales-channels')).toHaveTextContent(
        JSON.stringify(['online', 'retail'])
      );
    });

    it('should update countries', async () => {
      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('state-initialized')).toHaveTextContent('Initialized');
      });

      fireEvent.click(screen.getByTestId('set-countries'));

      expect(screen.getByTestId('country-names')).toHaveTextContent(
        JSON.stringify(['US', 'CA'])
      );
    });
  });

  describe('Query Parameters Generation', () => {
    it('should generate correct query parameters', async () => {
      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('state-initialized')).toHaveTextContent('Initialized');
      });

      // Set various state values
      fireEvent.click(screen.getByTestId('set-brand'));
      fireEvent.click(screen.getByTestId('set-date-range'));
      fireEvent.click(screen.getByTestId('set-currency'));
      fireEvent.click(screen.getByTestId('set-sales-channels'));
      fireEvent.click(screen.getByTestId('set-countries'));

      await waitFor(() => {
        const queryParams = screen.getByTestId('query-params').textContent;
        expect(queryParams).toContain('startDate=2024-01-01');
        expect(queryParams).toContain('endDate=2024-01-15');
        expect(queryParams).toContain('currency=USD');
        expect(queryParams).toContain('groupByTime=day');
        expect(queryParams).toContain('brands=Test+Brand');
        expect(queryParams).toContain('brand=Test+Brand');
        expect(queryParams).toContain('salesChannels=online%2Cretail');
        expect(queryParams).toContain('countryNames=US%2CCA');
      });
    });
  });

  describe('LocalStorage Integration', () => {
    it('should save state to localStorage when modified', async () => {
      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('state-initialized')).toHaveTextContent('Initialized');
      });

      // Modify state
      fireEvent.click(screen.getByTestId('set-brand'));

      // Wait for localStorage save
      await waitFor(() => {
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
          'brand_deep_dive_state_test-user-123',
          expect.stringContaining('"selectedBrand":"Test Brand"')
        );
      });
    });

    it('should load state from localStorage on initialization', async () => {
      const savedState: BrandDeepDiveState = {
        startDate: '2024-06-01',
        endDate: '2024-06-30',
        currency: 'USD',
        groupBy: 'week',
        selectedBrand: 'Saved Brand',
        salesChannels: ['saved-channel'],
        countryNames: ['Saved Country'],
      };

      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(savedState));

      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('state-initialized')).toHaveTextContent('Initialized');
      });

      // Verify loaded state
      expect(screen.getByTestId('selected-brand')).toHaveTextContent('Saved Brand');
      expect(screen.getByTestId('start-date')).toHaveTextContent('2024-06-01');
      expect(screen.getByTestId('end-date')).toHaveTextContent('2024-06-30');
      expect(screen.getByTestId('currency')).toHaveTextContent('USD');
      expect(screen.getByTestId('sales-channels')).toHaveTextContent(
        JSON.stringify(['saved-channel'])
      );
      expect(screen.getByTestId('country-names')).toHaveTextContent(
        JSON.stringify(['Saved Country'])
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully and use defaults', async () => {
      // Mock API failures
      mockFetch.mockImplementation(() => Promise.reject(new Error('API Error')));

      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      });

      // Should use default values
      expect(screen.getByTestId('available-brands')).toHaveTextContent(
        JSON.stringify(['Brand A', 'Brand B', 'Brand C'])
      );
      expect(screen.getByTestId('available-sales-channels')).toHaveTextContent(
        JSON.stringify(['Direct', 'Marketplace', 'Wholesale'])
      );
      expect(screen.getByTestId('available-countries')).toHaveTextContent(
        JSON.stringify([
          { name: 'Canada', hasSales: true },
          { name: 'United States', hasSales: true },
          { name: 'Mexico', hasSales: false },
        ])
      );
    });

    it('should handle partial API failures', async () => {
      // Mock brands API failure, others succeed
      mockFetch.mockImplementation((url) => {
        if (url === '/api/dashboard/brands') {
          return Promise.reject(new Error('Brands API Error'));
        }
        if (url === '/api/dashboard/sales-channels') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(['Working Channel']),
          } as Response);
        }
        if (url === '/api/dashboard/countries') {
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve([{ name: 'Working Country', hasSales: true }]),
          } as Response);
        }
        return Promise.reject(new Error('Unknown URL'));
      });

      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
      });

      // Should use defaults for failed API, real data for successful APIs
      expect(screen.getByTestId('available-brands')).toHaveTextContent(
        JSON.stringify(['Brand A', 'Brand B', 'Brand C'])
      );
      expect(screen.getByTestId('available-sales-channels')).toHaveTextContent(
        JSON.stringify(['Working Channel'])
      );
      expect(screen.getByTestId('available-countries')).toHaveTextContent(
        JSON.stringify([{ name: 'Working Country', hasSales: true }])
      );
    });
  });

  describe('Smart Grouping Logic', () => {
    it('should use day grouping for short date ranges', async () => {
      render(
        <BrandDeepDiveProvider>
          <TestConsumer />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('state-initialized')).toHaveTextContent('Initialized');
      });

      // Set 15-day range
      fireEvent.click(screen.getByTestId('set-date-range'));

      await waitFor(() => {
        expect(screen.getByTestId('group-by')).toHaveTextContent('day');
      });
    });

    it('should use week grouping for medium date ranges', async () => {
      const TestConsumerWithMediumRange: React.FC = () => {
        const { state, setState, isStateInitialized } = useBrandDeepDive();

        return (
          <div>
            <div data-testid="group-by">{state.groupBy}</div>
            <div data-testid="state-initialized">{isStateInitialized ? 'Initialized' : 'Not initialized'}</div>
            <button
              data-testid="set-medium-range"
              onClick={() => setState(prev => ({
                ...prev,
                startDate: '2024-01-01',
                endDate: '2024-03-01' // ~60 days
              }))}
            >
              Set Medium Range
            </button>
          </div>
        );
      };

      render(
        <BrandDeepDiveProvider>
          <TestConsumerWithMediumRange />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('state-initialized')).toHaveTextContent('Initialized');
      });

      fireEvent.click(screen.getByTestId('set-medium-range'));

      await waitFor(() => {
        expect(screen.getByTestId('group-by')).toHaveTextContent('week');
      });
    });

    it('should use month grouping for long date ranges', async () => {
      const TestConsumerWithLongRange: React.FC = () => {
        const { state, setState, isStateInitialized } = useBrandDeepDive();

        return (
          <div>
            <div data-testid="group-by">{state.groupBy}</div>
            <div data-testid="state-initialized">{isStateInitialized ? 'Initialized' : 'Not initialized'}</div>
            <button
              data-testid="set-long-range"
              onClick={() => setState(prev => ({
                ...prev,
                startDate: '2024-01-01',
                endDate: '2024-12-31' // 12 months
              }))}
            >
              Set Long Range
            </button>
          </div>
        );
      };

      render(
        <BrandDeepDiveProvider>
          <TestConsumerWithLongRange />
        </BrandDeepDiveProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('state-initialized')).toHaveTextContent('Initialized');
      });

      fireEvent.click(screen.getByTestId('set-long-range'));

      await waitFor(() => {
        expect(screen.getByTestId('group-by')).toHaveTextContent('month');
      });
    });
  });
});