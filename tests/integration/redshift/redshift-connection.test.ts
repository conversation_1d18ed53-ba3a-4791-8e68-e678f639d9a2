/**
 * Redshift Connection Integration Tests
 * 
 * These tests verify that the application can connect to the Redshift database
 * and execute basic queries. They serve as a foundation for more specific tests.
 */

import {
  cleanupTestEnvironment,
  initializeTestEnvironment
} from '../../utils/test-setup';
import {
  executeTestQuery,
  getTestDatabaseName,
  isRedshiftAvailable
} from '../../utils/redshift-test-utils';

import { testRedshiftConnection } from '@/lib/api/redshift';

// This test suite requires a connection to the Redshift database
// It will be skipped if the connection is not available
describe('Redshift Connection', () => {
  // Set up the test environment before running tests
  beforeAll(async () => {
    try {
      await initializeTestEnvironment();
    } catch (error) {
      console.warn('Failed to initialize test environment:', error);
      // The tests will still run, but they will be skipped if Redshift is not available
    }
  });

  // Clean up the test environment after running tests
  afterAll(async () => {
    await cleanupTestEnvironment();
  });

  // Check if Redshift is available before each test
  beforeEach(async () => {
    const available = await isRedshiftAvailable();
    if (!available) {
      // Skip the test if Redshift is not available
      console.warn('Skipping test: Redshift connection is not available');
      // Just return without running the test
      return;
    }
  });

  // Test that the connection to Redshift is successful
  it('should connect to Redshift successfully', async () => {
    // Check if Redshift is available first
    const available = await isRedshiftAvailable();
    if (!available) {
      console.log('Skipping test: Redshift connection is not available');
      return;
    }
    
    const result = await testRedshiftConnection();
    expect(result).toBe(true);
  });

  // Test that we can execute a simple query
  it('should execute a simple query', async () => {
    // Check if Redshift is available first
    const available = await isRedshiftAvailable();
    if (!available) {
      console.log('Skipping test: Redshift connection is not available');
      return;
    }
    
    const result = await executeTestQuery('SELECT 1 as test');
    expect(result.rows).toHaveLength(1);
    expect(result.rows[0]).toHaveProperty('test', 1);
  });

  // Test that we can get the database name
  it('should return the test database name', () => {
    const dbName = getTestDatabaseName();
    expect(dbName).toBeTruthy();
    expect(typeof dbName).toBe('string');
  });

  // Test that we can query the database version
  it('should query the database version', async () => {
    // Check if Redshift is available first
    const available = await isRedshiftAvailable();
    if (!available) {
      console.log('Skipping test: Redshift connection is not available');
      return;
    }
    
    const result = await executeTestQuery('SELECT version()');
    expect(result.rows).toHaveLength(1);
    expect(result.rows[0]).toHaveProperty('version');
    expect(typeof result.rows[0].version).toBe('string');
    expect(result.rows[0].version).toContain('Redshift');
  });
});