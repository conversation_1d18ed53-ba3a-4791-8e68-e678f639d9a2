/**
 * Integration Tests for Campaign Data Service
 * 
 * Tests the campaign-data-service integration with API endpoints
 * including data fetching, caching, and error handling.
 */

import { CampaignDataResponse, cancelOngoingRequests, fetchCampaignData, useCampaignData } from '@/lib/api/campaign-data-service';
import { cleanupTestEnvironment, initializeTestEnvironment } from '../../utils/test-setup';
import { useEffect, useState } from 'react';

import { isRedshiftAvailable } from '../../utils/redshift-test-utils';

// Mock fetch for testing
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Mock React hooks for testing the hook
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useState: jest.fn(),
  useEffect: jest.fn(),
}));

const mockUseState = useState as jest.MockedFunction<typeof useState>;
const mockUseEffect = useEffect as jest.MockedFunction<typeof useEffect>;

describe('Campaign Data Service Integration', () => {
  beforeAll(async () => {
    await initializeTestEnvironment();
  });

  afterAll(async () => {
    await cleanupTestEnvironment();
  });

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    mockFetch.mockClear();
    
    // Cancel any ongoing requests
    cancelOngoingRequests();
  });

  describe('fetchCampaignData', () => {
    it('should fetch campaign data successfully', async () => {
      const mockData: CampaignDataResponse[] = [
        {
          date: '2024-01-01',
          campaign_name: 'Test Campaign',
          brand_name: 'Test Brand',
          sales_channel_type: 'online',
          totalSpend: 1000,
          totalImpressions: 10000,
          totalClicks: 500,
          totalConversions: 25,
          totalConversionValue: 2500,
          roas: 2.5,
          cpa: 40,
        },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockData,
      } as Response);

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';
      const result = await fetchCampaignData(queryParams);

      expect(mockFetch).toHaveBeenCalledWith(`/api/marketing/campaign-data?${queryParams}`);
      expect(result).toEqual(mockData);
    });

    it('should handle API errors gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: async () => 'Database connection failed',
      } as Response);

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';

      await expect(fetchCampaignData(queryParams)).rejects.toThrow(
        'API request failed with status 500: Internal Server Error'
      );
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';

      await expect(fetchCampaignData(queryParams)).rejects.toThrow('Network error');
    });

    it('should prevent duplicate requests for same parameters', async () => {
      const mockData: CampaignDataResponse[] = [
        {
          date: '2024-01-01',
          campaign_name: 'Test Campaign',
          brand_name: 'Test Brand',
          sales_channel_type: 'online',
          totalSpend: 1000,
        },
      ];

      // Mock a delayed response
      mockFetch.mockImplementationOnce(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: async () => mockData,
          } as Response), 100)
        )
      );

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';

      // Make two simultaneous requests
      const promise1 = fetchCampaignData(queryParams);
      const promise2 = fetchCampaignData(queryParams);

      const [result1, result2] = await Promise.all([promise1, promise2]);

      // Should only make one fetch call
      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(result1).toEqual(mockData);
      expect(result2).toEqual(mockData);
    });

    it('should handle malformed JSON responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => { throw new Error('Invalid JSON'); },
      } as unknown as Response);

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';

      await expect(fetchCampaignData(queryParams)).rejects.toThrow('Invalid JSON');
    });

    it('should clean up ongoing requests after completion', async () => {
      const mockData: CampaignDataResponse[] = [];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockData,
      } as Response);

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';
      await fetchCampaignData(queryParams);

      // Make another request with same params - should make a new fetch call
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockData,
      } as Response);

      await fetchCampaignData(queryParams);

      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('should clean up ongoing requests on error', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';

      await expect(fetchCampaignData(queryParams)).rejects.toThrow('Network error');

      // Make another request with same params - should make a new fetch call
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response);

      await fetchCampaignData(queryParams);

      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('useCampaignData hook', () => {
    let mockSetData: jest.Mock;
    let mockSetLoading: jest.Mock;
    let mockSetError: jest.Mock;

    beforeEach(() => {
      mockSetData = jest.fn();
      mockSetLoading = jest.fn();
      mockSetError = jest.fn();

      // Mock useState to return our mock setters
      mockUseState
        .mockReturnValueOnce([[], mockSetData]) // data state
        .mockReturnValueOnce([true, mockSetLoading]) // loading state
        .mockReturnValueOnce([null, mockSetError]); // error state

      // Mock useEffect to immediately call the effect function
      mockUseEffect.mockImplementation((effect) => {
        const cleanup = effect();
        return cleanup;
      });
    });

    it('should initialize with correct default values', () => {
      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';
      const result = useCampaignData(queryParams);

      expect(result).toEqual({
        data: [],
        loading: true,
        error: null,
      });
    });

    it('should handle successful data fetching', async () => {
      const mockData: CampaignDataResponse[] = [
        {
          date: '2024-01-01',
          campaign_name: 'Test Campaign',
          brand_name: 'Test Brand',
          sales_channel_type: 'online',
          totalSpend: 1000,
        },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockData,
      } as Response);

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';
      useCampaignData(queryParams);

      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockSetLoading).toHaveBeenCalledWith(true);
      expect(mockSetError).toHaveBeenCalledWith(null);
    });

    it('should handle fetch errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('API Error'));

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';
      useCampaignData(queryParams);

      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockSetLoading).toHaveBeenCalledWith(true);
      expect(mockSetError).toHaveBeenCalledWith(null);
    });
  });

  describe('cancelOngoingRequests', () => {
    it('should cancel specific ongoing requests', async () => {
      const queryParams1 = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';
      const queryParams2 = 'startDate=2024-02-01&endDate=2024-02-28&brands=2';

      // Start two requests
      mockFetch.mockImplementation(() =>
        new Promise(resolve =>
          setTimeout(() => resolve({
            ok: true,
            json: async () => [],
          } as Response), 1000)
        )
      );

      fetchCampaignData(queryParams1);
      fetchCampaignData(queryParams2);

      // Cancel the first request
      cancelOngoingRequests(queryParams1);

      // Start a new request with the same params as the cancelled one
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response);

      const promise3 = fetchCampaignData(queryParams1);

      // Should make a new fetch call for the third request
      await promise3;

      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it('should cancel all ongoing requests', async () => {
      const queryParams1 = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';
      const queryParams2 = 'startDate=2024-02-01&endDate=2024-02-28&brands=2';

      // Start two requests
      mockFetch.mockImplementation(() =>
        new Promise(resolve =>
          setTimeout(() => resolve({
            ok: true,
            json: async () => [],
          } as Response), 1000)
        )
      );

      fetchCampaignData(queryParams1);
      fetchCampaignData(queryParams2);

      // Cancel all requests
      cancelOngoingRequests();

      // Start new requests with the same params
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => [],
      } as Response);

      const promise3 = fetchCampaignData(queryParams1);
      const promise4 = fetchCampaignData(queryParams2);

      await Promise.all([promise3, promise4]);

      // Should make new fetch calls for both requests
      expect(mockFetch).toHaveBeenCalledTimes(4);
    });
  });

  describe('Data Structure Validation', () => {
    it('should handle campaign data with all fields', async () => {
      const mockData: CampaignDataResponse[] = [
        {
          date: '2024-01-01',
          campaign_name: 'Complete Campaign',
          brand_name: 'Test Brand',
          sales_channel_type: 'online',
          totalSpend: 1000,
          totalImpressions: 10000,
          totalClicks: 500,
          totalConversions: 25,
          totalConversionValue: 2500,
          roas: 2.5,
          cpa: 40,
          customField: 'custom value',
        },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockData,
      } as Response);

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';
      const result = await fetchCampaignData(queryParams);

      expect(result).toEqual(mockData);
      expect(result[0]).toHaveProperty('date', '2024-01-01');
      expect(result[0]).toHaveProperty('campaign_name', 'Complete Campaign');
      expect(result[0]).toHaveProperty('totalSpend', 1000);
      expect(result[0]).toHaveProperty('customField', 'custom value');
    });

    it('should handle campaign data with minimal fields', async () => {
      const mockData: CampaignDataResponse[] = [
        {
          totalSpend: 500,
        },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockData,
      } as Response);

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';
      const result = await fetchCampaignData(queryParams);

      expect(result).toEqual(mockData);
      expect(result[0]).toHaveProperty('totalSpend', 500);
      expect(result[0].date).toBeUndefined();
      expect(result[0].campaign_name).toBeUndefined();
    });

    it('should handle empty response arrays', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response);

      const queryParams = 'startDate=2030-01-01&endDate=2030-01-31&brands=999';
      const result = await fetchCampaignData(queryParams);

      expect(result).toEqual([]);
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
    });
  });

  describe('Performance and Concurrency', () => {
    it('should handle multiple concurrent requests efficiently', async () => {
      const mockData: CampaignDataResponse[] = [
        { totalSpend: 1000 },
      ];

      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockData,
      } as Response);

      const requests = Array.from({ length: 10 }, (_, i) => 
        fetchCampaignData(`startDate=2024-01-01&endDate=2024-01-31&brands=${i + 1}`)
      );

      const results = await Promise.all(requests);

      // Should make 10 separate fetch calls (different query params)
      expect(mockFetch).toHaveBeenCalledTimes(10);
      
      // All results should be the same
      results.forEach(result => {
        expect(result).toEqual(mockData);
      });
    });

    it('should handle request timeouts gracefully', async () => {
      // Mock a request that never resolves
      mockFetch.mockImplementationOnce(() => new Promise(() => {}));

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1';

      // Start the request but don't wait for it
      fetchCampaignData(queryParams);

      // Cancel the request
      cancelOngoingRequests(queryParams);

      // Start a new request that will resolve
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response);

      const result = await fetchCampaignData(queryParams);

      expect(result).toEqual([]);
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Integration with Redshift', () => {
    it('should work with actual API endpoints when Redshift is available', async () => {
      const available = await isRedshiftAvailable();
      if (!available) {
        console.warn('Skipping test: Redshift connection is not available');
        return;
      }

      // Restore real fetch for this test
      mockFetch.mockRestore();

      const queryParams = 'startDate=2024-01-01&endDate=2024-01-31&brands=1&limit=5';
      
      try {
        const result = await fetchCampaignData(queryParams);
        
        expect(Array.isArray(result)).toBe(true);
        
        // If there's data, verify structure
        if (result.length > 0) {
          const campaign = result[0];
          expect(typeof campaign.totalSpend).toBe('number');
          
          // Optional fields should be undefined or have correct types
          if (campaign.date !== undefined) {
            expect(typeof campaign.date).toBe('string');
          }
          if (campaign.campaign_name !== undefined) {
            expect(typeof campaign.campaign_name).toBe('string');
          }
          if (campaign.totalImpressions !== undefined) {
            expect(typeof campaign.totalImpressions).toBe('number');
          }
        }
      } catch (error) {
        // If the API is not available, that's also a valid test result
        expect(error).toBeInstanceOf(Error);
      }
    });
  });
});