/**
 * Load Testing and Stress Testing Suite
 * 
 * Tests application behavior under various load conditions including
 * concurrent users, high request volumes, and stress scenarios.
 */

import {
  LoadTestMetrics,
  LoadTester,
  PerformanceAssertions,
  globalPerformanceUtils
} from '@/tests/utils/performance-utils';
import {
  cleanupTestEnvironment,
  initializeTestEnvironment
} from '@/tests/utils/test-setup';

import { createServer } from 'http';
import { isRedshiftAvailable } from '@/tests/utils/redshift-test-utils';
import { performance } from 'perf_hooks';
import supertest from 'supertest';

// Mock API responses for load testing
const mockResponses = {
  flexibleKpis: {
    'Net Revenue': {
      summary: { value: 1250000, previousValue: 1100000, change: 13.6 },
      timeSeries: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(2025, 0, i + 1).toISOString().split('T')[0],
        value: Math.random() * 50000 + 30000
      }))
    }
  },
  brandData: {
    brands: Array.from({ length: 25 }, (_, i) => ({
      id: i + 1,
      name: `Brand ${i + 1}`,
      revenue: Math.random() * 1000000,
      margin: Math.random() * 0.5
    }))
  },
  campaignData: {
    campaigns: Array.from({ length: 100 }, (_, i) => ({
      id: i + 1,
      name: `Campaign ${i + 1}`,
      spend: Math.random() * 10000,
      impressions: Math.random() * 100000,
      clicks: Math.random() * 5000
    }))
  }
};

// Create mock server for load testing
function createLoadTestServer() {
  const requestCounts = new Map<string, number>();
  const responseTimes = new Map<string, number[]>();

  const handler = (req: any, res: any) => {
    const startTime = performance.now();
    const endpoint = req.url?.split('?')[0] || 'unknown';
    
    // Track request counts
    requestCounts.set(endpoint, (requestCounts.get(endpoint) || 0) + 1);
    
    // Simulate realistic processing times based on endpoint
    let processingTime = 50; // Base time
    
    if (endpoint.includes('/api/dashboard/flexible-kpis')) {
      processingTime = 200 + Math.random() * 300; // 200-500ms
    } else if (endpoint.includes('/api/brand-deep-dive')) {
      processingTime = 300 + Math.random() * 400; // 300-700ms
    } else if (endpoint.includes('/api/campaigns')) {
      processingTime = 150 + Math.random() * 250; // 150-400ms
    }
    
    // Simulate database load - increase processing time with concurrent requests
    const currentLoad = Array.from(requestCounts.values()).reduce((sum, count) => sum + count, 0);
    if (currentLoad > 50) {
      processingTime *= 1.5; // 50% slower under high load
    }
    if (currentLoad > 100) {
      processingTime *= 2; // 100% slower under very high load
    }
    
    setTimeout(() => {
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      // Track response times
      if (!responseTimes.has(endpoint)) {
        responseTimes.set(endpoint, []);
      }
      responseTimes.get(endpoint)!.push(totalTime);
      
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('X-Response-Time', totalTime.toString());
      res.setHeader('X-Request-Count', requestCounts.get(endpoint)?.toString() || '0');
      
      // Return appropriate mock data
      if (endpoint.includes('/api/dashboard/flexible-kpis')) {
        res.statusCode = 200;
        res.end(JSON.stringify(mockResponses.flexibleKpis));
      } else if (endpoint.includes('/api/brand-deep-dive')) {
        res.statusCode = 200;
        res.end(JSON.stringify(mockResponses.brandData));
      } else if (endpoint.includes('/api/campaigns')) {
        res.statusCode = 200;
        res.end(JSON.stringify(mockResponses.campaignData));
      } else {
        res.statusCode = 404;
        res.end(JSON.stringify({ error: 'Not found' }));
      }
    }, processingTime);
  };

  const server = createServer(handler);
  return { server, requestCounts, responseTimes };
}

describe('Load Testing and Stress Testing', () => {
  let server: any;
  let request: supertest.SuperTest<supertest.Test>;
  let requestCounts: Map<string, number>;
  let responseTimes: Map<string, number[]>;
  const loadTester = new LoadTester();

  beforeAll(async () => {
    await initializeTestEnvironment();
    
    const mockServer = createLoadTestServer();
    server = mockServer.server;
    requestCounts = mockServer.requestCounts;
    responseTimes = mockServer.responseTimes;
    request = supertest(server);
  });

  afterAll(async () => {
    await cleanupTestEnvironment();
    if (server) {
      server.close();
    }
  });

  beforeEach(() => {
    requestCounts.clear();
    responseTimes.clear();
    globalPerformanceUtils.measurer.reset();
  });

  describe('Concurrent User Load Testing', () => {
    it('should handle 10 concurrent users efficiently', async () => {
      const concurrentUsers = 10;
      const testDuration = 5; // seconds
      
      const testFunction = async (): Promise<number> => {
        const startTime = performance.now();
        await request.get('/api/dashboard/flexible-kpis?kpis=Net%20Revenue').expect(200);
        const endTime = performance.now();
        return endTime - startTime;
      };

      const metrics = await loadTester.runLoadTest(testFunction, concurrentUsers, testDuration);

      console.log(`10 Concurrent Users Test Results:`);
      console.log(`Total Requests: ${metrics.totalRequests}`);
      console.log(`Successful Requests: ${metrics.successfulRequests}`);
      console.log(`Failed Requests: ${metrics.failedRequests}`);
      console.log(`Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms`);
      console.log(`Throughput: ${metrics.throughput.toFixed(2)} requests/sec`);
      console.log(`Error Rate: ${metrics.errorRate.toFixed(2)}%`);

      // Performance assertions for 10 users
      PerformanceAssertions.expectResponseTime(metrics.averageResponseTime, 1000);
      PerformanceAssertions.expectThroughput(metrics.throughput, 5); // At least 5 req/sec
      PerformanceAssertions.expectErrorRate(metrics.errorRate, 5); // Less than 5% errors

      globalPerformanceUtils.benchmarkManager.addBenchmark('load-test-10-users', {
        totalRequests: metrics.totalRequests,
        averageResponseTime: metrics.averageResponseTime,
        throughput: metrics.throughput,
        errorRate: metrics.errorRate
      });
    });

    it('should handle 50 concurrent users with acceptable performance', async () => {
      const concurrentUsers = 50;
      const testDuration = 10; // seconds
      
      const testFunction = async (): Promise<number> => {
        const startTime = performance.now();
        
        // Mix different endpoints to simulate real usage
        const endpoints = [
          '/api/dashboard/flexible-kpis?kpis=Net%20Revenue',
          '/api/dashboard/flexible-kpis?kpis=Gross%20Margin',
          '/api/brand-deep-dive?brand=1',
          '/api/campaigns?limit=50'
        ];
        
        const randomEndpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
        await request.get(randomEndpoint);
        
        const endTime = performance.now();
        return endTime - startTime;
      };

      const metrics = await loadTester.runLoadTest(testFunction, concurrentUsers, testDuration);

      console.log(`50 Concurrent Users Test Results:`);
      console.log(`Total Requests: ${metrics.totalRequests}`);
      console.log(`Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms`);
      console.log(`Max Response Time: ${metrics.maxResponseTime.toFixed(2)}ms`);
      console.log(`Throughput: ${metrics.throughput.toFixed(2)} requests/sec`);
      console.log(`Error Rate: ${metrics.errorRate.toFixed(2)}%`);

      // Performance assertions for 50 users (more relaxed)
      PerformanceAssertions.expectResponseTime(metrics.averageResponseTime, 2000, 0.5); // 50% tolerance
      PerformanceAssertions.expectThroughput(metrics.throughput, 10); // At least 10 req/sec
      PerformanceAssertions.expectErrorRate(metrics.errorRate, 10); // Less than 10% errors

      globalPerformanceUtils.benchmarkManager.addBenchmark('load-test-50-users', {
        totalRequests: metrics.totalRequests,
        averageResponseTime: metrics.averageResponseTime,
        maxResponseTime: metrics.maxResponseTime,
        throughput: metrics.throughput,
        errorRate: metrics.errorRate
      });
    });

    it('should handle 100 concurrent users with graceful degradation', async () => {
      const concurrentUsers = 100;
      const testDuration = 15; // seconds
      
      const testFunction = async (): Promise<number> => {
        const startTime = performance.now();
        
        try {
          await request.get('/api/dashboard/flexible-kpis?kpis=Net%20Revenue,Gross%20Margin,Adspend');
          const endTime = performance.now();
          return endTime - startTime;
        } catch (error) {
          // Return a high response time for failed requests
          const endTime = performance.now();
          return endTime - startTime;
        }
      };

      const metrics = await loadTester.runLoadTest(testFunction, concurrentUsers, testDuration);

      console.log(`100 Concurrent Users Test Results:`);
      console.log(`Total Requests: ${metrics.totalRequests}`);
      console.log(`Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms`);
      console.log(`Max Response Time: ${metrics.maxResponseTime.toFixed(2)}ms`);
      console.log(`Throughput: ${metrics.throughput.toFixed(2)} requests/sec`);
      console.log(`Error Rate: ${metrics.errorRate.toFixed(2)}%`);

      // Performance assertions for 100 users (stress test - more relaxed)
      PerformanceAssertions.expectResponseTime(metrics.averageResponseTime, 5000, 1.0); // 100% tolerance
      PerformanceAssertions.expectThroughput(metrics.throughput, 5); // At least 5 req/sec under stress
      PerformanceAssertions.expectErrorRate(metrics.errorRate, 20); // Less than 20% errors under stress

      globalPerformanceUtils.benchmarkManager.addBenchmark('load-test-100-users', {
        totalRequests: metrics.totalRequests,
        averageResponseTime: metrics.averageResponseTime,
        maxResponseTime: metrics.maxResponseTime,
        throughput: metrics.throughput,
        errorRate: metrics.errorRate
      });
    });
  });

  describe('API Endpoint Stress Testing', () => {
    it('should handle rapid successive requests to flexible-kpis endpoint', async () => {
      const rapidRequests = 50;
      const promises: Promise<any>[] = [];
      
      const startTime = performance.now();
      
      for (let i = 0; i < rapidRequests; i++) {
        promises.push(
          request.get('/api/dashboard/flexible-kpis?kpis=Net%20Revenue')
            .timeout(5000)
            .catch(err => ({ error: err.message }))
        );
      }
      
      const results = await Promise.allSettled(promises);
      const endTime = performance.now();
      
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      const totalTime = endTime - startTime;
      const throughput = rapidRequests / (totalTime / 1000);
      
      console.log(`Rapid Requests Test Results:`);
      console.log(`Total Requests: ${rapidRequests}`);
      console.log(`Successful: ${successful}`);
      console.log(`Failed: ${failed}`);
      console.log(`Total Time: ${totalTime.toFixed(2)}ms`);
      console.log(`Throughput: ${throughput.toFixed(2)} requests/sec`);
      
      // Should handle most requests successfully
      expect(successful).toBeGreaterThan(rapidRequests * 0.8); // At least 80% success
      PerformanceAssertions.expectThroughput(throughput, 5);
    });

    it('should handle mixed endpoint load efficiently', async () => {
      const testDuration = 8; // seconds
      const concurrentUsers = 25;
      
      const endpoints = [
        { path: '/api/dashboard/flexible-kpis?kpis=Net%20Revenue', weight: 0.4 },
        { path: '/api/dashboard/flexible-kpis?kpis=Gross%20Margin,Adspend', weight: 0.3 },
        { path: '/api/brand-deep-dive?brand=1', weight: 0.2 },
        { path: '/api/campaigns?limit=20', weight: 0.1 }
      ];
      
      const testFunction = async (): Promise<number> => {
        const random = Math.random();
        let cumulativeWeight = 0;
        let selectedEndpoint = endpoints[0].path;
        
        for (const endpoint of endpoints) {
          cumulativeWeight += endpoint.weight;
          if (random <= cumulativeWeight) {
            selectedEndpoint = endpoint.path;
            break;
          }
        }
        
        const startTime = performance.now();
        await request.get(selectedEndpoint);
        const endTime = performance.now();
        return endTime - startTime;
      };

      const metrics = await loadTester.runLoadTest(testFunction, concurrentUsers, testDuration);

      console.log(`Mixed Endpoint Load Test Results:`);
      console.log(`Total Requests: ${metrics.totalRequests}`);
      console.log(`Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms`);
      console.log(`Throughput: ${metrics.throughput.toFixed(2)} requests/sec`);
      console.log(`Error Rate: ${metrics.errorRate.toFixed(2)}%`);

      // Mixed load should perform well
      PerformanceAssertions.expectResponseTime(metrics.averageResponseTime, 1500);
      PerformanceAssertions.expectThroughput(metrics.throughput, 8);
      PerformanceAssertions.expectErrorRate(metrics.errorRate, 5);
    });
  });

  describe('Memory and Resource Stress Testing', () => {
    it('should handle large dataset requests without memory leaks', async () => {
      const initialMemory = globalPerformanceUtils.memoryProfiler.getCurrentMemoryUsage();
      
      // Simulate requests for large datasets
      const largeDataRequests = 20;
      const promises: Promise<any>[] = [];
      
      for (let i = 0; i < largeDataRequests; i++) {
        promises.push(
          request.get('/api/dashboard/flexible-kpis?kpis=Net%20Revenue,Gross%20Margin,Adspend,Contribution%20Margin&groupByDimension=brand&groupByTime=day')
            .timeout(10000)
        );
      }
      
      await Promise.all(promises);
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = globalPerformanceUtils.memoryProfiler.getCurrentMemoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      console.log(`Memory Stress Test Results:`);
      console.log(`Initial Memory: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
      console.log(`Final Memory: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
      console.log(`Memory Increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
      
      // Memory increase should be reasonable (less than 100MB for this test)
      PerformanceAssertions.expectMemoryUsage(memoryIncrease, 100);
    });

    it('should recover gracefully from high load scenarios', async () => {
      // Phase 1: High load
      console.log('Starting high load phase...');
      const highLoadMetrics = await loadTester.runLoadTest(
        async () => {
          const startTime = performance.now();
          await request.get('/api/dashboard/flexible-kpis?kpis=Net%20Revenue');
          const endTime = performance.now();
          return endTime - startTime;
        },
        75, // 75 concurrent users
        5   // for 5 seconds
      );
      
      // Phase 2: Cool down period
      console.log('Cooling down...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Phase 3: Normal load to test recovery
      console.log('Testing recovery with normal load...');
      const recoveryMetrics = await loadTester.runLoadTest(
        async () => {
          const startTime = performance.now();
          await request.get('/api/dashboard/flexible-kpis?kpis=Net%20Revenue');
          const endTime = performance.now();
          return endTime - startTime;
        },
        10, // 10 concurrent users
        3   // for 3 seconds
      );
      
      console.log(`High Load Phase - Avg Response: ${highLoadMetrics.averageResponseTime.toFixed(2)}ms`);
      console.log(`Recovery Phase - Avg Response: ${recoveryMetrics.averageResponseTime.toFixed(2)}ms`);
      
      // Recovery should show improved performance
      expect(recoveryMetrics.averageResponseTime).toBeLessThan(highLoadMetrics.averageResponseTime * 0.8);
      PerformanceAssertions.expectResponseTime(recoveryMetrics.averageResponseTime, 1000);
    });
  });

  describe('Database Load Testing', () => {
    it('should handle concurrent database queries efficiently', async () => {
      const isAvailable = await isRedshiftAvailable();
      if (!isAvailable) {
        console.warn('Skipping database load test: Redshift not available');
        return;
      }

      // Simulate concurrent database-heavy operations
      const dbTestFunction = async (): Promise<number> => {
        const startTime = performance.now();
        
        // Test endpoints that would hit the database
        const dbEndpoints = [
          '/api/dashboard/flexible-kpis?kpis=Net%20Revenue&startDate=2025-01-01&endDate=2025-01-31',
          '/api/dashboard/flexible-kpis?kpis=Gross%20Margin&groupByDimension=brand',
          '/api/brand-deep-dive?brand=1&startDate=2025-01-01&endDate=2025-01-31'
        ];
        
        const randomEndpoint = dbEndpoints[Math.floor(Math.random() * dbEndpoints.length)];
        await request.get(randomEndpoint);
        
        const endTime = performance.now();
        return endTime - startTime;
      };

      const dbMetrics = await loadTester.runLoadTest(dbTestFunction, 20, 10);

      console.log(`Database Load Test Results:`);
      console.log(`Total Requests: ${dbMetrics.totalRequests}`);
      console.log(`Average Response Time: ${dbMetrics.averageResponseTime.toFixed(2)}ms`);
      console.log(`Throughput: ${dbMetrics.throughput.toFixed(2)} requests/sec`);
      console.log(`Error Rate: ${dbMetrics.errorRate.toFixed(2)}%`);

      // Database operations should still be reasonably fast under load
      PerformanceAssertions.expectResponseTime(dbMetrics.averageResponseTime, 2000);
      PerformanceAssertions.expectErrorRate(dbMetrics.errorRate, 5);
    });
  });

  describe('Load Testing Summary and Analysis', () => {
    it('should generate comprehensive load testing report', async () => {
      const allBenchmarks = globalPerformanceUtils.benchmarkManager.getBenchmarks();
      const loadTestBenchmarks = allBenchmarks.filter(b => b.name.includes('load-test'));
      
      const loadTestReport = {
        timestamp: new Date().toISOString(),
        totalLoadTests: loadTestBenchmarks.length,
        testResults: loadTestBenchmarks.reduce((acc, benchmark) => {
          acc[benchmark.name] = benchmark.metrics;
          return acc;
        }, {} as Record<string, any>),
        scalabilityAnalysis: {
          users10: loadTestBenchmarks.find(b => b.name.includes('10-users'))?.metrics,
          users50: loadTestBenchmarks.find(b => b.name.includes('50-users'))?.metrics,
          users100: loadTestBenchmarks.find(b => b.name.includes('100-users'))?.metrics
        },
        performanceTrends: {
          responseTimeIncrease: calculateResponseTimeIncrease(loadTestBenchmarks),
          throughputDecrease: calculateThroughputDecrease(loadTestBenchmarks),
          errorRateIncrease: calculateErrorRateIncrease(loadTestBenchmarks)
        }
      };

      console.log('Load Testing Report:', JSON.stringify(loadTestReport, null, 2));

      // Verify we have meaningful load test data
      expect(loadTestReport.totalLoadTests).toBeGreaterThan(0);
      expect(loadTestReport.scalabilityAnalysis.users10).toBeDefined();
      
      // Performance should degrade gracefully with increased load
      if (loadTestReport.scalabilityAnalysis.users10 && loadTestReport.scalabilityAnalysis.users50) {
        const responseTime10 = loadTestReport.scalabilityAnalysis.users10.averageResponseTime;
        const responseTime50 = loadTestReport.scalabilityAnalysis.users50.averageResponseTime;
        
        // Response time should not increase more than 3x from 10 to 50 users
        expect(responseTime50).toBeLessThan(responseTime10 * 3);
      }
    });
  });
});

// Helper functions for performance analysis
function calculateResponseTimeIncrease(benchmarks: any[]): number {
  const users10 = benchmarks.find(b => b.name.includes('10-users'));
  const users100 = benchmarks.find(b => b.name.includes('100-users'));
  
  if (!users10 || !users100) return 0;
  
  return ((users100.metrics.averageResponseTime - users10.metrics.averageResponseTime) / users10.metrics.averageResponseTime) * 100;
}

function calculateThroughputDecrease(benchmarks: any[]): number {
  const users10 = benchmarks.find(b => b.name.includes('10-users'));
  const users100 = benchmarks.find(b => b.name.includes('100-users'));
  
  if (!users10 || !users100) return 0;
  
  return ((users10.metrics.throughput - users100.metrics.throughput) / users10.metrics.throughput) * 100;
}

function calculateErrorRateIncrease(benchmarks: any[]): number {
  const users10 = benchmarks.find(b => b.name.includes('10-users'));
  const users100 = benchmarks.find(b => b.name.includes('100-users'));
  
  if (!users10 || !users100) return 0;
  
  return users100.metrics.errorRate - users10.metrics.errorRate;
}