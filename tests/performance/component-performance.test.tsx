/**
 * Component Performance Tests
 * 
 * Tests performance of critical components including chart rendering,
 * slide view performance, brand analysis, and data table performance.
 */

import {
  PerformanceAssertions,
  createComponentPerformanceMeasurer,
  globalPerformanceUtils
} from '@/tests/utils/performance-utils';
import {
  cleanupTestEnvironment,
  initializeTestEnvironment
} from '@/tests/utils/test-setup';
import { render, screen, waitFor } from '@testing-library/react';

import React from 'react';
import { performance } from 'perf_hooks';

// Mock Next.js and auth
jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: jest.fn(), replace: jest.fn() }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/test',
}));

jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: { user: { id: 'test', brands: [1, 2, 3], isSuperAdmin: true } },
    status: 'authenticated'
  }),
}));

// Mock chart data hook
jest.mock('@/components/ecommerce-charts/use-chart-data', () => ({
  useChartData: () => ({
    chartData: {
      revenue: Array.from({ length: 100 }, (_, i) => ({
        date: `2025-01-${i + 1}`,
        value: Math.random() * 10000
      })),
      margin: Array.from({ length: 100 }, (_, i) => ({
        date: `2025-01-${i + 1}`,
        value: Math.random() * 0.5
      }))
    },
    loading: false,
    error: null
  })
}));

// Mock contexts
jest.mock('@/lib/contexts/brand-deep-dive-context', () => ({
  useBrandDeepDive: () => ({
    state: {
      selectedBrand: 1,
      startDate: '2025-01-01',
      endDate: '2025-01-31',
      currency: 'USD',
      groupBy: 'day',
      salesChannels: [],
      countryNames: []
    },
    actions: {
      setSelectedBrand: jest.fn(),
      setDateRange: jest.fn()
    }
  })
}));

jest.mock('@/lib/contexts/filter-context', () => ({
  useFilters: () => ({
    filters: {
      startDate: '2025-01-01',
      endDate: '2025-01-31',
      currency: 'USD',
      brands: [1, 2, 3],
      salesChannels: [],
      countryNames: []
    },
    getQueryParams: () => 'startDate=2025-01-01&endDate=2025-01-31'
  })
}));

// Mock chart libraries
jest.mock('recharts', () => {
  const mockReact = require('react');
  return {
    ResponsiveContainer: ({ children }: { children: any }) => 
      mockReact.createElement('div', { 'data-testid': 'responsive-container' }, children),
    LineChart: ({ children }: { children: any }) => 
      mockReact.createElement('div', { 'data-testid': 'line-chart' }, children),
    Line: () => mockReact.createElement('div', { 'data-testid': 'line' }),
    XAxis: () => mockReact.createElement('div', { 'data-testid': 'x-axis' }),
    YAxis: () => mockReact.createElement('div', { 'data-testid': 'y-axis' }),
    CartesianGrid: () => mockReact.createElement('div', { 'data-testid': 'grid' }),
    Tooltip: () => mockReact.createElement('div', { 'data-testid': 'tooltip' }),
    Legend: () => mockReact.createElement('div', { 'data-testid': 'legend' }),
    BarChart: ({ children }: { children: any }) => 
      mockReact.createElement('div', { 'data-testid': 'bar-chart' }, children),
    Bar: () => mockReact.createElement('div', { 'data-testid': 'bar' }),
  };
});

describe('Component Performance Tests', () => {
  const performanceMeasurer = createComponentPerformanceMeasurer();

  beforeAll(async () => {
    await initializeTestEnvironment();
  });

  afterAll(async () => {
    await cleanupTestEnvironment();
  });

  beforeEach(() => {
    globalPerformanceUtils.measurer.reset();
    globalPerformanceUtils.memoryProfiler.reset();
  });

  describe('Ecommerce Charts Performance (SLA: < 2 seconds)', () => {
    it('should render ecommerce charts index within 2 seconds', async () => {
      // Dynamic import to avoid module loading issues
      const { EcommerceCharts } = await import('@/components/ecommerce-charts/index');
      
      const metrics = await performanceMeasurer.measureAsync(
        'ecommerce-charts-render',
        async () => {
          render(<EcommerceCharts contextType="dashboard" />);
          
          await waitFor(() => {
            // Check for tab structure which should be present
            const tabsList = screen.getByRole('tablist');
            expect(tabsList).toBeInTheDocument();
          }, { timeout: 2000 });
        }
      );

      console.log(`Ecommerce Charts render time: ${metrics.duration.toFixed(2)}ms`);
      console.log(`Memory usage: ${globalPerformanceUtils.memoryProfiler.formatMemoryUsage(metrics.memoryUsage!)}`);

      // SLA: Chart rendering < 2 seconds
      PerformanceAssertions.expectResponseTime(metrics.duration, 2000);
      
      // Memory usage should be reasonable
      PerformanceAssertions.expectMemoryUsage(metrics.memoryUsage!.heapUsed, 100);

      globalPerformanceUtils.benchmarkManager.addBenchmark('ecommerce-charts-render', {
        duration: metrics.duration,
        memoryUsed: metrics.memoryUsage!.heapUsed / 1024 / 1024
      });
    });

    it('should handle large datasets efficiently', async () => {
      const { EcommerceCharts } = await import('@/components/ecommerce-charts/index');
      
      // Mock large dataset
      jest.doMock('@/components/ecommerce-charts/use-chart-data', () => ({
        useChartData: () => ({
          chartData: {
            revenue: Array.from({ length: 1000 }, (_, i) => ({
              date: `2025-01-${(i % 30) + 1}`,
              value: Math.random() * 10000
            })),
            margin: Array.from({ length: 1000 }, (_, i) => ({
              date: `2025-01-${(i % 30) + 1}`,
              value: Math.random() * 0.5
            }))
          },
          loading: false,
          error: null
        })
      }));

      const metrics = await performanceMeasurer.measureAsync(
        'ecommerce-charts-large-dataset',
        async () => {
          render(<EcommerceCharts contextType="dashboard" />);
          
          await waitFor(() => {
            const tabsList = screen.getByRole('tablist');
            expect(tabsList).toBeInTheDocument();
          }, { timeout: 3000 });
        }
      );

      console.log(`Ecommerce Charts large dataset render time: ${metrics.duration.toFixed(2)}ms`);
      
      // Should still be within 2 seconds even with large datasets
      PerformanceAssertions.expectResponseTime(metrics.duration, 2000);
      
      // Memory usage might be higher but should be reasonable
      PerformanceAssertions.expectMemoryUsage(metrics.memoryUsage!.heapUsed, 200);
    });
  });

  describe('Executive Summary Slide View Performance (SLA: < 2 seconds)', () => {
    it('should render slide view components within 2 seconds', async () => {
      // Create a mock slide view component
      const MockSlideView = () => {
        return (
          <div data-testid="slide-view">
            <div data-testid="slide-header">Executive Summary</div>
            <div data-testid="slide-content">
              {Array.from({ length: 50 }, (_, i) => (
                <div key={i} data-testid={`slide-item-${i}`}>
                  Slide Content Item {i}
                </div>
              ))}
            </div>
            <div data-testid="slide-navigation">Navigation</div>
          </div>
        );
      };

      const metrics = await performanceMeasurer.measureAsync(
        'slide-view-render',
        async () => {
          render(<MockSlideView />);
          
          await waitFor(() => {
            expect(screen.getByTestId('slide-view')).toBeInTheDocument();
            expect(screen.getByTestId('slide-header')).toBeInTheDocument();
            expect(screen.getByTestId('slide-content')).toBeInTheDocument();
            expect(screen.getByTestId('slide-navigation')).toBeInTheDocument();
          });
        }
      );

      console.log(`Slide View render time: ${metrics.duration.toFixed(2)}ms`);
      
      // SLA: Slide rendering < 2 seconds
      PerformanceAssertions.expectResponseTime(metrics.duration, 2000);
      
      globalPerformanceUtils.benchmarkManager.addBenchmark('slide-view-render', {
        duration: metrics.duration,
        memoryUsed: metrics.memoryUsage!.heapUsed / 1024 / 1024
      });
    });

    it('should handle slide transitions efficiently', async () => {
      const MockSlideContainer = () => {
        const [currentSlide, setCurrentSlide] = React.useState(0);
        const slides = Array.from({ length: 10 }, (_, i) => `Slide ${i + 1}`);

        React.useEffect(() => {
          // Simulate slide transitions
          const interval = setInterval(() => {
            setCurrentSlide(prev => (prev + 1) % slides.length);
          }, 100);
          
          return () => clearInterval(interval);
        }, [slides.length]);

        return (
          <div data-testid="slide-container">
            <div data-testid="current-slide">{slides[currentSlide]}</div>
            <div data-testid="slide-indicators">
              {slides.map((_, i) => (
                <div key={i} data-testid={`indicator-${i}`} className={i === currentSlide ? 'active' : ''}>
                  {i + 1}
                </div>
              ))}
            </div>
          </div>
        );
      };

      const metrics = await performanceMeasurer.measureAsync(
        'slide-transitions',
        async () => {
          render(<MockSlideContainer />);
          
          await waitFor(() => {
            expect(screen.getByTestId('slide-container')).toBeInTheDocument();
          });
          
          // Wait for some transitions to occur
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      );

      console.log(`Slide transitions time: ${metrics.duration.toFixed(2)}ms`);
      
      // Slide transitions should be smooth and fast
      PerformanceAssertions.expectResponseTime(metrics.duration, 1000);
    });
  });

  describe('Brand Deep Dive Performance (SLA: < 3 seconds)', () => {
    it('should render brand deep dive client within 3 seconds', async () => {
      // Mock brand deep dive client
      const MockBrandDeepDiveClient = () => {
        return (
          <div data-testid="brand-deep-dive">
            <div data-testid="brand-selector">Brand Selector</div>
            <div data-testid="kpi-cards">
              {Array.from({ length: 8 }, (_, i) => (
                <div key={i} data-testid={`kpi-card-${i}`}>
                  KPI Card {i}
                </div>
              ))}
            </div>
            <div data-testid="charts-section">
              <div data-testid="brand-charts">Brand Charts</div>
            </div>
            <div data-testid="marketing-campaigns">Marketing Campaigns</div>
          </div>
        );
      };

      const metrics = await performanceMeasurer.measureAsync(
        'brand-deep-dive-render',
        async () => {
          render(<MockBrandDeepDiveClient />);
          
          await waitFor(() => {
            expect(screen.getByTestId('brand-deep-dive')).toBeInTheDocument();
            expect(screen.getByTestId('brand-selector')).toBeInTheDocument();
            expect(screen.getByTestId('kpi-cards')).toBeInTheDocument();
            expect(screen.getByTestId('charts-section')).toBeInTheDocument();
            expect(screen.getByTestId('marketing-campaigns')).toBeInTheDocument();
          });
        }
      );

      console.log(`Brand Deep Dive render time: ${metrics.duration.toFixed(2)}ms`);
      
      // SLA: Brand analysis < 3 seconds
      PerformanceAssertions.expectResponseTime(metrics.duration, 3000);
      
      globalPerformanceUtils.benchmarkManager.addBenchmark('brand-deep-dive-render', {
        duration: metrics.duration,
        memoryUsed: metrics.memoryUsage!.heapUsed / 1024 / 1024
      });
    });
  });

  describe('Executive Summary Client Performance (SLA: < 3 seconds)', () => {
    it('should render executive summary client within 3 seconds', async () => {
      // Mock executive summary client
      const MockExecutiveSummaryClient = () => {
        return (
          <div data-testid="executive-summary">
            <div data-testid="report-header">Executive Summary Report</div>
            <div data-testid="period-selector">Period Selector</div>
            <div data-testid="kpi-grid">
              {Array.from({ length: 12 }, (_, i) => (
                <div key={i} data-testid={`kpi-${i}`}>
                  KPI {i}
                </div>
              ))}
            </div>
            <div data-testid="trend-section">Trend Analysis</div>
            <div data-testid="data-insights">Data Insights</div>
            <div data-testid="pdf-export">PDF Export</div>
          </div>
        );
      };

      const metrics = await performanceMeasurer.measureAsync(
        'executive-summary-render',
        async () => {
          render(<MockExecutiveSummaryClient />);
          
          await waitFor(() => {
            expect(screen.getByTestId('executive-summary')).toBeInTheDocument();
            expect(screen.getByTestId('report-header')).toBeInTheDocument();
            expect(screen.getByTestId('kpi-grid')).toBeInTheDocument();
            expect(screen.getByTestId('trend-section')).toBeInTheDocument();
          });
        }
      );

      console.log(`Executive Summary render time: ${metrics.duration.toFixed(2)}ms`);
      
      // SLA: Executive summary < 3 seconds
      PerformanceAssertions.expectResponseTime(metrics.duration, 3000);
      
      globalPerformanceUtils.benchmarkManager.addBenchmark('executive-summary-render', {
        duration: metrics.duration,
        memoryUsed: metrics.memoryUsage!.heapUsed / 1024 / 1024
      });
    });
  });

  describe('Data Table Performance (SLA: < 2 seconds with 1000+ records)', () => {
    it('should render data table with large dataset within 2 seconds', async () => {
      // Mock data table with large dataset
      const MockDataTable = () => {
        const data = Array.from({ length: 1200 }, (_, i) => ({
          id: i,
          name: `Item ${i}`,
          value: Math.random() * 10000,
          category: `Category ${i % 10}`,
          date: new Date(2025, 0, (i % 30) + 1).toISOString().split('T')[0],
          status: i % 3 === 0 ? 'Active' : i % 3 === 1 ? 'Pending' : 'Inactive'
        }));

        // Simulate virtualization by only rendering visible rows
        const visibleRows = data.slice(0, 50);

        return (
          <div data-testid="data-table">
            <div data-testid="table-header">
              <div>ID</div>
              <div>Name</div>
              <div>Value</div>
              <div>Category</div>
              <div>Date</div>
              <div>Status</div>
            </div>
            <div data-testid="table-body">
              {visibleRows.map(item => (
                <div key={item.id} data-testid={`row-${item.id}`}>
                  <div>{item.id}</div>
                  <div>{item.name}</div>
                  <div>{item.value.toFixed(2)}</div>
                  <div>{item.category}</div>
                  <div>{item.date}</div>
                  <div>{item.status}</div>
                </div>
              ))}
            </div>
            <div data-testid="table-pagination">
              Showing 1-50 of {data.length} items
            </div>
          </div>
        );
      };

      const metrics = await performanceMeasurer.measureAsync(
        'data-table-large-dataset',
        async () => {
          render(<MockDataTable />);
          
          await waitFor(() => {
            expect(screen.getByTestId('data-table')).toBeInTheDocument();
            expect(screen.getByTestId('table-header')).toBeInTheDocument();
            expect(screen.getByTestId('table-body')).toBeInTheDocument();
            expect(screen.getByTestId('table-pagination')).toBeInTheDocument();
          });
        }
      );

      console.log(`Data Table large dataset render time: ${metrics.duration.toFixed(2)}ms`);
      
      // SLA: Data table with 1000+ records < 2 seconds
      PerformanceAssertions.expectResponseTime(metrics.duration, 2000);
      
      // Memory usage should be reasonable even with large datasets
      PerformanceAssertions.expectMemoryUsage(metrics.memoryUsage!.heapUsed, 150);
      
      globalPerformanceUtils.benchmarkManager.addBenchmark('data-table-large-dataset', {
        duration: metrics.duration,
        memoryUsed: metrics.memoryUsage!.heapUsed / 1024 / 1024,
        recordCount: 1200
      });
    });

    it('should handle sorting and filtering efficiently', async () => {
      const MockSortableTable = () => {
        const [sortField, setSortField] = React.useState<string>('name');
        const [sortDirection, setSortDirection] = React.useState<'asc' | 'desc'>('asc');
        const [filter, setFilter] = React.useState<string>('');

        const data = Array.from({ length: 500 }, (_, i) => ({
          id: i,
          name: `Item ${i}`,
          value: Math.random() * 10000,
          category: `Category ${i % 5}`
        }));

        const filteredAndSorted = React.useMemo(() => {
          let result = data.filter(item => 
            item.name.toLowerCase().includes(filter.toLowerCase()) ||
            item.category.toLowerCase().includes(filter.toLowerCase())
          );

          result.sort((a, b) => {
            const aVal = a[sortField as keyof typeof a];
            const bVal = b[sortField as keyof typeof b];
            
            if (typeof aVal === 'string' && typeof bVal === 'string') {
              return sortDirection === 'asc' 
                ? aVal.localeCompare(bVal)
                : bVal.localeCompare(aVal);
            }
            
            if (typeof aVal === 'number' && typeof bVal === 'number') {
              return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
            }
            
            return 0;
          });

          return result.slice(0, 50); // Show first 50
        }, [data, sortField, sortDirection, filter]);

        return (
          <div data-testid="sortable-table">
            <input
              data-testid="filter-input"
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              placeholder="Filter..."
            />
            <div data-testid="table-controls">
              <button onClick={() => setSortField('name')}>Sort by Name</button>
              <button onClick={() => setSortField('value')}>Sort by Value</button>
              <button onClick={() => setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')}>
                Toggle Direction
              </button>
            </div>
            <div data-testid="filtered-results">
              {filteredAndSorted.map(item => (
                <div key={item.id} data-testid={`filtered-row-${item.id}`}>
                  {item.name} - {item.value.toFixed(2)} - {item.category}
                </div>
              ))}
            </div>
          </div>
        );
      };

      const metrics = await performanceMeasurer.measureAsync(
        'table-sorting-filtering',
        async () => {
          render(<MockSortableTable />);
          
          await waitFor(() => {
            expect(screen.getByTestId('sortable-table')).toBeInTheDocument();
            expect(screen.getByTestId('filter-input')).toBeInTheDocument();
            expect(screen.getByTestId('filtered-results')).toBeInTheDocument();
          });
        }
      );

      console.log(`Table sorting/filtering time: ${metrics.duration.toFixed(2)}ms`);
      
      // Sorting and filtering should be very fast
      PerformanceAssertions.expectResponseTime(metrics.duration, 1000);
    });
  });

  describe('Memory Management and Cleanup', () => {
    it('should properly clean up component resources', async () => {
      const MockComponentWithCleanup = () => {
        React.useEffect(() => {
          const interval = setInterval(() => {
            // Simulate some background work
          }, 100);

          const listener = () => {
            // Simulate event listener
          };
          
          window.addEventListener('resize', listener);

          return () => {
            clearInterval(interval);
            window.removeEventListener('resize', listener);
          };
        }, []);

        return <div data-testid="cleanup-component">Component with cleanup</div>;
      };

      const initialMemory = globalPerformanceUtils.memoryProfiler.getCurrentMemoryUsage();

      const metrics = await performanceMeasurer.measureAsync(
        'component-cleanup',
        async () => {
          const { unmount } = render(<MockComponentWithCleanup />);
          
          await waitFor(() => {
            expect(screen.getByTestId('cleanup-component')).toBeInTheDocument();
          });

          // Wait a bit for any background work
          await new Promise(resolve => setTimeout(resolve, 200));

          // Unmount the component
          unmount();
          
          // Force garbage collection if available
          if (global.gc) {
            global.gc();
          }
        }
      );

      const finalMemory = globalPerformanceUtils.memoryProfiler.getCurrentMemoryUsage();
      const memoryDelta = finalMemory.heapUsed - initialMemory.heapUsed;

      console.log(`Component cleanup time: ${metrics.duration.toFixed(2)}ms`);
      console.log(`Memory delta: ${(memoryDelta / 1024 / 1024).toFixed(2)}MB`);

      // Memory delta should be minimal after cleanup
      PerformanceAssertions.expectMemoryUsage(Math.abs(memoryDelta), 10); // Less than 10MB delta
    });
  });

  describe('Component Performance Summary', () => {
    it('should generate component performance report', async () => {
      const allBenchmarks = globalPerformanceUtils.benchmarkManager.getBenchmarks();
      
      const componentReport = {
        timestamp: new Date().toISOString(),
        totalComponentTests: allBenchmarks.length,
        componentBenchmarks: allBenchmarks.reduce((acc, benchmark) => {
          acc[benchmark.name] = {
            ...benchmark.metrics,
            environment: benchmark.environment
          };
          return acc;
        }, {} as Record<string, any>),
        slaCompliance: {
          chartRendering: allBenchmarks.some(b => 
            b.name.includes('chart') && b.metrics.duration < 2000
          ),
          slideView: allBenchmarks.some(b => 
            b.name.includes('slide') && b.metrics.duration < 2000
          ),
          brandDeepDive: allBenchmarks.some(b => 
            b.name.includes('brand-deep-dive') && b.metrics.duration < 3000
          ),
          executiveSummary: allBenchmarks.some(b => 
            b.name.includes('executive-summary') && b.metrics.duration < 3000
          ),
          dataTable: allBenchmarks.some(b => 
            b.name.includes('data-table') && b.metrics.duration < 2000
          )
        }
      };

      console.log('Component Performance Report:', JSON.stringify(componentReport, null, 2));

      // Verify we have meaningful component performance data
      expect(componentReport.totalComponentTests).toBeGreaterThan(0);
      
      // All component SLA requirements should be met
      Object.values(componentReport.slaCompliance).forEach(compliant => {
        if (typeof compliant === 'boolean') {
          expect(compliant).toBe(true);
        }
      });
    });
  });
});
