/**
 * Dashboard Performance Tests
 * 
 * Tests dashboard loading times, API response times, and chart rendering performance
 * according to the SLA requirements defined in the testing strategy.
 */

import {
  PerformanceAssertions,
  createComponentPerformanceMeasurer,
  globalPerformanceUtils,
  measureAPIPerformance
} from '@/tests/utils/performance-utils';
import {
  cleanupTestEnvironment,
  initializeTestEnvironment
} from '@/tests/utils/test-setup';
import { render, screen, waitFor } from '@testing-library/react';

import React from 'react';
import { createServer } from 'http';
import { isRedshiftAvailable } from '@/tests/utils/redshift-test-utils';
import { performance } from 'perf_hooks';
import supertest from 'supertest';

// Mock Next.js components and hooks
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    pathname: '/dashboard',
    query: {},
    asPath: '/dashboard',
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/dashboard',
}));

jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user',
        name: 'Test User',
        email: '<EMAIL>',
        brands: [1, 2, 3],
        isSuperAdmin: true,
      }
    },
    status: 'authenticated'
  }),
}));

// Mock chart components for performance testing
const MockEcommerceCharts = ({ contextType }: { contextType?: string }) => {
  const renderTime = Math.random() * 500 + 100; // 100-600ms
  
  return React.createElement('div', {
    'data-testid': 'ecommerce-charts',
    'data-render-time': renderTime,
    'data-context-type': contextType
  }, 'Ecommerce Charts Loaded');
};

jest.mock('@/components/ecommerce-charts', () => ({
  EcommerceCharts: MockEcommerceCharts
}));

// Mock API responses with realistic data
const mockFlexibleKPIsResponse = {
  'Net Revenue': {
    summary: { value: 1250000, previousValue: 1100000, change: 13.6 },
    timeSeries: Array.from({ length: 30 }, (_, i) => ({
      date: new Date(2025, 0, i + 1).toISOString().split('T')[0],
      value: Math.random() * 50000 + 30000
    }))
  },
  'Gross Margin': {
    summary: { value: 0.45, previousValue: 0.42, change: 7.1 },
    timeSeries: Array.from({ length: 30 }, (_, i) => ({
      date: new Date(2025, 0, i + 1).toISOString().split('T')[0],
      value: Math.random() * 0.1 + 0.4
    }))
  },
  'Adspend': {
    summary: { value: 125000, previousValue: 135000, change: -7.4 },
    timeSeries: Array.from({ length: 30 }, (_, i) => ({
      date: new Date(2025, 0, i + 1).toISOString().split('T')[0],
      value: Math.random() * 5000 + 3000
    }))
  }
};

// Create mock API server
function createMockAPIServer() {
  const handler = (req: any, res: any) => {
    const startTime = performance.now();
    
    // Simulate database query time based on endpoint complexity
    let queryTime = 100; // Base time
    
    if (req.url?.includes('/api/dashboard/flexible-kpis')) {
      // Simulate complex KPI calculations
      const url = new URL(`http://localhost${req.url}`);
      const kpis = url.searchParams.get('kpis')?.split(',') || [];
      const groupBy = url.searchParams.get('groupByDimension');
      const timeGroup = url.searchParams.get('groupByTime');
      
      queryTime = 200 + (kpis.length * 50); // Base + per KPI
      if (groupBy) queryTime += 100; // Grouping adds complexity
      if (timeGroup) queryTime += 150; // Time grouping adds complexity
    }
    
    // Simulate processing time
    setTimeout(() => {
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('X-Response-Time', totalTime.toString());
      res.setHeader('X-Query-Time', queryTime.toString());
      
      if (req.url?.includes('/api/dashboard/flexible-kpis')) {
        res.statusCode = 200;
        res.end(JSON.stringify(mockFlexibleKPIsResponse));
      } else {
        res.statusCode = 404;
        res.end(JSON.stringify({ error: 'Not found' }));
      }
    }, queryTime);
  };

  return createServer(handler);
}

describe('Dashboard Performance Tests', () => {
  let server: any;
  let request: supertest.SuperTest<supertest.Test>;
  const performanceMeasurer = createComponentPerformanceMeasurer();

  beforeAll(async () => {
    await initializeTestEnvironment();
    server = createMockAPIServer();
    request = supertest(server);
    
    // Reset performance benchmarks
    globalPerformanceUtils.benchmarkManager = new (require('@/tests/utils/performance-utils').BenchmarkManager)();
  });

  afterAll(async () => {
    await cleanupTestEnvironment();
    if (server) {
      server.close();
    }
  });

  beforeEach(() => {
    globalPerformanceUtils.measurer.reset();
    globalPerformanceUtils.memoryProfiler.reset();
  });

  describe('Dashboard Loading Performance (SLA: < 3 seconds)', () => {
    it('should load dashboard components within 3 seconds', async () => {
      const metrics = await performanceMeasurer.measureAsync(
        'dashboard-load',
        async () => {
          const DashboardComponent = () => React.createElement('div', {
            'data-testid': 'dashboard'
          }, [
            React.createElement('div', { 'data-testid': 'kpi-cards', key: 'kpi' }, 'KPI Cards'),
            React.createElement('div', { 'data-testid': 'charts-section', key: 'charts' }, 
              React.createElement('div', { 'data-testid': 'ecommerce-charts' }, 'Charts')
            )
          ]);
          
          render(React.createElement(DashboardComponent));
          
          await waitFor(() => {
            expect(screen.getByTestId('dashboard')).toBeInTheDocument();
            expect(screen.getByTestId('kpi-cards')).toBeInTheDocument();
            expect(screen.getByTestId('charts-section')).toBeInTheDocument();
          });
        }
      );

      console.log(`Dashboard load time: ${metrics.duration.toFixed(2)}ms`);
      console.log(`Memory usage: ${globalPerformanceUtils.memoryProfiler.formatMemoryUsage(metrics.memoryUsage!)}`);

      // SLA: Dashboard loading < 3 seconds
      PerformanceAssertions.expectResponseTime(metrics.duration, 3000);
      
      // Memory usage should be reasonable (< 50MB for dashboard load)
      PerformanceAssertions.expectMemoryUsage(metrics.memoryUsage!.heapUsed, 50);

      // Store benchmark
      globalPerformanceUtils.benchmarkManager.addBenchmark('dashboard-load', {
        duration: metrics.duration,
        memoryUsed: metrics.memoryUsage!.heapUsed / 1024 / 1024
      });
    });

    it('should load dashboard with large dataset within acceptable time', async () => {
      const metrics = await performanceMeasurer.measureAsync(
        'dashboard-large-dataset',
        async () => {
          // Simulate loading dashboard with 1000+ records
          const largeDataset = Array.from({ length: 1200 }, (_, i) => ({
            id: i,
            name: `Item ${i}`,
            value: Math.random() * 1000,
            date: new Date(2025, 0, (i % 30) + 1).toISOString()
          }));

          const LargeDashboardComponent = () => React.createElement('div', {
            'data-testid': 'dashboard-large'
          }, [
            React.createElement('div', { 'data-testid': 'data-table', key: 'table' },
              largeDataset.slice(0, 100).map(item => 
                React.createElement('div', {
                  key: item.id,
                  'data-testid': `row-${item.id}`
                }, `${item.name}: ${item.value}`)
              )
            )
          ]);
          
          render(React.createElement(LargeDashboardComponent));
          
          await waitFor(() => {
            expect(screen.getByTestId('dashboard-large')).toBeInTheDocument();
            expect(screen.getByTestId('data-table')).toBeInTheDocument();
          });
        }
      );

      console.log(`Dashboard large dataset load time: ${metrics.duration.toFixed(2)}ms`);
      
      // Should still be within 3 seconds for large datasets
      PerformanceAssertions.expectResponseTime(metrics.duration, 3000);
      
      // Memory usage should be reasonable even with large datasets
      PerformanceAssertions.expectMemoryUsage(metrics.memoryUsage!.heapUsed, 100);
    });
  });

  describe('API Response Performance (SLA: < 1 second)', () => {
    it('should respond to flexible-kpis endpoint within 1 second', async () => {
      const endpoint = '/api/dashboard/flexible-kpis?kpis=Net%20Revenue';
      
      const metrics = await measureAPIPerformance(
        endpoint,
        'GET',
        () => request.get(endpoint).expect(200)
      );

      console.log(`API response time: ${metrics.duration.toFixed(2)}ms`);
      console.log(`Status code: ${metrics.statusCode}`);

      // SLA: API responses < 1 second
      PerformanceAssertions.expectResponseTime(metrics.duration, 1000);
      
      // Store benchmark
      globalPerformanceUtils.benchmarkManager.addBenchmark('api-flexible-kpis-simple', {
        duration: metrics.duration,
        statusCode: metrics.statusCode
      });
    });

    it('should handle complex KPI queries within acceptable time', async () => {
      const endpoint = '/api/dashboard/flexible-kpis?kpis=Net%20Revenue,Gross%20Margin,Adspend,Contribution%20Margin&groupByDimension=brand&groupByTime=week';
      
      const metrics = await measureAPIPerformance(
        endpoint,
        'GET',
        () => request.get(endpoint).expect(200)
      );

      console.log(`Complex API response time: ${metrics.duration.toFixed(2)}ms`);

      // Complex queries may take longer but should still be under 1 second
      PerformanceAssertions.expectResponseTime(metrics.duration, 1000);
      
      globalPerformanceUtils.benchmarkManager.addBenchmark('api-flexible-kpis-complex', {
        duration: metrics.duration,
        statusCode: metrics.statusCode
      });
    });

    it('should handle concurrent API requests efficiently', async () => {
      const concurrentRequests = 10;
      const endpoint = '/api/dashboard/flexible-kpis?kpis=Net%20Revenue,Gross%20Margin';
      
      const startTime = performance.now();
      
      const promises = Array.from({ length: concurrentRequests }, () =>
        measureAPIPerformance(
          endpoint,
          'GET',
          () => request.get(endpoint).expect(200)
        )
      );
      
      const results = await Promise.all(promises);
      const endTime = performance.now();
      
      const totalTime = endTime - startTime;
      const averageResponseTime = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
      const maxResponseTime = Math.max(...results.map(r => r.duration));
      
      console.log(`Concurrent requests total time: ${totalTime.toFixed(2)}ms`);
      console.log(`Average response time: ${averageResponseTime.toFixed(2)}ms`);
      console.log(`Max response time: ${maxResponseTime.toFixed(2)}ms`);
      
      // All individual requests should be under 1 second
      results.forEach((result) => {
        PerformanceAssertions.expectResponseTime(result.duration, 1000, 0.2); // 20% tolerance for concurrent load
      });
      
      // Average should be well under the limit
      PerformanceAssertions.expectResponseTime(averageResponseTime, 800);
    });
  });

  describe('Chart Rendering Performance (SLA: < 2 seconds)', () => {
    it('should render ecommerce charts within 2 seconds', async () => {
      const metrics = await performanceMeasurer.measureAsync(
        'chart-rendering',
        async () => {
          render(React.createElement(MockEcommerceCharts, { contextType: 'dashboard' }));
          
          await waitFor(() => {
            const chartElement = screen.getByTestId('ecommerce-charts');
            expect(chartElement).toBeInTheDocument();
          });
        }
      );

      console.log(`Chart rendering time: ${metrics.duration.toFixed(2)}ms`);
      
      // SLA: Chart rendering < 2 seconds
      PerformanceAssertions.expectResponseTime(metrics.duration, 2000);
      
      globalPerformanceUtils.benchmarkManager.addBenchmark('chart-rendering', {
        duration: metrics.duration,
        memoryUsed: metrics.memoryUsage!.heapUsed / 1024 / 1024
      });
    });

    it('should render multiple charts efficiently', async () => {
      const metrics = await performanceMeasurer.measureAsync(
        'multiple-charts',
        async () => {
          const MultipleChartsComponent = () => React.createElement('div', {}, [
            React.createElement(MockEcommerceCharts, { contextType: 'dashboard', key: 'chart1' }),
            React.createElement(MockEcommerceCharts, { contextType: 'brandDeepDive', key: 'chart2' }),
            React.createElement('div', { 'data-testid': 'additional-charts', key: 'additional' }, [
              React.createElement('div', { key: 'revenue' }, 'Revenue Chart'),
              React.createElement('div', { key: 'margin' }, 'Margin Chart'),
              React.createElement('div', { key: 'cost' }, 'Cost Chart')
            ])
          ]);
          
          render(React.createElement(MultipleChartsComponent));
          
          await waitFor(() => {
            expect(screen.getAllByTestId('ecommerce-charts')).toHaveLength(2);
            expect(screen.getByTestId('additional-charts')).toBeInTheDocument();
          });
        }
      );

      console.log(`Multiple charts rendering time: ${metrics.duration.toFixed(2)}ms`);
      
      // Multiple charts should still render within 2 seconds
      PerformanceAssertions.expectResponseTime(metrics.duration, 2000);
    });
  });

  describe('Database Query Performance (SLA: < 500ms)', () => {
    it('should execute simple KPI queries within 500ms', async () => {
      const isAvailable = await isRedshiftAvailable();
      if (!isAvailable) {
        console.warn('Skipping database performance test: Redshift not available');
        return;
      }

      // This would test actual database queries in a real environment
      // For now, we simulate the query time based on our mock API
      const endpoint = '/api/dashboard/flexible-kpis?kpis=Net%20Revenue';
      
      const response = await request.get(endpoint);
      const queryTime = parseFloat(response.headers['x-query-time'] || '0');
      
      console.log(`Database query time: ${queryTime.toFixed(2)}ms`);
      
      // SLA: Database queries < 500ms
      PerformanceAssertions.expectResponseTime(queryTime, 500);
      
      globalPerformanceUtils.benchmarkManager.addBenchmark('db-query-simple', {
        queryTime,
        statusCode: response.status
      });
    });

    it('should handle complex aggregation queries efficiently', async () => {
      const isAvailable = await isRedshiftAvailable();
      if (!isAvailable) {
        console.warn('Skipping database performance test: Redshift not available');
        return;
      }

      const endpoint = '/api/dashboard/flexible-kpis?kpis=Net%20Revenue,Gross%20Margin,Adspend&groupByDimension=brand&groupByTime=month';
      
      const response = await request.get(endpoint);
      const queryTime = parseFloat(response.headers['x-query-time'] || '0');
      
      console.log(`Complex database query time: ${queryTime.toFixed(2)}ms`);
      
      // Complex queries may take longer but should be optimized
      PerformanceAssertions.expectResponseTime(queryTime, 500, 0.5); // 50% tolerance for complex queries
    });
  });

  describe('PDF Export Performance (SLA: < 5 seconds)', () => {
    it('should generate PDF exports within 5 seconds', async () => {
      const metrics = await performanceMeasurer.measureAsync(
        'pdf-export',
        async () => {
          // Simulate PDF generation
          const mockPDFData = {
            title: 'Executive Summary Report',
            data: mockFlexibleKPIsResponse,
            charts: ['revenue', 'margin', 'costs'],
            pages: 5
          };
          
          // Simulate PDF processing time (proportional to data size)
          const processingTime = Object.keys(mockPDFData.data).length * 100 + mockPDFData.pages * 200;
          await new Promise(resolve => setTimeout(resolve, Math.min(processingTime, 1000)));
          
          return mockPDFData;
        }
      );

      console.log(`PDF export time: ${metrics.duration.toFixed(2)}ms`);
      
      // SLA: PDF export < 5 seconds
      PerformanceAssertions.expectResponseTime(metrics.duration, 5000);
      
      globalPerformanceUtils.benchmarkManager.addBenchmark('pdf-export', {
        duration: metrics.duration,
        memoryUsed: metrics.memoryUsage!.heapUsed / 1024 / 1024
      });
    });
  });

  describe('Performance Regression Detection', () => {
    it('should detect performance improvements and degradations', async () => {
      // Add some baseline benchmarks
      globalPerformanceUtils.benchmarkManager.addBenchmark('test-metric', {
        responseTime: 500,
        memoryUsage: 25
      });
      
      // Wait a bit to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 10));
      
      // Test with improved performance
      const improvedMetrics = { responseTime: 450, memoryUsage: 20 };
      const improvedComparison = globalPerformanceUtils.benchmarkManager.compareBenchmarks('test-metric', improvedMetrics);
      
      expect(improvedComparison.improved).toContain('responseTime');
      expect(improvedComparison.improved).toContain('memoryUsage');
      
      // Test with degraded performance
      const degradedMetrics = { responseTime: 600, memoryUsage: 35 };
      const degradedComparison = globalPerformanceUtils.benchmarkManager.compareBenchmarks('test-metric', degradedMetrics);
      
      expect(degradedComparison.degraded).toContain('responseTime');
      expect(degradedComparison.degraded).toContain('memoryUsage');
      
      console.log('Performance regression detection working correctly');
    });
  });

  describe('Performance Summary and Reporting', () => {
    it('should generate comprehensive performance report', async () => {
      const allBenchmarks = globalPerformanceUtils.benchmarkManager.getBenchmarks();
      
      const report = {
        timestamp: new Date().toISOString(),
        totalTests: allBenchmarks.length,
        benchmarks: allBenchmarks.reduce((acc, benchmark) => {
          acc[benchmark.name] = benchmark.metrics;
          return acc;
        }, {} as Record<string, any>),
        slaCompliance: {
          dashboardLoad: allBenchmarks.some(b => b.name === 'dashboard-load' && b.metrics.duration < 3000),
          apiResponse: allBenchmarks.some(b => b.name.includes('api-') && b.metrics.duration < 1000),
          chartRendering: allBenchmarks.some(b => b.name === 'chart-rendering' && b.metrics.duration < 2000),
          pdfExport: allBenchmarks.some(b => b.name === 'pdf-export' && b.metrics.duration < 5000)
        }
      };
      
      console.log('Performance Report:', JSON.stringify(report, null, 2));
      
      // Verify we have collected meaningful performance data
      expect(report.totalTests).toBeGreaterThan(0);
      expect(Object.keys(report.benchmarks)).toContain('dashboard-load');
      
      // All SLA requirements should be met
      Object.values(report.slaCompliance).forEach(compliant => {
        if (typeof compliant === 'boolean') {
          expect(compliant).toBe(true);
        }
      });
    });
  });
});