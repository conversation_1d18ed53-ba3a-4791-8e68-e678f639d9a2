/**
 * Performance Tests for Flexible KPIs API Endpoint
 * 
 * These tests measure the performance of the flexible-kpis endpoint
 * with various parameter combinations to identify potential bottlenecks.
 */

// Mock next-auth/jwt to avoid ES modules issues
jest.mock('next-auth/jwt', () => ({
  getToken: jest.fn()
}));

// Mock redshift module
jest.mock('@/lib/api/redshift', () => ({
  redshiftPool: null,
  getRedshiftPool: jest.fn().mockReturnValue(null),
  testRedshiftConnection: jest.fn().mockResolvedValue(false)
}));

// Import the mocked modules
import * as nextAuthJwt from 'next-auth/jwt';

// Import core modules
import { IncomingMessage, ServerResponse, createServer } from 'http';
import {
  cleanupTestEnvironment,
  initializeTestEnvironment
} from '../utils/test-setup';

import {
  isRedshiftAvailable
} from '../utils/redshift-test-utils';
import supertest from 'supertest';

// Mock the GET function from the flexible-kpis route
// In a real test, you would use the actual implementation
// but for simplicity, we're creating a mock here
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const mockGET = async (request: Request) => {
  return new Response(JSON.stringify({
    'Net Revenue': {
      summary: { value: 10000 },
      timeSeries: [
        { date: '2025-01-01', value: 5000 },
        { date: '2025-01-02', value: 5000 }
      ]
    }
  }), { status: 200 });
};

// Create a test server for the API endpoint
function createTestServer() {
  console.log('Creating test server...');
  console.log('Request object available:', typeof Request !== 'undefined');
  console.log('Headers object available:', typeof Headers !== 'undefined');
  
  const handler = (req: IncomingMessage, res: ServerResponse) => {
    console.log('Handler called with URL:', req.url);
    
    try {
      // Create a Request object from the NextApiRequest
      console.log('Attempting to create Request object...');
      const request = new Request(
        `http://localhost/api/dashboard/flexible-kpis${req.url?.includes('?') ? req.url.substring(req.url.indexOf('?')) : ''}`,
        {
          headers: new Headers(req.headers as Record<string, string>),
          method: req.method || 'GET',
        }
      );
      console.log('Request object created successfully');
      
      // Call our mock GET function
      mockGET(request)
        .then(async (response) => {
        const body = await response.json();
        const status = response.status;
        
        res.statusCode = status;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify(body));
      })
      .catch((error) => {
        console.error('Error in test handler:', error);
        res.statusCode = 500;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({ error: 'Internal Server Error' }));
        });
    } catch (error) {
      console.error('Error creating Request object:', error);
      res.statusCode = 500;
      res.setHeader('Content-Type', 'application/json');
      res.end(JSON.stringify({ error: 'Request creation failed' }));
    }
  };

  const server = createServer(handler);
  return supertest(server);
}

// Helper function to measure response time
async function measureResponseTime(
  request: supertest.SuperTest<supertest.Test>,
  url: string
): Promise<number> {
  const startTime = Date.now();
  await request.get(url);
  const endTime = Date.now();
  return endTime - startTime;
}

describe('Flexible KPIs API Performance', () => {
  let request: supertest.SuperTest<supertest.Test>;
  
  // Set up the test environment before running tests
  beforeAll(async () => {
    try {
      console.log('Initializing test environment...');
      await initializeTestEnvironment();
      console.log('Test environment initialized');
      
      console.log('Creating test server...');
      request = createTestServer();
      console.log('Test server created');
      
      // Mock the auth token for all tests
      console.log('Mocking auth token...');
      jest.spyOn(nextAuthJwt, 'getToken').mockResolvedValue({
        id: 'user-1',
        name: 'Test User',
        email: '<EMAIL>',
        isImpersonating: false,
        isSuperAdmin: true,
        brands: [1, 2, 3],
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
        jti: 'test-jwt-id'
      });
    } catch (error) {
      console.warn('Failed to initialize test environment:', error);
    }
  });

  // Clean up the test environment after running tests
  afterAll(async () => {
    await cleanupTestEnvironment();
  });

  // Check if Redshift is available before each test
  beforeEach(async () => {
    const available = await isRedshiftAvailable();
    if (!available) {
      console.warn('Skipping test: Redshift connection is not available');
      return;
    }
  });

  // Test response time with minimal parameters
  it('should respond quickly with minimal parameters', async () => {
    const url = '/api/dashboard/flexible-kpis?kpis=Net%20Revenue';
    const responseTime = await measureResponseTime(request, url);
    
    console.log(`Response time with minimal parameters: ${responseTime}ms`);
    
    // Set a reasonable threshold for response time
    expect(responseTime).toBeLessThan(1000); // 1 second
  });

  // Test response time with date range
  it('should respond within acceptable time with date range', async () => {
    const url = '/api/dashboard/flexible-kpis?kpis=Net%20Revenue&startDate=2025-01-01&endDate=2025-01-31';
    const responseTime = await measureResponseTime(request, url);
    
    console.log(`Response time with date range: ${responseTime}ms`);
    
    // Set a reasonable threshold for response time
    expect(responseTime).toBeLessThan(2000); // 2 seconds
  });

  // Test response time with multiple KPIs
  it('should respond within acceptable time with multiple KPIs', async () => {
    const url = '/api/dashboard/flexible-kpis?kpis=Net%20Revenue,Gross%20Margin,Adspend,Contribution%20Margin';
    const responseTime = await measureResponseTime(request, url);
    
    console.log(`Response time with multiple KPIs: ${responseTime}ms`);
    
    // Set a reasonable threshold for response time
    expect(responseTime).toBeLessThan(3000); // 3 seconds
  });

  // Test response time with brand dimension
  it('should respond within acceptable time with brand dimension', async () => {
    const url = '/api/dashboard/flexible-kpis?kpis=Net%20Revenue&groupByDimension=brand';
    const responseTime = await measureResponseTime(request, url);
    
    console.log(`Response time with brand dimension: ${responseTime}ms`);
    
    // Set a reasonable threshold for response time
    expect(responseTime).toBeLessThan(3000); // 3 seconds
  });

  // Test response time with complex query
  it('should respond within acceptable time with complex query', async () => {
    const url = '/api/dashboard/flexible-kpis?kpis=Net%20Revenue,Gross%20Margin,Adspend,Contribution%20Margin&startDate=2025-01-01&endDate=2025-01-31&groupByDimension=brand&groupByTime=week';
    const responseTime = await measureResponseTime(request, url);
    
    console.log(`Response time with complex query: ${responseTime}ms`);
    
    // Set a reasonable threshold for response time
    expect(responseTime).toBeLessThan(5000); // 5 seconds
  });
});
