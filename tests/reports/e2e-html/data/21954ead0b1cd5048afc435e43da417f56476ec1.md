# Test info

- Name: Comprehensive Error Handling and Edge Cases >> should handle 404 errors gracefully
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:16:7

# Error details

```
Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:40:50
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
    - link "invalid-campaign-id":
      - /url: /marketing-dashboard/invalid-campaign-id
  - main:
    - link "Back to Dashboard":
      - /url: /marketing-dashboard
      - button "Back to Dashboard"
    - heading "Campaign Details" [level=1]
    - heading "Unable to Load Campaign" [level=3]
    - paragraph: "Could not load campaign details: Failed to fetch campaign details"
- alert
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 1 Issue
- button "Collapse issues badge":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import {
   3 |   waitForPageLoad,
   4 |   signInAsAdmin,
   5 |   takeScreenshot,
   6 |   waitForLoadingToComplete,
   7 |   getMainContent
   8 | } from './utils/test-helpers';
   9 |
   10 | test.describe('Comprehensive Error Handling and Edge Cases', () => {
   11 |   test.beforeEach(async ({ page }) => {
   12 |     // Sign in as admin for most tests
   13 |     await signInAsAdmin(page);
   14 |   });
   15 |
   16 |   test('should handle 404 errors gracefully', async ({ page }) => {
   17 |     const invalidUrls = [
   18 |       '/nonexistent-page',
   19 |       '/dashboard/invalid',
   20 |       '/admin/nonexistent',
   21 |       '/marketing-dashboard/invalid-campaign-id',
   22 |       '/brand-deep-dive/invalid'
   23 |     ];
   24 |
   25 |     for (const url of invalidUrls) {
   26 |       await page.goto(url);
   27 |       await waitForPageLoad(page);
   28 |
   29 |       // Should either show 404 page or redirect to valid page
   30 |       const currentUrl = page.url();
   31 |       console.log(`${url} -> ${currentUrl}`);
   32 |
   33 |       // Check for 404 page elements or redirect
   34 |       const is404 = currentUrl.includes('404') ||
   35 |                    await page.locator('text=/404|not found|page not found/i').isVisible();
   36 |       const isRedirect = !currentUrl.includes(url.split('/')[1]);
   37 |       const isValidPage = currentUrl.includes('/dashboard') || currentUrl.includes('/auth');
   38 |
   39 |       // Either shows 404, redirects, or shows a valid page (some routes might be valid)
>  40 |       expect(is404 || isRedirect || isValidPage).toBe(true);
      |                                                  ^ Error: expect(received).toBe(expected) // Object.is equality
   41 |       await takeScreenshot(page, `error-404-${url.replace(/\//g, '-')}`);
   42 |     }
   43 |   });
   44 |
   45 |   test('should handle network errors and offline scenarios', async ({ page }) => {
   46 |     await page.goto('/dashboard');
   47 |     await waitForPageLoad(page);
   48 |
   49 |     // Simulate network failure
   50 |     await page.context().setOffline(true);
   51 |
   52 |     // Try to navigate to another page
   53 |     try {
   54 |       await page.goto('/marketing-dashboard');
   55 |       await page.waitForTimeout(3000);
   56 |     } catch (error) {
   57 |       console.log('Expected network error:', error.message);
   58 |     }
   59 |
   60 |     // Should show offline message or handle gracefully
   61 |     const offlineIndicator = page.locator('text=/offline|network error|connection failed/i');
   62 |     const isOfflineHandled = await offlineIndicator.isVisible();
   63 |
   64 |     console.log(`Offline handling: ${isOfflineHandled ? 'Detected' : 'Not detected'}`);
   65 |     await takeScreenshot(page, 'network-offline');
   66 |
   67 |     // Restore network
   68 |     await page.context().setOffline(false);
   69 |     await page.waitForTimeout(2000);
   70 |
   71 |     // Should recover when network is restored
   72 |     await page.reload();
   73 |     await waitForPageLoad(page);
   74 |     await takeScreenshot(page, 'network-restored');
   75 |   });
   76 |
   77 |   test('should handle API errors gracefully', async ({ page }) => {
   78 |     // Intercept API calls and return errors
   79 |     await page.route('**/api/dashboard/**', route => {
   80 |       route.fulfill({
   81 |         status: 500,
   82 |         contentType: 'application/json',
   83 |         body: JSON.stringify({ error: 'Internal Server Error' })
   84 |       });
   85 |     });
   86 |
   87 |     await page.goto('/dashboard');
   88 |     await waitForPageLoad(page);
   89 |     await waitForLoadingToComplete(page);
   90 |
   91 |     // Should show error message or fallback content
   92 |     const errorMessage = page.locator('text=/error/i, text=/failed/i, text=/something went wrong/i');
   93 |     const hasErrorHandling = await errorMessage.isVisible();
   94 |
   95 |     console.log(`API error handling: ${hasErrorHandling ? 'Detected' : 'Not detected'}`);
   96 |     await takeScreenshot(page, 'api-error-500');
   97 |   });
   98 |
   99 |   test('should handle authentication errors', async ({ page }) => {
  100 |     // Test without authentication
  101 |     const unauthenticatedPage = await page.context().newPage();
  102 |
  103 |     const protectedPages = [
  104 |       '/dashboard',
  105 |       '/marketing-dashboard',
  106 |       '/brand-deep-dive',
  107 |       '/executive-summary',
  108 |       '/budget',
  109 |       '/ai-assistant',
  110 |       '/admin'
  111 |     ];
  112 |
  113 |     for (const url of protectedPages) {
  114 |       await unauthenticatedPage.goto(url);
  115 |       await waitForPageLoad(unauthenticatedPage);
  116 |
  117 |       // Should redirect to sign-in page or stay on dashboard if already authenticated
  118 |       const currentUrl = unauthenticatedPage.url();
  119 |       const isOnSignIn = currentUrl.includes('/auth/signin');
  120 |       const isOnDashboard = currentUrl.includes('/dashboard');
  121 |
  122 |       // Either redirected to sign-in or stayed on dashboard (if session is still valid)
  123 |       expect(isOnSignIn || isOnDashboard).toBe(true);
  124 |
  125 |       console.log(`${url} -> redirected to sign-in`);
  126 |     }
  127 |
  128 |     await takeScreenshot(unauthenticatedPage, 'auth-redirect');
  129 |     await unauthenticatedPage.close();
  130 |   });
  131 |
  132 |   test('should handle form validation errors', async ({ page }) => {
  133 |     await page.goto('/admin/users');
  134 |     await waitForPageLoad(page);
  135 |
  136 |     // Look for add user button
  137 |     const addButton = page.locator('button:has-text("Add"), button:has-text("Create")');
  138 |
  139 |     if (await addButton.isVisible()) {
  140 |       await addButton.click();
```