# Test info

- Name: Comprehensive Error Handling and Edge Cases >> should handle authentication errors
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:99:7

# Error details

```
Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:123:43
```

# Page snapshot

```yaml
- alert
- button "Open Next.js Dev Tools":
  - img
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: $17,245,998.18
    - img: 2/28/2025 4/30/2025 0 2,000,000 4,000,000 6,000,000 8,000,000
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,916,224.18
    - img: 2/28/2025 4/30/2025 0 200,000 400,000 600,000 800,000
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $614,536.13
    - img: 2/28/2025 4/30/2025 0 70,000 140,000 210,000 280,000
    - text: Net Revenue
    - button "KPI Definition"
    - text: $14,715,237.88
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,709,657.76
    - img: 2/28/2025 4/30/2025 0 300,000 600,000 900,000 1,200,000
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,167,088.33
    - img: 2/28/2025 4/30/2025 0 250,000 500,000 750,000 1,000,000
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,034,384.19
    - img: 2/28/2025 4/30/2025 0 95,000 190,000 285,000 380,000
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $9,520,150.47
    - img: 2/28/2025 4/30/2025 0 900,000 1,800,000 2,700,000 3,600,000
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $3,424,904.20
    - img: 2/28/2025 4/30/2025 0 350,000 700,000 1,050,000 1,400,000
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $12,945,054.68
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
```

# Test source

```ts
   23 |     ];
   24 |
   25 |     for (const url of invalidUrls) {
   26 |       await page.goto(url);
   27 |       await waitForPageLoad(page);
   28 |
   29 |       // Should either show 404 page or redirect to valid page
   30 |       const currentUrl = page.url();
   31 |       console.log(`${url} -> ${currentUrl}`);
   32 |
   33 |       // Check for 404 page elements or redirect
   34 |       const is404 = currentUrl.includes('404') ||
   35 |                    await page.locator('text=/404|not found|page not found/i').isVisible();
   36 |       const isRedirect = !currentUrl.includes(url.split('/')[1]);
   37 |       const isValidPage = currentUrl.includes('/dashboard') || currentUrl.includes('/auth');
   38 |
   39 |       // Either shows 404, redirects, or shows a valid page (some routes might be valid)
   40 |       expect(is404 || isRedirect || isValidPage).toBe(true);
   41 |       await takeScreenshot(page, `error-404-${url.replace(/\//g, '-')}`);
   42 |     }
   43 |   });
   44 |
   45 |   test('should handle network errors and offline scenarios', async ({ page }) => {
   46 |     await page.goto('/dashboard');
   47 |     await waitForPageLoad(page);
   48 |
   49 |     // Simulate network failure
   50 |     await page.context().setOffline(true);
   51 |
   52 |     // Try to navigate to another page
   53 |     try {
   54 |       await page.goto('/marketing-dashboard');
   55 |       await page.waitForTimeout(3000);
   56 |     } catch (error) {
   57 |       console.log('Expected network error:', error.message);
   58 |     }
   59 |
   60 |     // Should show offline message or handle gracefully
   61 |     const offlineIndicator = page.locator('text=/offline|network error|connection failed/i');
   62 |     const isOfflineHandled = await offlineIndicator.isVisible();
   63 |
   64 |     console.log(`Offline handling: ${isOfflineHandled ? 'Detected' : 'Not detected'}`);
   65 |     await takeScreenshot(page, 'network-offline');
   66 |
   67 |     // Restore network
   68 |     await page.context().setOffline(false);
   69 |     await page.waitForTimeout(2000);
   70 |
   71 |     // Should recover when network is restored
   72 |     await page.reload();
   73 |     await waitForPageLoad(page);
   74 |     await takeScreenshot(page, 'network-restored');
   75 |   });
   76 |
   77 |   test('should handle API errors gracefully', async ({ page }) => {
   78 |     // Intercept API calls and return errors
   79 |     await page.route('**/api/dashboard/**', route => {
   80 |       route.fulfill({
   81 |         status: 500,
   82 |         contentType: 'application/json',
   83 |         body: JSON.stringify({ error: 'Internal Server Error' })
   84 |       });
   85 |     });
   86 |
   87 |     await page.goto('/dashboard');
   88 |     await waitForPageLoad(page);
   89 |     await waitForLoadingToComplete(page);
   90 |
   91 |     // Should show error message or fallback content
   92 |     const errorMessage = page.locator('text=/error/i, text=/failed/i, text=/something went wrong/i');
   93 |     const hasErrorHandling = await errorMessage.isVisible();
   94 |
   95 |     console.log(`API error handling: ${hasErrorHandling ? 'Detected' : 'Not detected'}`);
   96 |     await takeScreenshot(page, 'api-error-500');
   97 |   });
   98 |
   99 |   test('should handle authentication errors', async ({ page }) => {
  100 |     // Test without authentication
  101 |     const unauthenticatedPage = await page.context().newPage();
  102 |
  103 |     const protectedPages = [
  104 |       '/dashboard',
  105 |       '/marketing-dashboard',
  106 |       '/brand-deep-dive',
  107 |       '/executive-summary',
  108 |       '/budget',
  109 |       '/ai-assistant',
  110 |       '/admin'
  111 |     ];
  112 |
  113 |     for (const url of protectedPages) {
  114 |       await unauthenticatedPage.goto(url);
  115 |       await waitForPageLoad(unauthenticatedPage);
  116 |
  117 |       // Should redirect to sign-in page or stay on dashboard if already authenticated
  118 |       const currentUrl = unauthenticatedPage.url();
  119 |       const isOnSignIn = currentUrl.includes('/auth/signin');
  120 |       const isOnDashboard = currentUrl.includes('/dashboard');
  121 |
  122 |       // Either redirected to sign-in or stayed on dashboard (if session is still valid)
> 123 |       expect(isOnSignIn || isOnDashboard).toBe(true);
      |                                           ^ Error: expect(received).toBe(expected) // Object.is equality
  124 |
  125 |       console.log(`${url} -> redirected to sign-in`);
  126 |     }
  127 |
  128 |     await takeScreenshot(unauthenticatedPage, 'auth-redirect');
  129 |     await unauthenticatedPage.close();
  130 |   });
  131 |
  132 |   test('should handle form validation errors', async ({ page }) => {
  133 |     await page.goto('/admin/users');
  134 |     await waitForPageLoad(page);
  135 |
  136 |     // Look for add user button
  137 |     const addButton = page.locator('button:has-text("Add"), button:has-text("Create")');
  138 |
  139 |     if (await addButton.isVisible()) {
  140 |       await addButton.click();
  141 |       await page.waitForTimeout(1000);
  142 |
  143 |       // Look for form
  144 |       const form = page.locator('form');
  145 |
  146 |       if (await form.isVisible()) {
  147 |         // Try to submit empty form
  148 |         const submitButton = form.locator('button[type="submit"], button:has-text("Save"), button:has-text("Create")');
  149 |
  150 |         if (await submitButton.isVisible()) {
  151 |           await submitButton.click();
  152 |           await page.waitForTimeout(1000);
  153 |
  154 |           // Should show validation errors
  155 |           const validationErrors = page.locator('.error, .invalid, text=/required|invalid/i');
  156 |           const hasValidation = await validationErrors.count() > 0;
  157 |
  158 |           console.log(`Form validation: ${hasValidation ? 'Working' : 'Not detected'}`);
  159 |           await takeScreenshot(page, 'form-validation-errors');
  160 |         }
  161 |       }
  162 |     }
  163 |   });
  164 |
  165 |   test('should handle large data sets without crashing', async ({ page }) => {
  166 |     // Test with potentially large datasets
  167 |     await page.goto('/marketing-dashboard');
  168 |     await waitForPageLoad(page);
  169 |
  170 |     // Set a very large date range to potentially load lots of data
  171 |     const startDateInput = page.locator('input[type="date"]').first();
  172 |     const endDateInput = page.locator('input[type="date"]').last();
  173 |
  174 |     if (await startDateInput.isVisible() && await endDateInput.isVisible()) {
  175 |       await startDateInput.fill('2020-01-01');
  176 |       await endDateInput.fill('2024-12-31');
  177 |       await page.waitForTimeout(5000);
  178 |       await waitForLoadingToComplete(page);
  179 |
  180 |       // Page should still be responsive
  181 |       const mainContent = await getMainContent(page);
  182 |       const isResponsive = await mainContent.isVisible();
  183 |       expect(isResponsive).toBe(true);
  184 |
  185 |       console.log('Large dataset handling: Page remained responsive');
  186 |       await takeScreenshot(page, 'large-dataset-handling');
  187 |     }
  188 |   });
  189 |
  190 |   test('should handle browser back/forward navigation', async ({ page }) => {
  191 |     // Navigate through several pages
  192 |     await page.goto('/dashboard');
  193 |     await waitForPageLoad(page);
  194 |
  195 |     await page.goto('/marketing-dashboard');
  196 |     await waitForPageLoad(page);
  197 |
  198 |     await page.goto('/brand-deep-dive');
  199 |     await waitForPageLoad(page);
  200 |
  201 |     // Test back navigation
  202 |     await page.goBack();
  203 |     await waitForPageLoad(page);
  204 |     expect(page.url()).toContain('/marketing-dashboard');
  205 |
  206 |     await page.goBack();
  207 |     await waitForPageLoad(page);
  208 |     expect(page.url()).toContain('/dashboard');
  209 |
  210 |     // Test forward navigation
  211 |     await page.goForward();
  212 |     await waitForPageLoad(page);
  213 |     expect(page.url()).toContain('/marketing-dashboard');
  214 |
  215 |     console.log('Browser navigation: Working correctly');
  216 |     await takeScreenshot(page, 'browser-navigation');
  217 |   });
  218 |
  219 |   test('should handle page refresh and state preservation', async ({ page }) => {
  220 |     await page.goto('/dashboard');
  221 |     await waitForPageLoad(page);
  222 |
  223 |     // Apply some filters if available
```