# Test info

- Name: Comprehensive Application Test Suite >> should verify authentication system works correctly
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:122:7

# Error details

```
TimeoutError: locator.waitFor: Timeout 10000ms exceeded.
Call log:
  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible

    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)
    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7
```

# Page snapshot

```yaml
- list:
  - listitem:
    - link "NOLK":
      - /url: /dashboard
      - img
      - text: NOLK
- list:
  - listitem:
    - link "Dashboard":
      - /url: /dashboard
      - img
      - text: Dashboard
  - listitem:
    - link "Brand Deep Dive":
      - /url: /brand-deep-dive
      - img
      - text: Brand Deep Dive
  - listitem:
    - link "Marketing Dashboard":
      - /url: /marketing-dashboard
      - img
      - text: Marketing Dashboard
  - listitem:
    - link "Executive Summary":
      - /url: /executive-summary
      - img
      - text: Executive Summary
  - listitem:
    - link "Budget":
      - /url: /budget
      - img
      - text: Budget
  - listitem:
    - link "AI Assistant":
      - /url: /ai-assistant
      - img
      - text: AI Assistant
- list:
  - listitem:
    - link "Settings":
      - /url: "#"
      - img
      - text: Settings
- list:
  - listitem:
    - button "F François Arbour farbour":
      - text: F François Arbour farbour
      - img
- main:
  - button "Toggle Sidebar"
  - navigation:
    - link "Dashboard":
      - /url: /
    - link "Dashboard":
      - /url: /dashboard
  - main:
    - heading "Dashboard" [level=1]
    - heading "Overview of your business performance" [level=2]
    - text: Filters
    - button "Saved Views"
    - button "Reset"
    - button
    - text: Date Range Start Date
    - textbox "Start Date": 2025-03-01
    - text: End Date
    - textbox "End Date": 2025-05-30
    - text: Quick Select
    - combobox: Date presets
    - text: Currency
    - combobox: CAD
    - text: Group By
    - combobox: Month
    - text: Data Filters
    - button "Brands"
    - button "Countries"
    - button "Sales Channels"
    - text: Master KPI Comparison Compare 'Gross Revenue' across selected brands using a line chart.
    - combobox "Select KPI": Gross Revenue
    - img: 2/28/2025 3/31/2025 4/30/2025 0 550K 1M 2M 2M
    - text: Brands Click to toggle. Sorted by latest KPI.
    - list:
      - button "Toggle visibility for Revant" [pressed]: Revant
      - button "Toggle visibility for Qalo" [pressed]: Qalo
      - button "Toggle visibility for Ergonofis" [pressed]: Ergonofis
      - button "Toggle visibility for Opposite Wall" [pressed]: Opposite Wall
      - button "Toggle visibility for Homesick" [pressed]: Homesick
      - button "Toggle visibility for Gravity" [pressed]: Gravity
      - button "Toggle visibility for Freakmount" [pressed]: Freakmount
      - button "Toggle visibility for MiHIGH" [pressed]: MiHIGH
      - button "Toggle visibility for Rose Boreal" [pressed]: Rose Boreal
      - button "Toggle visibility for Rachel" [pressed]: Rachel
      - button "Toggle visibility for Kana" [pressed]: Kana
      - button "Toggle visibility for Arctic Tumblers" [pressed]: Arctic Tumblers
      - button "Toggle visibility for Loctote" [pressed]: Loctote
      - button "Toggle visibility for Proper Pour" [pressed]: Proper Pour
      - button "Toggle visibility for Love Your Melon" [pressed]: Love Your Melon
      - button "Toggle visibility for Wolf & Grizzly" [pressed]: Wolf & Grizzly
      - button "Toggle visibility for Go Green" [pressed]: Go Green
      - button "Toggle visibility for Corretto" [pressed]: Corretto
      - button "Toggle visibility for Alex Bottle" [pressed]: Alex Bottle
    - heading "Key Performance Indicators" [level=3]
    - text: Revenue
    - button "KPI Definition"
    - text: $17,245,998.18
    - img: 2/28/2025 4/30/2025 0 2,000,000 4,000,000 6,000,000 8,000,000
    - text: Discount
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,916,224.18
    - img: 2/28/2025 4/30/2025 0 200,000 400,000 600,000 800,000
    - text: Refunds
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $614,536.13
    - img: 2/28/2025 4/30/2025 0 70,000 140,000 210,000 280,000
    - text: Net Revenue
    - button "KPI Definition"
    - text: $14,715,237.88
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
    - text: Landed Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,709,657.76
    - img: 2/28/2025 4/30/2025 0 300,000 600,000 900,000 1,200,000
    - text: Fulfillment Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $2,167,088.33
    - img: 2/28/2025 4/30/2025 0 250,000 500,000 750,000 1,000,000
    - text: Transaction Cost
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $1,034,384.19
    - img: 2/28/2025 4/30/2025 0 95,000 190,000 285,000 380,000
    - text: Gross Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $9,520,150.47
    - img: 2/28/2025 4/30/2025 0 900,000 1,800,000 2,700,000 3,600,000
    - text: Adspend
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $3,424,904.20
    - img: 2/28/2025 4/30/2025 0 350,000 700,000 1,050,000 1,400,000
    - text: Contribution Margin
    - button "KPI Definition"
    - button "Show Values"
    - button "Show Percentages"
    - text: $12,945,054.68
    - img: 2/28/2025 4/30/2025 0 1,500,000 3,000,000 4,500,000 6,000,000
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { Page, expect } from '@playwright/test';
   2 |
   3 | /**
   4 |  * Helper functions for E2E tests
   5 |  */
   6 |
   7 | /**
   8 |  * Wait for the page to be fully loaded
   9 |  */
   10 | export async function waitForPageLoad(page: Page) {
   11 |   await page.waitForLoadState('networkidle');
   12 |   await page.waitForLoadState('domcontentloaded');
   13 | }
   14 |
   15 | /**
   16 |  * Sign in with Google (mock for testing)
   17 |  */
   18 | export async function signInWithGoogle(page: Page) {
   19 |   // Navigate to sign-in page
   20 |   await page.goto('/auth/signin');
   21 |   await waitForPageLoad(page);
   22 |
   23 |   // Click the Google sign-in button
   24 |   await page.click('button:has-text("Continue with Google")');
   25 |
   26 |   // In a real test environment, you would handle OAuth flow
   27 |   // For now, we'll assume the sign-in is successful and we're redirected
   28 |   await page.waitForURL('/dashboard');
   29 |   await waitForPageLoad(page);
   30 | }
   31 |
   32 | /**
   33 |  * Sign in with username and password credentials
   34 |  */
   35 | export async function signInWithCredentials(page: Page, username: string, password: string) {
   36 |   // Navigate to sign-in page
   37 |   await page.goto('/auth/signin');
   38 |   await waitForPageLoad(page);
   39 |   await page.waitForTimeout(2000); // Wait for page to fully load
   40 |
   41 |   try {
   42 |     // Look for credentials tab button with more flexible selector
   43 |     const credentialsTab = page.locator('button').filter({ hasText: 'Username/Password' });
   44 |
   45 |     // Wait for the button to be visible
>  46 |     await credentialsTab.waitFor({ state: 'visible', timeout: 10000 });
      |                          ^ TimeoutError: locator.waitFor: Timeout 10000ms exceeded.
   47 |     await credentialsTab.click();
   48 |     await page.waitForTimeout(1500); // Wait for tab switch animation
   49 |
   50 |     // Wait for the form to be visible
   51 |     await page.waitForSelector('#username', { state: 'visible', timeout: 10000 });
   52 |
   53 |     // Fill in credentials using id selectors
   54 |     await page.fill('#username', username);
   55 |     await page.fill('#password', password);
   56 |
   57 |     // Click sign in button
   58 |     await page.click('button[type="submit"]:has-text("Sign In")');
   59 |
   60 |     // Wait for redirect to dashboard
   61 |     await page.waitForURL('/dashboard', { timeout: 30000 });
   62 |     await waitForPageLoad(page);
   63 |   } catch (error) {
   64 |     console.log('Sign in failed:', error);
   65 |     // Take screenshot for debugging
   66 |     await takeScreenshot(page, 'signin-failed');
   67 |     throw error;
   68 |   }
   69 | }
   70 |
   71 | /**
   72 |  * Sign in with the test admin user (farbour/admin)
   73 |  */
   74 | export async function signInAsAdmin(page: Page) {
   75 |   await signInWithCredentials(page, 'farbour', 'admin');
   76 | }
   77 |
   78 | /**
   79 |  * Sign out from the application
   80 |  */
   81 | export async function signOut(page: Page) {
   82 |   // Look for user menu or sign out button
   83 |   const userMenu = page.locator('[data-testid="user-menu"]').or(
   84 |     page.locator('button:has-text("Sign out")')
   85 |   );
   86 |
   87 |   if (await userMenu.isVisible()) {
   88 |     await userMenu.click();
   89 |
   90 |     // Look for sign out option
   91 |     const signOutButton = page.locator('button:has-text("Sign out")').or(
   92 |       page.locator('[data-testid="sign-out"]')
   93 |     );
   94 |
   95 |     if (await signOutButton.isVisible()) {
   96 |       await signOutButton.click();
   97 |     }
   98 |   }
   99 |
  100 |   // Wait for redirect to sign-in page
  101 |   await page.waitForURL('/auth/signin');
  102 | }
  103 |
  104 | /**
  105 |  * Navigate to a specific page and wait for it to load
  106 |  */
  107 | export async function navigateToPage(page: Page, path: string) {
  108 |   await page.goto(path);
  109 |   await waitForPageLoad(page);
  110 | }
  111 |
  112 | /**
  113 |  * Check if user is authenticated
  114 |  */
  115 | export async function isAuthenticated(page: Page): Promise<boolean> {
  116 |   try {
  117 |     // Check if we're on the dashboard or any authenticated page
  118 |     const currentUrl = page.url();
  119 |     return !currentUrl.includes('/auth/signin');
  120 |   } catch {
  121 |     return false;
  122 |   }
  123 | }
  124 |
  125 | /**
  126 |  * Wait for a specific element to be visible
  127 |  */
  128 | export async function waitForElement(page: Page, selector: string, timeout = 10000) {
  129 |   await page.waitForSelector(selector, { state: 'visible', timeout });
  130 | }
  131 |
  132 | /**
  133 |  * Take a screenshot with a descriptive name
  134 |  */
  135 | export async function takeScreenshot(page: Page, name: string) {
  136 |   await page.screenshot({
  137 |     path: `tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`,
  138 |     fullPage: true
  139 |   });
  140 | }
  141 |
  142 | /**
  143 |  * Check if the sidebar navigation is working
  144 |  */
  145 | export async function testSidebarNavigation(page: Page) {
  146 |   // Test main navigation items
```