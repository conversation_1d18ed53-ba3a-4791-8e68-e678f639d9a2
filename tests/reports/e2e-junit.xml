<testsuites id="" name="" tests="123" failures="4" skipped="0" errors="0" time="158.005777">
<testsuite name="comprehensive-admin-dashboard.spec.ts" timestamp="2025-05-30T10:19:55.392Z" hostname="chromium" tests="16" failures="0" skipped="0" time="170.058" errors="0">
<testcase name="Comprehensive Admin Dashboard Testing › should load admin dashboard with proper authorization" classname="comprehensive-admin-dashboard.spec.ts" time="13.435">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should display admin navigation menu" classname="comprehensive-admin-dashboard.spec.ts" time="13.5">
<system-out>
<![CDATA[Found 7 admin navigation items out of 8 expected
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Users management" classname="comprehensive-admin-dashboard.spec.ts" time="13.662">
<system-out>
<![CDATA[Users table has 6 headers and 14 rows
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Roles management" classname="comprehensive-admin-dashboard.spec.ts" time="13.585">
<system-out>
<![CDATA[Roles table has 4 headers and 4 rows
Found 10 role-related elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Permissions management" classname="comprehensive-admin-dashboard.spec.ts" time="13.625">
<system-out>
<![CDATA[Permissions table has 6 headers and 2 rows
Found 3 permission-related elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Groups management" classname="comprehensive-admin-dashboard.spec.ts" time="13.7">
<system-out>
<![CDATA[Groups table has 4 headers and 4 rows
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Brands management" classname="comprehensive-admin-dashboard.spec.ts" time="13.725">
<system-out>
<![CDATA[Brands table has 8 headers and 23 rows
Found 0 brand-related elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Settings page" classname="comprehensive-admin-dashboard.spec.ts" time="12.963">
<system-out>
<![CDATA[Found 0 settings elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test Backups page" classname="comprehensive-admin-dashboard.spec.ts" time="6.399">
<system-out>
<![CDATA[Found 3 backup-related elements
Found 0 backup buttons
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should navigate to and test DB Structure page" classname="comprehensive-admin-dashboard.spec.ts" time="7.561">
<system-out>
<![CDATA[Found 0 database structure elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should test admin API endpoints" classname="comprehensive-admin-dashboard.spec.ts" time="7.103">
<system-out>
<![CDATA[/api/admin/users: 200
/api/admin/roles: 200
/api/admin/permissions: 200
/api/admin/groups: 200
/api/admin/brands: 200
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should handle admin form submissions" classname="comprehensive-admin-dashboard.spec.ts" time="8.171">
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should be responsive across different screen sizes" classname="comprehensive-admin-dashboard.spec.ts" time="10.569">
<system-out>
<![CDATA[Admin navigation visible on desktop
Admin navigation visible on tablet
Admin navigation visible on mobile
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should have proper accessibility features" classname="comprehensive-admin-dashboard.spec.ts" time="7.416">
<system-out>
<![CDATA[Admin Dashboard landmarks: 0
Admin Dashboard accessibility info: { ariaLabels: [33m1[39m, roles: [33m2[39m, headings: [33m4[39m, landmarks: [33m0[39m }
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should load within performance thresholds" classname="comprehensive-admin-dashboard.spec.ts" time="7.323">
<system-out>
<![CDATA[Admin Dashboard loaded in 1672ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Admin Dashboard Testing › should not have critical console errors" classname="comprehensive-admin-dashboard.spec.ts" time="7.321">
<system-out>
<![CDATA[Critical console errors found: [
  [32m'Error fetching sales channels: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3801:56)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'[39m,
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'[39m
]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-api.spec.ts" timestamp="2025-05-30T10:19:55.392Z" hostname="chromium" tests="13" failures="0" skipped="0" time="82.171" errors="0">
<testcase name="Comprehensive API Testing › should test dashboard API endpoints" classname="comprehensive-api.spec.ts" time="6.498">
<system-out>
<![CDATA[/api/dashboard/flexible-kpis: 200
/api/dashboard/flexible-kpis returned data: [
  [32m'Gross Revenue'[39m,         [32m'Net Revenue'[39m,
  [32m'Gross Margin'[39m,          [32m'Adspend'[39m,
  [32m'Contribution Margin'[39m,   [32m'% Gross Margin'[39m,
  [32m'% Contribution Margin'[39m, [32m'% Adspend'[39m,
  [32m'Landed Cost'[39m,           [32m'Fulfillment Cost'[39m,
  [32m'Transaction Cost'[39m,      [32m'Discount'[39m,
  [32m'Refund'[39m,                [32m'% Landed Cost'[39m,
  [32m'% Fulfillment Cost'[39m,    [32m'% Transaction Cost'[39m,
  [32m'% Discount'[39m,            [32m'% Refund'[39m,
  [32m'ACOS'[39m,                  [32m'TACOS'[39m,
  [32m'TCAC'[39m,                  [32m'Website Traffic'[39m,
  [32m'Conversion Rate'[39m,       [32m'Organic Traffic'[39m,
  [32m'Paid Traffic'[39m
]
/api/dashboard/kpi-data: 404
/api/dashboard/chart-data: 404
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test marketing API endpoints" classname="comprehensive-api.spec.ts" time="5.433">
<system-out>
<![CDATA[/api/marketing/campaigns: 404
/api/marketing/kpis: 404
/api/marketing/spend-breakdown: 404
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test admin API endpoints with proper authorization" classname="comprehensive-api.spec.ts" time="7.367">
<system-out>
<![CDATA[/api/admin/users: 200
/api/admin/users returned data: [
  [32m'0'[39m,  [32m'1'[39m,  [32m'2'[39m,  [32m'3'[39m,
  [32m'4'[39m,  [32m'5'[39m,  [32m'6'[39m,  [32m'7'[39m,
  [32m'8'[39m,  [32m'9'[39m,  [32m'10'[39m, [32m'11'[39m,
  [32m'12'[39m, [32m'13'[39m
]
/api/admin/roles: 200
/api/admin/roles returned data: [ [32m'data'[39m, [32m'pagination'[39m ]
/api/admin/permissions: 200
/api/admin/permissions returned data: [ [32m'0'[39m, [32m'1'[39m ]
/api/admin/groups: 200
/api/admin/groups returned data: [ [32m'data'[39m, [32m'pagination'[39m ]
/api/admin/brands: 200
/api/admin/brands returned data: [ [32m'data'[39m, [32m'pagination'[39m ]
/api/admin/settings: 401
/api/admin/settings: Unauthorized (expected for non-admin users)
/api/admin/backups: 401
/api/admin/backups: Unauthorized (expected for non-admin users)
/api/admin/db-structure: 401
/api/admin/db-structure: Unauthorized (expected for non-admin users)
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test budget API endpoint" classname="comprehensive-api.spec.ts" time="5.805">
<system-out>
<![CDATA[Budget API: 200
Budget API returned data: [ [32m'budgets'[39m ]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test AI assistant API endpoint" classname="comprehensive-api.spec.ts" time="9.951">
<system-out>
<![CDATA[AI Assistant GET: 405
AI Assistant POST: 500
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API error handling" classname="comprehensive-api.spec.ts" time="6.149">
<system-out>
<![CDATA[/api/nonexistent: 404
/api/dashboard/invalid: 404
/api/admin/invalid: 404
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API with query parameters" classname="comprehensive-api.spec.ts" time="6.164">
<system-out>
<![CDATA[/api/dashboard/flexible-kpis?startDate=2024-01-01&endDate=2024-12-31: 200
/api/dashboard/flexible-kpis?startDate=2024-01-01&endDate=2024-12-31 with params returned: [
  [32m'Gross Revenue'[39m,         [32m'Net Revenue'[39m,
  [32m'Gross Margin'[39m,          [32m'Adspend'[39m,
  [32m'Contribution Margin'[39m,   [32m'% Gross Margin'[39m,
  [32m'% Contribution Margin'[39m, [32m'% Adspend'[39m,
  [32m'Landed Cost'[39m,           [32m'Fulfillment Cost'[39m,
  [32m'Transaction Cost'[39m,      [32m'Discount'[39m,
  [32m'Refund'[39m,                [32m'% Landed Cost'[39m,
  [32m'% Fulfillment Cost'[39m,    [32m'% Transaction Cost'[39m,
  [32m'% Discount'[39m,            [32m'% Refund'[39m,
  [32m'ACOS'[39m,                  [32m'TACOS'[39m,
  [32m'TCAC'[39m,                  [32m'Website Traffic'[39m,
  [32m'Conversion Rate'[39m,       [32m'Organic Traffic'[39m,
  [32m'Paid Traffic'[39m
]
/api/marketing/campaigns?brand=test&currency=USD: 404
/api/budget?brand=test&brands=true: 200
/api/budget?brand=test&brands=true with params returned: [ [32m'brands'[39m ]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API response times" classname="comprehensive-api.spec.ts" time="4.98">
<system-out>
<![CDATA[/api/dashboard/flexible-kpis: 200 in 265ms
/api/marketing/campaigns: 404 in 46ms
/api/budget: 200 in 30ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API data validation" classname="comprehensive-api.spec.ts" time="5.674">
</testcase>
<testcase name="Comprehensive API Testing › should test API authentication requirements" classname="comprehensive-api.spec.ts" time="5.907">
<system-out>
<![CDATA[/api/admin/users (unauthenticated): 200
/api/admin/users (unauthenticated): Failed as expected - Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m

Expected value: [32m200[39m
Received array: [31m[401, 302, 403][39m
/api/admin/roles (unauthenticated): 200
/api/admin/roles (unauthenticated): Failed as expected - Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m

Expected value: [32m200[39m
Received array: [31m[401, 302, 403][39m
/api/dashboard/flexible-kpis (unauthenticated): 200
/api/dashboard/flexible-kpis (unauthenticated): Failed as expected - Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoContain[2m([22m[32mexpected[39m[2m) // indexOf[22m

Expected value: [32m200[39m
Received array: [31m[401, 302, 403][39m
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API CORS headers" classname="comprehensive-api.spec.ts" time="5.595">
<system-out>
<![CDATA[API Response Headers:
Content-Type: application/json
Cache-Control: no-store, max-age=0
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API rate limiting (if implemented)" classname="comprehensive-api.spec.ts" time="6.218">
<system-out>
<![CDATA[Request 1: 200
Request 2: 200
Request 3: 200
Request 4: 200
Request 5: 200
Request 6: 200
Request 7: 200
Request 8: 200
Request 9: 200
Request 10: 200
Successful requests: 10, Rate limited: 0
]]>
</system-out>
</testcase>
<testcase name="Comprehensive API Testing › should test API monitoring endpoints" classname="comprehensive-api.spec.ts" time="6.43">
<system-out>
<![CDATA[/api/monitoring/health: 200
/api/monitoring/health health check: {
  overall: [32m'healthy'[39m,
  timestamp: [33m1748600429897[39m,
  uptime: [33m38.067379208[39m,
  version: [32m'0.1.0'[39m,
  environment: [32m'development'[39m,
  checks: [
    {
      service: [32m'SQLite Database'[39m,
      status: [32m'healthy'[39m,
      responseTime: [33m0[39m,
      details: [32m'Database responding normally'[39m,
      lastChecked: [33m1748600429876[39m
    },
    {
      service: [32m'Redshift Database'[39m,
      status: [32m'healthy'[39m,
      responseTime: [33m20[39m,
      details: [32m'Redshift responding normally'[39m,
      lastChecked: [33m1748600429896[39m
    },
    {
      service: [32m'NextAuth'[39m,
      status: [32m'healthy'[39m,
      details: [32m'NextAuth configured'[39m,
      lastChecked: [33m1748600429896[39m
    },
    {
      service: [32m'Google OAuth'[39m,
      status: [32m'healthy'[39m,
      details: [32m'Google OAuth configured'[39m,
      lastChecked: [33m1748600429896[39m
    }
  ],
  performance: {
    memory: {
      rss: [33m2968207360[39m,
      heapTotal: [33m437485568[39m,
      heapUsed: [33m403414952[39m,
      external: [33m62363260[39m,
      arrayBuffers: [33m59778163[39m
    },
    cpu: { user: [33m69675638[39m, system: [33m21827396[39m },
    queries: {
      totalQueries: [33m0[39m,
      slowQueries: [33m0[39m,
      avgDuration: [33m0[39m,
      cacheHitRate: [33m0[39m,
      topSlowQueries: []
    },
    apis: {
      totalRequests: [33m0[39m,
      errorRequests: [33m0[39m,
      slowRequests: [33m0[39m,
      avgDuration: [33m0[39m,
      errorRate: [33m0[39m,
      topSlowEndpoints: []
    },
    errors: { totalErrors: [33m0[39m, errorsByType: {}, recentErrors: [] }
  },
  database: {
    sqlite: {
      status: [32m'healthy'[39m,
      responseTime: [33m0[39m,
      details: [32m'Database responding normally'[39m
    },
    redshift: {
      status: [32m'healthy'[39m,
      responseTime: [33m20[39m,
      poolStats: [36m[Object][39m,
      details: [32m'Redshift responding normally'[39m
    }
  }
}
/api/monitoring/status: 404
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-brand-deep-dive.spec.ts" timestamp="2025-05-30T10:19:55.392Z" hostname="chromium" tests="12" failures="0" skipped="0" time="81.78" errors="0">
<testcase name="Comprehensive Brand Deep Dive Testing › should load brand deep dive page with all components" classname="comprehensive-brand-deep-dive.spec.ts" time="7.991">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should display brand selector and allow brand selection" classname="comprehensive-brand-deep-dive.spec.ts" time="7.093">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should display KPI cards specific to selected brand" classname="comprehensive-brand-deep-dive.spec.ts" time="5.874">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should render brand performance charts" classname="comprehensive-brand-deep-dive.spec.ts" time="8.026">
<system-out>
<![CDATA[Found 1 charts with selector: svg[class*="chart"]
Found 23 charts on brand deep dive page
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should display marketing campaigns for selected brand" classname="comprehensive-brand-deep-dive.spec.ts" time="5.909">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should have functional brand-specific filters" classname="comprehensive-brand-deep-dive.spec.ts" time="8.55">
<system-out>
<![CDATA[Found 1 active filters
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should display tabs and allow tab navigation" classname="comprehensive-brand-deep-dive.spec.ts" time="5.838">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should handle brand comparison if available" classname="comprehensive-brand-deep-dive.spec.ts" time="5.727">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should handle export functionality if available" classname="comprehensive-brand-deep-dive.spec.ts" time="5.96">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should be responsive across different screen sizes" classname="comprehensive-brand-deep-dive.spec.ts" time="9.208">
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should have proper accessibility features" classname="comprehensive-brand-deep-dive.spec.ts" time="5.958">
<system-out>
<![CDATA[Brand Deep Dive landmarks: 0
Brand Deep Dive accessibility info: { ariaLabels: [33m1[39m, roles: [33m6[39m, headings: [33m3[39m, landmarks: [33m0[39m }
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Brand Deep Dive Testing › should load within performance thresholds" classname="comprehensive-brand-deep-dive.spec.ts" time="5.646">
<system-out>
<![CDATA[Brand Deep Dive loaded in 962ms
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-budget-ai.spec.ts" timestamp="2025-05-30T10:19:55.392Z" hostname="chromium" tests="15" failures="0" skipped="0" time="98.406" errors="0">
<testcase name="Comprehensive Budget Page Testing › should load budget page with all components" classname="comprehensive-budget-ai.spec.ts" time="6.501">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Budget Page Testing › should display budget data and charts" classname="comprehensive-budget-ai.spec.ts" time="8.224">
<system-out>
<![CDATA[Budget table has 17 headers and 12 rows
Found 1 charts with selector: svg[class*="chart"]
Found 5 budget-related elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Budget Page Testing › should handle budget filters and brand selection" classname="comprehensive-budget-ai.spec.ts" time="5.688">
<system-out>
<![CDATA[Found 0 active filters
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Budget Page Testing › should test budget API endpoint" classname="comprehensive-budget-ai.spec.ts" time="5.758">
<system-out>
<![CDATA[Budget API response: 200
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Budget Page Testing › should be responsive on different screen sizes" classname="comprehensive-budget-ai.spec.ts" time="8.586">
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should load AI assistant page with all components" classname="comprehensive-budget-ai.spec.ts" time="7.29">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should display chat interface" classname="comprehensive-budget-ai.spec.ts" time="6.643">
<system-out>
<![CDATA[Found 1 chat interface elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should handle message input and sending" classname="comprehensive-budget-ai.spec.ts" time="5.634">
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should display conversation history" classname="comprehensive-budget-ai.spec.ts" time="6.023">
<system-out>
<![CDATA[Found 0 messages in conversation history
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should handle AI assistant features" classname="comprehensive-budget-ai.spec.ts" time="6.089">
<system-out>
<![CDATA[Found 0 AI assistant features
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should test AI assistant API endpoint" classname="comprehensive-budget-ai.spec.ts" time="5.742">
<system-out>
<![CDATA[AI Assistant API failed: Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

Expected: [32m200[39m
Received: [31m405[39m
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should handle suggested questions or templates" classname="comprehensive-budget-ai.spec.ts" time="5.384">
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should be responsive on different screen sizes" classname="comprehensive-budget-ai.spec.ts" time="8.952">
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should have proper accessibility features" classname="comprehensive-budget-ai.spec.ts" time="5.873">
<system-out>
<![CDATA[AI Assistant landmarks: 0
AI Assistant accessibility info: { ariaLabels: [33m1[39m, roles: [33m2[39m, headings: [33m2[39m, landmarks: [33m0[39m }
]]>
</system-out>
</testcase>
<testcase name="Comprehensive AI Assistant Testing › should load within performance thresholds" classname="comprehensive-budget-ai.spec.ts" time="6.019">
<system-out>
<![CDATA[AI Assistant loaded in 1111ms
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-dashboard.spec.ts" timestamp="2025-05-30T10:19:55.392Z" hostname="chromium" tests="10" failures="0" skipped="0" time="90.287" errors="0">
<testcase name="Comprehensive Dashboard Testing › should load dashboard with all core components" classname="comprehensive-dashboard.spec.ts" time="6.291">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should display and interact with KPI cards" classname="comprehensive-dashboard.spec.ts" time="7.274">
</testcase>
<testcase name="Comprehensive Dashboard Testing › should render charts correctly" classname="comprehensive-dashboard.spec.ts" time="9.141">
<system-out>
<![CDATA[Found 11 charts with selector: .recharts-wrapper
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should have functional filters" classname="comprehensive-dashboard.spec.ts" time="8.71">
<system-out>
<![CDATA[Found 1 active filters: [ [32m'input[type="date"] (1/2)'[39m ]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should handle responsive design" classname="comprehensive-dashboard.spec.ts" time="9.458">
</testcase>
<testcase name="Comprehensive Dashboard Testing › should have proper accessibility features" classname="comprehensive-dashboard.spec.ts" time="6.375">
<system-out>
<![CDATA[Dashboard landmarks: 0
Accessibility info: { ariaLabels: [33m21[39m, roles: [33m26[39m, headings: [33m3[39m, landmarks: [33m0[39m }
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should handle navigation between dashboard sections" classname="comprehensive-dashboard.spec.ts" time="20.638">
</testcase>
<testcase name="Comprehensive Dashboard Testing › should handle data refresh and updates" classname="comprehensive-dashboard.spec.ts" time="8.18">
</testcase>
<testcase name="Comprehensive Dashboard Testing › should not have critical console errors" classname="comprehensive-dashboard.spec.ts" time="7.115">
<system-out>
<![CDATA[Critical console errors found: [
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'[39m,
  [32m'Error fetching sales channels: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3801:56)'[39m
]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Dashboard Testing › should load within performance thresholds" classname="comprehensive-dashboard.spec.ts" time="7.105">
<system-out>
<![CDATA[Dashboard loaded in 2160ms
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-error-handling.spec.ts" timestamp="2025-05-30T10:19:55.392Z" hostname="chromium" tests="12" failures="3" skipped="0" time="190.255" errors="0">
<testcase name="Comprehensive Error Handling and Edge Cases › should handle 404 errors gracefully" classname="comprehensive-error-handling.spec.ts" time="10.722">
<failure message="comprehensive-error-handling.spec.ts:16:7 should handle 404 errors gracefully" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:16:7 › Comprehensive Error Handling and Edge Cases › should handle 404 errors gracefully 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      38 |
      39 |       // Either shows 404, redirects, or shows a valid page (some routes might be valid)
    > 40 |       expect(is404 || isRedirect || isValidPage).toBe(true);
         |                                                  ^
      41 |       await takeScreenshot(page, `error-404-${url.replace(/\//g, '-')}`);
      42 |     }
      43 |   });
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:40:50

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[/nonexistent-page -> http://localhost:6699/nonexistent-page
/dashboard/invalid -> http://localhost:6699/dashboard/invalid
/admin/nonexistent -> http://localhost:6699/admin/nonexistent
/marketing-dashboard/invalid-campaign-id -> http://localhost:6699/marketing-dashboard/invalid-campaign-id

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle network errors and offline scenarios" classname="comprehensive-error-handling.spec.ts" time="10.175">
<system-out>
<![CDATA[Expected network error: page.goto: net::ERR_INTERNET_DISCONNECTED at http://localhost:6699/marketing-dashboard
Call log:
[2m  - navigating to "http://localhost:6699/marketing-dashboard", waiting until "load"[22m

Offline handling: Not detected
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle API errors gracefully" classname="comprehensive-error-handling.spec.ts" time="6.371">
<system-out>
<![CDATA[API error handling: Not detected
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle authentication errors" classname="comprehensive-error-handling.spec.ts" time="8.415">
<failure message="comprehensive-error-handling.spec.ts:99:7 should handle authentication errors" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:99:7 › Comprehensive Error Handling and Edge Cases › should handle authentication errors 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      121 |
      122 |       // Either redirected to sign-in or stayed on dashboard (if session is still valid)
    > 123 |       expect(isOnSignIn || isOnDashboard).toBe(true);
          |                                           ^
      124 |
      125 |       console.log(`${url} -> redirected to sign-in`);
      126 |     }
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:123:43

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-2.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #4: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[/dashboard -> redirected to sign-in

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-2.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video-1.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle form validation errors" classname="comprehensive-error-handling.spec.ts" time="7.119">
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle large data sets without crashing" classname="comprehensive-error-handling.spec.ts" time="12.397">
<system-out>
<![CDATA[Large dataset handling: Page remained responsive
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle browser back/forward navigation" classname="comprehensive-error-handling.spec.ts" time="14.75">
<system-out>
<![CDATA[Browser navigation: Working correctly
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle page refresh and state preservation" classname="comprehensive-error-handling.spec.ts" time="8.062">
<system-out>
<![CDATA[Page refresh: Handled successfully
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle concurrent user actions" classname="comprehensive-error-handling.spec.ts" time="60.122">
<failure message="comprehensive-error-handling.spec.ts:244:7 should handle concurrent user actions" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-error-handling.spec.ts:244:7 › Comprehensive Error Handling and Edge Cases › should handle concurrent user actions 

    Test timeout of 60000ms exceeded.

    Error: page.waitForTimeout: Target page, context or browser has been closed

      260 |     await Promise.all(promises);
      261 |
    > 262 |     await page.waitForTimeout(2000);
          |                ^
      263 |     await waitForLoadingToComplete(page);
      264 |
      265 |     // Page should still be functional
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:262:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle memory leaks during navigation" classname="comprehensive-error-handling.spec.ts" time="32.18">
<system-out>
<![CDATA[Initial memory: 37.77MB
Final memory: 37.77MB
Memory increase: 0.00MB
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle edge cases in data filtering" classname="comprehensive-error-handling.spec.ts" time="12.016">
<system-out>
<![CDATA[Invalid date range handling: Detected
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Error Handling and Edge Cases › should handle session timeout gracefully" classname="comprehensive-error-handling.spec.ts" time="7.926">
<system-out>
<![CDATA[Session timeout: Redirected to sign-in
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-executive-summary.spec.ts" timestamp="2025-05-30T10:19:55.392Z" hostname="chromium" tests="13" failures="0" skipped="0" time="93.964" errors="0">
<testcase name="Comprehensive Executive Summary Testing › should load executive summary with all components" classname="comprehensive-executive-summary.spec.ts" time="6.528">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display brand selector and period selector" classname="comprehensive-executive-summary.spec.ts" time="6.94">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display executive KPI cards with proper formatting" classname="comprehensive-executive-summary.spec.ts" time="6.645">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should switch between cards and slides view" classname="comprehensive-executive-summary.spec.ts" time="7.53">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display trend charts and analysis" classname="comprehensive-executive-summary.spec.ts" time="8.398">
<system-out>
<![CDATA[Found 3 charts with selector: .recharts-wrapper
Found 0 trend elements and 0 trend indicators
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should handle PDF export functionality" classname="comprehensive-executive-summary.spec.ts" time="8.672">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display period comparison data" classname="comprehensive-executive-summary.spec.ts" time="7.194">
<system-out>
<![CDATA[Found 10 comparison elements and 0 comparison values
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should handle currency switching" classname="comprehensive-executive-summary.spec.ts" time="6.508">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should display methodology and notes if available" classname="comprehensive-executive-summary.spec.ts" time="6.384">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should be responsive across different screen sizes" classname="comprehensive-executive-summary.spec.ts" time="9.716">
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should have proper accessibility features" classname="comprehensive-executive-summary.spec.ts" time="6.512">
<system-out>
<![CDATA[Executive Summary landmarks: 0
Executive Summary accessibility info: { ariaLabels: [33m29[39m, roles: [33m13[39m, headings: [33m11[39m, landmarks: [33m0[39m }
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should load within performance thresholds" classname="comprehensive-executive-summary.spec.ts" time="6.044">
<system-out>
<![CDATA[Executive Summary loaded in 1400ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Executive Summary Testing › should not have critical console errors" classname="comprehensive-executive-summary.spec.ts" time="6.893">
<system-out>
<![CDATA[Critical console errors found: [
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'[39m,
  [32m'Error fetching sales channels: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3801:56)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'[39m
]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-marketing-dashboard.spec.ts" timestamp="2025-05-30T10:19:55.392Z" hostname="chromium" tests="13" failures="0" skipped="0" time="91.715" errors="0">
<testcase name="Comprehensive Marketing Dashboard Testing › should load marketing dashboard with all components" classname="comprehensive-marketing-dashboard.spec.ts" time="6.986">
<system-out>
<![CDATA[Page title: Create Next App
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should display marketing KPI cards" classname="comprehensive-marketing-dashboard.spec.ts" time="6.998">
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should display campaign data table with proper functionality" classname="comprehensive-marketing-dashboard.spec.ts" time="6.246">
<system-out>
<![CDATA[Campaign table has 12 headers and 50 rows
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should render spend breakdown charts" classname="comprehensive-marketing-dashboard.spec.ts" time="8.513">
<system-out>
<![CDATA[Found 9 charts with selector: .recharts-wrapper
Found 0 spend-related chart elements
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should have functional marketing filters" classname="comprehensive-marketing-dashboard.spec.ts" time="8.41">
<system-out>
<![CDATA[Found 1 active filters
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should allow campaign search and filtering" classname="comprehensive-marketing-dashboard.spec.ts" time="6.36">
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should navigate to campaign details" classname="comprehensive-marketing-dashboard.spec.ts" time="6.737">
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should display proper marketing metrics calculations" classname="comprehensive-marketing-dashboard.spec.ts" time="6.712">
<system-out>
<![CDATA[Found 2 marketing metrics out of 8 expected
Found 57 percentage values and 168 currency values
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should handle data export functionality" classname="comprehensive-marketing-dashboard.spec.ts" time="5.911">
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should be responsive across different screen sizes" classname="comprehensive-marketing-dashboard.spec.ts" time="9.609">
<system-out>
<![CDATA[Table is in scrollable container on desktop
Table is in scrollable container on tablet
Table is in scrollable container on mobile
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should have proper accessibility features" classname="comprehensive-marketing-dashboard.spec.ts" time="6.148">
<system-out>
<![CDATA[Marketing Dashboard landmarks: 0
Marketing Dashboard accessibility info: { ariaLabels: [33m1[39m, roles: [33m9[39m, headings: [33m4[39m, landmarks: [33m0[39m }
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should load within performance thresholds" classname="comprehensive-marketing-dashboard.spec.ts" time="6.462">
<system-out>
<![CDATA[Marketing Dashboard loaded in 1641ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Marketing Dashboard Testing › should not have critical console errors" classname="comprehensive-marketing-dashboard.spec.ts" time="6.623">
<system-out>
<![CDATA[Critical console errors found: [
  [32m'Error fetching sales channels: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3801:56)'[39m,
  [32m'Error fetching saved views: TypeError: Failed to fetch\n'[39m +
    [32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\n'[39m +
    [32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\n'[39m +
    [32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'[39m,
  [32m'Error fetching brands: TypeError: Failed to fetch\n'[39m +
    [32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\n'[39m +
    [32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\n'[39m +
    [32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\n'[39m +
    [32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\n'[39m +
    [32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\n'[39m +
    [32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\n'[39m +
    [32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\n'[39m +
    [32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'[39m
]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-performance.spec.ts" timestamp="2025-05-30T10:19:55.392Z" hostname="chromium" tests="11" failures="0" skipped="0" time="127.639" errors="0">
<testcase name="Comprehensive Performance Testing › should measure page load performance across all main pages" classname="comprehensive-performance.spec.ts" time="12.789">
<system-out>
<![CDATA[Dashboard: 1624ms (max: 8000ms)
Brand Deep Dive: 1118ms (max: 12000ms)
Marketing Dashboard: 1049ms (max: 15000ms)
Executive Summary: 1012ms (max: 10000ms)
Budget: 928ms (max: 8000ms)
AI Assistant: 920ms (max: 6000ms)
Admin Dashboard: 885ms (max: 8000ms)

=== Performance Summary ===
✅ PASS Dashboard: 1624ms
✅ PASS Brand Deep Dive: 1118ms
✅ PASS Marketing Dashboard: 1049ms
✅ PASS Executive Summary: 1012ms
✅ PASS Budget: 928ms
✅ PASS AI Assistant: 920ms
✅ PASS Admin Dashboard: 885ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should measure Time to First Contentful Paint (FCP)" classname="comprehensive-performance.spec.ts" time="21.296">
</testcase>
<testcase name="Comprehensive Performance Testing › should measure Largest Contentful Paint (LCP)" classname="comprehensive-performance.spec.ts" time="27.398">
</testcase>
<testcase name="Comprehensive Performance Testing › should measure JavaScript bundle sizes" classname="comprehensive-performance.spec.ts" time="6.552">
<system-out>
<![CDATA[JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_fd44f5a4._.js - 1.01KB
JS Bundle: node_modules_%40swc_helpers_cjs_00636ac3._.js - 1.25KB
JS Bundle: node_modules_next_dist_2ecbf5fa._.js - 17.73KB
JS Bundle: node_modules_next_dist_compiled_2ce9398a._.js - 192.75KB
JS Bundle: node_modules_next_dist_client_8f19e6fb._.js - 166.58KB
JS Bundle: _e69f0d32._.js - 0.81KB
JS Bundle: app_favicon_ico_mjs_659ce808._.js - 0.55KB
JS Bundle: _93808211._.js - 16.07KB
JS Bundle: app_layout_tsx_c0237562._.js - 0.58KB
JS Bundle: node_modules_next_dist_1a6ee436._.js - 18.44KB
JS Bundle: _45166852._.js - 17.95KB
JS Bundle: _5e541529._.js - 70.10KB
JS Bundle: node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js - 17.12KB
JS Bundle: node_modules_lodash_f240f67a._.js - 32.65KB
JS Bundle: node_modules_%40floating-ui_9ec1fa39._.js - 21.34KB
JS Bundle: app_dashboard_page_tsx_63d9a548._.js - 0.87KB
JS Bundle: node_modules_%40radix-ui_fead58fd._.js - 55.73KB
JS Bundle: node_modules_recharts_es6_98d167ba._.js - 117.77KB
JS Bundle: node_modules_53dbc141._.js - 142.33KB
JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js - 4.05KB
JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_66796270._.js - 0.57KB
Total JS Bundle Size: 896.22KB
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test memory usage during navigation" classname="comprehensive-performance.spec.ts" time="9.881">
<system-out>
<![CDATA[/dashboard Memory: 42.63MB used
/brand-deep-dive Memory: 42.63MB used
/marketing-dashboard Memory: 42.63MB used
/executive-summary Memory: 42.63MB used

=== Memory Usage Summary ===
/dashboard: 42.63MB used, 92.89MB total
/brand-deep-dive: 42.63MB used, 92.89MB total
/marketing-dashboard: 42.63MB used, 92.89MB total
/executive-summary: 42.63MB used, 92.89MB total
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test network performance and resource loading" classname="comprehensive-performance.spec.ts" time="7.17">
<system-out>
<![CDATA[Network Performance:
Domain Lookup: 0.00ms
Connection: 0.00ms
Request: 435.70ms
Response: 22.70ms
DOM Processing: 11.20ms
Total Resources: 35
Slow Resources (>1s): 0
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test performance under different network conditions" classname="comprehensive-performance.spec.ts" time="7.469">
<system-out>
<![CDATA[Dashboard load time with slow network: 2336ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test performance with large datasets" classname="comprehensive-performance.spec.ts" time="13.529">
<system-out>
<![CDATA[/marketing-dashboard with data rendering: 4187ms
/brand-deep-dive with data rendering: 4410ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test scroll performance on long pages" classname="comprehensive-performance.spec.ts" time="6.964">
<system-out>
<![CDATA[Scroll performance: 1016ms
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Performance Testing › should test chart rendering performance" classname="comprehensive-performance.spec.ts" time="6.565">
</testcase>
<testcase name="Comprehensive Performance Testing › should test accessibility performance" classname="comprehensive-performance.spec.ts" time="8.026">
<system-out>
<![CDATA[/dashboard accessibility scan: 1218ms
Accessibility elements found: { ariaLabels: [33m21[39m, roles: [33m26[39m, headings: [33m3[39m, landmarks: [33m0[39m }
/dashboard landmarks: 0
/marketing-dashboard accessibility scan: 1376ms
Accessibility elements found: { ariaLabels: [33m1[39m, roles: [33m7[39m, headings: [33m4[39m, landmarks: [33m0[39m }
/marketing-dashboard landmarks: 0
/admin accessibility scan: 782ms
Accessibility elements found: { ariaLabels: [33m1[39m, roles: [33m2[39m, headings: [33m4[39m, landmarks: [33m0[39m }
/admin landmarks: 0
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="comprehensive-test-runner.spec.ts" timestamp="2025-05-30T10:19:55.392Z" hostname="chromium" tests="8" failures="1" skipped="0" time="100.164" errors="0">
<testcase name="Comprehensive Application Test Suite › should run complete application smoke test" classname="comprehensive-test-runner.spec.ts" time="17.034">
<system-out>
<![CDATA[🚀 Starting comprehensive application smoke test...
📄 Testing Dashboard...
✅ Dashboard: 1969ms
📄 Testing Brand Deep Dive...
✅ Brand Deep Dive: 1027ms
📄 Testing Marketing Dashboard...
✅ Marketing Dashboard: 1078ms
📄 Testing Executive Summary...
✅ Executive Summary: 1619ms
📄 Testing Budget...
✅ Budget: 774ms
📄 Testing AI Assistant...
✅ AI Assistant: 1119ms
🔐 Testing admin pages...
✅ Admin Dashboard: Accessible
✅ Admin Users: Accessible
✅ Admin Roles: Accessible
✅ Admin Permissions: Accessible

📊 SMOKE TEST SUMMARY:
========================
✅ Dashboard: 1969ms
✅ Brand Deep Dive: 1027ms
✅ Marketing Dashboard: 1078ms
✅ Executive Summary: 1619ms
✅ Budget: 774ms
✅ AI Assistant: 1119ms

🎯 Results: 6/6 tests passed
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify authentication system works correctly" classname="comprehensive-test-runner.spec.ts" time="19.118">
<failure message="comprehensive-test-runner.spec.ts:122:7 should verify authentication system works correctly" type="FAILURE">
<![CDATA[  [chromium] › comprehensive-test-runner.spec.ts:122:7 › Comprehensive Application Test Suite › should verify authentication system works correctly 

    TimeoutError: locator.waitFor: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible


       at utils/test-helpers.ts:46

      44 |
      45 |     // Wait for the button to be visible
    > 46 |     await credentialsTab.waitFor({ state: 'visible', timeout: 10000 });
         |                          ^
      47 |     await credentialsTab.click();
      48 |     await page.waitForTimeout(1500); // Wait for tab switch animation
      49 |
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔐 Testing authentication system...
Sign in failed: locator.waitFor: Timeout 10000ms exceeded.
Call log:
[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible[22m

    at signInWithCredentials [90m(/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/utils/test-helpers.ts:46:26[90m)[39m
    at signInAsAdmin [90m(/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/utils/test-helpers.ts:75:3[90m)[39m
    at [90m/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/comprehensive-test-runner.spec.ts:127:7 {
  name: [32m'TimeoutError'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@25'[39m,
    location: {
      file: [32m'/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts'[39m,
      line: [33m46[39m,
      column: [33m26[39m,
      function: [32m'signInWithCredentials'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'locator.waitFor(button >> internal:has-text="Username/Password"i)'[39m,
    apiName: [32m'locator.waitFor'[39m,
    params: {
      selector: [32m'button >> internal:has-text="Username/Password"i'[39m,
      strict: [33mtrue[39m,
      omitReturnValue: [33mtrue[39m,
      state: [32m'visible'[39m,
      timeout: [33m10000[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@25'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1748600540158[39m,
    error: {
      message: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\x1B[22m\n"[39m,
      stack: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\x1B[22m\n"[39m +
        [32m'\n'[39m +
        [32m'    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\n'[39m +
        [32m'    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\n'[39m +
        [32m'    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7'[39m,
      cause: [90mundefined[39m
    }
  }
}
❌ Authentication test failed: locator.waitFor: Timeout 10000ms exceeded.
Call log:
[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible[22m

    at signInWithCredentials [90m(/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/utils/test-helpers.ts:46:26[90m)[39m
    at signInAsAdmin [90m(/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/utils/test-helpers.ts:75:3[90m)[39m
    at [90m/Users/<USER>/Projects/NOLK/nolk-v4/[39mtests/e2e/comprehensive-test-runner.spec.ts:127:7 {
  name: [32m'TimeoutError'[39m,
  [[32mSymbol(step)[39m]: {
    stepId: [32m'pw:api@25'[39m,
    location: {
      file: [32m'/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts'[39m,
      line: [33m46[39m,
      column: [33m26[39m,
      function: [32m'signInWithCredentials'[39m
    },
    category: [32m'pw:api'[39m,
    title: [32m'locator.waitFor(button >> internal:has-text="Username/Password"i)'[39m,
    apiName: [32m'locator.waitFor'[39m,
    params: {
      selector: [32m'button >> internal:has-text="Username/Password"i'[39m,
      strict: [33mtrue[39m,
      omitReturnValue: [33mtrue[39m,
      state: [32m'visible'[39m,
      timeout: [33m10000[39m
    },
    boxedStack: [90mundefined[39m,
    steps: [],
    attachmentIndices: [],
    info: TestStepInfoImpl {
      annotations: [],
      _testInfo: [36m[TestInfoImpl][39m,
      _stepId: [32m'pw:api@25'[39m
    },
    complete: [36m[Function: complete][39m,
    endWallTime: [33m1748600540158[39m,
    error: {
      message: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\x1B[22m\n"[39m,
      stack: [32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\n'[39m +
        [32m'Call log:\n'[39m +
        [32m"\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\x1B[22m\n"[39m +
        [32m'\n'[39m +
        [32m'    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\n'[39m +
        [32m'    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\n'[39m +
        [32m'    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7'[39m,
      cause: [90mundefined[39m
    }
  }
}

[[ATTACHMENT|e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify all navigation links work" classname="comprehensive-test-runner.spec.ts" time="12.531">
<system-out>
<![CDATA[🧭 Testing navigation system...
✅ Navigation to Dashboard: Working
✅ Navigation to Marketing Dashboard: Working
✅ Navigation to Executive Summary: Working
✅ Navigation to Budget: Working
✅ Navigation to AI Assistant: Working
🎯 Navigation: 5/6 links working
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify data loading and display" classname="comprehensive-test-runner.spec.ts" time="12.016">
<system-out>
<![CDATA[📊 Testing data loading and display...
📈 Dashboard KPIs: 0 data elements found
⚠️ Dashboard KPIs: No data elements found
📈 Marketing Campaigns: 1 data elements found
✅ Marketing Campaigns: Data loaded successfully
📈 Brand Data: 0 data elements found
⚠️ Brand Data: No data elements found
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify responsive design works" classname="comprehensive-test-runner.spec.ts" time="10.383">
<system-out>
<![CDATA[📱 Testing responsive design...
✅ Desktop (1920x1080): Layout working
✅ Tablet (1024x768): Layout working
✅ Mobile (375x667): Layout working
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify accessibility standards" classname="comprehensive-test-runner.spec.ts" time="9.684">
<system-out>
<![CDATA[♿ Testing accessibility standards...
🔍 /dashboard accessibility:
  - Headings: 3
  - ARIA labels: 21
  - Landmarks: 0
⚠️ /dashboard: No landmarks found, but continuing test
✅ /dashboard: Accessibility standards met
🔍 /marketing-dashboard accessibility:
  - Headings: 4
  - ARIA labels: 1
  - Landmarks: 0
⚠️ /marketing-dashboard: No landmarks found, but continuing test
✅ /marketing-dashboard: Accessibility standards met
🔍 /admin accessibility:
  - Headings: 4
  - ARIA labels: 1
  - Landmarks: 0
⚠️ /admin: No landmarks found, but continuing test
✅ /admin: Accessibility standards met
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should verify performance benchmarks" classname="comprehensive-test-runner.spec.ts" time="8.616">
<system-out>
<![CDATA[⚡ Testing performance benchmarks...
✅ Dashboard: 1929ms (under 8000ms limit)
✅ Marketing Dashboard: 1099ms (under 15000ms limit)
✅ Admin Dashboard: 744ms (under 8000ms limit)
]]>
</system-out>
</testcase>
<testcase name="Comprehensive Application Test Suite › should run final comprehensive validation" classname="comprehensive-test-runner.spec.ts" time="10.782">
<system-out>
<![CDATA[🎯 Running final comprehensive validation...
✅ Journey step: /brand-deep-dive completed successfully
✅ Journey step: /marketing-dashboard completed successfully
✅ Journey step: /executive-summary completed successfully
✅ Journey step: /admin completed successfully
🎉 Comprehensive test suite completed successfully!
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>