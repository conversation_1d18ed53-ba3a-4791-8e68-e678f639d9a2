{"config": {"configFile": "/Users/<USER>/Projects/NOLK/nolk-v4/playwright.config.ts", "rootDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 8}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "tests/reports/e2e-html"}], ["json", {"outputFile": "tests/reports/e2e-results.json"}], ["junit", {"outputFile": "tests/reports/e2e-junit.xml"}], ["list", null], ["github", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:6699", "reuseExistingServer": true, "timeout": 120000, "stdout": "ignore", "stderr": "pipe"}}, "suites": [{"title": "comprehensive-admin-dashboard.spec.ts", "file": "comprehensive-admin-dashboard.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Admin Dashboard Testing", "file": "comprehensive-admin-dashboard.spec.ts", "line": 18, "column": 6, "specs": [{"title": "should load admin dashboard with proper authorization", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 13435, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:19:55.630Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-16b85f59f2456bf32e23", "file": "comprehensive-admin-dashboard.spec.ts", "line": 24, "column": 7}, {"title": "should display admin navigation menu", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 13500, "errors": [], "stdout": [{"text": "Found 7 admin navigation items out of 8 expected\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:19:55.638Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-4792a66ddeeea9a2a3c9", "file": "comprehensive-admin-dashboard.spec.ts", "line": 47, "column": 7}, {"title": "should navigate to and test Users management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 13662, "errors": [], "stdout": [{"text": "Users table has 6 headers and 14 rows\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:19:55.635Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-6b7414a6e27d0d9ab046", "file": "comprehensive-admin-dashboard.spec.ts", "line": 83, "column": 7}, {"title": "should navigate to and test Roles management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 13585, "errors": [], "stdout": [{"text": "Roles table has 4 headers and 4 rows\n"}, {"text": "Found 10 role-related elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:19:55.637Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-da56bb8214f3323a0543", "file": "comprehensive-admin-dashboard.spec.ts", "line": 110, "column": 7}, {"title": "should navigate to and test Permissions management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 13625, "errors": [], "stdout": [{"text": "Permissions table has 6 headers and 2 rows\n"}, {"text": "Found 3 permission-related elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:19:55.636Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-88ec15a23f6ff3204adc", "file": "comprehensive-admin-dashboard.spec.ts", "line": 132, "column": 7}, {"title": "should navigate to and test Groups management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 13700, "errors": [], "stdout": [{"text": "Groups table has 4 headers and 4 rows\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:19:55.632Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-55174642dd7fb926e06d", "file": "comprehensive-admin-dashboard.spec.ts", "line": 154, "column": 7}, {"title": "should navigate to and test Brands management", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 13725, "errors": [], "stdout": [{"text": "Brands table has 8 headers and 23 rows\n"}, {"text": "Found 0 brand-related elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:19:55.634Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-5741a3e4a32e5a8a9c58", "file": "comprehensive-admin-dashboard.spec.ts", "line": 171, "column": 7}, {"title": "should navigate to and test Settings page", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 12963, "errors": [], "stdout": [{"text": "Found 0 settings elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:19:55.634Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-6ceb279fd5af9e228700", "file": "comprehensive-admin-dashboard.spec.ts", "line": 194, "column": 7}, {"title": "should navigate to and test Backups page", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 6399, "errors": [], "stdout": [{"text": "Found 3 backup-related elements\n"}, {"text": "Found 0 backup buttons\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:08.779Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-35186b574f191d8a32d1", "file": "comprehensive-admin-dashboard.spec.ts", "line": 210, "column": 7}, {"title": "should navigate to and test DB Structure page", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 7561, "errors": [], "stdout": [{"text": "Found 0 database structure elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:09.305Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-1bff8957448028ec1efb", "file": "comprehensive-admin-dashboard.spec.ts", "line": 231, "column": 7}, {"title": "should test admin API endpoints", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 7103, "errors": [], "stdout": [{"text": "/api/admin/users: 200\n"}, {"text": "/api/admin/roles: 200\n"}, {"text": "/api/admin/permissions: 200\n"}, {"text": "/api/admin/groups: 200\n"}, {"text": "/api/admin/brands: 200\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:09.313Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-e6258fb3366cf621289e", "file": "comprehensive-admin-dashboard.spec.ts", "line": 247, "column": 7}, {"title": "should handle admin form submissions", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 8171, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:09.411Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-478df801d18c98c9299f", "file": "comprehensive-admin-dashboard.spec.ts", "line": 270, "column": 7}, {"title": "should be responsive across different screen sizes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 10569, "errors": [], "stdout": [{"text": "Admin navigation visible on desktop\n"}, {"text": "Admin navigation visible on tablet\n"}, {"text": "Admin navigation visible on mobile\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:09.458Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-ad4adc0ebd440367e42f", "file": "comprehensive-admin-dashboard.spec.ts", "line": 301, "column": 7}, {"title": "should have proper accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 7416, "errors": [], "stdout": [{"text": "Admin Dashboard landmarks: 0\n"}, {"text": "Admin Dashboard accessibility info: { ariaLabels: \u001b[33m1\u001b[39m, roles: \u001b[33m2\u001b[39m, headings: \u001b[33m4\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:09.485Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-eb25bc91404e7bb88691", "file": "comprehensive-admin-dashboard.spec.ts", "line": 329, "column": 7}, {"title": "should load within performance thresholds", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 7323, "errors": [], "stdout": [{"text": "Admin Dashboard loaded in 1672ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:09.512Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-53d39c0592d076d33218", "file": "comprehensive-admin-dashboard.spec.ts", "line": 344, "column": 7}, {"title": "should not have critical console errors", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 7321, "errors": [], "stdout": [{"text": "Critical console errors found: [\n  \u001b[32m'Error fetching sales channels: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3801:56)'\u001b[39m,\n  \u001b[32m'Error fetching brands: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\\n'\u001b[39m +\n    \u001b[32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'\u001b[39m,\n  \u001b[32m'Error fetching saved views: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\\n'\u001b[39m +\n    \u001b[32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\\n'\u001b[39m +\n    \u001b[32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'\u001b[39m\n]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:09.549Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "553aa5e56666af0f27b5-20f33e1c3e3f352722f1", "file": "comprehensive-admin-dashboard.spec.ts", "line": 359, "column": 7}]}]}, {"title": "comprehensive-api.spec.ts", "file": "comprehensive-api.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive API Testing", "file": "comprehensive-api.spec.ts", "line": 7, "column": 6, "specs": [{"title": "should test dashboard API endpoints", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 6498, "errors": [], "stdout": [{"text": "/api/dashboard/flexible-kpis: 200\n"}, {"text": "/api/dashboard/flexible-kpis returned data: [\n  \u001b[32m'Gross Revenue'\u001b[39m,         \u001b[32m'Net Revenue'\u001b[39m,\n  \u001b[32m'Gross Margin'\u001b[39m,          \u001b[32m'Adspend'\u001b[39m,\n  \u001b[32m'Contribution Margin'\u001b[39m,   \u001b[32m'% Gross Margin'\u001b[39m,\n  \u001b[32m'% Contribution Margin'\u001b[39m, \u001b[32m'% Adspend'\u001b[39m,\n  \u001b[32m'Landed Cost'\u001b[39m,           \u001b[32m'Fulfillment Cost'\u001b[39m,\n  \u001b[32m'Transaction Cost'\u001b[39m,      \u001b[32m'Discount'\u001b[39m,\n  \u001b[32m'Refund'\u001b[39m,                \u001b[32m'% Landed Cost'\u001b[39m,\n  \u001b[32m'% Fulfillment Cost'\u001b[39m,    \u001b[32m'% Transaction Cost'\u001b[39m,\n  \u001b[32m'% Discount'\u001b[39m,            \u001b[32m'% Refund'\u001b[39m,\n  \u001b[32m'ACOS'\u001b[39m,                  \u001b[32m'TACOS'\u001b[39m,\n  \u001b[32m'TCAC'\u001b[39m,                  \u001b[32m'Website Traffic'\u001b[39m,\n  \u001b[32m'Conversion Rate'\u001b[39m,       \u001b[32m'Organic Traffic'\u001b[39m,\n  \u001b[32m'Paid Traffic'\u001b[39m\n]\n"}, {"text": "/api/dashboard/kpi-data: 404\n"}, {"text": "/api/dashboard/chart-data: 404\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:15.189Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-d46edda83bf5bf3f7920", "file": "comprehensive-api.spec.ts", "line": 13, "column": 7}, {"title": "should test marketing API endpoints", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 5433, "errors": [], "stdout": [{"text": "/api/marketing/campaigns: 404\n"}, {"text": "/api/marketing/kpis: 404\n"}, {"text": "/api/marketing/spend-breakdown: 404\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:16.424Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-e6c629d90e606cf44106", "file": "comprehensive-api.spec.ts", "line": 35, "column": 7}, {"title": "should test admin API endpoints with proper authorization", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 7367, "errors": [], "stdout": [{"text": "/api/admin/users: 200\n"}, {"text": "/api/admin/users returned data: [\n  \u001b[32m'0'\u001b[39m,  \u001b[32m'1'\u001b[39m,  \u001b[32m'2'\u001b[39m,  \u001b[32m'3'\u001b[39m,\n  \u001b[32m'4'\u001b[39m,  \u001b[32m'5'\u001b[39m,  \u001b[32m'6'\u001b[39m,  \u001b[32m'7'\u001b[39m,\n  \u001b[32m'8'\u001b[39m,  \u001b[32m'9'\u001b[39m,  \u001b[32m'10'\u001b[39m, \u001b[32m'11'\u001b[39m,\n  \u001b[32m'12'\u001b[39m, \u001b[32m'13'\u001b[39m\n]\n"}, {"text": "/api/admin/roles: 200\n"}, {"text": "/api/admin/roles returned data: [ \u001b[32m'data'\u001b[39m, \u001b[32m'pagination'\u001b[39m ]\n"}, {"text": "/api/admin/permissions: 200\n"}, {"text": "/api/admin/permissions returned data: [ \u001b[32m'0'\u001b[39m, \u001b[32m'1'\u001b[39m ]\n"}, {"text": "/api/admin/groups: 200\n"}, {"text": "/api/admin/groups returned data: [ \u001b[32m'data'\u001b[39m, \u001b[32m'pagination'\u001b[39m ]\n"}, {"text": "/api/admin/brands: 200\n"}, {"text": "/api/admin/brands returned data: [ \u001b[32m'data'\u001b[39m, \u001b[32m'pagination'\u001b[39m ]\n"}, {"text": "/api/admin/settings: 401\n"}, {"text": "/api/admin/settings: Unauthorized (expected for non-admin users)\n"}, {"text": "/api/admin/backups: 401\n"}, {"text": "/api/admin/backups: Unauthorized (expected for non-admin users)\n"}, {"text": "/api/admin/db-structure: 401\n"}, {"text": "/api/admin/db-structure: Unauthorized (expected for non-admin users)\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:16.845Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-3b1cf828069c693bdc8e", "file": "comprehensive-api.spec.ts", "line": 57, "column": 7}, {"title": "should test budget API endpoint", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5805, "errors": [], "stdout": [{"text": "Budget API: 200\n"}, {"text": "Budget API returned data: [ \u001b[32m'budgets'\u001b[39m ]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:16.876Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-c9f347ceb02be0529cf1", "file": "comprehensive-api.spec.ts", "line": 87, "column": 7}, {"title": "should test AI assistant API endpoint", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 9951, "errors": [], "stdout": [{"text": "AI Assistant GET: 405\n"}, {"text": "AI Assistant POST: 500\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:16.879Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-960ba329222c9fe6ef5f", "file": "comprehensive-api.spec.ts", "line": 101, "column": 7}, {"title": "should test API error handling", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 6149, "errors": [], "stdout": [{"text": "/api/nonexistent: 404\n"}, {"text": "/api/dashboard/invalid: 404\n"}, {"text": "/api/admin/invalid: 404\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:16.911Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-69fae84ccd164bb43df7", "file": "comprehensive-api.spec.ts", "line": 125, "column": 7}, {"title": "should test API with query parameters", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 6164, "errors": [], "stdout": [{"text": "/api/dashboard/flexible-kpis?startDate=2024-01-01&endDate=2024-12-31: 200\n"}, {"text": "/api/dashboard/flexible-kpis?startDate=2024-01-01&endDate=2024-12-31 with params returned: [\n  \u001b[32m'Gross Revenue'\u001b[39m,         \u001b[32m'Net Revenue'\u001b[39m,\n  \u001b[32m'Gross Margin'\u001b[39m,          \u001b[32m'Adspend'\u001b[39m,\n  \u001b[32m'Contribution Margin'\u001b[39m,   \u001b[32m'% Gross Margin'\u001b[39m,\n  \u001b[32m'% Contribution Margin'\u001b[39m, \u001b[32m'% Adspend'\u001b[39m,\n  \u001b[32m'Landed Cost'\u001b[39m,           \u001b[32m'Fulfillment Cost'\u001b[39m,\n  \u001b[32m'Transaction Cost'\u001b[39m,      \u001b[32m'Discount'\u001b[39m,\n  \u001b[32m'Refund'\u001b[39m,                \u001b[32m'% Landed Cost'\u001b[39m,\n  \u001b[32m'% Fulfillment Cost'\u001b[39m,    \u001b[32m'% Transaction Cost'\u001b[39m,\n  \u001b[32m'% Discount'\u001b[39m,            \u001b[32m'% Refund'\u001b[39m,\n  \u001b[32m'ACOS'\u001b[39m,                  \u001b[32m'TACOS'\u001b[39m,\n  \u001b[32m'TCAC'\u001b[39m,                  \u001b[32m'Website Traffic'\u001b[39m,\n  \u001b[32m'Conversion Rate'\u001b[39m,       \u001b[32m'Organic Traffic'\u001b[39m,\n  \u001b[32m'Paid Traffic'\u001b[39m\n]\n"}, {"text": "/api/marketing/campaigns?brand=test&currency=USD: 404\n"}, {"text": "/api/budget?brand=test&brands=true: 200\n"}, {"text": "/api/budget?brand=test&brands=true with params returned: [ \u001b[32m'brands'\u001b[39m ]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:17.590Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-b941ed9034005094099f", "file": "comprehensive-api.spec.ts", "line": 146, "column": 7}, {"title": "should test API response times", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 4980, "errors": [], "stdout": [{"text": "/api/dashboard/flexible-kpis: 200 in 265ms\n"}, {"text": "/api/marketing/campaigns: 404 in 46ms\n"}, {"text": "/api/budget: 200 in 30ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:20.037Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-6f18c7464c495e0725a1", "file": "comprehensive-api.spec.ts", "line": 168, "column": 7}, {"title": "should test API data validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 5674, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:21.690Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-71bf185d8fda80937344", "file": "comprehensive-api.spec.ts", "line": 192, "column": 7}, {"title": "should test API authentication requirements", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 5907, "errors": [], "stdout": [{"text": "/api/admin/users (unauthenticated): 200\n"}, {"text": "/api/admin/users (unauthenticated): Failed as expected - Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m200\u001b[39m\nReceived array: \u001b[31m[401, 302, 403]\u001b[39m\n"}, {"text": "/api/admin/roles (unauthenticated): 200\n"}, {"text": "/api/admin/roles (unauthenticated): Failed as expected - Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m200\u001b[39m\nReceived array: \u001b[31m[401, 302, 403]\u001b[39m\n"}, {"text": "/api/dashboard/flexible-kpis (unauthenticated): 200\n"}, {"text": "/api/dashboard/flexible-kpis (unauthenticated): Failed as expected - Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m200\u001b[39m\nReceived array: \u001b[31m[401, 302, 403]\u001b[39m\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:21.864Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-d5e579e72fbc4a710dae", "file": "comprehensive-api.spec.ts", "line": 218, "column": 7}, {"title": "should test API CORS headers", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5595, "errors": [], "stdout": [{"text": "API Response Headers:\n"}, {"text": "Content-Type: application/json\n"}, {"text": "Cache-Control: no-store, max-age=0\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:22.684Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-e5e6632f7baa1e16dff4", "file": "comprehensive-api.spec.ts", "line": 243, "column": 7}, {"title": "should test API rate limiting (if implemented)", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 6218, "errors": [], "stdout": [{"text": "Request 1: 200\n"}, {"text": "Request 2: 200\n"}, {"text": "Request 3: 200\n"}, {"text": "Request 4: 200\n"}, {"text": "Request 5: 200\n"}, {"text": "Request 6: 200\n"}, {"text": "Request 7: 200\n"}, {"text": "Request 8: 200\n"}, {"text": "Request 9: 200\n"}, {"text": "Request 10: 200\n"}, {"text": "Successful requests: 10, Rate limited: 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:23.065Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-79e5ae70e269e15bab49", "file": "comprehensive-api.spec.ts", "line": 262, "column": 7}, {"title": "should test API monitoring endpoints", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 6430, "errors": [], "stdout": [{"text": "/api/monitoring/health: 200\n"}, {"text": "/api/monitoring/health health check: {\n  overall: \u001b[32m'healthy'\u001b[39m,\n  timestamp: \u001b[33m1748600429897\u001b[39m,\n  uptime: \u001b[33m38.067379208\u001b[39m,\n  version: \u001b[32m'0.1.0'\u001b[39m,\n  environment: \u001b[32m'development'\u001b[39m,\n  checks: [\n    {\n      service: \u001b[32m'SQLite Database'\u001b[39m,\n      status: \u001b[32m'healthy'\u001b[39m,\n      responseTime: \u001b[33m0\u001b[39m,\n      details: \u001b[32m'Database responding normally'\u001b[39m,\n      lastChecked: \u001b[33m1748600429876\u001b[39m\n    },\n    {\n      service: \u001b[32m'Redshift Database'\u001b[39m,\n      status: \u001b[32m'healthy'\u001b[39m,\n      responseTime: \u001b[33m20\u001b[39m,\n      details: \u001b[32m'Redshift responding normally'\u001b[39m,\n      lastChecked: \u001b[33m1748600429896\u001b[39m\n    },\n    {\n      service: \u001b[32m'NextAuth'\u001b[39m,\n      status: \u001b[32m'healthy'\u001b[39m,\n      details: \u001b[32m'NextAuth configured'\u001b[39m,\n      lastChecked: \u001b[33m1748600429896\u001b[39m\n    },\n    {\n      service: \u001b[32m'Google OAuth'\u001b[39m,\n      status: \u001b[32m'healthy'\u001b[39m,\n      details: \u001b[32m'Google OAuth configured'\u001b[39m,\n      lastChecked: \u001b[33m1748600429896\u001b[39m\n    }\n  ],\n  performance: {\n    memory: {\n      rss: \u001b[33m2968207360\u001b[39m,\n      heapTotal: \u001b[33m437485568\u001b[39m,\n      heapUsed: \u001b[33m403414952\u001b[39m,\n      external: \u001b[33m62363260\u001b[39m,\n      arrayBuffers: \u001b[33m59778163\u001b[39m\n    },\n    cpu: { user: \u001b[33m69675638\u001b[39m, system: \u001b[33m21827396\u001b[39m },\n    queries: {\n      totalQueries: \u001b[33m0\u001b[39m,\n      slowQueries: \u001b[33m0\u001b[39m,\n      avgDuration: \u001b[33m0\u001b[39m,\n      cacheHitRate: \u001b[33m0\u001b[39m,\n      topSlowQueries: []\n    },\n    apis: {\n      totalRequests: \u001b[33m0\u001b[39m,\n      errorRequests: \u001b[33m0\u001b[39m,\n      slowRequests: \u001b[33m0\u001b[39m,\n      avgDuration: \u001b[33m0\u001b[39m,\n      errorRate: \u001b[33m0\u001b[39m,\n      topSlowEndpoints: []\n    },\n    errors: { totalErrors: \u001b[33m0\u001b[39m, errorsByType: {}, recentErrors: [] }\n  },\n  database: {\n    sqlite: {\n      status: \u001b[32m'healthy'\u001b[39m,\n      responseTime: \u001b[33m0\u001b[39m,\n      details: \u001b[32m'Database responding normally'\u001b[39m\n    },\n    redshift: {\n      status: \u001b[32m'healthy'\u001b[39m,\n      responseTime: \u001b[33m20\u001b[39m,\n      poolStats: \u001b[36m[Object]\u001b[39m,\n      details: \u001b[32m'Redshift responding normally'\u001b[39m\n    }\n  }\n}\n"}, {"text": "/api/monitoring/status: 404\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:23.756Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "db640a72db8d04ba1693-46251ebc505ea0d64369", "file": "comprehensive-api.spec.ts", "line": 293, "column": 7}]}]}, {"title": "comprehensive-brand-deep-dive.spec.ts", "file": "comprehensive-brand-deep-dive.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Brand Deep Dive Testing", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 15, "column": 6, "specs": [{"title": "should load brand deep dive page with all components", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 7991, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:24.218Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-60ee1fcb54ee3ecf5abe", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 21, "column": 7}, {"title": "should display brand selector and allow brand selection", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 7093, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:25.024Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-452374d3464cf88bf199", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 40, "column": 7}, {"title": "should display KPI cards specific to selected brand", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 5874, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:26.836Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-9dba5927d678596274c4", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 67, "column": 7}, {"title": "should render brand performance charts", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 8026, "errors": [], "stdout": [{"text": "Found 1 charts with selector: svg[class*=\"chart\"]\n"}, {"text": "Found 23 charts on brand deep dive page\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:27.369Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-d399a48d82b2d976c1e7", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 90, "column": 7}, {"title": "should display marketing campaigns for selected brand", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 5909, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:27.785Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-f42e06c014abcba64d06", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 116, "column": 7}, {"title": "should have functional brand-specific filters", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8550, "errors": [], "stdout": [{"text": "Found 1 active filters\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:28.284Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-8166cd95e0738d86bf81", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 142, "column": 7}, {"title": "should display tabs and allow tab navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 5838, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:29.290Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-298a596789ceb9b7c03a", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 183, "column": 7}, {"title": "should handle brand comparison if available", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 5727, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:30.196Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-93fbfee592aa608119b0", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 210, "column": 7}, {"title": "should handle export functionality if available", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 5960, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:32.121Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-a55dc13a303e9a88f4dc", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 230, "column": 7}, {"title": "should be responsive across different screen sizes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 9208, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:32.214Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-a3770fab3a14a421942b", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 248, "column": 7}, {"title": "should have proper accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 5958, "errors": [], "stdout": [{"text": "Brand Deep Dive landmarks: 0\n"}, {"text": "Brand Deep Dive accessibility info: { ariaLabels: \u001b[33m1\u001b[39m, roles: \u001b[33m6\u001b[39m, headings: \u001b[33m3\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:32.715Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-e6fb6a8910206d7fe498", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 280, "column": 7}, {"title": "should load within performance thresholds", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 5646, "errors": [], "stdout": [{"text": "Brand Deep Dive loaded in 962ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:33.700Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2e173649ad4b74a0ea79-4102223de0ec5edbd3dd", "file": "comprehensive-brand-deep-dive.spec.ts", "line": 295, "column": 7}]}]}, {"title": "comprehensive-budget-ai.spec.ts", "file": "comprehensive-budget-ai.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Budget Page Testing", "file": "comprehensive-budget-ai.spec.ts", "line": 17, "column": 6, "specs": [{"title": "should load budget page with all components", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 6501, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:35.135Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-acea5ba122acf431d3cd", "file": "comprehensive-budget-ai.spec.ts", "line": 23, "column": 7}, {"title": "should display budget data and charts", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 8224, "errors": [], "stdout": [{"text": "Budget table has 17 headers and 12 rows\n"}, {"text": "Found 1 charts with selector: svg[class*=\"chart\"]\n"}, {"text": "Found 5 budget-related elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:35.402Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-dadb25d4613a7e1ed40b", "file": "comprehensive-budget-ai.spec.ts", "line": 42, "column": 7}, {"title": "should handle budget filters and brand selection", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 5688, "errors": [], "stdout": [{"text": "Found 0 active filters\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:35.931Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-6c267e5c278bce4cbdac", "file": "comprehensive-budget-ai.spec.ts", "line": 64, "column": 7}, {"title": "should test budget API endpoint", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5758, "errors": [], "stdout": [{"text": "Budget API response: 200\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:36.843Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-d605c769b96857ddf2c1", "file": "comprehensive-budget-ai.spec.ts", "line": 91, "column": 7}, {"title": "should be responsive on different screen sizes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 8586, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:38.087Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-470ff1f0d1cd3ac31e75", "file": "comprehensive-budget-ai.spec.ts", "line": 104, "column": 7}]}, {"title": "Comprehensive AI Assistant Testing", "file": "comprehensive-budget-ai.spec.ts", "line": 125, "column": 6, "specs": [{"title": "should load AI assistant page with all components", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 7290, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:38.680Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-f33e48accaf786fadff2", "file": "comprehensive-budget-ai.spec.ts", "line": 131, "column": 7}, {"title": "should display chat interface", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 6643, "errors": [], "stdout": [{"text": "Found 1 chat interface elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:39.352Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-b03aadffa76cbe29a987", "file": "comprehensive-budget-ai.spec.ts", "line": 150, "column": 7}, {"title": "should handle message input and sending", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 5634, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:41.430Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-be06faa88ea896e117c7", "file": "comprehensive-budget-ai.spec.ts", "line": 175, "column": 7}, {"title": "should display conversation history", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 6023, "errors": [], "stdout": [{"text": "Found 0 messages in conversation history\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:41.625Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-0ab4b4350709df6d0e8f", "file": "comprehensive-budget-ai.spec.ts", "line": 207, "column": 7}, {"title": "should handle AI assistant features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 6089, "errors": [], "stdout": [{"text": "Found 0 AI assistant features\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:41.643Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-48fe36971c94b67f4e5a", "file": "comprehensive-budget-ai.spec.ts", "line": 232, "column": 7}, {"title": "should test AI assistant API endpoint", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 5742, "errors": [], "stdout": [{"text": "AI Assistant API failed: Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m200\u001b[39m\nReceived: \u001b[31m405\u001b[39m\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:42.606Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-44d1f0ddb113fe3c9d6a", "file": "comprehensive-budget-ai.spec.ts", "line": 264, "column": 7}, {"title": "should handle suggested questions or templates", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 5384, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:43.630Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-a309bcd3bc9bbe857353", "file": "comprehensive-budget-ai.spec.ts", "line": 277, "column": 7}, {"title": "should be responsive on different screen sizes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 8952, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:45.978Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-6740ecd54b234653501d", "file": "comprehensive-budget-ai.spec.ts", "line": 306, "column": 7}, {"title": "should have proper accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 5873, "errors": [], "stdout": [{"text": "AI Assistant landmarks: 0\n"}, {"text": "AI Assistant accessibility info: { ariaL<PERSON>ls: \u001b[33m1\u001b[39m, roles: \u001b[33m2\u001b[39m, headings: \u001b[33m2\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:45.999Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-9077241b2920b9ab9f29", "file": "comprehensive-budget-ai.spec.ts", "line": 333, "column": 7}, {"title": "should load within performance thresholds", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 6019, "errors": [], "stdout": [{"text": "AI Assistant loaded in 1111ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:46.681Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "737a8975eec8bf10c274-664eca952a27eddf890c", "file": "comprehensive-budget-ai.spec.ts", "line": 348, "column": 7}]}]}, {"title": "comprehensive-dashboard.spec.ts", "file": "comprehensive-dashboard.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Dashboard Testing", "file": "comprehensive-dashboard.spec.ts", "line": 16, "column": 6, "specs": [{"title": "should load dashboard with all core components", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 6291, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:47.072Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-4f9a72da30ecc9aacaf4", "file": "comprehensive-dashboard.spec.ts", "line": 22, "column": 7}, {"title": "should display and interact with KPI cards", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 7274, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:47.654Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-f917a0bb813545912932", "file": "comprehensive-dashboard.spec.ts", "line": 43, "column": 7}, {"title": "should render charts correctly", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 9141, "errors": [], "stdout": [{"text": "Found 11 charts with selector: .recharts-wrapper\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:47.736Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-630828bc8bbb22b424af", "file": "comprehensive-dashboard.spec.ts", "line": 69, "column": 7}, {"title": "should have functional filters", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8710, "errors": [], "stdout": [{"text": "Found 1 active filters: [ \u001b[32m'input[type=\"date\"] (1/2)'\u001b[39m ]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:48.354Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-ac4ef4a0b79a2ac2ecfa", "file": "comprehensive-dashboard.spec.ts", "line": 79, "column": 7}, {"title": "should handle responsive design", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 9458, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:49.019Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-fb423ac24a6e8dfe0e37", "file": "comprehensive-dashboard.spec.ts", "line": 115, "column": 7}, {"title": "should have proper accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 6375, "errors": [], "stdout": [{"text": "Dashboard landmarks: 0\n"}, {"text": "Accessibility info: { aria<PERSON><PERSON>ls: \u001b[33m21\u001b[39m, roles: \u001b[33m26\u001b[39m, headings: \u001b[33m3\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:51.878Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-ca9cc54405668b80412b", "file": "comprehensive-dashboard.spec.ts", "line": 136, "column": 7}, {"title": "should handle navigation between dashboard sections", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 20638, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:52.710Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-e3724662986d23136e41", "file": "comprehensive-dashboard.spec.ts", "line": 151, "column": 7}, {"title": "should handle data refresh and updates", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 8180, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:53.368Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-9d6d44a615b7160e3f91", "file": "comprehensive-dashboard.spec.ts", "line": 186, "column": 7}, {"title": "should not have critical console errors", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 7115, "errors": [], "stdout": [{"text": "Critical console errors found: [\n  \u001b[32m'Error fetching saved views: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\\n'\u001b[39m +\n    \u001b[32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\\n'\u001b[39m +\n    \u001b[32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'\u001b[39m,\n  \u001b[32m'Error fetching brands: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\\n'\u001b[39m +\n    \u001b[32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'\u001b[39m,\n  \u001b[32m'Error fetching sales channels: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3801:56)'\u001b[39m\n]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:54.934Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-4da02c01f94906859d61", "file": "comprehensive-dashboard.spec.ts", "line": 202, "column": 7}, {"title": "should load within performance thresholds", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 7105, "errors": [], "stdout": [{"text": "Dashboard loaded in 2160ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:54.941Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "16e8e095986a497091c8-95be82971e6ac17c6b8a", "file": "comprehensive-dashboard.spec.ts", "line": 232, "column": 7}]}]}, {"title": "comprehensive-error-handling.spec.ts", "file": "comprehensive-error-handling.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Error Handling and Edge Cases", "file": "comprehensive-error-handling.spec.ts", "line": 10, "column": 6, "specs": [{"title": "should handle 404 errors gracefully", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 10722, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:40:50", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 50, "line": 40}, "snippet": "\u001b[0m \u001b[90m 38 |\u001b[39m\n \u001b[90m 39 |\u001b[39m       \u001b[90m// Either shows 404, redirects, or shows a valid page (some routes might be valid)\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 40 |\u001b[39m       expect(is404 \u001b[33m||\u001b[39m isRedirect \u001b[33m||\u001b[39m isValidPage)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 41 |\u001b[39m       \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m`error-404-${url.replace(/\\//g, '-')}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 42 |\u001b[39m     }\n \u001b[90m 43 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 50, "line": 40}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 38 |\u001b[39m\n \u001b[90m 39 |\u001b[39m       \u001b[90m// Either shows 404, redirects, or shows a valid page (some routes might be valid)\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 40 |\u001b[39m       expect(is404 \u001b[33m||\u001b[39m isRedirect \u001b[33m||\u001b[39m isValidPage)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 41 |\u001b[39m       \u001b[36mawait\u001b[39m takeScreenshot(page\u001b[33m,\u001b[39m \u001b[32m`error-404-${url.replace(/\\//g, '-')}`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 42 |\u001b[39m     }\n \u001b[90m 43 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:40:50\u001b[22m"}], "stdout": [{"text": "/nonexistent-page -> http://localhost:6699/nonexistent-page\n"}, {"text": "/dashboard/invalid -> http://localhost:6699/dashboard/invalid\n"}, {"text": "/admin/nonexistent -> http://localhost:6699/admin/nonexistent\n"}, {"text": "/marketing-dashboard/invalid-campaign-id -> http://localhost:6699/marketing-dashboard/invalid-campaign-id\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:56.882Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-bb43b-andle-404-errors-gracefully-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 50, "line": 40}}], "status": "unexpected"}], "id": "fced13aa990448fd797b-2e176de4e4573722893a", "file": "comprehensive-error-handling.spec.ts", "line": 16, "column": 7}, {"title": "should handle network errors and offline scenarios", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 10175, "errors": [], "stdout": [{"text": "Expected network error: page.goto: net::ERR_INTERNET_DISCONNECTED at http://localhost:6699/marketing-dashboard\nCall log:\n\u001b[2m  - navigating to \"http://localhost:6699/marketing-dashboard\", waiting until \"load\"\u001b[22m\n\n"}, {"text": "Offline handling: Not detected\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:57.069Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-200626f1d67aaf8ff047", "file": "comprehensive-error-handling.spec.ts", "line": 45, "column": 7}, {"title": "should handle API errors gracefully", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 6371, "errors": [], "stdout": [{"text": "API error handling: Not detected\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:58.262Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-e29093ae1f4ba65fe1ad", "file": "comprehensive-error-handling.spec.ts", "line": 77, "column": 7}, {"title": "should handle authentication errors", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "failed", "duration": 8415, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:123:43", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 43, "line": 123}, "snippet": "\u001b[0m \u001b[90m 121 |\u001b[39m\n \u001b[90m 122 |\u001b[39m       \u001b[90m// Either redirected to sign-in or stayed on dashboard (if session is still valid)\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 123 |\u001b[39m       expect(isOnSignIn \u001b[33m||\u001b[39m isOnDashboard)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 124 |\u001b[39m\n \u001b[90m 125 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m`${url} -> redirected to sign-in`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 126 |\u001b[39m     }\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 43, "line": 123}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 121 |\u001b[39m\n \u001b[90m 122 |\u001b[39m       \u001b[90m// Either redirected to sign-in or stayed on dashboard (if session is still valid)\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 123 |\u001b[39m       expect(isOnSignIn \u001b[33m||\u001b[39m isOnDashboard)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 124 |\u001b[39m\n \u001b[90m 125 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m`${url} -> redirected to sign-in`\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 126 |\u001b[39m     }\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:123:43\u001b[22m"}], "stdout": [{"text": "/dashboard -> redirected to sign-in\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:20:58.483Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-1.png"}, {"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/test-failed-2.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-149a6-andle-authentication-errors-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 43, "line": 123}}], "status": "unexpected"}], "id": "fced13aa990448fd797b-8c537d4584aa02be048a", "file": "comprehensive-error-handling.spec.ts", "line": 99, "column": 7}, {"title": "should handle form validation errors", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 7119, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:01.553Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-d3f696244c2aa4d69252", "file": "comprehensive-error-handling.spec.ts", "line": 132, "column": 7}, {"title": "should handle large data sets without crashing", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 12397, "errors": [], "stdout": [{"text": "Large dataset handling: Page remained responsive\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:02.053Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-5674aa103fe56936cbec", "file": "comprehensive-error-handling.spec.ts", "line": 165, "column": 7}, {"title": "should handle browser back/forward navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 14750, "errors": [], "stdout": [{"text": "Browser navigation: Working correctly\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:02.055Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-908245d7230eea4c023b", "file": "comprehensive-error-handling.spec.ts", "line": 190, "column": 7}, {"title": "should handle page refresh and state preservation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 8062, "errors": [], "stdout": [{"text": "Page refresh: Handled successfully\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:04.637Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-4aac42820df35f47bc43", "file": "comprehensive-error-handling.spec.ts", "line": 219, "column": 7}, {"title": "should handle concurrent user actions", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 7, "status": "timedOut", "duration": 60122, "error": {"message": "\u001b[31mTest timeout of 60000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 60000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 60000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts", "column": 16, "line": 262}, "message": "Error: page.waitForTimeout: Target page, context or browser has been closed\n\n\u001b[0m \u001b[90m 260 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[33mPromise\u001b[39m\u001b[33m.\u001b[39mall(promises)\u001b[33m;\u001b[39m\n \u001b[90m 261 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 262 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 263 |\u001b[39m     \u001b[36mawait\u001b[39m waitForLoadingToComplete(page)\u001b[33m;\u001b[39m\n \u001b[90m 264 |\u001b[39m\n \u001b[90m 265 |\u001b[39m     \u001b[90m// Page should still be functional\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-error-handling.spec.ts:262:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:07.290Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-error-handli-c694f-dle-concurrent-user-actions-chromium/video.webm"}]}], "status": "unexpected"}], "id": "fced13aa990448fd797b-6ef62111590327d4e750", "file": "comprehensive-error-handling.spec.ts", "line": 244, "column": 7}, {"title": "should handle memory leaks during navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 32180, "errors": [], "stdout": [{"text": "Initial memory: 37.77MB\n"}, {"text": "Final memory: 37.77MB\n"}, {"text": "Memory increase: 0.00MB\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:07.249Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-461d3bc92413584e9dd0", "file": "comprehensive-error-handling.spec.ts", "line": 274, "column": 7}, {"title": "should handle edge cases in data filtering", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "passed", "duration": 12016, "errors": [], "stdout": [{"text": "Invalid date range handling: Detected\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:07.981Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-1c76e7613c59f307454c", "file": "comprehensive-error-handling.spec.ts", "line": 315, "column": 7}, {"title": "should handle session timeout gracefully", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 7926, "errors": [], "stdout": [{"text": "Session timeout: Redirected to sign-in\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:08.678Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "fced13aa990448fd797b-5b27b05dfcf3bcea9458", "file": "comprehensive-error-handling.spec.ts", "line": 343, "column": 7}]}]}, {"title": "comprehensive-executive-summary.spec.ts", "file": "comprehensive-executive-summary.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Executive Summary Testing", "file": "comprehensive-executive-summary.spec.ts", "line": 15, "column": 6, "specs": [{"title": "should load executive summary with all components", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 6528, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:12.706Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-edbc18e8c5268436b6ec", "file": "comprehensive-executive-summary.spec.ts", "line": 21, "column": 7}, {"title": "should display brand selector and period selector", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 6940, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:13.355Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-a146f8e4919665dae76f", "file": "comprehensive-executive-summary.spec.ts", "line": 40, "column": 7}, {"title": "should display executive KPI cards with proper formatting", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 6645, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:14.457Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-be38ed06f94f062cd420", "file": "comprehensive-executive-summary.spec.ts", "line": 84, "column": 7}, {"title": "should switch between cards and slides view", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 7530, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:16.614Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-939a7fb67443870ceb9b", "file": "comprehensive-executive-summary.spec.ts", "line": 114, "column": 7}, {"title": "should display trend charts and analysis", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 8398, "errors": [], "stdout": [{"text": "Found 3 charts with selector: .recharts-wrapper\n"}, {"text": "Found 0 trend elements and 0 trend indicators\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:16.817Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-b051bf0a519664a2f954", "file": "comprehensive-executive-summary.spec.ts", "line": 157, "column": 7}, {"title": "should handle PDF export functionality", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 8672, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:19.241Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-2777232c2ec0185122e1", "file": "comprehensive-executive-summary.spec.ts", "line": 178, "column": 7}, {"title": "should display period comparison data", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "passed", "duration": 7194, "errors": [], "stdout": [{"text": "Found 10 comparison elements and 0 comparison values\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:20.085Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-0ff33efba48fb91860f6", "file": "comprehensive-executive-summary.spec.ts", "line": 207, "column": 7}, {"title": "should handle currency switching", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 6508, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:20.306Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-fc190dcac7328cfa725d", "file": "comprehensive-executive-summary.spec.ts", "line": 227, "column": 7}, {"title": "should display methodology and notes if available", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 6384, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:21.109Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-e9d48d12321137dd4e0b", "file": "comprehensive-executive-summary.spec.ts", "line": 260, "column": 7}, {"title": "should be responsive across different screen sizes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 9716, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:24.149Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-a394186e34028aa6596d", "file": "comprehensive-executive-summary.spec.ts", "line": 284, "column": 7}, {"title": "should have proper accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 6512, "errors": [], "stdout": [{"text": "Executive Summary landmarks: 0\n"}, {"text": "Executive Summary accessibility info: { ariaLabels: \u001b[33m29\u001b[39m, roles: \u001b[33m13\u001b[39m, headings: \u001b[33m11\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:25.220Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-94515537b1e16b52ee05", "file": "comprehensive-executive-summary.spec.ts", "line": 315, "column": 7}, {"title": "should load within performance thresholds", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 6044, "errors": [], "stdout": [{"text": "Executive Summary loaded in 1400ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:26.820Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-8c956e7ff334c766de40", "file": "comprehensive-executive-summary.spec.ts", "line": 330, "column": 7}, {"title": "should not have critical console errors", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "passed", "duration": 6893, "errors": [], "stdout": [{"text": "Critical console errors found: [\n  \u001b[32m'Error fetching saved views: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\\n'\u001b[39m +\n    \u001b[32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\\n'\u001b[39m +\n    \u001b[32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'\u001b[39m,\n  \u001b[32m'Error fetching sales channels: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3801:56)'\u001b[39m,\n  \u001b[32m'Error fetching brands: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\\n'\u001b[39m +\n    \u001b[32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'\u001b[39m\n]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:27.283Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0a5a97eda325cb9c37e6-beae99e6201ea459605e", "file": "comprehensive-executive-summary.spec.ts", "line": 345, "column": 7}]}]}, {"title": "comprehensive-marketing-dashboard.spec.ts", "file": "comprehensive-marketing-dashboard.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Marketing Dashboard Testing", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 17, "column": 6, "specs": [{"title": "should load marketing dashboard with all components", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 6986, "errors": [], "stdout": [{"text": "Page title: Create Next App\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:27.498Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-fd258598690994fc76da", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 23, "column": 7}, {"title": "should display marketing KPI cards", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 6998, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:27.919Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-ee35e66dca54085de23c", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 42, "column": 7}, {"title": "should display campaign data table with proper functionality", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 6246, "errors": [], "stdout": [{"text": "Campaign table has 12 headers and 50 rows\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:31.743Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-2b58c28b8273d739e102", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 65, "column": 7}, {"title": "should render spend breakdown charts", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 8513, "errors": [], "stdout": [{"text": "Found 9 charts with selector: .recharts-wrapper\n"}, {"text": "Found 0 spend-related chart elements\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:32.870Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-0209b8a324a45bafeda1", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 95, "column": 7}, {"title": "should have functional marketing filters", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 8410, "errors": [], "stdout": [{"text": "Found 1 active filters\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:33.878Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-bc6b5e788d34d6df7d43", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 112, "column": 7}, {"title": "should allow campaign search and filtering", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "passed", "duration": 6360, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:34.184Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-495b0de60c02b4f03dbe", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 170, "column": 7}, {"title": "should navigate to campaign details", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 6737, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:34.491Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-c86317274e379426ca70", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 192, "column": 7}, {"title": "should display proper marketing metrics calculations", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 6712, "errors": [], "stdout": [{"text": "Found 2 marketing metrics out of 8 expected\n"}, {"text": "Found 57 percentage values and 168 currency values\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:34.922Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-afe33de2b8573e891233", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 227, "column": 7}, {"title": "should handle data export functionality", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 5911, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:37.994Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-4eadf400b53eee94110e", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 273, "column": 7}, {"title": "should be responsive across different screen sizes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 9609, "errors": [], "stdout": [{"text": "Table is in scrollable container on desktop\n"}, {"text": "Table is in scrollable container on tablet\n"}, {"text": "Table is in scrollable container on mobile\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:39.440Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-9d8723fc6ef8d74e67d7", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 293, "column": 7}, {"title": "should have proper accessibility features", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "passed", "duration": 6148, "errors": [], "stdout": [{"text": "Marketing Dashboard landmarks: 0\n"}, {"text": "Marketing Dashboard accessibility info: { ariaLabels: \u001b[33m1\u001b[39m, roles: \u001b[33m9\u001b[39m, headings: \u001b[33m4\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:40.552Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-137bd2b2a6d37164e03b", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 325, "column": 7}, {"title": "should load within performance thresholds", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 6462, "errors": [], "stdout": [{"text": "Marketing Dashboard loaded in 1641ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:41.234Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-f749b7daf31bcaeea641", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 351, "column": 7}, {"title": "should not have critical console errors", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 6623, "errors": [], "stdout": [{"text": "Critical console errors found: [\n  \u001b[32m'Error fetching sales channels: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3801:56)'\u001b[39m,\n  \u001b[32m'Error fetching saved views: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at getSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7215:32)\\n'\u001b[39m +\n    \u001b[32m'    at fetchSavedViews (http://localhost:6699/_next/static/chunks/_5e541529._.js:7409:192)\\n'\u001b[39m +\n    \u001b[32m'    at SavedViewsMenu.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:7399:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)'\u001b[39m,\n  \u001b[32m'Error fetching brands: TypeError: Failed to fetch\\n'\u001b[39m +\n    \u001b[32m'    at fetchFilterOptions (http://localhost:6699/_next/static/chunks/_5e541529._.js:3754:54)\\n'\u001b[39m +\n    \u001b[32m'    at FilterProvider.useEffect (http://localhost:6699/_next/static/chunks/_5e541529._.js:3863:13)\\n'\u001b[39m +\n    \u001b[32m'    at react-stack-bottom-frame (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:14961:22)\\n'\u001b[39m +\n    \u001b[32m'    at runWithFiberInDEV (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3073:74)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookEffectListMount (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8637:628)\\n'\u001b[39m +\n    \u001b[32m'    at commitHookPassiveMountEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8672:60)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9633:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9647:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)\\n'\u001b[39m +\n    \u001b[32m'    at reconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9632:17)\\n'\u001b[39m +\n    \u001b[32m'    at recursivelyTraverseReconnectPassiveEffects (http://localhost:6699/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:9624:68)'\u001b[39m\n]\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:41.388Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "3d5e7d77d5c4168dec16-4e868d7207cd9d0d88a5", "file": "comprehensive-marketing-dashboard.spec.ts", "line": 366, "column": 7}]}]}, {"title": "comprehensive-performance.spec.ts", "file": "comprehensive-performance.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Performance Testing", "file": "comprehensive-performance.spec.ts", "line": 10, "column": 6, "specs": [{"title": "should measure page load performance across all main pages", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 12789, "errors": [], "stdout": [{"text": "Dashboard: 1624ms (max: 8000ms)\n"}, {"text": "Brand Deep Dive: 1118ms (max: 12000ms)\n"}, {"text": "Marketing Dashboard: 1049ms (max: 15000ms)\n"}, {"text": "Executive Summary: 1012ms (max: 10000ms)\n"}, {"text": "Budget: 928ms (max: 8000ms)\n"}, {"text": "AI Assistant: 920ms (max: 6000ms)\n"}, {"text": "Admin Dashboard: 885ms (max: 8000ms)\n"}, {"text": "\n=== Performance Summary ===\n"}, {"text": "✅ PASS Dashboard: 1624ms\n"}, {"text": "✅ PASS Brand Deep Dive: 1118ms\n"}, {"text": "✅ PASS Marketing Dashboard: 1049ms\n"}, {"text": "✅ PASS Executive Summary: 1012ms\n"}, {"text": "✅ PASS Budget: 928ms\n"}, {"text": "✅ PASS AI Assistant: 920ms\n"}, {"text": "✅ PASS Admin Dashboard: 885ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:41.641Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-1d56667aee38502da4ef", "file": "comprehensive-performance.spec.ts", "line": 16, "column": 7}, {"title": "should measure Time to First Contentful Paint (FCP)", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 21296, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:42.294Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-93da73880c03e7afe5ef", "file": "comprehensive-performance.spec.ts", "line": 73, "column": 7}, {"title": "should measure Largest Contentful Paint (LCP)", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 27398, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:43.912Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-5c101555131811069b32", "file": "comprehensive-performance.spec.ts", "line": 103, "column": 7}, {"title": "should measure JavaScript bundle sizes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "passed", "duration": 6552, "errors": [], "stdout": [{"text": "JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_fd44f5a4._.js - 1.01KB\n"}, {"text": "JS Bundle: node_modules_%40swc_helpers_cjs_00636ac3._.js - 1.25KB\n"}, {"text": "JS Bundle: node_modules_next_dist_2ecbf5fa._.js - 17.73KB\n"}, {"text": "JS Bundle: node_modules_next_dist_compiled_2ce9398a._.js - 192.75KB\n"}, {"text": "JS Bundle: node_modules_next_dist_client_8f19e6fb._.js - 166.58KB\n"}, {"text": "JS Bundle: _e69f0d32._.js - 0.81KB\n"}, {"text": "JS Bundle: app_favicon_ico_mjs_659ce808._.js - 0.55KB\n"}, {"text": "JS Bundle: _93808211._.js - 16.07KB\n"}, {"text": "JS Bundle: app_layout_tsx_c0237562._.js - 0.58KB\n"}, {"text": "JS Bundle: node_modules_next_dist_1a6ee436._.js - 18.44KB\n"}, {"text": "JS Bundle: _45166852._.js - 17.95KB\n"}, {"text": "JS Bundle: _5e541529._.js - 70.10KB\n"}, {"text": "JS Bundle: node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js - 17.12KB\n"}, {"text": "JS Bundle: node_modules_lodash_f240f67a._.js - 32.65KB\n"}, {"text": "JS Bundle: node_modules_%40floating-ui_9ec1fa39._.js - 21.34KB\n"}, {"text": "JS Bundle: app_dashboard_page_tsx_63d9a548._.js - 0.87KB\n"}, {"text": "JS Bundle: node_modules_%40radix-ui_fead58fd._.js - 55.73KB\n"}, {"text": "JS Bundle: node_modules_recharts_es6_98d167ba._.js - 117.77KB\n"}, {"text": "JS Bundle: node_modules_53dbc141._.js - 142.33KB\n"}, {"text": "JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js - 4.05KB\n"}, {"text": "JS Bundle: %5Bturbopack%5D_browser_dev_hmr-client_hmr-client_ts_66796270._.js - 0.57KB\n"}, {"text": "Total JS Bundle Size: 896.22KB\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:46.709Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-6ec5a6c72a2d51937516", "file": "comprehensive-performance.spec.ts", "line": 132, "column": 7}, {"title": "should test memory usage during navigation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 9881, "errors": [], "stdout": [{"text": "/dashboard Memory: 42.63MB used\n"}, {"text": "/brand-deep-dive Memory: 42.63MB used\n"}, {"text": "/marketing-dashboard Memory: 42.63MB used\n"}, {"text": "/executive-summary Memory: 42.63MB used\n"}, {"text": "\n=== Memory Usage Summary ===\n"}, {"text": "/dashboard: 42.63MB used, 92.89MB total\n"}, {"text": "/brand-deep-dive: 42.63MB used, 92.89MB total\n"}, {"text": "/marketing-dashboard: 42.63MB used, 92.89MB total\n"}, {"text": "/executive-summary: 42.63MB used, 92.89MB total\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:47.704Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-ca9f8c82d816ffa61610", "file": "comprehensive-performance.spec.ts", "line": 162, "column": 7}, {"title": "should test network performance and resource loading", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 7170, "errors": [], "stdout": [{"text": "Network Performance:\n"}, {"text": "Domain Lookup: 0.00ms\n"}, {"text": "Connection: 0.00ms\n"}, {"text": "Request: 435.70ms\n"}, {"text": "Response: 22.70ms\n"}, {"text": "DOM Processing: 11.20ms\n"}, {"text": "Total Resources: 35\n"}, {"text": "Slow Resources (>1s): 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:48.020Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-2e40d0cfd50b752a3d19", "file": "comprehensive-performance.spec.ts", "line": 201, "column": 7}, {"title": "should test performance under different network conditions", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 7469, "errors": [], "stdout": [{"text": "Dashboard load time with slow network: 2336ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:49.062Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-2f662ff03c0eb5065d72", "file": "comprehensive-performance.spec.ts", "line": 238, "column": 7}, {"title": "should test performance with large datasets", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "passed", "duration": 13529, "errors": [], "stdout": [{"text": "/marketing-dashboard with data rendering: 4187ms\n"}, {"text": "/brand-deep-dive with data rendering: 4410ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:53.266Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-d0e0777ac1cb5a35fb09", "file": "comprehensive-performance.spec.ts", "line": 257, "column": 7}, {"title": "should test scroll performance on long pages", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 6964, "errors": [], "stdout": [{"text": "Scroll performance: 1016ms\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:54.435Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-ec696b9e3ff5403b6776", "file": "comprehensive-performance.spec.ts", "line": 282, "column": 7}, {"title": "should test chart rendering performance", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 6565, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:55.196Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-382e241d17cafef30520", "file": "comprehensive-performance.spec.ts", "line": 309, "column": 7}, {"title": "should test accessibility performance", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8026, "errors": [], "stdout": [{"text": "/dashboard accessibility scan: 1218ms\n"}, {"text": "Accessibility elements found: { ariaLabels: \u001b[33m21\u001b[39m, roles: \u001b[33m26\u001b[39m, headings: \u001b[33m3\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}, {"text": "/dashboard landmarks: 0\n"}, {"text": "/marketing-dashboard accessibility scan: 1376ms\n"}, {"text": "Accessibility elements found: { ariaLabels: \u001b[33m1\u001b[39m, roles: \u001b[33m7\u001b[39m, headings: \u001b[33m4\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}, {"text": "/marketing-dashboard landmarks: 0\n"}, {"text": "/admin accessibility scan: 782ms\n"}, {"text": "Accessibility elements found: { ariaLabels: \u001b[33m1\u001b[39m, roles: \u001b[33m2\u001b[39m, headings: \u001b[33m4\u001b[39m, landmarks: \u001b[33m0\u001b[39m }\n"}, {"text": "/admin landmarks: 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:56.536Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "595014779053d37e3f5b-f179641bd966b296b09f", "file": "comprehensive-performance.spec.ts", "line": 341, "column": 7}]}]}, {"title": "comprehensive-test-runner.spec.ts", "file": "comprehensive-test-runner.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Comprehensive Application Test Suite", "file": "comprehensive-test-runner.spec.ts", "line": 12, "column": 6, "specs": [{"title": "should run complete application smoke test", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 17034, "errors": [], "stdout": [{"text": "🚀 Starting comprehensive application smoke test...\n"}, {"text": "📄 Testing Dashboard...\n"}, {"text": "✅ Dashboard: 1969ms\n"}, {"text": "📄 Testing Brand Deep Dive...\n"}, {"text": "✅ Brand Deep Dive: 1027ms\n"}, {"text": "📄 Testing Marketing Dashboard...\n"}, {"text": "✅ Marketing Dashboard: 1078ms\n"}, {"text": "📄 Testing Executive Summary...\n"}, {"text": "✅ Executive Summary: 1619ms\n"}, {"text": "📄 Testing Budget...\n"}, {"text": "✅ Budget: 774ms\n"}, {"text": "📄 Testing AI Assistant...\n"}, {"text": "✅ AI Assistant: 1119ms\n"}, {"text": "🔐 Testing admin pages...\n"}, {"text": "✅ Admin Dashboard: Accessible\n"}, {"text": "✅ Admin Users: Accessible\n"}, {"text": "✅ Admin Roles: Accessible\n"}, {"text": "✅ Admin Permissions: Accessible\n"}, {"text": "\n📊 SMOKE TEST SUMMARY:\n"}, {"text": "========================\n"}, {"text": "✅ Dashboard: 1969ms\n"}, {"text": "✅ Brand Deep Dive: 1027ms\n"}, {"text": "✅ Marketing Dashboard: 1078ms\n"}, {"text": "✅ Executive Summary: 1619ms\n"}, {"text": "✅ Budget: 774ms\n"}, {"text": "✅ AI Assistant: 1119ms\n"}, {"text": "\n🎯 Results: 6/6 tests passed\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:21:57.593Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-b584c045f062ccf853a7", "file": "comprehensive-test-runner.spec.ts", "line": 18, "column": 7}, {"title": "should verify authentication system works correctly", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 19118, "error": {"message": "TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\u001b[22m\n", "stack": "TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\u001b[22m\n\n    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\n    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 26, "line": 46}, "snippet": "\u001b[90m   at \u001b[39mutils/test-helpers.ts:46\n\n\u001b[0m \u001b[90m 44 |\u001b[39m\n \u001b[90m 45 |\u001b[39m     \u001b[90m// Wait for the button to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 46 |\u001b[39m     \u001b[36mawait\u001b[39m credentialsTab\u001b[33m.\u001b[39mwaitFor({ state\u001b[33m:\u001b[39m \u001b[32m'visible'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 47 |\u001b[39m     \u001b[36mawait\u001b[39m credentialsTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1500\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for tab switch animation\u001b[39m\n \u001b[90m 49 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 26, "line": 46}, "message": "TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:46\n\n\u001b[0m \u001b[90m 44 |\u001b[39m\n \u001b[90m 45 |\u001b[39m     \u001b[90m// Wait for the button to be visible\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 46 |\u001b[39m     \u001b[36mawait\u001b[39m credentialsTab\u001b[33m.\u001b[39mwaitFor({ state\u001b[33m:\u001b[39m \u001b[32m'visible'\u001b[39m\u001b[33m,\u001b[39m timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 47 |\u001b[39m     \u001b[36mawait\u001b[39m credentialsTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m1500\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for tab switch animation\u001b[39m\n \u001b[90m 49 |\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7\u001b[22m"}], "stdout": [{"text": "🔐 Testing authentication system...\n"}, {"text": "Sign in failed: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\u001b[22m\n\n    at signInWithCredentials \u001b[90m(/Users/<USER>/Projects/NOLK/nolk-v4/\u001b[39mtests/e2e/utils/test-helpers.ts:46:26\u001b[90m)\u001b[39m\n    at signInAsAdmin \u001b[90m(/Users/<USER>/Projects/NOLK/nolk-v4/\u001b[39mtests/e2e/utils/test-helpers.ts:75:3\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/Projects/NOLK/nolk-v4/\u001b[39mtests/e2e/comprehensive-test-runner.spec.ts:127:7 {\n  name: \u001b[32m'TimeoutError'\u001b[39m,\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@25'\u001b[39m,\n    location: {\n      file: \u001b[32m'/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts'\u001b[39m,\n      line: \u001b[33m46\u001b[39m,\n      column: \u001b[33m26\u001b[39m,\n      function: \u001b[32m'signInWithCredentials'\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m'locator.waitFor(button >> internal:has-text=\"Username/Password\"i)'\u001b[39m,\n    apiName: \u001b[32m'locator.waitFor'\u001b[39m,\n    params: {\n      selector: \u001b[32m'button >> internal:has-text=\"Username/Password\"i'\u001b[39m,\n      strict: \u001b[33mtrue\u001b[39m,\n      omitReturnValue: \u001b[33mtrue\u001b[39m,\n      state: \u001b[32m'visible'\u001b[39m,\n      timeout: \u001b[33m10000\u001b[39m\n    },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@25'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1748600540158\u001b[39m,\n    error: {\n      message: \u001b[32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\\x1B[22m\\n\"\u001b[39m,\n      stack: \u001b[32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\\x1B[22m\\n\"\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\\n'\u001b[39m +\n        \u001b[32m'    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\\n'\u001b[39m +\n        \u001b[32m'    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}, {"text": "❌ Authentication test failed: locator.waitFor: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\u001b[22m\n\n    at signInWithCredentials \u001b[90m(/Users/<USER>/Projects/NOLK/nolk-v4/\u001b[39mtests/e2e/utils/test-helpers.ts:46:26\u001b[90m)\u001b[39m\n    at signInAsAdmin \u001b[90m(/Users/<USER>/Projects/NOLK/nolk-v4/\u001b[39mtests/e2e/utils/test-helpers.ts:75:3\u001b[90m)\u001b[39m\n    at \u001b[90m/Users/<USER>/Projects/NOLK/nolk-v4/\u001b[39mtests/e2e/comprehensive-test-runner.spec.ts:127:7 {\n  name: \u001b[32m'TimeoutError'\u001b[39m,\n  [\u001b[32mSymbol(step)\u001b[39m]: {\n    stepId: \u001b[32m'pw:api@25'\u001b[39m,\n    location: {\n      file: \u001b[32m'/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts'\u001b[39m,\n      line: \u001b[33m46\u001b[39m,\n      column: \u001b[33m26\u001b[39m,\n      function: \u001b[32m'signInWithCredentials'\u001b[39m\n    },\n    category: \u001b[32m'pw:api'\u001b[39m,\n    title: \u001b[32m'locator.waitFor(button >> internal:has-text=\"Username/Password\"i)'\u001b[39m,\n    apiName: \u001b[32m'locator.waitFor'\u001b[39m,\n    params: {\n      selector: \u001b[32m'button >> internal:has-text=\"Username/Password\"i'\u001b[39m,\n      strict: \u001b[33mtrue\u001b[39m,\n      omitReturnValue: \u001b[33mtrue\u001b[39m,\n      state: \u001b[32m'visible'\u001b[39m,\n      timeout: \u001b[33m10000\u001b[39m\n    },\n    boxedStack: \u001b[90mundefined\u001b[39m,\n    steps: [],\n    attachmentIndices: [],\n    info: TestStepInfoImpl {\n      annotations: [],\n      _testInfo: \u001b[36m[TestInfoImpl]\u001b[39m,\n      _stepId: \u001b[32m'pw:api@25'\u001b[39m\n    },\n    complete: \u001b[36m[Function: complete]\u001b[39m,\n    endWallTime: \u001b[33m1748600540158\u001b[39m,\n    error: {\n      message: \u001b[32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\\x1B[22m\\n\"\u001b[39m,\n      stack: \u001b[32m'TimeoutError: locator.waitFor: Timeout 10000ms exceeded.\\n'\u001b[39m +\n        \u001b[32m'Call log:\\n'\u001b[39m +\n        \u001b[32m\"\\x1B[2m  - waiting for locator('button').filter({ hasText: 'Username/Password' }) to be visible\\x1B[22m\\n\"\u001b[39m +\n        \u001b[32m'\\n'\u001b[39m +\n        \u001b[32m'    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:46:26)\\n'\u001b[39m +\n        \u001b[32m'    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:75:3)\\n'\u001b[39m +\n        \u001b[32m'    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/comprehensive-test-runner.spec.ts:127:7'\u001b[39m,\n      cause: \u001b[90mundefined\u001b[39m\n    }\n  }\n}\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:22:01.407Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/comprehensive-test-runner--24bf2-tion-system-works-correctly-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 26, "line": 46}}], "status": "unexpected"}], "id": "68b8c085ca31fe9a4ab1-a084ac754b4d6845a235", "file": "comprehensive-test-runner.spec.ts", "line": 122, "column": 7}, {"title": "should verify all navigation links work", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 12531, "errors": [], "stdout": [{"text": "🧭 Testing navigation system...\n"}, {"text": "✅ Navigation to Dashboard: Working\n"}, {"text": "✅ Navigation to Marketing Dashboard: Working\n"}, {"text": "✅ Navigation to Executive Summary: Working\n"}, {"text": "✅ Navigation to Budget: Working\n"}, {"text": "✅ Navigation to AI Assistant: Working\n"}, {"text": "🎯 Navigation: 5/6 links working\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:22:01.770Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-891466e9a0d3c7f664a2", "file": "comprehensive-test-runner.spec.ts", "line": 143, "column": 7}, {"title": "should verify data loading and display", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 12016, "errors": [], "stdout": [{"text": "📊 Testing data loading and display...\n"}, {"text": "📈 Dashboard KPIs: 0 data elements found\n"}, {"text": "⚠️ Dashboard KPIs: No data elements found\n"}, {"text": "📈 Marketing Campaigns: 1 data elements found\n"}, {"text": "✅ Marketing Campaigns: Data loaded successfully\n"}, {"text": "📈 Brand Data: 0 data elements found\n"}, {"text": "⚠️ Brand Data: No data elements found\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:22:03.596Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-585574cae176d7c31753", "file": "comprehensive-test-runner.spec.ts", "line": 189, "column": 7}, {"title": "should verify responsive design works", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 10383, "errors": [], "stdout": [{"text": "📱 Testing responsive design...\n"}, {"text": "✅ Desktop (1920x1080): Layout working\n"}, {"text": "✅ Tablet (1024x768): Layout working\n"}, {"text": "✅ Mobile (375x667): Layout working\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:22:04.570Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-a204b6df0fba0ed7cacc", "file": "comprehensive-test-runner.spec.ts", "line": 224, "column": 7}, {"title": "should verify accessibility standards", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "passed", "duration": 9684, "errors": [], "stdout": [{"text": "♿ Testing accessibility standards...\n"}, {"text": "🔍 /dashboard accessibility:\n"}, {"text": "  - Headings: 3\n"}, {"text": "  - ARIA labels: 21\n"}, {"text": "  - Landmarks: 0\n"}, {"text": "⚠️ /dashboard: No landmarks found, but continuing test\n"}, {"text": "✅ /dashboard: Accessibility standards met\n"}, {"text": "🔍 /marketing-dashboard accessibility:\n"}, {"text": "  - Headings: 4\n"}, {"text": "  - ARIA labels: 1\n"}, {"text": "  - Landmarks: 0\n"}, {"text": "⚠️ /marketing-dashboard: No landmarks found, but continuing test\n"}, {"text": "✅ /marketing-dashboard: Accessibility standards met\n"}, {"text": "🔍 /admin accessibility:\n"}, {"text": "  - Headings: 4\n"}, {"text": "  - ARIA labels: 1\n"}, {"text": "  - Landmarks: 0\n"}, {"text": "⚠️ /admin: No landmarks found, but continuing test\n"}, {"text": "✅ /admin: Accessibility standards met\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:22:06.806Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-786bf0f134286060393f", "file": "comprehensive-test-runner.spec.ts", "line": 254, "column": 7}, {"title": "should verify performance benchmarks", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 10, "parallelIndex": 7, "status": "passed", "duration": 8616, "errors": [], "stdout": [{"text": "⚡ Testing performance benchmarks...\n"}, {"text": "✅ Dashboard: 1929ms (under 8000ms limit)\n"}, {"text": "✅ Marketing Dashboard: 1099ms (under 15000ms limit)\n"}, {"text": "✅ Admin Dashboard: 744ms (under 8000ms limit)\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:22:07.852Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-982dc5b689434e831929", "file": "comprehensive-test-runner.spec.ts", "line": 283, "column": 7}, {"title": "should run final comprehensive validation", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 10782, "errors": [], "stdout": [{"text": "🎯 Running final comprehensive validation...\n"}, {"text": "✅ Journey step: /brand-deep-dive completed successfully\n"}, {"text": "✅ Journey step: /marketing-dashboard completed successfully\n"}, {"text": "✅ Journey step: /executive-summary completed successfully\n"}, {"text": "✅ Journey step: /admin completed successfully\n"}, {"text": "🎉 Comprehensive test suite completed successfully!\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-30T10:22:11.316Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "68b8c085ca31fe9a4ab1-b32ada70a3c078f61a08", "file": "comprehensive-test-runner.spec.ts", "line": 314, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-30T10:19:44.205Z", "duration": 158005.777, "expected": 119, "skipped": 0, "unexpected": 4, "flaky": 0}}