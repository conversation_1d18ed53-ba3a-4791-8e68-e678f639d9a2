/**
 * Performance Monitoring and Alerting System
 * 
 * Provides continuous performance monitoring, regression detection,
 * and automated alerting for the NOLK application.
 */

import {
  BenchmarkManager,
  PerformanceBenchmark,
  PerformanceMetrics
} from '@/tests/utils/performance-utils';

import fs from 'fs';
import path from 'path';
import { performance } from 'perf_hooks';

interface PerformanceAlert {
  id: string;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metric: string;
  message: string;
  currentValue: number;
  threshold: number;
  trend: 'improving' | 'stable' | 'degrading';
}

interface MonitoringConfig {
  thresholds: {
    dashboardLoad: { warning: 2500, critical: 3000 };
    apiResponse: { warning: 800, critical: 1000 };
    chartRendering: { warning: 1500, critical: 2000 };
    dbQuery: { warning: 400, critical: 500 };
    pdfExport: { warning: 4000, critical: 5000 };
    memoryUsage: { warning: 100, critical: 200 }; // MB
    errorRate: { warning: 5, critical: 10 }; // percentage
  };
  regressionThreshold: 20; // percentage increase to trigger alert
  trendWindow: 10; // number of recent benchmarks to analyze
  alertCooldown: 3600000; // 1 hour in milliseconds
}

export class PerformanceMonitor {
  private config: MonitoringConfig;
  private benchmarkManager: BenchmarkManager;
  private alertHistory: PerformanceAlert[] = [];
  private dataDir: string;

  constructor(config?: Partial<MonitoringConfig>) {
    this.config = {
      thresholds: {
        dashboardLoad: { warning: 2500, critical: 3000 },
        apiResponse: { warning: 800, critical: 1000 },
        chartRendering: { warning: 1500, critical: 2000 },
        dbQuery: { warning: 400, critical: 500 },
        pdfExport: { warning: 4000, critical: 5000 },
        memoryUsage: { warning: 100, critical: 200 },
        errorRate: { warning: 5, critical: 10 }
      },
      regressionThreshold: 20,
      trendWindow: 10,
      alertCooldown: 3600000,
      ...config
    };

    this.benchmarkManager = new BenchmarkManager();
    this.dataDir = path.join(process.cwd(), 'tests', 'benchmarks');
    this.ensureDataDirectory();
    this.loadHistoricalData();
  }

  private ensureDataDirectory(): void {
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
  }

  private loadHistoricalData(): void {
    try {
      const files = fs.readdirSync(this.dataDir)
        .filter(file => file.startsWith('benchmarks-') && file.endsWith('.json'))
        .sort()
        .slice(-this.config.trendWindow);

      files.forEach(file => {
        try {
          const data = JSON.parse(fs.readFileSync(path.join(this.dataDir, file), 'utf8'));
          if (data.metrics) {
            Object.entries(data.metrics).forEach(([testType, metrics]: [string, any]) => {
              if (metrics.benchmarks) {
                Object.entries(metrics.benchmarks).forEach(([benchmarkName, value]: [string, any]) => {
                  this.benchmarkManager.addBenchmark(`${testType}.${benchmarkName}`, {
                    value: typeof value === 'number' ? value : 0,
                    timestamp: data.timestamp
                  });
                });
              }
            });
          }
        } catch (error) {
          console.warn(`Failed to load benchmark file ${file}:`, error);
        }
      });
    } catch (error) {
      console.warn('Failed to load historical benchmark data:', error);
    }
  }

  /**
   * Analyze current performance metrics and detect regressions
   */
  analyzePerformance(currentMetrics: Record<string, PerformanceMetrics>): PerformanceAlert[] {
    const alerts: PerformanceAlert[] = [];
    const timestamp = Date.now();

    Object.entries(currentMetrics).forEach(([testType, metrics]) => {
      // Check absolute thresholds
      this.checkAbsoluteThresholds(testType, metrics, alerts, timestamp);

      // Check for performance regressions
      this.checkPerformanceRegressions(testType, metrics, alerts, timestamp);

      // Analyze trends
      this.analyzeTrends(testType, metrics, alerts, timestamp);
    });

    // Filter out alerts that are in cooldown period
    const filteredAlerts = alerts.filter(alert => 
      !this.isInCooldown(alert.metric, timestamp)
    );

    // Store new alerts
    this.alertHistory.push(...filteredAlerts);
    this.cleanupOldAlerts();

    return filteredAlerts;
  }

  private checkAbsoluteThresholds(
    testType: string,
    metrics: PerformanceMetrics,
    alerts: PerformanceAlert[],
    timestamp: number
  ): void {
    const duration = metrics.duration;
    const memoryUsage = metrics.memoryUsage ? metrics.memoryUsage.heapUsed / 1024 / 1024 : 0;

    // Map test types to threshold keys
    const thresholdMap: Record<string, keyof MonitoringConfig['thresholds']> = {
      'dashboard-load': 'dashboardLoad',
      'api-': 'apiResponse',
      'chart-': 'chartRendering',
      'db-': 'dbQuery',
      'pdf-': 'pdfExport'
    };

    let thresholdKey: keyof MonitoringConfig['thresholds'] | undefined;
    
    Object.entries(thresholdMap).forEach(([pattern, key]) => {
      if (testType.includes(pattern)) {
        thresholdKey = key;
      }
    });

    if (thresholdKey && this.config.thresholds[thresholdKey]) {
      const threshold = this.config.thresholds[thresholdKey];
      
      if (duration > threshold.critical) {
        alerts.push({
          id: `${testType}-duration-critical-${timestamp}`,
          timestamp,
          severity: 'critical',
          metric: `${testType}.duration`,
          message: `Critical performance degradation: ${testType} took ${duration.toFixed(2)}ms (threshold: ${threshold.critical}ms)`,
          currentValue: duration,
          threshold: threshold.critical,
          trend: 'degrading'
        });
      } else if (duration > threshold.warning) {
        alerts.push({
          id: `${testType}-duration-warning-${timestamp}`,
          timestamp,
          severity: 'medium',
          metric: `${testType}.duration`,
          message: `Performance warning: ${testType} took ${duration.toFixed(2)}ms (threshold: ${threshold.warning}ms)`,
          currentValue: duration,
          threshold: threshold.warning,
          trend: 'degrading'
        });
      }
    }

    // Check memory usage
    const memoryThreshold = this.config.thresholds.memoryUsage;
    if (memoryUsage > memoryThreshold.critical) {
      alerts.push({
        id: `${testType}-memory-critical-${timestamp}`,
        timestamp,
        severity: 'critical',
        metric: `${testType}.memory`,
        message: `Critical memory usage: ${testType} used ${memoryUsage.toFixed(2)}MB (threshold: ${memoryThreshold.critical}MB)`,
        currentValue: memoryUsage,
        threshold: memoryThreshold.critical,
        trend: 'degrading'
      });
    } else if (memoryUsage > memoryThreshold.warning) {
      alerts.push({
        id: `${testType}-memory-warning-${timestamp}`,
        timestamp,
        severity: 'medium',
        metric: `${testType}.memory`,
        message: `Memory usage warning: ${testType} used ${memoryUsage.toFixed(2)}MB (threshold: ${memoryThreshold.warning}MB)`,
        currentValue: memoryUsage,
        threshold: memoryThreshold.warning,
        trend: 'degrading'
      });
    }
  }

  private checkPerformanceRegressions(
    testType: string,
    metrics: PerformanceMetrics,
    alerts: PerformanceAlert[],
    timestamp: number
  ): void {
    const benchmarkName = `${testType}.duration`;
    const recentBenchmarks = this.benchmarkManager.getBenchmarks(benchmarkName)
      .slice(-5); // Last 5 benchmarks

    if (recentBenchmarks.length < 2) {
      return; // Not enough data for comparison
    }

    const baseline = recentBenchmarks.slice(0, -1).reduce((sum, b) => sum + (b.metrics.value || 0), 0) / (recentBenchmarks.length - 1);
    const current = metrics.duration;
    const regressionPercentage = ((current - baseline) / baseline) * 100;

    if (regressionPercentage > this.config.regressionThreshold) {
      alerts.push({
        id: `${testType}-regression-${timestamp}`,
        timestamp,
        severity: regressionPercentage > 50 ? 'critical' : 'high',
        metric: benchmarkName,
        message: `Performance regression detected: ${testType} is ${regressionPercentage.toFixed(1)}% slower than baseline (${baseline.toFixed(2)}ms → ${current.toFixed(2)}ms)`,
        currentValue: current,
        threshold: baseline * (1 + this.config.regressionThreshold / 100),
        trend: 'degrading'
      });
    }
  }

  private analyzeTrends(
    testType: string,
    metrics: PerformanceMetrics,
    alerts: PerformanceAlert[],
    timestamp: number
  ): void {
    const benchmarkName = `${testType}.duration`;
    const recentBenchmarks = this.benchmarkManager.getBenchmarks(benchmarkName)
      .slice(-this.config.trendWindow);

    if (recentBenchmarks.length < 5) {
      return; // Not enough data for trend analysis
    }

    // Calculate trend using linear regression
    const values = recentBenchmarks.map(b => b.metrics.value || 0);
    const trend = this.calculateTrend(values);

    if (trend.slope > 0 && trend.correlation > 0.7) {
      // Degrading trend
      const projectedIncrease = trend.slope * 10; // Project 10 periods ahead
      
      if (projectedIncrease > values[values.length - 1] * 0.2) { // 20% increase projected
        alerts.push({
          id: `${testType}-trend-${timestamp}`,
          timestamp,
          severity: 'medium',
          metric: benchmarkName,
          message: `Degrading performance trend detected: ${testType} shows consistent slowdown (slope: ${trend.slope.toFixed(2)}ms/test)`,
          currentValue: values[values.length - 1],
          threshold: values[values.length - 1] * 1.2,
          trend: 'degrading'
        });
      }
    }
  }

  private calculateTrend(values: number[]): { slope: number; correlation: number } {
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;

    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
    const sumYY = y.reduce((sum, yi) => sum + yi * yi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const correlation = (n * sumXY - sumX * sumY) / 
      Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

    return { slope, correlation: Math.abs(correlation) };
  }

  private isInCooldown(metric: string, timestamp: number): boolean {
    const recentAlerts = this.alertHistory.filter(
      alert => alert.metric === metric && 
      timestamp - alert.timestamp < this.config.alertCooldown
    );
    return recentAlerts.length > 0;
  }

  private cleanupOldAlerts(): void {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
    this.alertHistory = this.alertHistory.filter(alert => alert.timestamp > cutoff);
  }

  /**
   * Generate performance monitoring report
   */
  generateMonitoringReport(): {
    summary: {
      totalAlerts: number;
      criticalAlerts: number;
      recentTrends: Record<string, string>;
    };
    alerts: PerformanceAlert[];
    recommendations: string[];
  } {
    const recentAlerts = this.alertHistory.filter(
      alert => Date.now() - alert.timestamp < 24 * 60 * 60 * 1000 // Last 24 hours
    );

    const criticalAlerts = recentAlerts.filter(alert => alert.severity === 'critical');
    
    const recommendations: string[] = [];
    
    if (criticalAlerts.length > 0) {
      recommendations.push('Immediate attention required: Critical performance issues detected');
    }
    
    const memoryAlerts = recentAlerts.filter(alert => alert.metric.includes('memory'));
    if (memoryAlerts.length > 0) {
      recommendations.push('Review memory usage patterns and implement garbage collection optimizations');
    }
    
    const regressionAlerts = recentAlerts.filter(alert => alert.id.includes('regression'));
    if (regressionAlerts.length > 0) {
      recommendations.push('Investigate recent code changes that may have caused performance regressions');
    }
    
    const trendAlerts = recentAlerts.filter(alert => alert.id.includes('trend'));
    if (trendAlerts.length > 0) {
      recommendations.push('Monitor performance trends closely and consider proactive optimizations');
    }

    return {
      summary: {
        totalAlerts: recentAlerts.length,
        criticalAlerts: criticalAlerts.length,
        recentTrends: this.getRecentTrends()
      },
      alerts: recentAlerts.sort((a, b) => b.timestamp - a.timestamp),
      recommendations
    };
  }

  private getRecentTrends(): Record<string, string> {
    const trends: Record<string, string> = {};
    
    ['dashboard-load', 'api-response', 'chart-rendering'].forEach(testType => {
      const benchmarks = this.benchmarkManager.getBenchmarks(`${testType}.duration`).slice(-5);
      if (benchmarks.length >= 3) {
        const values = benchmarks.map(b => b.metrics.value || 0);
        const trend = this.calculateTrend(values);
        
        if (trend.correlation > 0.5) {
          if (trend.slope > 0) {
            trends[testType] = 'degrading';
          } else if (trend.slope < -0.1) {
            trends[testType] = 'improving';
          } else {
            trends[testType] = 'stable';
          }
        } else {
          trends[testType] = 'stable';
        }
      }
    });
    
    return trends;
  }

  /**
   * Save monitoring data to file
   */
  saveMonitoringData(data: any): void {
    const filename = `monitoring-${Date.now()}.json`;
    const filepath = path.join(this.dataDir, filename);
    
    fs.writeFileSync(filepath, JSON.stringify({
      timestamp: new Date().toISOString(),
      data,
      alerts: this.alertHistory.slice(-50) // Keep last 50 alerts
    }, null, 2));
  }
}

/**
 * CI/CD Integration utilities
 */
export class CIIntegration {
  static generateJUnitReport(metrics: Record<string, PerformanceMetrics>): string {
    const totalTests = Object.keys(metrics).length;
    const failures = Object.values(metrics).filter(m => m.customMetrics?.failed).length;
    
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Performance Tests" tests="${totalTests}" failures="${failures}" time="${Object.values(metrics).reduce((sum, m) => sum + m.duration, 0) / 1000}">
  ${Object.entries(metrics).map(([testType, metric]) => `
  <testsuite name="${testType}" tests="1" failures="${metric.customMetrics?.failed ? 1 : 0}" time="${metric.duration / 1000}">
    <testcase name="${testType}" time="${metric.duration / 1000}">
      ${metric.customMetrics?.failed ? `<failure message="Performance threshold exceeded">${metric.customMetrics.failureMessage || 'Performance test failed'}</failure>` : ''}
    </testcase>
  </testsuite>
  `).join('')}
</testsuites>`;
    
    return xml;
  }

  static generateGitHubComment(alerts: PerformanceAlert[]): string {
    if (alerts.length === 0) {
      return `## 🚀 Performance Test Results

✅ All performance tests passed! No issues detected.

- Dashboard loading: ✅ Within SLA
- API responses: ✅ Within SLA  
- Chart rendering: ✅ Within SLA
- Memory usage: ✅ Within limits`;
    }

    const critical = alerts.filter(a => a.severity === 'critical');
    const warnings = alerts.filter(a => a.severity !== 'critical');

    return `## ⚠️ Performance Test Results

${critical.length > 0 ? `### 🚨 Critical Issues (${critical.length})
${critical.map(alert => `- **${alert.metric}**: ${alert.message}`).join('\n')}` : ''}

${warnings.length > 0 ? `### ⚠️ Warnings (${warnings.length})
${warnings.map(alert => `- **${alert.metric}**: ${alert.message}`).join('\n')}` : ''}

### 📊 Summary
- Total alerts: ${alerts.length}
- Critical: ${critical.length}
- Warnings: ${warnings.length}

Please review and address these performance issues before merging.`;
  }

  static shouldBlockDeployment(alerts: PerformanceAlert[]): boolean {
    return alerts.some(alert => alert.severity === 'critical');
  }
}

// Export for use in CI/CD scripts
export default PerformanceMonitor;