#!/usr/bin/env node

/**
 * Performance Test Runner Script
 * 
 * Runs comprehensive performance tests and generates detailed reports
 * with benchmark comparisons and SLA validation.
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  testTimeout: 60000, // 60 seconds
  maxWorkers: '50%',
  verbose: true,
  coverage: true,
  outputDir: path.join(__dirname, '../benchmarks'),
  reportDir: path.join(__dirname, '../reports'),
  slaThresholds: {
    dashboardLoad: 3000,    // 3 seconds
    apiResponse: 1000,      // 1 second
    chartRendering: 2000,   // 2 seconds
    dbQuery: 500,          // 500ms
    pdfExport: 5000        // 5 seconds
  }
};

// Ensure output directories exist
function ensureDirectories() {
  [config.outputDir, config.reportDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

// Run Jest tests with specific pattern
function runTests(pattern, outputFile) {
  return new Promise((resolve, reject) => {
    const jestArgs = [
      '--testMatch', pattern,
      '--testTimeout', config.testTimeout.toString(),
      '--maxWorkers', config.maxWorkers,
      '--verbose',
      '--json',
      '--outputFile', outputFile
    ];

    if (config.coverage) {
      jestArgs.push('--coverage', '--coverageDirectory', path.join(config.reportDir, 'coverage'));
    }

    console.log(`Running tests: ${pattern}`);
    console.log(`Command: npx jest ${jestArgs.join(' ')}`);

    const jest = spawn('npx', ['jest', ...jestArgs], {
      stdio: ['inherit', 'pipe', 'pipe'],
      cwd: path.resolve(__dirname, '../../..')
    });

    let stdout = '';
    let stderr = '';

    jest.stdout.on('data', (data) => {
      stdout += data;
      if (config.verbose) {
        process.stdout.write(data);
      }
    });

    jest.stderr.on('data', (data) => {
      stderr += data;
      if (config.verbose) {
        process.stderr.write(data);
      }
    });

    jest.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr, code });
      } else {
        reject(new Error(`Jest exited with code ${code}\nStderr: ${stderr}`));
      }
    });

    jest.on('error', (error) => {
      reject(error);
    });
  });
}

// Parse Jest JSON output
function parseJestOutput(outputFile) {
  try {
    const content = fs.readFileSync(outputFile, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.warn(`Failed to parse Jest output from ${outputFile}:`, error.message);
    return null;
  }
}

// Extract performance metrics from test results
function extractPerformanceMetrics(testResults) {
  const metrics = {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    duration: 0,
    benchmarks: {},
    slaViolations: []
  };

  if (!testResults || !testResults.testResults) {
    return metrics;
  }

  testResults.testResults.forEach(fileResult => {
    metrics.totalTests += fileResult.numPassingTests + fileResult.numFailingTests;
    metrics.passedTests += fileResult.numPassingTests;
    metrics.failedTests += fileResult.numFailingTests;
    metrics.duration += fileResult.perfStats?.end - fileResult.perfStats?.start || 0;

    // Extract console output for benchmark data
    if (fileResult.console) {
      fileResult.console.forEach(log => {
        if (log.message.includes('Performance Report:') || log.message.includes('benchmark')) {
          try {
            // Try to extract JSON from console output
            const jsonMatch = log.message.match(/\{.*\}/s);
            if (jsonMatch) {
              const data = JSON.parse(jsonMatch[0]);
              Object.assign(metrics.benchmarks, data);
            }
          } catch (e) {
            // Ignore parsing errors
          }
        }
      });
    }
  });

  // Check SLA violations
  Object.entries(config.slaThresholds).forEach(([metric, threshold]) => {
    const actualValue = metrics.benchmarks[metric];
    if (actualValue && actualValue > threshold) {
      metrics.slaViolations.push({
        metric,
        threshold,
        actual: actualValue,
        violation: ((actualValue - threshold) / threshold * 100).toFixed(2) + '%'
      });
    }
  });

  return metrics;
}

// Generate HTML report
function generateHTMLReport(allMetrics) {
  const timestamp = new Date().toISOString();
  
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NOLK Performance Test Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; margin: 40px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .metric-card { background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 15px 0; }
        .success { border-left: 4px solid #28a745; }
        .warning { border-left: 4px solid #ffc107; }
        .danger { border-left: 4px solid #dc3545; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
        th { background-color: #f8f9fa; font-weight: 600; }
        .badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; }
        .badge-success { background-color: #d4edda; color: #155724; }
        .badge-danger { background-color: #f8d7da; color: #721c24; }
        .chart { height: 200px; background: #f8f9fa; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>NOLK v4 Performance Test Report</h1>
        <p><strong>Generated:</strong> ${timestamp}</p>
        <p><strong>Test Environment:</strong> ${process.platform} ${process.arch}</p>
    </div>

    <div class="grid">
        ${Object.entries(allMetrics).map(([testType, metrics]) => `
        <div class="metric-card ${metrics.slaViolations.length === 0 ? 'success' : 'danger'}">
            <h3>${testType.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</h3>
            <div class="grid">
                <div>
                    <strong>Total Tests:</strong> ${metrics.totalTests}<br>
                    <strong>Passed:</strong> <span class="badge badge-success">${metrics.passedTests}</span><br>
                    <strong>Failed:</strong> <span class="badge ${metrics.failedTests > 0 ? 'badge-danger' : 'badge-success'}">${metrics.failedTests}</span>
                </div>
                <div>
                    <strong>Duration:</strong> ${(metrics.duration / 1000).toFixed(2)}s<br>
                    <strong>SLA Violations:</strong> <span class="badge ${metrics.slaViolations.length > 0 ? 'badge-danger' : 'badge-success'}">${metrics.slaViolations.length}</span>
                </div>
            </div>
            
            ${metrics.slaViolations.length > 0 ? `
            <h4>SLA Violations:</h4>
            <table>
                <thead>
                    <tr><th>Metric</th><th>Threshold</th><th>Actual</th><th>Violation</th></tr>
                </thead>
                <tbody>
                    ${metrics.slaViolations.map(v => `
                    <tr>
                        <td>${v.metric}</td>
                        <td>${v.threshold}ms</td>
                        <td>${v.actual}ms</td>
                        <td class="badge badge-danger">${v.violation}</td>
                    </tr>
                    `).join('')}
                </tbody>
            </table>
            ` : '<p class="badge badge-success">All SLA requirements met!</p>'}
        </div>
        `).join('')}
    </div>

    <div class="metric-card">
        <h3>Performance Benchmarks</h3>
        <table>
            <thead>
                <tr><th>Test Suite</th><th>Key Metrics</th><th>Status</th></tr>
            </thead>
            <tbody>
                ${Object.entries(allMetrics).map(([testType, metrics]) => `
                <tr>
                    <td>${testType}</td>
                    <td>
                        ${Object.entries(metrics.benchmarks).slice(0, 3).map(([key, value]) => 
                          `${key}: ${typeof value === 'number' ? value.toFixed(2) : value}`
                        ).join('<br>')}
                    </td>
                    <td>
                        <span class="badge ${metrics.slaViolations.length === 0 ? 'badge-success' : 'badge-danger'}">
                            ${metrics.slaViolations.length === 0 ? 'PASS' : 'FAIL'}
                        </span>
                    </td>
                </tr>
                `).join('')}
            </tbody>
        </table>
    </div>

    <div class="metric-card">
        <h3>Recommendations</h3>
        <ul>
            ${Object.values(allMetrics).some(m => m.slaViolations.length > 0) ? 
              '<li><strong>Performance Issues Detected:</strong> Review SLA violations above and optimize affected components.</li>' : 
              '<li><strong>All Performance Targets Met:</strong> Continue monitoring for regressions.</li>'
            }
            <li><strong>Memory Management:</strong> Monitor heap usage during load tests for potential memory leaks.</li>
            <li><strong>Database Optimization:</strong> Consider query optimization for endpoints exceeding 500ms response time.</li>
            <li><strong>Caching Strategy:</strong> Implement or improve caching for frequently accessed data.</li>
        </ul>
    </div>
</body>
</html>
  `;

  const reportPath = path.join(config.reportDir, `performance-report-${Date.now()}.html`);
  fs.writeFileSync(reportPath, html);
  return reportPath;
}

// Save benchmark data
function saveBenchmarkData(allMetrics) {
  const benchmarkFile = path.join(config.outputDir, `benchmarks-${Date.now()}.json`);
  const data = {
    timestamp: new Date().toISOString(),
    environment: {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version
    },
    metrics: allMetrics
  };
  
  fs.writeFileSync(benchmarkFile, JSON.stringify(data, null, 2));
  return benchmarkFile;
}

// Main execution function
async function main() {
  console.log('🚀 Starting NOLK Performance Test Suite...\n');
  
  ensureDirectories();
  
  const testSuites = [
    {
      name: 'dashboardPerformance',
      pattern: '**/tests/performance/dashboard-performance.test.ts',
      description: 'Dashboard loading and API response performance'
    },
    {
      name: 'componentPerformance',
      pattern: '**/tests/performance/component-performance.test.tsx',
      description: 'Component rendering and interaction performance'
    },
    {
      name: 'loadTesting',
      pattern: '**/tests/load/load-testing.test.ts',
      description: 'Load testing and stress testing'
    }
  ];

  const allMetrics = {};
  const startTime = Date.now();

  for (const suite of testSuites) {
    console.log(`\n📊 Running ${suite.description}...`);
    
    try {
      const outputFile = path.join(config.outputDir, `${suite.name}-results.json`);
      await runTests(suite.pattern, outputFile);
      
      const testResults = parseJestOutput(outputFile);
      const metrics = extractPerformanceMetrics(testResults);
      allMetrics[suite.name] = metrics;
      
      console.log(`✅ ${suite.name} completed: ${metrics.passedTests}/${metrics.totalTests} tests passed`);
      
      if (metrics.slaViolations.length > 0) {
        console.log(`⚠️  ${metrics.slaViolations.length} SLA violations detected`);
      }
      
    } catch (error) {
      console.error(`❌ ${suite.name} failed:`, error.message);
      allMetrics[suite.name] = {
        totalTests: 0,
        passedTests: 0,
        failedTests: 1,
        duration: 0,
        benchmarks: {},
        slaViolations: [],
        error: error.message
      };
    }
  }

  const totalTime = Date.now() - startTime;
  console.log(`\n🏁 Performance testing completed in ${(totalTime / 1000).toFixed(2)}s`);

  // Generate reports
  console.log('\n📋 Generating reports...');
  const htmlReport = generateHTMLReport(allMetrics);
  const benchmarkFile = saveBenchmarkData(allMetrics);

  console.log(`\n📊 Reports generated:`);
  console.log(`   HTML Report: ${htmlReport}`);
  console.log(`   Benchmark Data: ${benchmarkFile}`);

  // Summary
  const totalTests = Object.values(allMetrics).reduce((sum, m) => sum + m.totalTests, 0);
  const totalPassed = Object.values(allMetrics).reduce((sum, m) => sum + m.passedTests, 0);
  const totalViolations = Object.values(allMetrics).reduce((sum, m) => sum + m.slaViolations.length, 0);

  console.log(`\n📈 Summary:`);
  console.log(`   Total Tests: ${totalTests}`);
  console.log(`   Passed: ${totalPassed}`);
  console.log(`   SLA Violations: ${totalViolations}`);
  console.log(`   Overall Status: ${totalViolations === 0 ? '✅ PASS' : '❌ FAIL'}`);

  // Exit with appropriate code
  process.exit(totalViolations === 0 ? 0 : 1);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled rejection:', error);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception:', error);
  process.exit(1);
});

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Performance test suite failed:', error);
    process.exit(1);
  });
}

module.exports = { main, config };