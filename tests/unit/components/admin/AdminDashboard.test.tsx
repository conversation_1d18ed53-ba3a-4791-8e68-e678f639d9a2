/**
 * @fileoverview Unit tests for components/admin/AdminDashboard.tsx
 * Tests admin dashboard functionality, data fetching, error handling, and UI components
 * Coverage target: 90%+
 */

import { render, screen, waitFor } from '@testing-library/react';

import AdminDashboard from '@/components/admin/AdminDashboard';
import React from 'react';

// Mock fetch globally
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Mock Next.js Link component
jest.mock('next/link', () => {
  const MockLink = ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  );
  MockLink.displayName = 'MockLink';
  return MockLink;
});

describe('components/admin/AdminDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock console methods to reduce test noise
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render dashboard header correctly', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Manage users, roles, and system settings')).toBeInTheDocument();
      expect(screen.getByText('System Active')).toBeInTheDocument();
    });

    it('should render all metric cards', async () => {
      const mockStats = {
        userCount: 25,
        brandCount: 8,
        roleCount: 5,
        groupCount: 3,
        permissionCount: 20,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Total Users')).toBeInTheDocument();
        expect(screen.getByText('Total Brands')).toBeInTheDocument();
        expect(screen.getByText('Total Roles')).toBeInTheDocument();
        expect(screen.getByText('Total Groups')).toBeInTheDocument();
        expect(screen.getByText('Total Permissions')).toBeInTheDocument();
      });

      await waitFor(() => {
        expect(screen.getByText('25')).toBeInTheDocument();
        expect(screen.getByText('8')).toBeInTheDocument();
        expect(screen.getByText('5')).toBeInTheDocument();
        expect(screen.getByText('3')).toBeInTheDocument();
        expect(screen.getByText('20')).toBeInTheDocument();
      });
    });

    it('should render all management sections', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('User Management')).toBeInTheDocument();
        expect(screen.getByText('Role Management')).toBeInTheDocument();
        expect(screen.getByText('Permission Management')).toBeInTheDocument();
        expect(screen.getByText('Group Management')).toBeInTheDocument();
        expect(screen.getByText('Brand Management')).toBeInTheDocument();
        expect(screen.getByText('Database Structure')).toBeInTheDocument();
      });
    });

    it('should render management section links with correct hrefs', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        const userManagementLink = screen.getByText('User Management').closest('a');
        expect(userManagementLink).toHaveAttribute('href', '/admin/users');

        const roleManagementLink = screen.getByText('Role Management').closest('a');
        expect(roleManagementLink).toHaveAttribute('href', '/admin/roles');

        const permissionManagementLink = screen.getByText('Permission Management').closest('a');
        expect(permissionManagementLink).toHaveAttribute('href', '/admin/permissions');

        const groupManagementLink = screen.getByText('Group Management').closest('a');
        expect(groupManagementLink).toHaveAttribute('href', '/admin/groups');

        const brandManagementLink = screen.getByText('Brand Management').closest('a');
        expect(brandManagementLink).toHaveAttribute('href', '/admin/brands');

        const dbStructureLink = screen.getByText('Database Structure').closest('a');
        expect(dbStructureLink).toHaveAttribute('href', '/admin/db-structure');
      });
    });
  });

  describe('Loading States', () => {
    it('should show loading skeletons initially', async () => {
      mockFetch.mockImplementation(() => new Promise(() => {})); // Never resolves

      render(<AdminDashboard />);

      // Wait for the component to render and enter loading state
      await waitFor(() => {
        expect(screen.getByText('Key Metrics')).toBeInTheDocument();
      });

      // Check for skeleton loading in metric cards
      const skeletons = screen.getAllByTestId('skeleton');
      expect(skeletons.length).toBeGreaterThan(0);
    });

    it('should show loading skeletons for recent activities', () => {
      mockFetch.mockImplementation(() => new Promise(() => {})); // Never resolves

      render(<AdminDashboard />);

      expect(screen.getByText('Latest User Additions')).toBeInTheDocument();
      expect(screen.getByText('Recent Login Activities')).toBeInTheDocument();
    });
  });

  describe('Data Fetching', () => {
    it('should fetch dashboard stats on mount', async () => {
      const mockStats = {
        userCount: 15,
        brandCount: 7,
        roleCount: 4,
        groupCount: 3,
        permissionCount: 18,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      expect(mockFetch).toHaveBeenCalledWith('/api/admin/dashboard-stats');

      await waitFor(() => {
        expect(screen.getByText('15')).toBeInTheDocument();
        expect(screen.getByText('7')).toBeInTheDocument();
        expect(screen.getByText('4')).toBeInTheDocument();
        expect(screen.getByText('3')).toBeInTheDocument();
        expect(screen.getByText('18')).toBeInTheDocument();
      });
    });

    it('should display latest user additions', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [
          {
            id: 1,
            name: 'John Doe',
            email: '<EMAIL>',
            createdAt: '2024-01-15T10:30:00Z',
          },
          {
            id: 2,
            name: 'Jane Smith',
            email: '<EMAIL>',
            createdAt: '2024-01-14T15:45:00Z',
          },
        ],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('should display recent user logins', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: [
          {
            id: 1,
            name: 'Alice Johnson',
            email: '<EMAIL>',
            lastLoginAt: '2024-01-15T14:30:00Z',
          },
          {
            id: 2,
            name: 'Bob Wilson',
            email: '<EMAIL>',
            lastLoginAt: '2024-01-15T12:15:00Z',
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('Bob Wilson')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('should show empty state for no recent user additions', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('No recent user additions')).toBeInTheDocument();
      });
    });

    it('should show empty state for no recent login activities', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('No recent login activities')).toBeInTheDocument();
        expect(screen.getByText('Login tracking will appear here once implemented')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('should display error message when API call fails', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Error loading dashboard data')).toBeInTheDocument();
        expect(screen.getByText('Network error')).toBeInTheDocument();
      });
    });

    it('should display error message when API returns non-ok response', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Error loading dashboard data')).toBeInTheDocument();
        expect(screen.getByText('HTTP error! status: 500')).toBeInTheDocument();
      });
    });

    it('should handle unknown error types', async () => {
      mockFetch.mockRejectedValueOnce('Unknown error');

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Error loading dashboard data')).toBeInTheDocument();
        expect(screen.getByText('An unknown error occurred')).toBeInTheDocument();
      });
    });

    it('should log errors to console', async () => {
      const error = new Error('Test error');
      mockFetch.mockRejectedValueOnce(error);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(console.error).toHaveBeenCalledWith(error);
      });
    });
  });

  describe('Data Display', () => {
    it('should display N/A for null metric values', async () => {
      const mockStats = {
        userCount: null,
        brandCount: null,
        roleCount: null,
        groupCount: null,
        permissionCount: null,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        const naElements = screen.getAllByText('N/A');
        expect(naElements).toHaveLength(5); // One for each metric card
      });
    });

    it('should display zero values correctly', async () => {
      const mockStats = {
        userCount: 0,
        brandCount: 0,
        roleCount: 0,
        groupCount: 0,
        permissionCount: 0,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        const zeroElements = screen.getAllByText('0');
        expect(zeroElements.length).toBeGreaterThanOrEqual(5);
      });
    });

    it('should format dates correctly in user additions', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [
          {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>',
            createdAt: '2024-01-15T10:30:00Z',
          },
        ],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Test User')).toBeInTheDocument();
        // Date formatting will depend on locale, but should be present
        const dateElements = screen.getAllByText(/\d+\/\d+\/\d+/);
        expect(dateElements.length).toBeGreaterThan(0);
      });
    });

    it('should format dates correctly in login activities', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: [
          {
            id: 1,
            name: 'Login User',
            email: '<EMAIL>',
            lastLoginAt: '2024-01-15T14:30:00Z',
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Login User')).toBeInTheDocument();
        // Date formatting will include time, should be present
        const dateTimeElements = screen.getAllByText(/\d+\/\d+\/\d+/);
        expect(dateTimeElements.length).toBeGreaterThan(0);
      });
    });

    it('should limit user additions to 5 items', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: Array.from({ length: 10 }, (_, i) => ({
          id: i + 1,
          name: `User ${i + 1}`,
          email: `user${i + 1}@example.com`,
          createdAt: '2024-01-15T10:30:00Z',
        })),
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('User 1')).toBeInTheDocument();
        expect(screen.getByText('User 5')).toBeInTheDocument();
        expect(screen.queryByText('User 6')).not.toBeInTheDocument();
      });
    });

    it('should limit login activities to 5 items', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: Array.from({ length: 10 }, (_, i) => ({
          id: i + 1,
          name: `Login User ${i + 1}`,
          email: `login${i + 1}@example.com`,
          lastLoginAt: '2024-01-15T14:30:00Z',
        })),
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Login User 1')).toBeInTheDocument();
        expect(screen.getByText('Login User 5')).toBeInTheDocument();
        expect(screen.queryByText('Login User 6')).not.toBeInTheDocument();
      });
    });
  });

  describe('UI Elements', () => {
    it('should display Live Data badge', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      expect(screen.getByText('Live Data')).toBeInTheDocument();
    });

    it('should display section headers with icons', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      expect(screen.getByText('Key Metrics')).toBeInTheDocument();
      expect(screen.getByText('Recent Activity')).toBeInTheDocument();
      expect(screen.getByText('Management Sections')).toBeInTheDocument();
    });

    it('should display metric descriptions', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Active system users')).toBeInTheDocument();
        expect(screen.getByText('Managed brands')).toBeInTheDocument();
        expect(screen.getByText('Permission roles')).toBeInTheDocument();
        expect(screen.getByText('User groups')).toBeInTheDocument();
        expect(screen.getByText('System permissions')).toBeInTheDocument();
      });
    });

    it('should display management section descriptions', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Manage users, roles, and access permissions')).toBeInTheDocument();
        expect(screen.getByText('Configure user roles and permission sets')).toBeInTheDocument();
        expect(screen.getByText('Define and manage application permissions')).toBeInTheDocument();
        expect(screen.getByText('Organize users into logical groups')).toBeInTheDocument();
        expect(screen.getByText('Manage brand information and settings')).toBeInTheDocument();
        expect(screen.getByText('View and manage database schema')).toBeInTheDocument();
      });
    });

    it('should display management section stats', async () => {
      const mockStats = {
        userCount: 25,
        brandCount: 8,
        roleCount: 5,
        groupCount: 3,
        permissionCount: 20,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(<AdminDashboard />);

      await waitFor(() => {
        expect(screen.getByText('25 users')).toBeInTheDocument();
        expect(screen.getByText('8 brands')).toBeInTheDocument();
        expect(screen.getByText('5 roles')).toBeInTheDocument();
        expect(screen.getByText('3 groups')).toBeInTheDocument();
        expect(screen.getByText('20 permissions')).toBeInTheDocument();
        expect(screen.getByText('Schema info')).toBeInTheDocument();
      });
    });
  });

  describe('Performance Tests', () => {
    it('should render efficiently with large datasets', async () => {
      const mockStats = {
        userCount: 1000,
        brandCount: 100,
        roleCount: 50,
        groupCount: 25,
        permissionCount: 200,
        latestUserAdditions: Array.from({ length: 10 }, (_, i) => ({
          id: i + 1,
          name: `User ${i + 1}`,
          email: `user${i + 1}@example.com`,
          createdAt: '2024-01-15T10:30:00Z',
        })),
        recentUserLogins: Array.from({ length: 10 }, (_, i) => ({
          id: i + 1,
          name: `Login User ${i + 1}`,
          email: `login${i + 1}@example.com`,
          lastLoginAt: '2024-01-15T14:30:00Z',
        })),
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      const start = performance.now();
      render(<AdminDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('1000')).toBeInTheDocument();
      });
      
      const end = performance.now();
      expect(end - start).toBeLessThan(1000);
    });

    it('should handle rapid re-renders efficiently', async () => {
      const mockStats = {
        userCount: 10,
        brandCount: 5,
        roleCount: 3,
        groupCount: 2,
        permissionCount: 15,
        latestUserAdditions: [],
        recentUserLogins: [],
      };

      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockStats,
      } as Response);

      const start = performance.now();
      
      for (let i = 0; i < 5; i++) {
        const { unmount } = render(<AdminDashboard />);
        await waitFor(() => {
          expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
        });
        unmount();
      }
      
      const end = performance.now();
      expect(end - start).toBeLessThan(500);
    });
  });
});
