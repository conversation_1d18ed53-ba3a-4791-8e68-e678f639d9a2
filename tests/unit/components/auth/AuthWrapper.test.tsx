/**
 * @fileoverview Unit tests for components/auth/AuthWrapper.tsx
 * Tests authentication wrapper component, route protection, and redirect logic
 * Coverage target: 90%+
 */

import { render, screen, waitFor } from '@testing-library/react';
import { usePathname, useRouter } from 'next/navigation';

import { AuthWrapper } from '@/components/auth/AuthWrapper';
import React from 'react';
import { useSession } from 'next-auth/react';

// Mock next-auth
jest.mock('next-auth/react');
const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;

// Mock next/navigation
jest.mock('next/navigation');
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

// Mock window.location
const mockLocation = {
  href: '',
};
Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

describe('components/auth/AuthWrapper', () => {
  const mockPush = jest.fn();
  const mockReplace = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock console methods to reduce test noise
    jest.spyOn(console, 'log').mockImplementation(() => {});
    
    // Setup router mock
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: mockReplace,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    });

    // Reset window.location
    mockLocation.href = '';
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Loading States', () => {
    it('should show loading spinner when session is loading', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'loading',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/dashboard');

      render(
        <AuthWrapper>
          <div>Protected Content</div>
        </AuthWrapper>
      );

      expect(screen.getByText('Loading...')).toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    it('should show loading spinner with correct styling', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'loading',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/dashboard');

      render(
        <AuthWrapper>
          <div>Content</div>
        </AuthWrapper>
      );

      const loadingContainer = screen.getByText('Loading...').parentElement?.parentElement;
      expect(loadingContainer).toHaveClass('flex', 'justify-center', 'items-center', 'min-h-screen');
    });
  });

  describe('Public Routes', () => {
    const publicRoutes = [
      '/auth/signin',
      '/auth/signup',
      '/auth/error',
      '/auth/verify-request',
    ];

    publicRoutes.forEach(route => {
      it(`should render children for public route: ${route}`, () => {
        mockUseSession.mockReturnValue({
          data: null,
          status: 'unauthenticated',
          update: jest.fn(),
        });
        mockUsePathname.mockReturnValue(route);

        render(
          <AuthWrapper>
            <div>Public Content</div>
          </AuthWrapper>
        );

        expect(screen.getByText('Public Content')).toBeInTheDocument();
      });
    });

    it('should render children for public route with subpaths', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/auth/signin/callback');

      render(
        <AuthWrapper>
          <div>Public Subpath Content</div>
        </AuthWrapper>
      );

      expect(screen.getByText('Public Subpath Content')).toBeInTheDocument();
    });
  });

  describe('Protected Routes - Unauthenticated', () => {
    it('should redirect to signin for protected route when unauthenticated', async () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/dashboard');

      render(
        <AuthWrapper>
          <div>Protected Content</div>
        </AuthWrapper>
      );

      expect(screen.getByText('Redirecting to login...')).toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();

      await waitFor(() => {
        expect(mockLocation.href).toBe('/auth/signin?callbackUrl=%2Fdashboard');
      });
    });

    it('should handle complex paths in callback URL', async () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/admin/users?filter=active');

      render(
        <AuthWrapper>
          <div>Admin Content</div>
        </AuthWrapper>
      );

      await waitFor(() => {
        expect(mockLocation.href).toBe('/auth/signin?callbackUrl=%2Fadmin%2Fusers%3Ffilter%3Dactive');
      });
    });

    it('should show redirecting state immediately for protected routes', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/protected-page');

      render(
        <AuthWrapper>
          <div>Protected Content</div>
        </AuthWrapper>
      );

      expect(screen.getByText('Redirecting to login...')).toBeInTheDocument();
      
      const redirectContainer = screen.getByText('Redirecting to login...').parentElement?.parentElement;
      expect(redirectContainer).toHaveClass('flex', 'justify-center', 'items-center', 'min-h-screen');
    });
  });

  describe('Authenticated Users', () => {
    const mockSession = {
      user: {
        id: 'user-1',
        name: 'Test User',
        email: '<EMAIL>',
        roles: ['User'],
        permissions: ['view_dashboard'],
        groups: [],
        brands: [],
      },
      expires: new Date(Date.now() + 86400000).toISOString(),
    };

    it('should render children for authenticated user on protected route', () => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/dashboard');

      render(
        <AuthWrapper>
          <div>Dashboard Content</div>
        </AuthWrapper>
      );

      expect(screen.getByText('Dashboard Content')).toBeInTheDocument();
    });

    it('should redirect authenticated user from signin page to dashboard', async () => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/auth/signin');

      render(
        <AuthWrapper>
          <div>Signin Form</div>
        </AuthWrapper>
      );

      expect(screen.getByText('Redirecting to dashboard...')).toBeInTheDocument();
      expect(screen.queryByText('Signin Form')).not.toBeInTheDocument();

      await waitFor(() => {
        expect(mockLocation.href).toBe('/dashboard');
      });
    });

    it('should redirect authenticated user from any auth page to dashboard', async () => {
      const authPages = ['/auth/signin', '/auth/signup', '/auth/error'];

      for (const authPage of authPages) {
        mockUseSession.mockReturnValue({
          data: mockSession,
          status: 'authenticated',
          update: jest.fn(),
        });
        mockUsePathname.mockReturnValue(authPage);

        const { unmount } = render(
          <AuthWrapper>
            <div>Auth Content</div>
          </AuthWrapper>
        );

        expect(screen.getByText('Redirecting to dashboard...')).toBeInTheDocument();
        
        await waitFor(() => {
          expect(mockLocation.href).toBe('/dashboard');
        });

        // Reset for next iteration
        mockLocation.href = '';
        unmount();
      }
    });

    it('should show redirecting state for authenticated users on auth pages', () => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/auth/signin');

      render(
        <AuthWrapper>
          <div>Signin Form</div>
        </AuthWrapper>
      );

      expect(screen.getByText('Redirecting to dashboard...')).toBeInTheDocument();
      
      const redirectContainer = screen.getByText('Redirecting to dashboard...').parentElement?.parentElement;
      expect(redirectContainer).toHaveClass('flex', 'justify-center', 'items-center', 'min-h-screen');
    });
  });

  describe('Route Protection Logic', () => {
    it('should correctly identify public routes', () => {
      const testCases = [
        { path: '/auth/signin', shouldBePublic: true },
        { path: '/auth/signin/google', shouldBePublic: true },
        { path: '/auth/signup', shouldBePublic: true },
        { path: '/auth/error', shouldBePublic: true },
        { path: '/auth/verify-request', shouldBePublic: true },
        { path: '/dashboard', shouldBePublic: false },
        { path: '/admin', shouldBePublic: false },
        { path: '/profile', shouldBePublic: false },
        { path: '/', shouldBePublic: false },
      ];

      testCases.forEach(({ path, shouldBePublic }) => {
        mockUsePathname.mockReturnValue(path);
        
        if (shouldBePublic) {
          mockUseSession.mockReturnValue({
            data: null,
            status: 'unauthenticated',
            update: jest.fn(),
          });

          const { unmount } = render(
            <AuthWrapper>
              <div>Test Content</div>
            </AuthWrapper>
          );

          expect(screen.getByText('Test Content')).toBeInTheDocument();
          unmount();
        } else {
          mockUseSession.mockReturnValue({
            data: null,
            status: 'unauthenticated',
            update: jest.fn(),
          });

          const { unmount } = render(
            <AuthWrapper>
              <div>Test Content</div>
            </AuthWrapper>
          );

          expect(screen.getByText('Redirecting to login...')).toBeInTheDocument();
          unmount();
        }
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle session status change from loading to unauthenticated', async () => {
      const { rerender } = render(<AuthWrapper><div>Content</div></AuthWrapper>);

      // Initially loading
      mockUseSession.mockReturnValue({
        data: null,
        status: 'loading',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/dashboard');

      rerender(<AuthWrapper><div>Content</div></AuthWrapper>);
      expect(screen.getByText('Loading...')).toBeInTheDocument();

      // Then unauthenticated
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: jest.fn(),
      });

      rerender(<AuthWrapper><div>Content</div></AuthWrapper>);
      expect(screen.getByText('Redirecting to login...')).toBeInTheDocument();
    });

    it('should handle session status change from loading to authenticated', () => {
      const mockSession = {
        user: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          roles: ['User'],
          permissions: ['view_dashboard'],
          groups: [],
          brands: [],
        },
        expires: new Date(Date.now() + 86400000).toISOString(),
      };

      const { rerender } = render(<AuthWrapper><div>Content</div></AuthWrapper>);

      // Initially loading
      mockUseSession.mockReturnValue({
        data: null,
        status: 'loading',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/dashboard');

      rerender(<AuthWrapper><div>Content</div></AuthWrapper>);
      expect(screen.getByText('Loading...')).toBeInTheDocument();

      // Then authenticated
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: jest.fn(),
      });

      rerender(<AuthWrapper><div>Content</div></AuthWrapper>);
      expect(screen.getByText('Content')).toBeInTheDocument();
    });

    it('should handle empty pathname', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('');

      render(
        <AuthWrapper>
          <div>Content</div>
        </AuthWrapper>
      );

      expect(screen.getByText('Redirecting to login...')).toBeInTheDocument();
    });

    it('should handle root path', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/');

      render(
        <AuthWrapper>
          <div>Content</div>
        </AuthWrapper>
      );

      expect(screen.getByText('Redirecting to login...')).toBeInTheDocument();
    });

    it('should not redirect multiple times', async () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/dashboard');

      const { rerender } = render(
        <AuthWrapper>
          <div>Content</div>
        </AuthWrapper>
      );

      // First render should trigger redirect
      await waitFor(() => {
        expect(mockLocation.href).toBe('/auth/signin?callbackUrl=%2Fdashboard');
      });

      // Reset href to simulate redirect completion
      const firstRedirectUrl = mockLocation.href;
      mockLocation.href = '';

      // Re-render should not trigger another redirect
      rerender(
        <AuthWrapper>
          <div>Content</div>
        </AuthWrapper>
      );

      // Should still show redirecting state but not change location again
      expect(screen.getByText('Redirecting to login...')).toBeInTheDocument();
      expect(mockLocation.href).toBe('');
    });
  });

  describe('Accessibility and UX', () => {
    it('should provide appropriate loading states with proper ARIA attributes', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'loading',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/dashboard');

      render(
        <AuthWrapper>
          <div>Content</div>
        </AuthWrapper>
      );

      const loadingText = screen.getByText('Loading...');
      expect(loadingText).toBeInTheDocument();
      
      // Check that the loading spinner is present
      const spinner = loadingText.parentElement?.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
    });

    it('should provide clear redirect messages', () => {
      // Test unauthenticated redirect message
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/dashboard');

      const { rerender } = render(
        <AuthWrapper>
          <div>Content</div>
        </AuthWrapper>
      );

      expect(screen.getByText('Redirecting to login...')).toBeInTheDocument();

      // Test authenticated redirect message
      const mockSession = {
        user: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          roles: ['User'],
          permissions: ['view_dashboard'],
          groups: [],
          brands: [],
        },
        expires: new Date(Date.now() + 86400000).toISOString(),
      };

      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/auth/signin');

      rerender(
        <AuthWrapper>
          <div>Content</div>
        </AuthWrapper>
      );

      expect(screen.getByText('Redirecting to dashboard...')).toBeInTheDocument();
    });
  });

  describe('Performance Tests', () => {
    it('should render efficiently for authenticated users', () => {
      const mockSession = {
        user: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          roles: ['User'],
          permissions: ['view_dashboard'],
          groups: [],
          brands: [],
        },
        expires: new Date(Date.now() + 86400000).toISOString(),
      };

      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
        update: jest.fn(),
      });
      mockUsePathname.mockReturnValue('/dashboard');

      const start = performance.now();
      
      for (let i = 0; i < 10; i++) {
        const { unmount } = render(
          <AuthWrapper>
            <div>Performance Test Content {i}</div>
          </AuthWrapper>
        );
        unmount();
      }
      
      const end = performance.now();
      expect(end - start).toBeLessThan(100);
    });

    it('should handle rapid session state changes efficiently', () => {
      const { rerender } = render(<AuthWrapper><div>Content</div></AuthWrapper>);

      const start = performance.now();

      // Simulate rapid state changes
      for (let i = 0; i < 20; i++) {
        mockUseSession.mockReturnValue({
          data: null,
          status: i % 2 === 0 ? 'loading' : 'unauthenticated',
          update: jest.fn(),
        });
        mockUsePathname.mockReturnValue('/dashboard');

        rerender(<AuthWrapper><div>Content</div></AuthWrapper>);
      }

      const end = performance.now();
      expect(end - start).toBeLessThan(100);
    });
  });
});