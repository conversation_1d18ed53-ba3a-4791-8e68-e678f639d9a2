/**
 * @fileoverview Unit tests for components/auth/SignInComponent.tsx
 * Tests sign-in functionality, error handling, and user interactions
 * Coverage target: 90%+
 */

import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { useRouter, useSearchParams } from 'next/navigation';

import React from 'react';
import SignInComponent from '@/components/auth/SignInComponent';
import { signIn } from 'next-auth/react';

// Mock next-auth
jest.mock('next-auth/react');
const mockSignIn = signIn as jest.MockedFunction<typeof signIn>;

// Mock next/navigation
jest.mock('next/navigation');
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockUseSearchParams = useSearchParams as jest.MockedFunction<typeof useSearchParams>;

describe('components/auth/SignInComponent', () => {
  const mockPush = jest.fn();
  const mockGet = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock console methods to reduce test noise
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // Setup router mock
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    });

    // Setup search params mock
    mockUseSearchParams.mockReturnValue({
      get: mockGet,
      has: jest.fn(),
      getAll: jest.fn(),
      keys: jest.fn(),
      values: jest.fn(),
      entries: jest.fn(),
      forEach: jest.fn(),
      toString: jest.fn(),
    } as any);

    // Default search params
    mockGet.mockImplementation((key: string) => {
      if (key === 'callbackUrl') return null;
      if (key === 'error') return null;
      return null;
    });

    // Mock environment variable
    process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID = 'test-client-id';
  });

  afterEach(() => {
    jest.restoreAllMocks();
    delete process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
  });

  describe('Component Rendering', () => {
    it('should render sign-in form with correct elements', () => {
      render(<SignInComponent />);

      expect(screen.getByText('NOLK')).toBeInTheDocument();
      expect(screen.getByText('Sign in to your account')).toBeInTheDocument();
      expect(screen.getByText('Please sign in with your Google account to access the dashboard')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /continue with google/i })).toBeInTheDocument();
    });

    it('should render NOLK logo with correct styling', () => {
      render(<SignInComponent />);

      const logoContainer = screen.getByText('NOLK').previousElementSibling;
      expect(logoContainer).toHaveClass('mx-auto', 'mb-6', 'flex', 'h-16', 'w-16', 'items-center', 'justify-center', 'rounded-lg', 'bg-blue-600');
    });

    it('should render Google button with correct styling', () => {
      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      expect(button).toHaveClass('w-full', 'flex', 'justify-center', 'items-center');
      expect(button).not.toBeDisabled();
    });

    it('should render terms and privacy policy text', () => {
      render(<SignInComponent />);

      expect(screen.getByText('By signing in, you agree to our terms of service and privacy policy.')).toBeInTheDocument();
    });
  });

  describe('Callback URL Handling', () => {
    it('should use default callback URL when none provided', () => {
      mockGet.mockImplementation((key: string) => {
        if (key === 'callbackUrl') return null;
        return null;
      });

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(button);

      expect(mockSignIn).toHaveBeenCalledWith('google', {
        callbackUrl: '/dashboard',
        redirect: false,
      });
    });

    it('should use provided callback URL', () => {
      mockGet.mockImplementation((key: string) => {
        if (key === 'callbackUrl') return '/admin/users';
        return null;
      });

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(button);

      expect(mockSignIn).toHaveBeenCalledWith('google', {
        callbackUrl: '/admin/users',
        redirect: false,
      });
    });

    it('should handle encoded callback URLs', () => {
      mockGet.mockImplementation((key: string) => {
        if (key === 'callbackUrl') return '%2Fadmin%2Fusers%3Ffilter%3Dactive';
        return null;
      });

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(button);

      expect(mockSignIn).toHaveBeenCalledWith('google', {
        callbackUrl: '%2Fadmin%2Fusers%3Ffilter%3Dactive',
        redirect: false,
      });
    });
  });

  describe('Error Handling', () => {
    it('should display Configuration error message', () => {
      mockGet.mockImplementation((key: string) => {
        if (key === 'error') return 'Configuration';
        return null;
      });

      render(<SignInComponent />);

      expect(screen.getByText('Google authentication is not configured properly.')).toBeInTheDocument();
    });

    it('should display AccessDenied error message', () => {
      mockGet.mockImplementation((key: string) => {
        if (key === 'error') return 'AccessDenied';
        return null;
      });

      render(<SignInComponent />);

      expect(screen.getByText('Access denied. Please contact your administrator.')).toBeInTheDocument();
    });

    it('should display Verification error message', () => {
      mockGet.mockImplementation((key: string) => {
        if (key === 'error') return 'Verification';
        return null;
      });

      render(<SignInComponent />);

      expect(screen.getByText('The verification link was invalid or has expired.')).toBeInTheDocument();
    });

    it('should display generic error message for unknown errors', () => {
      mockGet.mockImplementation((key: string) => {
        if (key === 'error') return 'UnknownError';
        return null;
      });

      render(<SignInComponent />);

      expect(screen.getByText('Authentication error: UnknownError')).toBeInTheDocument();
    });

    it('should display error with correct styling', () => {
      mockGet.mockImplementation((key: string) => {
        if (key === 'error') return 'Configuration';
        return null;
      });

      render(<SignInComponent />);

      const errorContainer = screen.getByText('Google authentication is not configured properly.').parentElement?.parentElement;
      expect(errorContainer).toHaveClass('rounded-md', 'bg-red-50', 'p-4');
    });
  });

  describe('Google Configuration', () => {
    it('should show configuration error when Google is not configured', () => {
      delete process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
      
      // Mock window.location for non-production environment
      Object.defineProperty(window, 'location', {
        value: { origin: 'http://localhost:3000' },
        writable: true,
      });

      render(<SignInComponent />);

      expect(screen.getByText('Google authentication is not configured properly.')).toBeInTheDocument();
      
      const button = screen.getByRole('button', { name: /continue with google/i });
      expect(button).toBeDisabled();
    });

    it('should allow sign-in when Google is configured via environment variable', () => {
      process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID = 'test-client-id';

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      expect(button).not.toBeDisabled();
      expect(screen.queryByText('Google authentication is not configured properly.')).not.toBeInTheDocument();
    });

    it('should allow sign-in on production domain even without env var', () => {
      delete process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
      
      // Mock window.location for production environment
      Object.defineProperty(window, 'location', {
        value: { origin: 'https://insights.nolk.com' },
        writable: true,
      });

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      expect(button).not.toBeDisabled();
      expect(screen.queryByText('Google authentication is not configured properly.')).not.toBeInTheDocument();
    });
  });

  describe('Sign-In Process', () => {
    it('should show loading state during sign-in', async () => {
      mockSignIn.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(button);

      expect(screen.getByText('Signing in...')).toBeInTheDocument();
      expect(button).toBeDisabled();
      
      // Check for loading spinner
      const spinner = screen.getByText('Signing in...').previousElementSibling;
      expect(spinner).toHaveClass('animate-spin');
    });

    it('should handle successful sign-in with redirect URL', async () => {
      mockSignIn.mockResolvedValue({
        error: null,
        status: 200,
        ok: true,
        url: '/dashboard',
      });

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(button);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/dashboard');
      });
    });

    it('should handle sign-in error from NextAuth', async () => {
      mockSignIn.mockResolvedValue({
        error: 'OAuthSignin',
        status: 400,
        ok: false,
        url: null,
      });

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(button);

      await waitFor(() => {
        expect(console.error).toHaveBeenCalledWith('Sign in error:', 'OAuthSignin');
      });
    });

    it('should handle sign-in exception', async () => {
      const error = new Error('Network error');
      mockSignIn.mockRejectedValue(error);

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(button);

      await waitFor(() => {
        expect(console.error).toHaveBeenCalledWith('Sign in failed:', error);
      });
    });

    it('should reset loading state after sign-in completes', async () => {
      mockSignIn.mockResolvedValue({
        error: null,
        status: 200,
        ok: true,
        url: '/dashboard',
      });

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(button);

      // Should show loading initially
      expect(screen.getByText('Signing in...')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.queryByText('Signing in...')).not.toBeInTheDocument();
        expect(screen.getByText('Continue with Google')).toBeInTheDocument();
      });
    });

    it('should reset loading state after sign-in error', async () => {
      mockSignIn.mockRejectedValue(new Error('Test error'));

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(button);

      await waitFor(() => {
        expect(screen.queryByText('Signing in...')).not.toBeInTheDocument();
        expect(screen.getByText('Continue with Google')).toBeInTheDocument();
      });
    });
  });

  describe('Button States', () => {
    it('should disable button when loading', async () => {
      mockSignIn.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(button);

      expect(button).toBeDisabled();
      expect(button).toHaveClass('disabled:opacity-50', 'disabled:cursor-not-allowed');
    });

    it('should disable button when Google is not configured', () => {
      delete process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
      
      Object.defineProperty(window, 'location', {
        value: { origin: 'http://localhost:3000' },
        writable: true,
      });

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      expect(button).toBeDisabled();
    });

    it('should enable button when Google is configured and not loading', () => {
      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      expect(button).not.toBeDisabled();
    });
  });

  describe('Accessibility', () => {
    it('should have proper button role and accessibility attributes', () => {
      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      expect(button).toHaveAttribute('type', 'button');
    });

    it('should have proper focus styles', () => {
      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      expect(button).toHaveClass('focus:outline-none', 'focus:ring-2', 'focus:ring-offset-2', 'focus:ring-blue-500');
    });

    it('should provide clear visual feedback for disabled state', () => {
      delete process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
      
      Object.defineProperty(window, 'location', {
        value: { origin: 'http://localhost:3000' },
        writable: true,
      });

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      expect(button).toHaveClass('disabled:opacity-50', 'disabled:cursor-not-allowed');
    });
  });

  describe('Visual Elements', () => {
    it('should render Google logo in button', () => {
      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      const svg = button.querySelector('svg');
      expect(svg).toBeInTheDocument();
      expect(svg).toHaveClass('w-5', 'h-5', 'mr-3');
    });

    it('should render loading spinner when signing in', async () => {
      mockSignIn.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(button);

      const spinner = screen.getByText('Signing in...').previousElementSibling;
      expect(spinner).toHaveClass('animate-spin');
      expect(spinner).toHaveAttribute('xmlns', 'http://www.w3.org/2000/svg');
    });

    it('should have proper container styling', () => {
      render(<SignInComponent />);

      const container = screen.getByText('NOLK').closest('div');
      expect(container?.parentElement?.parentElement).toHaveClass('flex', 'min-h-screen', 'items-center', 'justify-center', 'bg-gray-50');
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined search params gracefully', () => {
      mockUseSearchParams.mockReturnValue(null as any);

      expect(() => render(<SignInComponent />)).not.toThrow();
    });

    it('should handle window object not being available (SSR)', () => {
      const originalWindow = global.window;
      delete (global as any).window;

      expect(() => render(<SignInComponent />)).not.toThrow();

      global.window = originalWindow;
    });

    it('should handle multiple rapid clicks on sign-in button', async () => {
      mockSignIn.mockResolvedValue({
        error: null,
        status: 200,
        ok: true,
        url: '/dashboard',
      });

      render(<SignInComponent />);

      const button = screen.getByRole('button', { name: /continue with google/i });
      
      // Click multiple times rapidly
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);

      // Should only call signIn once due to loading state
      await waitFor(() => {
        expect(mockSignIn).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Performance Tests', () => {
    it('should render efficiently', () => {
      const start = performance.now();
      
      for (let i = 0; i < 10; i++) {
        const { unmount } = render(<SignInComponent />);
        unmount();
      }
      
      const end = performance.now();
      expect(end - start).toBeLessThan(100);
    });

    it('should handle sign-in process efficiently', async () => {
      mockSignIn.mockResolvedValue({
        error: null,
        status: 200,
        ok: true,
        url: '/dashboard',
      });

      render(<SignInComponent />);

      const start = performance.now();
      
      const button = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(button);

      await waitFor(() => {
        expect(mockSignIn).toHaveBeenCalled();
      });
      
      const end = performance.now();
      expect(end - start).toBeLessThan(50);
    });
  });
});