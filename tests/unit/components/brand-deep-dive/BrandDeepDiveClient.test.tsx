/**
 * @fileoverview Unit tests for app/brand-deep-dive/BrandDeepDiveClient.tsx
 * Tests brand deep dive main client component functionality and user interactions
 * Coverage target: 75%+ (Tier 2)
 */

import { render, screen } from '@testing-library/react';

import BrandDeepDiveClient from '@/app/brand-deep-dive/BrandDeepDiveClient';
import React from 'react';
import { jest } from '@jest/globals';

// Mock the context
const mockBrandDeepDiveState = {
  selectedBrand: null as string | null,
  isLoading: false,
  error: null as string | null,
};

const mockBrandDeepDiveActions = {
  setSelectedBrand: jest.fn(),
  clearError: jest.fn(),
};

jest.mock('@/lib/contexts/brand-deep-dive-context', () => ({
  BrandDeepDiveProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="brand-deep-dive-provider">{children}</div>,
  useBrandDeepDive: () => ({
    state: mockBrandDeepDiveState,
    actions: mockBrandDeepDiveActions,
  }),
}));

// Mock FilterProvider
jest.mock('@/lib/contexts/filter-context', () => ({
  FilterProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="filter-provider">{children}</div>,
}));

// Mock AppLayout
jest.mock('@/components/shared/AppLayout', () => ({
  AppLayout: ({ children, pageTitle, pageDescription }: { 
    children: React.ReactNode; 
    pageTitle: string; 
    pageDescription: string; 
  }) => (
    <div data-testid="app-layout">
      <h1>{pageTitle}</h1>
      <p>{pageDescription}</p>
      {children}
    </div>
  ),
}));

// Mock navigation config
jest.mock('@/components/main/MainNavConfig', () => ({
  mainNavItems: [{ title: 'Dashboard', href: '/' }],
  mainSecondaryNavItems: [{ title: 'Settings', href: '/settings' }],
}));

// Mock child components
jest.mock('@/components/enhanced-dashboard-filters', () => ({
  EnhancedDashboardFilters: ({ showBrandFilter, contextType, pageType }: { 
    showBrandFilter: boolean; 
    contextType: string; 
    pageType: string; 
  }) => (
    <div data-testid="enhanced-dashboard-filters" data-show-brand-filter={showBrandFilter} data-context-type={contextType} data-page-type={pageType}>
      Enhanced Dashboard Filters
    </div>
  ),
}));

jest.mock('@/components/brand-deep-dive/brand-deep-dive-kpi-cards', () => ({
  BrandDeepDiveKpiCards: () => <div data-testid="brand-deep-dive-kpi-cards">Brand KPI Cards</div>,
}));

jest.mock('@/components/brand-deep-dive/brand-deep-dive-charts', () => ({
  BrandDeepDiveCharts: () => <div data-testid="brand-deep-dive-charts">Brand Charts</div>,
}));

jest.mock('@/components/brand-deep-dive/brand-deep-dive-marketing-kpi-cards', () => ({
  BrandDeepDiveMarketingKpiCards: () => <div data-testid="brand-deep-dive-marketing-kpi-cards">Marketing KPI Cards</div>,
}));

// Mock UI components
jest.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>{children}</div>
  ),
  CardContent: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card-content" className={className}>{children}</div>
  ),
  CardHeader: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-header">{children}</div>
  ),
  CardTitle: ({ children }: { children: React.ReactNode }) => (
    <h3 data-testid="card-title">{children}</h3>
  ),
  CardDescription: ({ children }: { children: React.ReactNode }) => (
    <p data-testid="card-description">{children}</p>
  ),
}));

jest.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, defaultValue, className }: { children: React.ReactNode; defaultValue: string; className?: string }) => (
    <div data-testid="tabs" data-default-value={defaultValue} className={className}>{children}</div>
  ),
  TabsList: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="tabs-list" className={className}>{children}</div>
  ),
  TabsTrigger: ({ children, value }: { children: React.ReactNode; value: string }) => (
    <button data-testid={`tabs-trigger-${value}`} data-value={value}>{children}</button>
  ),
  TabsContent: ({ children, value, className }: { children: React.ReactNode; value: string; className?: string }) => (
    <div data-testid={`tabs-content-${value}`} className={className}>{children}</div>
  ),
}));

describe('app/brand-deep-dive/BrandDeepDiveClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mock state
    mockBrandDeepDiveState.selectedBrand = null;
    mockBrandDeepDiveState.isLoading = false;
    mockBrandDeepDiveState.error = null;
  });

  describe('Component Rendering', () => {
    it('should render the main client component with providers', () => {
      render(<BrandDeepDiveClient />);

      expect(screen.getByTestId('brand-deep-dive-provider')).toBeDefined();
      expect(screen.getByTestId('filter-provider')).toBeDefined();
      expect(screen.getByTestId('app-layout')).toBeDefined();
    });

    it('should render page title and description', () => {
      render(<BrandDeepDiveClient />);

      expect(screen.getByText('Brand Deep Dive')).toBeDefined();
      expect(screen.getByText('Detailed analysis of brand performance metrics')).toBeDefined();
    });

    it('should render enhanced dashboard filters with correct props', () => {
      render(<BrandDeepDiveClient />);

      const filters = screen.getByTestId('enhanced-dashboard-filters');
      expect(filters).toBeDefined();
      expect(filters.getAttribute('data-show-brand-filter')).toBe('true');
      expect(filters.getAttribute('data-context-type')).toBe('brandDeepDive');
      expect(filters.getAttribute('data-page-type')).toBe('brand-deep-dive');
    });
  });

  describe('No Brand Selected State', () => {
    it('should show no brand selected message when no brand is selected', () => {
      mockBrandDeepDiveState.selectedBrand = null;
      
      render(<BrandDeepDiveClient />);

      expect(screen.getByText('No Brand Selected')).toBeDefined();
      expect(screen.getByText('Please select a brand from the dropdown above to view detailed performance metrics and analysis.')).toBeDefined();
      expect(screen.getByText('📊')).toBeDefined();
    });

    it('should not render tabs when no brand is selected', () => {
      mockBrandDeepDiveState.selectedBrand = null;
      
      render(<BrandDeepDiveClient />);

      expect(screen.queryByTestId('tabs')).toBeNull();
      expect(screen.queryByTestId('tabs-trigger-financial')).toBeNull();
      expect(screen.queryByTestId('tabs-trigger-marketing')).toBeNull();
    });

    it('should render empty state card with proper styling', () => {
      mockBrandDeepDiveState.selectedBrand = null;
      
      render(<BrandDeepDiveClient />);

      const card = screen.getByTestId('card');
      expect(card.className).toContain('w-full');
      
      const cardContent = screen.getByTestId('card-content');
      expect(cardContent.className).toContain('flex');
      expect(cardContent.className).toContain('flex-col');
      expect(cardContent.className).toContain('items-center');
      expect(cardContent.className).toContain('justify-center');
      expect(cardContent.className).toContain('py-12');
    });
  });

  describe('Brand Selected State', () => {
    beforeEach(() => {
      mockBrandDeepDiveState.selectedBrand = 'Test Brand';
    });

    it('should render tabs when brand is selected', () => {
      render(<BrandDeepDiveClient />);

      expect(screen.getByTestId('tabs')).toBeDefined();
      expect(screen.getByTestId('tabs').getAttribute('data-default-value')).toBe('financial');
      expect(screen.getByTestId('tabs').className).toContain('w-full');
    });

    it('should render financial and marketing tab triggers', () => {
      render(<BrandDeepDiveClient />);

      expect(screen.getByTestId('tabs-trigger-financial')).toBeDefined();
      expect(screen.getByTestId('tabs-trigger-marketing')).toBeDefined();
      expect(screen.getByText('Financial')).toBeDefined();
      expect(screen.getByText('Marketing')).toBeDefined();
    });

    it('should render tabs list with proper grid layout', () => {
      render(<BrandDeepDiveClient />);

      const tabsList = screen.getByTestId('tabs-list');
      expect(tabsList.className).toContain('grid');
      expect(tabsList.className).toContain('w-full');
      expect(tabsList.className).toContain('grid-cols-2');
    });

    it('should render financial tab content', () => {
      render(<BrandDeepDiveClient />);

      const financialContent = screen.getByTestId('tabs-content-financial');
      expect(financialContent).toBeDefined();
      expect(financialContent.className).toContain('space-y-4');
      expect(financialContent.className).toContain('pt-4');
    });

    it('should render marketing tab content', () => {
      render(<BrandDeepDiveClient />);

      const marketingContent = screen.getByTestId('tabs-content-marketing');
      expect(marketingContent).toBeDefined();
      expect(marketingContent.className).toContain('space-y-4');
      expect(marketingContent.className).toContain('pt-4');
    });
  });

  describe('Financial Tab Content', () => {
    beforeEach(() => {
      mockBrandDeepDiveState.selectedBrand = 'Test Brand';
    });

    it('should render brand deep dive KPI cards', () => {
      render(<BrandDeepDiveClient />);

      expect(screen.getByTestId('brand-deep-dive-kpi-cards')).toBeDefined();
      expect(screen.getByText('Brand KPI Cards')).toBeDefined();
    });

    it('should render financial performance trends card', () => {
      render(<BrandDeepDiveClient />);

      expect(screen.getByTestId('card-title')).toBeDefined();
      expect(screen.getByText('Financial Performance Trends')).toBeDefined();
      expect(screen.getByText('Key financial metrics over time for Test Brand')).toBeDefined();
    });

    it('should render brand deep dive charts', () => {
      render(<BrandDeepDiveClient />);

      expect(screen.getByTestId('brand-deep-dive-charts')).toBeDefined();
      expect(screen.getByText('Brand Charts')).toBeDefined();
    });

    it('should include selected brand name in chart description', () => {
      mockBrandDeepDiveState.selectedBrand = 'Nike';
      
      render(<BrandDeepDiveClient />);

      expect(screen.getByText('Key financial metrics over time for Nike')).toBeDefined();
    });
  });

  describe('Marketing Tab Content', () => {
    beforeEach(() => {
      mockBrandDeepDiveState.selectedBrand = 'Test Brand';
    });

    it('should render marketing KPI cards', () => {
      render(<BrandDeepDiveClient />);

      expect(screen.getByTestId('brand-deep-dive-marketing-kpi-cards')).toBeDefined();
      expect(screen.getByText('Marketing KPI Cards')).toBeDefined();
    });
  });

  describe('Loading States', () => {
    it('should handle loading state', () => {
      mockBrandDeepDiveState.isLoading = true;
      mockBrandDeepDiveState.selectedBrand = 'Test Brand';
      
      render(<BrandDeepDiveClient />);

      // Component should still render but may show loading indicators in child components
      expect(screen.getByTestId('brand-deep-dive-provider')).toBeDefined();
    });
  });

  describe('Error States', () => {
    it('should handle error state', () => {
      mockBrandDeepDiveState.error = 'Failed to load brand data';
      mockBrandDeepDiveState.selectedBrand = 'Test Brand';
      
      render(<BrandDeepDiveClient />);

      // Component should still render but may show error indicators in child components
      expect(screen.getByTestId('brand-deep-dive-provider')).toBeDefined();
    });
  });

  describe('Context Integration', () => {
    it('should provide brand deep dive context to child components', () => {
      render(<BrandDeepDiveClient />);

      expect(screen.getByTestId('brand-deep-dive-provider')).toBeDefined();
    });

    it('should provide filter context to child components', () => {
      render(<BrandDeepDiveClient />);

      expect(screen.getByTestId('filter-provider')).toBeDefined();
    });

    it('should nest contexts in correct order', () => {
      render(<BrandDeepDiveClient />);

      const brandProvider = screen.getByTestId('brand-deep-dive-provider');
      const filterProvider = screen.getByTestId('filter-provider');
      
      expect(brandProvider).toBeDefined();
      expect(filterProvider).toBeDefined();
      
      // Filter provider should be nested inside brand provider
      expect(brandProvider.contains(filterProvider)).toBe(true);
    });
  });

  describe('Responsive Design', () => {
    it('should render with full width classes', () => {
      mockBrandDeepDiveState.selectedBrand = 'Test Brand';
      
      render(<BrandDeepDiveClient />);

      const tabs = screen.getByTestId('tabs');
      expect(tabs.className).toContain('w-full');
      
      const tabsList = screen.getByTestId('tabs-list');
      expect(tabsList.className).toContain('w-full');
    });

    it('should use grid layout for tabs', () => {
      mockBrandDeepDiveState.selectedBrand = 'Test Brand';
      
      render(<BrandDeepDiveClient />);

      const tabsList = screen.getByTestId('tabs-list');
      expect(tabsList.className).toContain('grid');
      expect(tabsList.className).toContain('grid-cols-2');
    });
  });

  describe('Accessibility', () => {
    it('should render semantic HTML structure', () => {
      render(<BrandDeepDiveClient />);

      expect(screen.getByRole('heading', { level: 1 })).toBeDefined();
    });

    it('should have proper tab structure when brand is selected', () => {
      mockBrandDeepDiveState.selectedBrand = 'Test Brand';
      
      render(<BrandDeepDiveClient />);

      const financialTab = screen.getByTestId('tabs-trigger-financial');
      const marketingTab = screen.getByTestId('tabs-trigger-marketing');
      
      expect(financialTab.getAttribute('data-value')).toBe('financial');
      expect(marketingTab.getAttribute('data-value')).toBe('marketing');
    });

    it('should provide meaningful empty state message', () => {
      mockBrandDeepDiveState.selectedBrand = null;
      
      render(<BrandDeepDiveClient />);

      const heading = screen.getByText('No Brand Selected');
      const description = screen.getByText(/Please select a brand from the dropdown/);
      
      expect(heading).toBeDefined();
      expect(description).toBeDefined();
    });
  });

  describe('Performance', () => {
    it('should render efficiently with multiple child components', () => {
      mockBrandDeepDiveState.selectedBrand = 'Test Brand';
      
      const start = performance.now();
      render(<BrandDeepDiveClient />);
      const end = performance.now();

      expect(end - start).toBeLessThan(100);
      expect(screen.getByTestId('brand-deep-dive-provider')).toBeDefined();
    });

    it('should handle brand state changes efficiently', () => {
      const { rerender } = render(<BrandDeepDiveClient />);

      const start = performance.now();
      
      // Simulate brand selection
      mockBrandDeepDiveState.selectedBrand = 'Nike';
      rerender(<BrandDeepDiveClient />);
      
      // Simulate brand change
      mockBrandDeepDiveState.selectedBrand = 'Adidas';
      rerender(<BrandDeepDiveClient />);
      
      const end = performance.now();
      expect(end - start).toBeLessThan(100);
    });
  });
});