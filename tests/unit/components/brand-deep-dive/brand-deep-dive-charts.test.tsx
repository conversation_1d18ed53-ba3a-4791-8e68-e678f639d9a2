/**
 * @fileoverview Unit tests for components/brand-deep-dive/brand-deep-dive-charts.tsx
 * Tests brand deep dive charts component functionality, data visualization, and user interactions
 * Coverage target: 75%+ (Tier 2)
 */

import { fireEvent, render, screen, waitFor } from '@testing-library/react';

import { BrandDeepDiveCharts } from '@/components/brand-deep-dive/brand-deep-dive-charts';
import React from 'react';
import { jest } from '@jest/globals';

// Mock the context
const mockBrandDeepDiveState = {
  selectedBrand: 'Test Brand' as string | null,
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  currency: 'CAD' as 'CAD' | 'USD',
  groupBy: 'day' as 'day' | 'week' | 'month',
  salesChannels: [],
  countryNames: [],
  isLoading: false,
  error: null as string | null,
};

jest.mock('@/lib/contexts/brand-deep-dive-context', () => ({
  useBrandDeepDive: () => ({
    state: mockBrandDeepDiveState,
  }),
}));

// Mock the API client
const mockKpiData = {
  'Gross Revenue': {
    summary: { value: 100000, percentChange: 5.5 },
    timeSeries: [
      { date: '2024-01-01', value: 5000 },
      { date: '2024-01-02', value: 7500 },
      { date: '2024-01-03', value: 6000 },
    ],
  },
  'Net Revenue': {
    summary: { value: 85000, percentChange: 3.2 },
    timeSeries: [
      { date: '2024-01-01', value: 4250 },
      { date: '2024-01-02', value: 6375 },
      { date: '2024-01-03', value: 5100 },
    ],
  },
  'Discount': {
    summary: { value: 10000, percentChange: -2.1 },
    timeSeries: [
      { date: '2024-01-01', value: 500 },
      { date: '2024-01-02', value: 750 },
      { date: '2024-01-03', value: 600 },
    ],
  },
  '% Discount': {
    summary: { value: 0.1, percentChange: -1.5 },
    timeSeries: [
      { date: '2024-01-01', value: 0.1 },
      { date: '2024-01-02', value: 0.1 },
      { date: '2024-01-03', value: 0.1 },
    ],
  },
};

const mockFetchKpiData = jest.fn().mockResolvedValue(mockKpiData);

jest.mock('@/lib/api/dashboard-client', () => ({
  fetchKpiData: mockFetchKpiData,
}));

// Mock chart utils
jest.mock('@/lib/chart-utils', () => {
  const mockJest = require('@jest/globals').jest;
  return {
    CHART_COLORS: {
      purple: '#8b5cf6',
      red: '#ef4444',
      pink: '#ec4899',
      green: '#10b981',
      blue: '#3b82f6',
      cyan: '#06b6d4',
      indigo: '#6366f1',
      orange: '#f97316',
      yellow: '#eab308',
    },
    formatCurrency: mockJest.fn((value: number | null, currency: string) => {
      if (value === null || value === undefined) return 'N/A';
      const symbol = currency === 'USD' ? '$' : 'CA$';
      return `${symbol}${new Intl.NumberFormat('en-CA').format(value)}`;
    }),
    formatDate: mockJest.fn((date: string) => new Date(date).toLocaleDateString('en-CA')),
    formatPercent: mockJest.fn((value: number) => `${(value * 100).toFixed(1)}%`),
    formatRawPercent: mockJest.fn((value: number) => `${value.toFixed(1)}%`),
  };
});

// Mock recharts
jest.mock('recharts', () => ({
  BarChart: ({ children }: { children: React.ReactNode }) => <div data-testid="bar-chart">{children}</div>,
  Bar: ({ dataKey }: { dataKey: string }) => <div data-testid={`bar-${dataKey}`} />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Legend: () => <div data-testid="legend" />,
  Tooltip: () => <div data-testid="tooltip" />,
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => <div data-testid="responsive-container">{children}</div>,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
}));

// Mock UI components
jest.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>{children}</div>
  ),
  CardContent: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card-content" className={className}>{children}</div>
  ),
  CardHeader: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card-header" className={className}>{children}</div>
  ),
  CardTitle: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <h3 data-testid="card-title" className={className}>{children}</h3>
  ),
}));

jest.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, defaultValue, className }: { children: React.ReactNode; defaultValue: string; className?: string }) => (
    <div data-testid="tabs" data-default-value={defaultValue} className={className}>{children}</div>
  ),
  TabsList: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="tabs-list" className={className}>{children}</div>
  ),
  TabsTrigger: ({ children, value }: { children: React.ReactNode; value: string }) => (
    <button data-testid={`tabs-trigger-${value}`} data-value={value}>{children}</button>
  ),
  TabsContent: ({ children, value, className }: { children: React.ReactNode; value: string; className?: string }) => (
    <div data-testid={`tabs-content-${value}`} className={className}>{children}</div>
  ),
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, variant, size, className }: { 
    children: React.ReactNode; 
    onClick?: () => void; 
    variant?: string; 
    size?: string; 
    className?: string; 
  }) => (
    <button data-testid="button" onClick={onClick} data-variant={variant} data-size={size} className={className}>
      {children}
    </button>
  ),
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, variant, className }: { children: React.ReactNode; variant?: string; className?: string }) => (
    <span data-testid="badge" data-variant={variant} className={className}>{children}</span>
  ),
}));

jest.mock('@/components/ui/skeleton', () => ({
  Skeleton: ({ className }: { className?: string }) => (
    <div data-testid="skeleton" className={className}>Loading...</div>
  ),
}));

jest.mock('@/components/ui/tooltip', () => ({
  TooltipProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="tooltip-provider">{children}</div>,
  Tooltip: ({ children }: { children: React.ReactNode }) => <div data-testid="tooltip-wrapper">{children}</div>,
  TooltipTrigger: ({ children }: { children: React.ReactNode }) => <div data-testid="tooltip-trigger">{children}</div>,
  TooltipContent: ({ children }: { children: React.ReactNode }) => <div data-testid="tooltip-content">{children}</div>,
}));

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  BarChart3: ({ className }: { className?: string }) => <div data-testid="bar-chart3-icon" className={className} />,
  DollarSign: ({ className }: { className?: string }) => <div data-testid="dollar-sign-icon" className={className} />,
  HelpCircle: ({ className }: { className?: string }) => <div data-testid="help-circle-icon" className={className} />,
  Percent: ({ className }: { className?: string }) => <div data-testid="percent-icon" className={className} />,
  TrendingUp: ({ className }: { className?: string }) => <div data-testid="trending-up-icon" className={className} />,
}));

describe('components/brand-deep-dive/brand-deep-dive-charts', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mock state
    mockBrandDeepDiveState.selectedBrand = 'Test Brand';
    mockBrandDeepDiveState.isLoading = false;
    mockBrandDeepDiveState.error = null;
  });

  describe('Component Rendering', () => {
    it('should render the main charts component', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByText('Financial Performance Trends')).toBeDefined();
        expect(screen.getByText('Comprehensive KPI analysis with smart time grouping for Test Brand')).toBeDefined();
      });
    });

    it('should render tabs for different KPI groups', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByTestId('tabs')).toBeDefined();
        expect(screen.getByTestId('tabs').getAttribute('data-default-value')).toBe('revenue');
        expect(screen.getByTestId('tabs-list')).toBeDefined();
      });
    });

    it('should render all KPI group tabs', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByTestId('tabs-trigger-revenue')).toBeDefined();
        expect(screen.getByTestId('tabs-trigger-margins')).toBeDefined();
        expect(screen.getByTestId('tabs-trigger-costs')).toBeDefined();
        expect(screen.getByTestId('tabs-trigger-advertising')).toBeDefined();
        expect(screen.getByTestId('tabs-trigger-traffic')).toBeDefined();
      });
    });

    it('should render smart analytics indicator', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByTestId('bar-chart3-icon')).toBeDefined();
        expect(screen.getByText('Smart Analytics')).toBeDefined();
      });
    });
  });

  describe('No Brand Selected State', () => {
    it('should show message when no brand is selected', () => {
      mockBrandDeepDiveState.selectedBrand = null;
      
      render(<BrandDeepDiveCharts />);

      expect(screen.getByText('Please select a brand to view detailed performance charts.')).toBeDefined();
    });

    it('should render card with proper styling for no brand state', () => {
      mockBrandDeepDiveState.selectedBrand = null;
      
      render(<BrandDeepDiveCharts />);

      const card = screen.getByTestId('card');
      expect(card.className).toContain('w-full');
    });
  });

  describe('Error State', () => {
    it('should display error message when data fetch fails', async () => {
      mockFetchKpiData.mockRejectedValueOnce(new Error('API Error'));

      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByText('Failed to load chart data')).toBeDefined();
      });
    });

    it('should render error card with proper styling', async () => {
      mockFetchKpiData.mockRejectedValueOnce(new Error('API Error'));

      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        const card = screen.getByTestId('card');
        expect(card.className).toContain('w-full');
      });
    });
  });

  describe('Loading State', () => {
    it('should show skeleton while loading', async () => {
      mockFetchKpiData.mockImplementation(() => new Promise(() => {})); // Never resolves

      render(<BrandDeepDiveCharts />);

      // Should show skeleton initially
      expect(screen.getByTestId('skeleton')).toBeDefined();
    });
  });

  describe('Chart Rendering', () => {
    it('should render charts with data', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByTestId('responsive-container')).toBeDefined();
        expect(screen.getByTestId('bar-chart')).toBeDefined();
        expect(screen.getByTestId('cartesian-grid')).toBeDefined();
        expect(screen.getByTestId('x-axis')).toBeDefined();
        expect(screen.getByTestId('y-axis')).toBeDefined();
        expect(screen.getByTestId('tooltip')).toBeDefined();
        expect(screen.getByTestId('legend')).toBeDefined();
      });
    });

    it('should render bars for available KPIs', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByTestId('bar-Gross Revenue')).toBeDefined();
        expect(screen.getByTestId('bar-Net Revenue')).toBeDefined();
        expect(screen.getByTestId('bar-Discount')).toBeDefined();
      });
    });

    it('should show KPI count badge', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        const badges = screen.getAllByTestId('badge');
        const kpiBadge = badges.find(badge => badge.textContent?.includes('KPIs'));
        expect(kpiBadge).toBeDefined();
      });
    });

    it('should show grouping badge', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        const badges = screen.getAllByTestId('badge');
        const groupingBadge = badges.find(badge => badge.textContent === 'Daily');
        expect(groupingBadge).toBeDefined();
      });
    });
  });

  describe('Display Mode Toggle', () => {
    it('should render value and percentage toggle buttons for revenue group', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        const buttons = screen.getAllByTestId('button');
        const valueButton = buttons.find(btn => btn.textContent?.includes('Values'));
        const percentButton = buttons.find(btn => btn.textContent?.includes('Percentages'));
        
        expect(valueButton).toBeDefined();
        expect(percentButton).toBeDefined();
      });
    });

    it('should toggle display mode when percentage button is clicked', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        const buttons = screen.getAllByTestId('button');
        const percentButton = buttons.find(btn => btn.textContent?.includes('Percentages'));
        
        if (percentButton) {
          fireEvent.click(percentButton);
          // After clicking, should show percentage KPIs
          expect(screen.getByTestId('bar-% Discount')).toBeDefined();
        }
      });
    });

    it('should show correct button variants based on display mode', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        const buttons = screen.getAllByTestId('button');
        const valueButton = buttons.find(btn => btn.textContent?.includes('Values'));
        
        // Initially value mode should be active
        expect(valueButton?.getAttribute('data-variant')).toBe('default');
      });
    });
  });

  describe('Tooltip Information', () => {
    it('should render help circle icon for tooltip trigger', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByTestId('help-circle-icon')).toBeDefined();
        expect(screen.getByTestId('tooltip-trigger')).toBeDefined();
      });
    });

    it('should render tooltip content with KPI information', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByTestId('tooltip-content')).toBeDefined();
      });
    });
  });

  describe('Data Processing', () => {
    it('should handle grouped response data correctly', async () => {
      const groupedData = {
        'Gross Revenue': {
          'Brand A': mockKpiData['Gross Revenue'],
        },
      };
      
      mockFetchKpiData.mockResolvedValueOnce(groupedData);

      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByTestId('bar-chart')).toBeDefined();
      });
    });

    it('should handle empty data gracefully', async () => {
      mockFetchKpiData.mockResolvedValueOnce({});

      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByText('No data available for revenue analysis metrics.')).toBeDefined();
      });
    });

    it('should sort chart data by date', async () => {
      const unsortedData = {
        'Gross Revenue': {
          summary: { value: 100000, percentChange: 5.5 },
          timeSeries: [
            { date: '2024-01-03', value: 6000 },
            { date: '2024-01-01', value: 5000 },
            { date: '2024-01-02', value: 7500 },
          ],
        },
      };
      
      mockFetchKpiData.mockResolvedValueOnce(unsortedData);

      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByTestId('bar-chart')).toBeDefined();
      });
    });
  });

  describe('Currency Formatting', () => {
    it('should format currency values correctly for CAD', async () => {
      mockBrandDeepDiveState.currency = 'CAD';
      
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByTestId('bar-chart')).toBeDefined();
      });
    });

    it('should format currency values correctly for USD', async () => {
      mockBrandDeepDiveState.currency = 'USD';
      
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByTestId('bar-chart')).toBeDefined();
      });
    });
  });

  describe('Responsive Design', () => {
    it('should render with proper responsive classes', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        const mainDiv = screen.getByText('Financial Performance Trends').closest('div');
        expect(mainDiv?.className).toContain('w-full');
        expect(mainDiv?.className).toContain('space-y-6');
      });
    });

    it('should render tabs with grid layout', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        const tabsList = screen.getByTestId('tabs-list');
        expect(tabsList.className).toContain('grid');
        expect(tabsList.className).toContain('grid-cols-5');
      });
    });

    it('should render chart with proper height', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        const cardContent = screen.getByTestId('card-content');
        expect(cardContent.className).toContain('h-[600px]');
      });
    });
  });

  describe('Accessibility', () => {
    it('should render semantic headings', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByRole('heading', { level: 2 })).toBeDefined();
      });
    });

    it('should provide screen reader text for help button', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        expect(screen.getByText('KPI Definitions')).toBeDefined();
      });
    });

    it('should render buttons with proper accessibility attributes', async () => {
      render(<BrandDeepDiveCharts />);

      await waitFor(() => {
        const buttons = screen.getAllByTestId('button');
        buttons.forEach(button => {
          expect(button.tagName).toBe('BUTTON');
        });
      });
    });
  });

  describe('Performance', () => {
    it('should render efficiently with large datasets', async () => {
      const largeDataset = {
        'Gross Revenue': {
          summary: { value: 100000, percentChange: 5.5 },
          timeSeries: Array.from({ length: 365 }, (_, i) => ({
            date: new Date(2024, 0, i + 1).toISOString().split('T')[0],
            value: Math.random() * 10000,
          })),
        },
      };
      
      mockFetchKpiData.mockResolvedValueOnce(largeDataset);

      const start = performance.now();
      render(<BrandDeepDiveCharts />);
      
      await waitFor(() => {
        expect(screen.getByTestId('bar-chart')).toBeDefined();
      });
      
      const end = performance.now();
      expect(end - start).toBeLessThan(1000);
    });

    it('should handle rapid state changes efficiently', async () => {
      const { rerender } = render(<BrandDeepDiveCharts />);

      const start = performance.now();
      
      // Simulate multiple state changes
      mockBrandDeepDiveState.currency = 'USD';
      rerender(<BrandDeepDiveCharts />);
      
      mockBrandDeepDiveState.groupBy = 'week';
      rerender(<BrandDeepDiveCharts />);
      
      const end = performance.now();
      expect(end - start).toBeLessThan(100);
    });
  });
});
