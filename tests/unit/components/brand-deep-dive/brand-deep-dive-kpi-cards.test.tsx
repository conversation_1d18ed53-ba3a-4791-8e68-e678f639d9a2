/**
 * @fileoverview Unit tests for components/brand-deep-dive/brand-deep-dive-kpi-cards.tsx
 * Tests brand deep dive KPI cards wrapper component functionality
 * Coverage target: 75%+ (Tier 2)
 */

import { render, screen } from '@testing-library/react';

import { BrandDeepDiveKpiCards } from '@/components/brand-deep-dive/brand-deep-dive-kpi-cards';
import React from 'react';
import { jest } from '@jest/globals';

// Mock the EcommerceKpiCards component
jest.mock('@/components/ecommerce-kpi-cards', () => ({
  EcommerceKpiCards: ({ contextType }: { contextType: string }) => (
    <div data-testid="ecommerce-kpi-cards" data-context-type={contextType}>
      Ecommerce KPI Cards Component
    </div>
  ),
}));

describe('components/brand-deep-dive/brand-deep-dive-kpi-cards', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render the wrapper component', () => {
      render(<BrandDeepDiveKpiCards />);

      expect(screen.getByTestId('ecommerce-kpi-cards')).toBeDefined();
      expect(screen.getByText('Ecommerce KPI Cards Component')).toBeDefined();
    });

    it('should pass correct contextType to EcommerceKpiCards', () => {
      render(<BrandDeepDiveKpiCards />);

      const kpiCardsComponent = screen.getByTestId('ecommerce-kpi-cards');
      expect(kpiCardsComponent.getAttribute('data-context-type')).toBe('brandDeepDive');
    });
  });

  describe('Component Integration', () => {
    it('should render without crashing', () => {
      expect(() => {
        render(<BrandDeepDiveKpiCards />);
      }).not.toThrow();
    });

    it('should be a pure wrapper component', () => {
      const { container } = render(<BrandDeepDiveKpiCards />);
      
      // Should only contain the wrapped component
      expect(container.firstChild).toBe(screen.getByTestId('ecommerce-kpi-cards'));
    });
  });

  describe('Props Passing', () => {
    it('should pass brandDeepDive as contextType', () => {
      render(<BrandDeepDiveKpiCards />);

      const kpiCardsComponent = screen.getByTestId('ecommerce-kpi-cards');
      expect(kpiCardsComponent.getAttribute('data-context-type')).toBe('brandDeepDive');
    });
  });

  describe('Performance', () => {
    it('should render efficiently', () => {
      const start = performance.now();
      render(<BrandDeepDiveKpiCards />);
      const end = performance.now();

      expect(end - start).toBeLessThan(10);
      expect(screen.getByTestId('ecommerce-kpi-cards')).toBeDefined();
    });

    it('should handle multiple renders efficiently', () => {
      const { rerender } = render(<BrandDeepDiveKpiCards />);

      const start = performance.now();
      
      for (let i = 0; i < 5; i++) {
        rerender(<BrandDeepDiveKpiCards />);
      }
      
      const end = performance.now();
      expect(end - start).toBeLessThan(50);
    });
  });

  describe('Component Structure', () => {
    it('should maintain component hierarchy', () => {
      const { container } = render(<BrandDeepDiveKpiCards />);
      
      // Should have a single child element
      expect(container.children).toHaveLength(1);
      expect(container.firstChild).toBe(screen.getByTestId('ecommerce-kpi-cards'));
    });

    it('should not add any wrapper elements', () => {
      const { container } = render(<BrandDeepDiveKpiCards />);
      
      // The component should directly render the EcommerceKpiCards without additional wrappers
      expect(container.firstChild?.nodeName).toBe('DIV');
      expect((container.firstChild as Element)?.getAttribute('data-testid')).toBe('ecommerce-kpi-cards');
    });
  });

  describe('Accessibility', () => {
    it('should maintain accessibility of wrapped component', () => {
      render(<BrandDeepDiveKpiCards />);

      // The wrapped component should be accessible
      const kpiCardsComponent = screen.getByTestId('ecommerce-kpi-cards');
      expect(kpiCardsComponent).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle rendering errors gracefully', () => {
      // Mock console.error to avoid noise in test output
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      expect(() => {
        render(<BrandDeepDiveKpiCards />);
      }).not.toThrow();

      consoleSpy.mockRestore();
    });
  });

  describe('Component Contract', () => {
    it('should export the component correctly', () => {
      expect(BrandDeepDiveKpiCards).toBeDefined();
      expect(typeof BrandDeepDiveKpiCards).toBe('function');
    });

    it('should be a React functional component', () => {
      const component = <BrandDeepDiveKpiCards />;
      expect(React.isValidElement(component)).toBe(true);
    });
  });
});