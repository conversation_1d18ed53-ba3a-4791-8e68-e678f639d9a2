/**
 * @fileoverview Unit tests for components/ecommerce-charts/utils.ts
 * Tests ecommerce chart utility functions including formatters and compact formatters
 * Coverage target: 90%+
 */

import { createCompactFormatter, createFormatters } from '@/components/ecommerce-charts/utils';

// Mock the chart-utils module
jest.mock('@/lib/chart-utils', () => ({
  formatCurrency: jest.fn((value: number | null, currency: string) => {
    if (value === null || value === undefined) return 'N/A';
    const symbol = currency === 'USD' ? '$' : currency === 'CAD' ? '$' : '£';
    return `${symbol}${value.toLocaleString(undefined, { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2
    })}`;
  }),
  formatDate: jest.fn((dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    return date.toLocaleDateString();
  }),
  formatPercent: jest.fn((value: number | null | undefined) => {
    if (value === null || value === undefined) return 'N/A';
    return `${(value * 100).toFixed(2)}%`;
  }),
}));

describe('components/ecommerce-charts/utils', () => {
  describe('createFormatters', () => {
    it('should create formatters with CAD currency', () => {
      const formatters = createFormatters('CAD');
      
      expect(formatters).toHaveProperty('formatCurrency');
      expect(formatters).toHaveProperty('formatPercent');
      expect(formatters).toHaveProperty('rechartsDateFormatter');
      expect(formatters).toHaveProperty('rechartsLabelFormatter');
      expect(formatters).toHaveProperty('customTooltipFormatter');
      
      expect(typeof formatters.formatCurrency).toBe('function');
      expect(typeof formatters.formatPercent).toBe('function');
      expect(typeof formatters.rechartsDateFormatter).toBe('function');
      expect(typeof formatters.rechartsLabelFormatter).toBe('function');
      expect(typeof formatters.customTooltipFormatter).toBe('function');
    });

    it('should create formatters with USD currency', () => {
      const formatters = createFormatters('USD');
      
      expect(formatters.formatCurrency(1234.56)).toBe('$1,234.56');
    });

    it('should create formatters with GBP currency', () => {
      const formatters = createFormatters('GBP');
      
      expect(formatters.formatCurrency(1234.56)).toBe('£1,234.56');
    });

    describe('formatCurrency function', () => {
      it('should format currency values correctly', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.formatCurrency(1234.56)).toBe('$1,234.56');
        expect(formatters.formatCurrency(0)).toBe('$0.00');
        expect(formatters.formatCurrency(-500)).toBe('$-500.00');
      });

      it('should handle null values', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.formatCurrency(null)).toBe('N/A');
      });
    });

    describe('formatPercent function', () => {
      it('should format percentage values correctly', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.formatPercent(0.1234)).toBe('12.34%');
        expect(formatters.formatPercent(0.5)).toBe('50.00%');
        expect(formatters.formatPercent(1.0)).toBe('100.00%');
      });

      it('should handle null values', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.formatPercent(null)).toBe('N/A');
      });
    });

    describe('rechartsDateFormatter function', () => {
      it('should format date strings correctly', () => {
        const formatters = createFormatters('CAD');
        
        const result = formatters.rechartsDateFormatter('2024-01-15');
        expect(result).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
      });

      it('should handle invalid dates', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.rechartsDateFormatter('invalid-date')).toBe('invalid-date');
      });

      it('should handle empty strings', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.rechartsDateFormatter('')).toBe('N/A');
      });
    });

    describe('rechartsLabelFormatter function', () => {
      it('should format label strings correctly', () => {
        const formatters = createFormatters('CAD');
        
        const result = formatters.rechartsLabelFormatter('2024-01-15');
        expect(result).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
      });

      it('should handle invalid labels', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.rechartsLabelFormatter('invalid-date')).toBe('invalid-date');
      });
    });

    describe('customTooltipFormatter function', () => {
      it('should format percentage values for percentage fields', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.customTooltipFormatter(0.1234, '% Conversion Rate')).toBe('12.34%');
        expect(formatters.customTooltipFormatter(0.5, '% Growth')).toBe('50.00%');
      });

      it('should format currency values for non-percentage fields', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.customTooltipFormatter(1234.56, 'Revenue')).toBe('$1,234.56');
        expect(formatters.customTooltipFormatter(500, 'Cost')).toBe('$500.00');
      });

      it('should handle non-numeric values', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.customTooltipFormatter('not a number', 'Revenue')).toBe('N/A');
        expect(formatters.customTooltipFormatter(null, 'Revenue')).toBe('N/A');
        expect(formatters.customTooltipFormatter(undefined, 'Revenue')).toBe('N/A');
        expect(formatters.customTooltipFormatter({}, 'Revenue')).toBe('N/A');
        expect(formatters.customTooltipFormatter([], 'Revenue')).toBe('N/A');
      });

      it('should handle edge cases for field names', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.customTooltipFormatter(100, '%')).toBe('$100.00');
        expect(formatters.customTooltipFormatter(100, '% ')).toBe('10000.00%');
        expect(formatters.customTooltipFormatter(100, 'Percent%')).toBe('$100.00');
      });

      it('should handle zero values correctly', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.customTooltipFormatter(0, '% Rate')).toBe('0.00%');
        expect(formatters.customTooltipFormatter(0, 'Revenue')).toBe('$0.00');
      });

      it('should handle negative values correctly', () => {
        const formatters = createFormatters('CAD');
        
        expect(formatters.customTooltipFormatter(-0.1, '% Rate')).toBe('-10.00%');
        expect(formatters.customTooltipFormatter(-100, 'Loss')).toBe('$-100.00');
      });
    });

    describe('formatter consistency', () => {
      it('should maintain currency consistency across all formatters', () => {
        const usdFormatters = createFormatters('USD');
        const cadFormatters = createFormatters('CAD');
        const gbpFormatters = createFormatters('GBP');
        
        expect(usdFormatters.formatCurrency(100)).toContain('$');
        expect(cadFormatters.formatCurrency(100)).toContain('$');
        expect(gbpFormatters.formatCurrency(100)).toContain('£');
      });

      it('should create independent formatter instances', () => {
        const formatters1 = createFormatters('USD');
        const formatters2 = createFormatters('CAD');
        
        expect(formatters1).not.toBe(formatters2);
        expect(formatters1.formatCurrency).not.toBe(formatters2.formatCurrency);
      });
    });
  });

  describe('createCompactFormatter', () => {
    describe('percentage formatter', () => {
      it('should format percentage values correctly', () => {
        const formatter = createCompactFormatter(true);
        
        expect(formatter(0.1234)).toBe('12%');
        expect(formatter(0.5)).toBe('50%');
        expect(formatter(1.0)).toBe('100%');
        expect(formatter(1.5)).toBe('150%');
      });

      it('should handle zero percentage', () => {
        const formatter = createCompactFormatter(true);
        
        expect(formatter(0)).toBe('0%');
      });

      it('should handle negative percentages', () => {
        const formatter = createCompactFormatter(true);
        
        expect(formatter(-0.1)).toBe('-10%');
      });

      it('should use Canadian locale formatting', () => {
        const formatter = createCompactFormatter(true);
        
        // The formatter uses en-CA locale
        const result = formatter(0.1234);
        expect(result).toMatch(/\d+%/);
      });

      it('should format without decimal places', () => {
        const formatter = createCompactFormatter(true);
        
        expect(formatter(0.1234)).toBe('12%');
        expect(formatter(0.1289)).toBe('13%');
      });
    });

    describe('compact number formatter', () => {
      it('should format large numbers with compact notation', () => {
        const formatter = createCompactFormatter(false);
        
        expect(formatter(1234567)).toBe('1.2M');
        expect(formatter(1234)).toBe('1.2K');
        expect(formatter(1234567890)).toBe('1.2B');
      });

      it('should format small numbers without compact notation', () => {
        const formatter = createCompactFormatter(false);
        
        expect(formatter(123)).toBe('123');
        expect(formatter(999)).toBe('999');
      });

      it('should handle zero', () => {
        const formatter = createCompactFormatter(false);
        
        expect(formatter(0)).toBe('0');
      });

      it('should handle negative numbers', () => {
        const formatter = createCompactFormatter(false);
        
        expect(formatter(-1234567)).toBe('-1.2M');
        expect(formatter(-123)).toBe('-123');
      });

      it('should use Canadian locale formatting', () => {
        const formatter = createCompactFormatter(false);
        
        // The formatter uses en-CA locale with compact display
        const result = formatter(1234567);
        expect(result).toMatch(/\d+\.?\d*[KMB]?/);
      });

      it('should handle decimal numbers', () => {
        const formatter = createCompactFormatter(false);
        
        expect(formatter(1234.56)).toBe('1.2K');
        expect(formatter(1234567.89)).toBe('1.2M');
      });
    });

    describe('formatter independence', () => {
      it('should create independent formatters', () => {
        const percentFormatter = createCompactFormatter(true);
        const numberFormatter = createCompactFormatter(false);
        
        expect(percentFormatter).not.toBe(numberFormatter);
        
        // Same input should produce different outputs
        expect(percentFormatter(0.5)).toBe('50%');
        expect(numberFormatter(0.5)).toBe('0.5');
      });

      it('should handle the same values differently based on type', () => {
        const percentFormatter = createCompactFormatter(true);
        const numberFormatter = createCompactFormatter(false);
        
        expect(percentFormatter(1)).toBe('100%');
        expect(numberFormatter(1)).toBe('1');
        
        expect(percentFormatter(1000)).toBe('100,000%');
        expect(numberFormatter(1000)).toBe('1K');
      });
    });

    describe('edge cases', () => {
      it('should handle very large numbers', () => {
        const formatter = createCompactFormatter(false);
        
        expect(formatter(1e12)).toMatch(/\d+\.?\d*T/);
      });

      it('should handle very small decimal numbers', () => {
        const percentFormatter = createCompactFormatter(true);
        const numberFormatter = createCompactFormatter(false);
        
        expect(percentFormatter(0.001)).toBe('0%');
        expect(numberFormatter(0.001)).toBe('0.001');
      });

      it('should handle Infinity and NaN', () => {
        const formatter = createCompactFormatter(false);
        
        expect(formatter(Infinity)).toBe('∞');
        expect(formatter(-Infinity)).toBe('-∞');
        expect(formatter(NaN)).toBe('NaN');
      });
    });
  });

  describe('Performance tests', () => {
    it('should create formatters efficiently', () => {
      const start = performance.now();
      
      for (let i = 0; i < 1000; i++) {
        createFormatters('CAD');
        createCompactFormatter(true);
        createCompactFormatter(false);
      }
      
      const end = performance.now();
      expect(end - start).toBeLessThan(100);
    });

    it('should format values efficiently', () => {
      const formatters = createFormatters('CAD');
      const compactFormatter = createCompactFormatter(false);
      
      const start = performance.now();
      
      for (let i = 0; i < 1000; i++) {
        formatters.formatCurrency(Math.random() * 1000000);
        formatters.customTooltipFormatter(Math.random(), 'Revenue');
        compactFormatter(Math.random() * 1000000);
      }
      
      const end = performance.now();
      expect(end - start).toBeLessThan(200);
    });
  });
});