/**
 * @fileoverview Unit tests for components/ecommerce-charts/use-chart-data.ts
 * Tests chart data hook functionality, KPI data processing, and error handling
 * Coverage target: 90%+
 */

import { renderHook, waitFor } from '@testing-library/react';

import { ChartFilters } from '@/components/ecommerce-charts/types';
import { fetchKpiData } from '@/lib/api/dashboard-client';
import { useChartData } from '@/components/ecommerce-charts/use-chart-data';

// Mock the dashboard client
jest.mock('@/lib/api/dashboard-client');
const mockFetchKpiData = fetchKpiData as jest.MockedFunction<typeof fetchKpiData>;

describe('components/ecommerce-charts/use-chart-data', () => {
  const defaultFilters: ChartFilters = {
    startDate: '2024-01-01',
    endDate: '2024-01-31',
    currency: 'CAD',
    brands: ['Brand A'],
    salesChannels: ['Online'],
    countryNames: ['Canada'],
  };

  const mockGetQueryParams = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock console methods to reduce test noise
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});

    // Default query params
    mockGetQueryParams.mockReturnValue('startDate=2024-01-01&endDate=2024-01-31&currency=CAD&groupByTime=day');
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Hook Initialization', () => {
    it('should initialize with loading state', () => {
      mockFetchKpiData.mockImplementation(() => new Promise(() => {})); // Never resolves

      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      expect(result.current.loading).toBe(true);
      expect(result.current.error).toBeNull();
      expect(result.current.chartData).toEqual([]);
    });

    it('should call fetchKpiData with correct parameters', async () => {
      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [
            { date: '2024-01-01', value: 1000 },
            { date: '2024-01-02', value: 1500 },
          ],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalledWith({
          startDate: '2024-01-01',
          endDate: '2024-01-31',
          currency: 'CAD',
          groupByTime: 'day',
          brands: ['Brand A'],
          salesChannels: ['Online'],
          countryNames: ['Canada'],
          kpis: [
            'Gross Revenue',
            'Net Revenue',
            'Discount',
            'Refund',
            'Gross Margin',
            '% Gross Margin',
            'Landed Cost',
            '% Landed Cost',
            'Fulfillment Cost',
            '% Fulfillment Cost',
            'Transaction Cost',
            '% Transaction Cost',
            'Adspend',
            '% Adspend',
            'Contribution Margin',
            '% Contribution Margin',
          ],
        });
      });
    });
  });

  describe('Data Fetching', () => {
    it('should handle successful simple response', async () => {
      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [
            { date: '2024-01-01', value: 1000 },
            { date: '2024-01-02', value: 1500 },
          ],
        },
        'Net Revenue': {
          summary: { value: 8000 },
          timeSeries: [
            { date: '2024-01-01', value: 800 },
            { date: '2024-01-02', value: 1200 },
          ],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBeNull();
        expect(result.current.chartData).toHaveLength(2);
      });

      expect(result.current.chartData[0]).toEqual({
        date: '2024-01-01',
        'Gross Revenue': 1000,
        'Net Revenue': 800,
      });

      expect(result.current.chartData[1]).toEqual({
        date: '2024-01-02',
        'Gross Revenue': 1500,
        'Net Revenue': 1200,
      });
    });

    it('should handle grouped response by using first dimension', async () => {
      const mockGroupedResponse = {
        'Gross Revenue': {
          'Brand A': {
            summary: { value: 5000 },
            timeSeries: [
              { date: '2024-01-01', value: 500 },
              { date: '2024-01-02', value: 750 },
            ],
          },
          'Brand B': {
            summary: { value: 3000 },
            timeSeries: [
              { date: '2024-01-01', value: 300 },
              { date: '2024-01-02', value: 450 },
            ],
          },
        },
        'Net Revenue': {
          'Brand A': {
            summary: { value: 4000 },
            timeSeries: [
              { date: '2024-01-01', value: 400 },
              { date: '2024-01-02', value: 600 },
            ],
          },
          'Brand B': {
            summary: { value: 2400 },
            timeSeries: [
              { date: '2024-01-01', value: 240 },
              { date: '2024-01-02', value: 360 },
            ],
          },
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockGroupedResponse);

      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBeNull();
        expect(result.current.chartData).toHaveLength(2);
      });

      // Should use first dimension (Brand A)
      expect(result.current.chartData[0]).toEqual({
        date: '2024-01-01',
        'Gross Revenue': 500,
        'Net Revenue': 400,
      });

      expect(console.warn).toHaveBeenCalledWith('Received grouped response, but this component only supports simple responses');
    });

    it('should handle error when grouped response has no dimensions', async () => {
      const mockGroupedResponse = {
        'Gross Revenue': {},
        'Net Revenue': {},
      };

      mockFetchKpiData.mockResolvedValueOnce(mockGroupedResponse);

      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBe('Failed to load chart data');
        expect(result.current.chartData).toEqual([]);
      });

      expect(console.error).toHaveBeenCalledWith('Failed to load chart data:', expect.any(Error));
    });

    it('should handle API errors', async () => {
      const error = new Error('API Error');
      mockFetchKpiData.mockRejectedValueOnce(error);

      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBe('Failed to load chart data');
        expect(result.current.chartData).toEqual([]);
      });

      expect(console.error).toHaveBeenCalledWith('Failed to load chart data:', error);
    });
  });

  describe('Query Parameter Handling', () => {
    it('should use groupByTime from query params when provided', async () => {
      mockGetQueryParams.mockReturnValue('startDate=2024-01-01&endDate=2024-01-31&currency=CAD&groupByTime=week');

      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalledWith(
          expect.objectContaining({
            groupByTime: 'week',
          })
        );
      });
    });

    it('should default to day grouping when not provided', async () => {
      mockGetQueryParams.mockReturnValue('startDate=2024-01-01&endDate=2024-01-31&currency=CAD');

      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalledWith(
          expect.objectContaining({
            groupByTime: 'day',
          })
        );
      });
    });

    it('should parse brands from query params', async () => {
      mockGetQueryParams.mockReturnValue('startDate=2024-01-01&endDate=2024-01-31&currency=CAD&brands=Brand%20A,Brand%20B');

      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalledWith(
          expect.objectContaining({
            brands: ['Brand A', 'Brand B'],
          })
        );
      });
    });

    it('should parse salesChannels from query params', async () => {
      mockGetQueryParams.mockReturnValue('startDate=2024-01-01&endDate=2024-01-31&currency=CAD&salesChannels=Online,Retail');

      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalledWith(
          expect.objectContaining({
            salesChannels: ['Online', 'Retail'],
          })
        );
      });
    });

    it('should parse countryNames from query params', async () => {
      mockGetQueryParams.mockReturnValue('startDate=2024-01-01&endDate=2024-01-31&currency=CAD&countryNames=Canada,USA');

      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalledWith(
          expect.objectContaining({
            countryNames: ['Canada', 'USA'],
          })
        );
      });
    });

    it('should fallback to filter values when query params are missing', async () => {
      mockGetQueryParams.mockReturnValue('');

      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalledWith(
          expect.objectContaining({
            startDate: '2024-01-01',
            endDate: '2024-01-31',
            currency: 'CAD',
            brands: ['Brand A'],
            salesChannels: ['Online'],
            countryNames: ['Canada'],
          })
        );
      });
    });
  });

  describe('Chart Data Processing', () => {
    it('should return empty array when no KPI data', async () => {
      mockFetchKpiData.mockResolvedValueOnce({});

      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.chartData).toEqual([]);
      });
    });

    it('should return empty array when first KPI has no timeSeries', async () => {
      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.chartData).toEqual([]);
      });
    });

    it('should handle null values in timeSeries', async () => {
      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [
            { date: '2024-01-01', value: 1000 },
            { date: '2024-01-02', value: null },
          ],
        },
        'Net Revenue': {
          summary: { value: 8000 },
          timeSeries: [
            { date: '2024-01-01', value: null },
            { date: '2024-01-02', value: 1200 },
          ],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.chartData).toHaveLength(2);
      });

      expect(result.current.chartData[0]).toEqual({
        date: '2024-01-01',
        'Gross Revenue': 1000,
        'Net Revenue': null,
      });

      expect(result.current.chartData[1]).toEqual({
        date: '2024-01-02',
        'Gross Revenue': null,
        'Net Revenue': 1200,
      });
    });

    it('should sort chart data by date', async () => {
      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [
            { date: '2024-01-03', value: 3000 },
            { date: '2024-01-01', value: 1000 },
            { date: '2024-01-02', value: 2000 },
          ],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.chartData).toHaveLength(3);
      });

      expect(result.current.chartData[0].date).toBe('2024-01-01');
      expect(result.current.chartData[1].date).toBe('2024-01-02');
      expect(result.current.chartData[2].date).toBe('2024-01-03');
    });

    it('should handle mismatched date ranges between KPIs', async () => {
      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [
            { date: '2024-01-01', value: 1000 },
            { date: '2024-01-02', value: 2000 },
          ],
        },
        'Net Revenue': {
          summary: { value: 8000 },
          timeSeries: [
            { date: '2024-01-01', value: 800 },
            { date: '2024-01-03', value: 1200 }, // Different date
          ],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.chartData).toHaveLength(2);
      });

      // Should only include dates from the first KPI
      expect(result.current.chartData[0]).toEqual({
        date: '2024-01-01',
        'Gross Revenue': 1000,
        'Net Revenue': 800,
      });

      expect(result.current.chartData[1]).toEqual({
        date: '2024-01-02',
        'Gross Revenue': 2000,
        'Net Revenue': undefined, // No data for this date in Net Revenue
      });
    });
  });

  describe('Hook Dependencies', () => {
    it('should refetch data when filters change', async () => {
      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      mockFetchKpiData.mockResolvedValue(mockResponse);

      const { rerender } = renderHook(
        ({ filters }) => useChartData(filters, mockGetQueryParams),
        { initialProps: { filters: defaultFilters } }
      );

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalledTimes(1);
      });

      // Change filters
      const newFilters = { ...defaultFilters, currency: 'USD' as const };
      rerender({ filters: newFilters });

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalledTimes(2);
      });
    });

    it('should refetch data when getQueryParams changes', async () => {
      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      mockFetchKpiData.mockResolvedValue(mockResponse);

      const newGetQueryParams = jest.fn().mockReturnValue('startDate=2024-02-01&endDate=2024-02-28&currency=USD');

      const { rerender } = renderHook(
        ({ getQueryParams }) => useChartData(defaultFilters, getQueryParams),
        { initialProps: { getQueryParams: mockGetQueryParams } }
      );

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalledTimes(1);
      });

      // Change getQueryParams function
      rerender({ getQueryParams: newGetQueryParams });

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty query params string', async () => {
      mockGetQueryParams.mockReturnValue('');

      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBeNull();
      });
    });

    it('should handle malformed query params', async () => {
      mockGetQueryParams.mockReturnValue('invalid=query&params');

      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.error).toBeNull();
      });
    });

    it('should handle empty brands array in query params', async () => {
      mockGetQueryParams.mockReturnValue('startDate=2024-01-01&endDate=2024-01-31&currency=CAD&brands=');

      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(mockResponse);

      renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalledWith(
          expect.objectContaining({
            brands: ['Brand A'], // Should fallback to filter value
          })
        );
      });
    });
  });

  describe('Performance Tests', () => {
    it('should handle large datasets efficiently', async () => {
      const largeMockResponse = {
        'Gross Revenue': {
          summary: { value: 100000 },
          timeSeries: Array.from({ length: 365 }, (_, i) => ({
            date: `2024-01-${String(i + 1).padStart(2, '0')}`,
            value: Math.random() * 1000,
          })),
        },
        'Net Revenue': {
          summary: { value: 80000 },
          timeSeries: Array.from({ length: 365 }, (_, i) => ({
            date: `2024-01-${String(i + 1).padStart(2, '0')}`,
            value: Math.random() * 800,
          })),
        },
      };

      mockFetchKpiData.mockResolvedValueOnce(largeMockResponse);

      const start = performance.now();
      const { result } = renderHook(() => useChartData(defaultFilters, mockGetQueryParams));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const end = performance.now();
      expect(end - start).toBeLessThan(1000);
      expect(result.current.chartData).toHaveLength(365);
    });

    it('should handle multiple rapid filter changes efficiently', async () => {
      const mockResponse = {
        'Gross Revenue': {
          summary: { value: 10000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      mockFetchKpiData.mockResolvedValue(mockResponse);

      const { rerender } = renderHook(
        ({ filters }) => useChartData(filters, mockGetQueryParams),
        { initialProps: { filters: defaultFilters } }
      );

      const start = performance.now();

      // Rapidly change filters
      for (let i = 0; i < 10; i++) {
        const newFilters = { ...defaultFilters, startDate: `2024-01-${String(i + 1).padStart(2, '0')}` };
        rerender({ filters: newFilters });
      }

      await waitFor(() => {
        expect(mockFetchKpiData).toHaveBeenCalled();
      });

      const end = performance.now();
      expect(end - start).toBeLessThan(500);
    });
  });
});