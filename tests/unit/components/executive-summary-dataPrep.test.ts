/**
 * @fileoverview Unit tests for components/executive-summary/dataPrep.ts
 * Tests data preparation functions for executive summary KPI processing
 * Coverage target: 90%+
 */

import {
  GroupedKpiResponse,
  SimpleKpiResponse,
} from '@/components/executive-summary/types';

import { prepareKpiSummaryData } from '@/components/executive-summary/dataPrep';

describe('components/executive-summary/dataPrep', () => {
  describe('prepareKpiSummaryData', () => {
    const mockSimpleKpiData: SimpleKpiResponse = {
      'Gross Revenue': { summary: { value: 100000 } },
      'Net Revenue': { summary: { value: 80000 } },
      'Gross Margin': { summary: { value: 60000 } },
      'Contribution Margin': { summary: { value: 40000 } },
      'Adspend': { summary: { value: 20000 } },
    };

    const mockYtdData: SimpleKpiResponse = {
      'Gross Revenue': { summary: { value: 500000 } },
      'Net Revenue': { summary: { value: 400000 } },
      'Gross Margin': { summary: { value: 300000 } },
      'Contribution Margin': { summary: { value: 200000 } },
      'Adspend': { summary: { value: 100000 } },
    };

    const mockLastYearData: SimpleKpiResponse = {
      'Gross Revenue': { summary: { value: 90000 } },
      'Net Revenue': { summary: { value: 72000 } },
      'Gross Margin': { summary: { value: 54000 } },
      'Contribution Margin': { summary: { value: 36000 } },
      'Adspend': { summary: { value: 18000 } },
    };

    const mockBudgetData = {
      'January-2024': {
        'Gross Revenue': 25000,
        'Net Revenue': 20000,
        'Gross Margin': 15000,
        'Contribution Margin': 10000,
        'Pay-Per-Click': 3000,
        'Content': 2000,
      },
      'February-2024': {
        'Gross Revenue': 25000,
        'Net Revenue': 20000,
        'Gross Margin': 15000,
        'Contribution Margin': 10000,
        'D2C Marketing': 2500,
        'Paid Marketing Marketplaces': 2500,
      },
    };

    const mockSelectedPeriod = {
      months: ['January', 'February'],
      year: '2024',
    };

    it('should return empty array when no kpiData provided', () => {
      const result = prepareKpiSummaryData(null, mockYtdData, mockLastYearData, mockBudgetData, mockSelectedPeriod);
      expect(result).toEqual([]);
    });

    it('should return empty array when no ytdData provided', () => {
      const result = prepareKpiSummaryData(mockSimpleKpiData, null, mockLastYearData, mockBudgetData, mockSelectedPeriod);
      expect(result).toEqual([]);
    });

    it('should prepare KPI summary data with all inputs', () => {
      const result = prepareKpiSummaryData(
        mockSimpleKpiData,
        mockYtdData,
        mockLastYearData,
        mockBudgetData,
        mockSelectedPeriod
      );

      expect(result).toHaveLength(5);
      
      // Check Total Revenue KPI
      const totalRevenue = result.find(kpi => kpi.kpi === 'Total Revenue');
      expect(totalRevenue).toEqual({
        kpi: 'Total Revenue',
        mtd: 100000,
        vsbudget: 100, // (100000 - 50000) / 50000 * 100
        ytd: 500000,
        lastyear: 90000,
        vslastyear: 11.11, // (100000 - 90000) / 90000 * 100
      });

      // Check Net Revenue KPI
      const netRevenue = result.find(kpi => kpi.kpi === 'Net Revenue');
      expect(netRevenue).toEqual({
        kpi: 'Net Revenue',
        mtd: 80000,
        vsbudget: 100, // (80000 - 40000) / 40000 * 100
        ytd: 400000,
        lastyear: 72000,
        vslastyear: 11.11, // (80000 - 72000) / 72000 * 100
      });

      // Check Adspend KPI (aggregated from marketing accounts)
      const adspend = result.find(kpi => kpi.kpi === 'Adspend');
      expect(adspend).toEqual({
        kpi: 'Adspend',
        mtd: 20000,
        vsbudget: 100, // (20000 - 10000) / 10000 * 100
        ytd: 100000,
        lastyear: 18000,
        vslastyear: 11.11, // (20000 - 18000) / 18000 * 100
      });
    });

    it('should handle grouped KPI response', () => {
      const mockGroupedKpiData: GroupedKpiResponse = {
        'Brand A': {
          'Gross Revenue': { summary: { value: 60000 } },
          'Net Revenue': { summary: { value: 48000 } },
        },
        'Brand B': {
          'Gross Revenue': { summary: { value: 40000 } },
          'Net Revenue': { summary: { value: 32000 } },
        },
      };

      const result = prepareKpiSummaryData(
        mockGroupedKpiData,
        mockYtdData,
        mockLastYearData,
        null,
        null
      );

      expect(result).toHaveLength(5);
      
      // Should use first found KPI value (Brand A)
      const totalRevenue = result.find(kpi => kpi.kpi === 'Total Revenue');
      expect(totalRevenue?.mtd).toBe(60000);
    });

    it('should handle missing KPIs in data', () => {
      const incompleteKpiData: SimpleKpiResponse = {
        'Gross Revenue': { summary: { value: 100000 } },
        // Missing other KPIs
      };

      const result = prepareKpiSummaryData(
        incompleteKpiData,
        mockYtdData,
        mockLastYearData,
        null,
        null
      );

      expect(result).toHaveLength(5);
      
      const netRevenue = result.find(kpi => kpi.kpi === 'Net Revenue');
      expect(netRevenue?.mtd).toBeNull();
      expect(netRevenue?.vslastyear).toBeNull();
    });

    it('should handle null last year data', () => {
      const result = prepareKpiSummaryData(
        mockSimpleKpiData,
        mockYtdData,
        null,
        null,
        null
      );

      expect(result).toHaveLength(5);
      
      result.forEach(kpi => {
        expect(kpi.lastyear).toBeNull();
        expect(kpi.vslastyear).toBeNull();
      });
    });

    it('should handle null budget data', () => {
      const result = prepareKpiSummaryData(
        mockSimpleKpiData,
        mockYtdData,
        mockLastYearData,
        null,
        mockSelectedPeriod
      );

      expect(result).toHaveLength(5);
      
      result.forEach(kpi => {
        expect(kpi.vsbudget).toBeNull();
      });
    });

    it('should handle null selected period', () => {
      const result = prepareKpiSummaryData(
        mockSimpleKpiData,
        mockYtdData,
        mockLastYearData,
        mockBudgetData,
        null
      );

      expect(result).toHaveLength(5);
      
      result.forEach(kpi => {
        expect(kpi.vsbudget).toBeNull();
      });
    });

    it('should aggregate budget data across multiple months', () => {
      const extendedBudgetData = {
        ...mockBudgetData,
        'March-2024': {
          'Gross Revenue': 30000,
          'Net Revenue': 24000,
          'Pay-Per-Click': 4000,
        },
      };

      const extendedPeriod = {
        months: ['January', 'February', 'March'],
        year: '2024',
      };

      const result = prepareKpiSummaryData(
        mockSimpleKpiData,
        mockYtdData,
        mockLastYearData,
        extendedBudgetData,
        extendedPeriod
      );

      const totalRevenue = result.find(kpi => kpi.kpi === 'Total Revenue');
      // Budget should be 25000 + 25000 + 30000 = 80000
      expect(totalRevenue?.vsbudget).toBe(25); // (100000 - 80000) / 80000 * 100
    });

    it('should handle missing budget months', () => {
      const incompleteBudgetData = {
        'January-2024': {
          'Gross Revenue': 25000,
        },
        // Missing February-2024
      };

      const result = prepareKpiSummaryData(
        mockSimpleKpiData,
        mockYtdData,
        mockLastYearData,
        incompleteBudgetData,
        mockSelectedPeriod
      );

      const totalRevenue = result.find(kpi => kpi.kpi === 'Total Revenue');
      // Should only use January budget: 25000
      expect(totalRevenue?.vsbudget).toBe(300); // (100000 - 25000) / 25000 * 100
    });

    it('should handle zero budget values correctly', () => {
      const zeroBudgetData = {
        'January-2024': {
          'Gross Revenue': 0,
          'Net Revenue': 0,
        },
      };

      const singleMonthPeriod = {
        months: ['January'],
        year: '2024',
      };

      const result = prepareKpiSummaryData(
        mockSimpleKpiData,
        mockYtdData,
        mockLastYearData,
        zeroBudgetData,
        singleMonthPeriod
      );

      result.forEach(kpi => {
        if (kpi.kpi === 'Total Revenue' || kpi.kpi === 'Net Revenue') {
          expect(kpi.vsbudget).toBeNull(); // Should be null when budget is 0
        }
      });
    });

    it('should handle zero last year values correctly', () => {
      const zeroLastYearData: SimpleKpiResponse = {
        'Gross Revenue': { summary: { value: 0 } },
        'Net Revenue': { summary: { value: 0 } },
        'Gross Margin': { summary: { value: 0 } },
        'Contribution Margin': { summary: { value: 0 } },
        'Adspend': { summary: { value: 0 } },
      };

      const result = prepareKpiSummaryData(
        mockSimpleKpiData,
        mockYtdData,
        zeroLastYearData,
        null,
        null
      );

      result.forEach(kpi => {
        expect(kpi.vslastyear).toBeNull(); // Should be null when last year is 0
      });
    });

    it('should map marketing accounts to Adspend budget correctly', () => {
      const marketingBudgetData = {
        'January-2024': {
          'Pay-Per-Click': 1000,
          'Content': 2000,
          'D2C Marketing': 3000,
          'Paid Marketing Marketplaces': 4000,
          'Other Account': 5000, // Should not be included
        },
      };

      const result = prepareKpiSummaryData(
        mockSimpleKpiData,
        mockYtdData,
        mockLastYearData,
        marketingBudgetData,
        { months: ['January'], year: '2024' }
      );

      const adspend = result.find(kpi => kpi.kpi === 'Adspend');
      // Budget should be 1000 + 2000 + 3000 + 4000 = 10000
      expect(adspend?.vsbudget).toBe(100); // (20000 - 10000) / 10000 * 100
    });

    it('should handle non-numeric budget values', () => {
      const invalidBudgetData = {
        'January-2024': {
          'Gross Revenue': 'invalid' as unknown as number,
          'Net Revenue': null as unknown as number,
          'Gross Margin': undefined as unknown as number,
          'Valid Account': 15000,
        },
      };

      const result = prepareKpiSummaryData(
        mockSimpleKpiData,
        mockYtdData,
        mockLastYearData,
        invalidBudgetData,
        { months: ['January'], year: '2024' }
      );

      // Should only process valid numeric values
      const totalRevenue = result.find(kpi => kpi.kpi === 'Total Revenue');
      expect(totalRevenue?.vsbudget).toBeNull(); // No valid Gross Revenue budget

      const netRevenue = result.find(kpi => kpi.kpi === 'Net Revenue');
      expect(netRevenue?.vsbudget).toBeNull(); // No valid Net Revenue budget
    });

    it('should calculate percentage differences correctly', () => {
      const testKpiData: SimpleKpiResponse = {
        'Gross Revenue': { summary: { value: 150 } }, // 50% increase from 100
      };

      const testLastYearData: SimpleKpiResponse = {
        'Gross Revenue': { summary: { value: 100 } },
      };

      const testBudgetData = {
        'January-2024': {
          'Gross Revenue': 120, // 25% increase from budget
        },
      };

      const result = prepareKpiSummaryData(
        testKpiData,
        mockYtdData,
        testLastYearData,
        testBudgetData,
        { months: ['January'], year: '2024' }
      );

      const totalRevenue = result.find(kpi => kpi.kpi === 'Total Revenue');
      expect(totalRevenue?.vslastyear).toBe(50); // (150 - 100) / 100 * 100
      expect(totalRevenue?.vsbudget).toBe(25); // (150 - 120) / 120 * 100
    });

    it('should handle negative values correctly', () => {
      const negativeKpiData: SimpleKpiResponse = {
        'Gross Revenue': { summary: { value: -50000 } },
        'Net Revenue': { summary: { value: -40000 } },
      };

      const negativeLastYearData: SimpleKpiResponse = {
        'Gross Revenue': { summary: { value: -60000 } },
        'Net Revenue': { summary: { value: -48000 } },
      };

      const result = prepareKpiSummaryData(
        negativeKpiData,
        mockYtdData,
        negativeLastYearData,
        null,
        null
      );

      const totalRevenue = result.find(kpi => kpi.kpi === 'Total Revenue');
      expect(totalRevenue?.mtd).toBe(-50000);
      expect(totalRevenue?.vslastyear).toBe(-16.67); // (-50000 - (-60000)) / 60000 * 100
    });

    it('should return all required KPI categories', () => {
      const result = prepareKpiSummaryData(
        mockSimpleKpiData,
        mockYtdData,
        mockLastYearData,
        null,
        null
      );

      const expectedKpis = [
        'Total Revenue',
        'Net Revenue',
        'Gross Profit',
        'Contribution Margin',
        'Adspend',
      ];

      expectedKpis.forEach(expectedKpi => {
        expect(result.find(kpi => kpi.kpi === expectedKpi)).toBeDefined();
      });
    });
  });

  describe('Edge cases and performance', () => {
    it('should handle very large datasets efficiently', () => {
      const largeBudgetData: Record<string, Record<string, number>> = {};
      
      // Create budget data for 12 months
      for (let i = 1; i <= 12; i++) {
        const monthName = new Date(2024, i - 1).toLocaleString('en-US', { month: 'long' });
        largeBudgetData[`${monthName}-2024`] = {
          'Gross Revenue': Math.random() * 100000,
          'Net Revenue': Math.random() * 80000,
          'Gross Margin': Math.random() * 60000,
          'Contribution Margin': Math.random() * 40000,
          'Pay-Per-Click': Math.random() * 10000,
          'Content': Math.random() * 8000,
          'D2C Marketing': Math.random() * 12000,
          'Paid Marketing Marketplaces': Math.random() * 15000,
        };
      }

      const largePeriod = {
        months: Array.from({ length: 12 }, (_, i) => 
          new Date(2024, i).toLocaleString('en-US', { month: 'long' })
        ),
        year: '2024',
      };

      const mockKpiData: SimpleKpiResponse = {
        'Gross Revenue': { summary: { value: 1000000 } },
        'Net Revenue': { summary: { value: 800000 } },
        'Gross Margin': { summary: { value: 600000 } },
        'Contribution Margin': { summary: { value: 400000 } },
        'Adspend': { summary: { value: 200000 } },
      };

      const mockYtdData: SimpleKpiResponse = {
        'Gross Revenue': { summary: { value: 5000000 } },
        'Net Revenue': { summary: { value: 4000000 } },
        'Gross Margin': { summary: { value: 3000000 } },
        'Contribution Margin': { summary: { value: 2000000 } },
        'Adspend': { summary: { value: 1000000 } },
      };

      const start = performance.now();
      const result = prepareKpiSummaryData(
        mockKpiData,
        mockYtdData,
        mockKpiData,
        largeBudgetData,
        largePeriod
      );
      const end = performance.now();

      expect(result).toHaveLength(5);
      expect(end - start).toBeLessThan(50); // Should complete within 50ms
    });
  });
});