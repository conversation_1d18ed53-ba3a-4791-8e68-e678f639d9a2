/**
 * @fileoverview Unit tests for components/executive-summary/utils.ts
 * Tests executive summary utility functions including formatting, calculations, and period handling
 * Coverage target: 90%+
 */

import {
  GroupedKpiResponse,
  Period,
  PeriodType,
  SimpleKpiResponse,
} from '@/components/executive-summary/types';
import {
  calculateTrendData,
  formatCurrency,
  formatPercentage,
  generateAvailablePeriods,
  getLastCompletePeriod,
  getMonthTrendParams,
  getMtdParams,
  getPeriodParams,
  getWeekTrendParams,
  getYearTrendParams,
  getYtdParams,
  isPeriodComplete,
} from '@/components/executive-summary/utils';

// Mock date-fns functions for consistent testing
jest.mock('date-fns', () => ({
  ...jest.requireActual('date-fns'),
  format: jest.fn((date: Date, formatStr: string) => {
    if (formatStr === 'yyyy-MM-dd') {
      return date.toISOString().split('T')[0];
    }
    if (formatStr === 'MMMM yyyy') {
      return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    }
    return date.toISOString();
  }),
}));

describe('components/executive-summary/utils', () => {
  // Mock console.log to avoid test output pollution
  beforeAll(() => {
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe('formatCurrency', () => {
    it('should format CAD currency correctly', () => {
      expect(formatCurrency(1234.56, 'CAD')).toBe('CA$1,235');
      expect(formatCurrency(1000, 'CAD')).toBe('CA$1,000');
      expect(formatCurrency(0, 'CAD')).toBe('CA$0');
    });

    it('should format USD currency correctly', () => {
      expect(formatCurrency(1234.56, 'USD')).toBe('$1,235');
      expect(formatCurrency(1000, 'USD')).toBe('$1,000');
      expect(formatCurrency(0, 'USD')).toBe('$0');
    });

    it('should handle negative values', () => {
      expect(formatCurrency(-1234.56, 'USD')).toBe('-$1,235');
      expect(formatCurrency(-1000, 'CAD')).toBe('-CA$1,000');
    });

    it('should handle null values', () => {
      expect(formatCurrency(null, 'USD')).toBe('N/A');
      expect(formatCurrency(null, 'CAD')).toBe('N/A');
    });

    it('should round to nearest dollar', () => {
      expect(formatCurrency(1234.49, 'USD')).toBe('$1,234');
      expect(formatCurrency(1234.51, 'USD')).toBe('$1,235');
    });

    it('should handle large numbers', () => {
      expect(formatCurrency(1234567, 'USD')).toBe('$1,234,567');
      expect(formatCurrency(1000000, 'CAD')).toBe('CA$1,000,000');
    });
  });

  describe('formatPercentage', () => {
    it('should format positive percentages correctly', () => {
      expect(formatPercentage(12.34)).toBe('12.3%');
      expect(formatPercentage(50)).toBe('50.0%');
      expect(formatPercentage(100)).toBe('100.0%');
    });

    it('should format negative percentages correctly', () => {
      expect(formatPercentage(-12.34)).toBe('-12.3%');
      expect(formatPercentage(-5)).toBe('-5.0%');
    });

    it('should handle zero', () => {
      expect(formatPercentage(0)).toBe('0.0%');
    });

    it('should handle null values', () => {
      expect(formatPercentage(null)).toBe('N/A');
    });

    it('should format decimal values correctly', () => {
      expect(formatPercentage(0.123)).toBe('0.1%');
      expect(formatPercentage(0.156)).toBe('0.2%');
    });

    it('should handle large percentages', () => {
      expect(formatPercentage(1000)).toBe('1,000.0%');
      expect(formatPercentage(12345)).toBe('12,345.0%');
    });
  });

  describe('calculateTrendData', () => {
    const mockSimpleResponse: SimpleKpiResponse = {
      'Net Revenue': {
        summary: { value: 10000 },
        timeSeries: [
          { date: '2024-01-01', value: 1000 },
          { date: '2024-01-02', value: 1500 },
          { date: '2024-01-03', value: 2000 },
          { date: '2024-01-04', value: 2500 },
        ],
      },
    };

    const mockGroupedResponse: GroupedKpiResponse = {
      'Brand A': {
        'Net Revenue': {
          summary: { value: 5000 },
          timeSeries: [
            { date: '2024-01-01', value: 500 },
            { date: '2024-01-02', value: 750 },
            { date: '2024-01-03', value: 1000 },
            { date: '2024-01-04', value: 1250 },
          ],
        },
      },
    };

    it('should handle null data', () => {
      const result = calculateTrendData(null, 'Q1 2024');
      
      expect(result).toEqual({
        period: 'Q1 2024',
        value: null,
        change: null,
        kpiName: 'Net Revenue',
      });
    });

    it('should calculate trend data for simple response', () => {
      const result = calculateTrendData(mockSimpleResponse, 'Q1 2024', 'Net Revenue');
      
      expect(result.period).toBe('Q1 2024');
      expect(result.kpiName).toBe('Net Revenue');
      expect(result.value).toBe(4500); // Sum of last 2 values
      expect(result.change).toBe(80); // ((4500 - 2500) / 2500) * 100
    });

    it('should calculate trend data for grouped response', () => {
      const result = calculateTrendData(mockGroupedResponse, 'Q1 2024', 'Net Revenue');
      
      expect(result.period).toBe('Q1 2024');
      expect(result.kpiName).toBe('Net Revenue');
      expect(result.value).toBe(2250); // Sum of last 2 values
      expect(result.change).toBe(80); // ((2250 - 1250) / 1250) * 100
    });

    it('should handle missing KPI in response', () => {
      const result = calculateTrendData(mockSimpleResponse, 'Q1 2024', 'Missing KPI');
      
      expect(result).toEqual({
        period: 'Q1 2024',
        value: null,
        change: null,
        kpiName: 'Missing KPI',
      });
    });

    it('should handle insufficient time series data', () => {
      const responseWithShortSeries: SimpleKpiResponse = {
        'Net Revenue': {
          summary: { value: 1000 },
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      };

      const result = calculateTrendData(responseWithShortSeries, 'Q1 2024', 'Net Revenue');
      
      expect(result.value).toBe(1000); // Uses summary value
      expect(result.change).toBeNull();
    });

    it('should handle zero previous total', () => {
      const responseWithZeroPrevious: SimpleKpiResponse = {
        'Net Revenue': {
          summary: { value: 2000 },
          timeSeries: [
            { date: '2024-01-01', value: 0 },
            { date: '2024-01-02', value: 0 },
            { date: '2024-01-03', value: 1000 },
            { date: '2024-01-04', value: 1000 },
          ],
        },
      };

      const result = calculateTrendData(responseWithZeroPrevious, 'Q1 2024', 'Net Revenue');
      
      expect(result.value).toBe(2000);
      expect(result.change).toBeNull(); // No change calculation when previous is 0
    });

    it('should handle null values in time series', () => {
      const responseWithNulls: SimpleKpiResponse = {
        'Net Revenue': {
          summary: { value: 1500 },
          timeSeries: [
            { date: '2024-01-01', value: null },
            { date: '2024-01-02', value: 500 },
            { date: '2024-01-03', value: null },
            { date: '2024-01-04', value: 1000 },
          ],
        },
      };

      const result = calculateTrendData(responseWithNulls, 'Q1 2024', 'Net Revenue');
      
      expect(result.value).toBe(1000); // Sum treating nulls as 0
      expect(result.change).toBe(100); // ((1000 - 500) / 500) * 100
    });
  });

  describe('API parameter generators', () => {
    describe('getMtdParams', () => {
      it('should generate MTD parameters with CAD currency', () => {
        const params = getMtdParams('CAD');
        
        expect(params.get('currency')).toBe('CAD');
        expect(params.get('groupByTime')).toBe('month');
        expect(params.get('kpis')).toBe('Gross Revenue,Net Revenue,Gross Margin,Adspend,Contribution Margin,% Gross Margin,% Contribution Margin');
        expect(params.get('brands')).toBeNull();
      });

      it('should include brand filter when specified', () => {
        const params = getMtdParams('USD', 'Brand A');
        
        expect(params.get('currency')).toBe('USD');
        expect(params.get('brands')).toBe('Brand A');
      });

      it('should not include brand filter for "all brands"', () => {
        const params = getMtdParams('CAD', 'All Brands');
        
        expect(params.get('brands')).toBeNull();
      });

      it('should handle case insensitive "all brands"', () => {
        const params = getMtdParams('CAD', 'all brands');
        
        expect(params.get('brands')).toBeNull();
      });
    });

    describe('getYtdParams', () => {
      it('should generate YTD parameters', () => {
        const params = getYtdParams('USD', 'Brand B');
        
        expect(params.get('currency')).toBe('USD');
        expect(params.get('groupByTime')).toBe('year');
        expect(params.get('brands')).toBe('Brand B');
      });
    });

    describe('getWeekTrendParams', () => {
      it('should generate week trend parameters', () => {
        const params = getWeekTrendParams('CAD');
        
        expect(params.get('currency')).toBe('CAD');
        expect(params.get('groupByTime')).toBe('day');
        expect(params.get('timeRange')).toBe('last14days');
        expect(params.get('kpis')).toBe('Gross Revenue,Net Revenue,Gross Margin,Adspend,Contribution Margin');
      });
    });

    describe('getMonthTrendParams', () => {
      it('should generate month trend parameters', () => {
        const params = getMonthTrendParams('USD', 'Brand C');
        
        expect(params.get('currency')).toBe('USD');
        expect(params.get('groupByTime')).toBe('week');
        expect(params.get('timeRange')).toBe('last2months');
        expect(params.get('brands')).toBe('Brand C');
      });
    });

    describe('getYearTrendParams', () => {
      it('should generate year trend parameters', () => {
        const params = getYearTrendParams('CAD');
        
        expect(params.get('currency')).toBe('CAD');
        expect(params.get('groupByTime')).toBe('month');
        expect(params.get('timeRange')).toBe('last12months');
      });
    });

    describe('getPeriodParams', () => {
      const mockPeriod: Period = {
        type: PeriodType.MONTH,
        year: 2024,
        value: 1,
        label: 'January 2024',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        isComplete: true,
      };

      it('should generate period parameters for month', () => {
        const params = getPeriodParams(mockPeriod, 'CAD', 'Brand A');
        
        expect(params.get('currency')).toBe('CAD');
        expect(params.get('startDate')).toBe('2024-01-01');
        expect(params.get('endDate')).toBe('2024-01-31');
        expect(params.get('groupByTime')).toBe('day');
        expect(params.get('brands')).toBe('Brand A');
      });

      it('should handle quarter period type', () => {
        const quarterPeriod: Period = {
          ...mockPeriod,
          type: PeriodType.QUARTER,
        };

        const params = getPeriodParams(quarterPeriod, 'USD');
        
        expect(params.get('groupByTime')).toBe('month');
      });

      it('should handle year period type', () => {
        const yearPeriod: Period = {
          ...mockPeriod,
          type: PeriodType.YEAR,
        };

        const params = getPeriodParams(yearPeriod, 'CAD');
        
        expect(params.get('groupByTime')).toBe('month');
      });

      it('should use custom KPIs when provided', () => {
        const customKpis = ['Revenue', 'Margin'];
        const params = getPeriodParams(mockPeriod, 'CAD', undefined, customKpis);
        
        expect(params.get('kpis')).toBe('Revenue,Margin');
      });
    });
  });

  describe('Period handling functions', () => {
    describe('isPeriodComplete', () => {
      it('should return true for past periods', () => {
        const pastPeriod: Period = {
          type: PeriodType.MONTH,
          year: 2023,
          value: 1,
          label: 'January 2023',
          startDate: new Date('2023-01-01'),
          endDate: new Date('2023-01-31'),
          isComplete: false,
        };

        expect(isPeriodComplete(pastPeriod)).toBe(true);
      });

      it('should return false for future periods', () => {
        const futureDate = new Date();
        futureDate.setFullYear(futureDate.getFullYear() + 1);

        const futurePeriod: Period = {
          type: PeriodType.MONTH,
          year: futureDate.getFullYear(),
          value: 1,
          label: 'Future Month',
          startDate: new Date(futureDate.getFullYear(), 0, 1),
          endDate: new Date(futureDate.getFullYear(), 0, 31),
          isComplete: false,
        };

        expect(isPeriodComplete(futurePeriod)).toBe(false);
      });
    });

    describe('generateAvailablePeriods', () => {
      it('should generate month periods', () => {
        const periods = generateAvailablePeriods(PeriodType.MONTH, 3);
        
        expect(periods).toHaveLength(3);
        expect(periods[0].type).toBe(PeriodType.MONTH);
        expect(periods[0].label).toMatch(/\w+ \d{4}/); // e.g., "January 2024"
      });

      it('should generate quarter periods', () => {
        const periods = generateAvailablePeriods(PeriodType.QUARTER, 2);
        
        expect(periods).toHaveLength(2);
        expect(periods[0].type).toBe(PeriodType.QUARTER);
        expect(periods[0].label).toMatch(/Q[1-4] \d{4}/); // e.g., "Q1 2024"
      });

      it('should generate year periods', () => {
        const periods = generateAvailablePeriods(PeriodType.YEAR, 2);
        
        expect(periods).toHaveLength(2);
        expect(periods[0].type).toBe(PeriodType.YEAR);
        expect(periods[0].label).toMatch(/\d{4}/); // e.g., "2024"
      });

      it('should generate periods in reverse chronological order', () => {
        const periods = generateAvailablePeriods(PeriodType.MONTH, 3);
        
        expect(periods[0].startDate.getTime()).toBeGreaterThan(periods[1].startDate.getTime());
        expect(periods[1].startDate.getTime()).toBeGreaterThan(periods[2].startDate.getTime());
      });

      it('should set isComplete property correctly', () => {
        const periods = generateAvailablePeriods(PeriodType.MONTH, 12);
        
        // At least some older periods should be complete
        const completePeriods = periods.filter(p => p.isComplete);
        expect(completePeriods.length).toBeGreaterThan(0);
      });
    });

    describe('getLastCompletePeriod', () => {
      it('should return current year for YEAR period type', () => {
        const period = getLastCompletePeriod(PeriodType.YEAR);
        
        expect(period.type).toBe(PeriodType.YEAR);
        expect(period.year).toBe(new Date().getFullYear());
      });

      it('should return most recent complete period for MONTH', () => {
        const period = getLastCompletePeriod(PeriodType.MONTH);
        
        expect(period.type).toBe(PeriodType.MONTH);
        expect(period.isComplete).toBe(true);
      });

      it('should return most recent complete period for QUARTER', () => {
        const period = getLastCompletePeriod(PeriodType.QUARTER);
        
        expect(period.type).toBe(PeriodType.QUARTER);
        expect(period.isComplete).toBe(true);
      });
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle empty time series in calculateTrendData', () => {
      const emptySeriesResponse: SimpleKpiResponse = {
        'Net Revenue': {
          summary: { value: 1000 },
          timeSeries: [],
        },
      };

      const result = calculateTrendData(emptySeriesResponse, 'Q1 2024', 'Net Revenue');
      
      expect(result.value).toBe(1000);
      expect(result.change).toBeNull();
    });

    it('should handle malformed data in calculateTrendData', () => {
      const malformedResponse = {
        'Net Revenue': {
          // Missing summary
          timeSeries: [{ date: '2024-01-01', value: 1000 }],
        },
      } as unknown as SimpleKpiResponse;

      const result = calculateTrendData(malformedResponse, 'Q1 2024', 'Net Revenue');
      
      expect(result.value).toBeNull();
      expect(result.change).toBeNull();
    });

    it('should handle extreme percentage values', () => {
      expect(formatPercentage(999999)).toBe('999,999.0%');
      expect(formatPercentage(-999999)).toBe('-999,999.0%');
    });

    it('should handle extreme currency values', () => {
      expect(formatCurrency(999999999, 'USD')).toBe('$999,999,999');
      expect(formatCurrency(-999999999, 'CAD')).toBe('-CA$999,999,999');
    });
  });

  describe('Performance tests', () => {
    it('should generate periods efficiently', () => {
      const start = performance.now();
      
      for (let i = 0; i < 100; i++) {
        generateAvailablePeriods(PeriodType.MONTH, 12);
        generateAvailablePeriods(PeriodType.QUARTER, 8);
        generateAvailablePeriods(PeriodType.YEAR, 5);
      }
      
      const end = performance.now();
      expect(end - start).toBeLessThan(100);
    });

    it('should format values efficiently', () => {
      const start = performance.now();
      
      for (let i = 0; i < 1000; i++) {
        formatCurrency(Math.random() * 1000000, 'USD');
        formatPercentage(Math.random() * 100);
      }
      
      const end = performance.now();
      expect(end - start).toBeLessThan(50);
    });
  });
});