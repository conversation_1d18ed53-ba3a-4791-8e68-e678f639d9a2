/**
 * @fileoverview Unit tests for app/executive-summary/ExecutiveSummaryClient.tsx
 * Tests executive summary main client component functionality and user interactions
 * Coverage target: 75%+ (Tier 2)
 */

import { fireEvent, render, screen } from '@testing-library/react';

import ExecutiveSummaryClient from '@/app/executive-summary/ExecutiveSummaryClient';
import React from 'react';
import { jest } from '@jest/globals';

// Mock Next.js router
const mockPush = jest.fn();
const mockSearchParams = new URLSearchParams();

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
  useSearchParams: () => mockSearchParams,
}));

// Mock the custom hooks
const mockCurrencyHook = {
  currency: 'CAD' as 'CAD' | 'USD',
  setCurrency: jest.fn(),
};

const mockFilterHook = {
  filterData: {
    brands: [
      { name: 'Brand A', id: 1 },
      { name: '<PERSON> B', id: 2 },
    ],
  },
  loadingFilters: false,
  filterError: null as Error | null,
  selectedBrand: { name: 'Brand A', id: 1 },
  setSelectedBrand: jest.fn(),
};

const mockTimePeriodHook = {
  selectedPeriodType: 'MONTH',
  selectedPeriod: {
    type: 'MONTH',
    year: 2024,
    value: 1,
    label: 'January 2024',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-01-31'),
    isComplete: true,
  },
  availablePeriods: [
    {
      type: 'MONTH',
      year: 2024,
      value: 1,
      label: 'January 2024',
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-01-31'),
      isComplete: true,
    },
    {
      type: 'MONTH',
      year: 2024,
      value: 2,
      label: 'February 2024',
      startDate: new Date('2024-02-01'),
      endDate: new Date('2024-02-29'),
      isComplete: true,
    },
  ],
  changePeriodType: jest.fn(),
  selectPeriod: jest.fn(),
};

const mockKpiHook = {
  kpiData: {
    'Gross Revenue': {
      summary: { value: 100000, percentChange: 5.5 },
      timeSeries: [
        { date: '2024-01-01', value: 5000 },
        { date: '2024-01-02', value: 7500 },
      ],
    },
  },
  ytdData: {
    'Gross Revenue': {
      summary: { value: 500000, percentChange: 8.2 },
      timeSeries: [],
    },
  },
  lastYearData: null,
  budgetData: null,
  weekTrend: [],
  monthTrend: [],
  yearTrend: [],
  loadingData: false,
  apiError: null as Error | null,
  fetchKpiData: jest.fn(),
  formatPeriodForBudget: jest.fn(),
};

jest.mock('@/components/executive-summary', () => ({
  useCurrencyPreference: () => mockCurrencyHook,
  useFilterData: () => mockFilterHook,
  useTimePeriod: () => mockTimePeriodHook,
  useKpiData: () => mockKpiHook,
}));

jest.mock('@/components/executive-summary/dataPrep', () => ({
  prepareKpiSummaryData: () => [
    {
      kpi: 'Gross Revenue',
      mtd: 100000,
      vsbudget: 5.5,
      vslastyear: 8.2,
    },
  ],
}));

// Mock navigation config
jest.mock('@/components/main/MainNavConfig', () => ({
  mainNavItems: [{ title: 'Dashboard', href: '/' }],
  mainSecondaryNavItems: [{ title: 'Settings', href: '/settings' }],
}));

// Mock AppLayout
jest.mock('@/components/shared/AppLayout', () => ({
  AppLayout: ({ children, pageTitle, pageDescription }: { 
    children: React.ReactNode; 
    pageTitle: string; 
    pageDescription: string; 
  }) => (
    <div data-testid="app-layout">
      <h1>{pageTitle}</h1>
      <p>{pageDescription}</p>
      {children}
    </div>
  ),
}));

// Mock child components
jest.mock('@/components/executive-summary/KpiCardsGrid', () => ({
  __esModule: true,
  default: ({ kpiSummaryData, currency }: { 
    kpiSummaryData: unknown[]; 
    currency: string; 
  }) => (
    <div data-testid="kpi-cards-grid" data-currency={currency}>
      KPI Cards Grid ({kpiSummaryData.length} items)
    </div>
  ),
}));

jest.mock('@/components/executive-summary/PdfExport', () => ({
  __esModule: true,
  default: ({ selectedBrand, currency }: { selectedBrand: string; currency: string }) => (
    <div data-testid="pdf-export" data-brand={selectedBrand} data-currency={currency}>
      PDF Export Component
    </div>
  ),
}));

jest.mock('@/components/executive-summary/slides/components/ScrollableSlideView', () => ({
  ScrollableSlideView: ({ kpiData, currency }: { 
    kpiData: unknown[]; 
    currency: string; 
  }) => (
    <div data-testid="scrollable-slide-view" data-currency={currency}>
      Scrollable Slide View ({kpiData.length} KPIs)
    </div>
  ),
}));

jest.mock('@/components/executive-summary/SimpleBrandSelector', () => ({
  SimpleBrandSelector: ({ brands, selectedBrand, onBrandSelect }: { 
    brands: { name: string; id: number }[]; 
    selectedBrand: { name: string; id: number } | null; 
    onBrandSelect: (brand: { name: string; id: number }) => void; 
  }) => (
    <div data-testid="simple-brand-selector">
      <select 
        value={selectedBrand?.name || ''} 
        onChange={(e) => {
          const brand = brands.find(b => b.name === e.target.value);
          if (brand) onBrandSelect(brand);
        }}
      >
        {brands.map(brand => (
          <option key={brand.id} value={brand.name}>{brand.name}</option>
        ))}
      </select>
    </div>
  ),
}));

// Mock UI components
jest.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>{children}</div>
  ),
  CardContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-content">{children}</div>
  ),
  CardHeader: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-header">{children}</div>
  ),
  CardTitle: ({ children }: { children: React.ReactNode }) => (
    <h3 data-testid="card-title">{children}</h3>
  ),
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, variant, size }: { 
    children: React.ReactNode; 
    onClick?: () => void; 
    disabled?: boolean; 
    variant?: string; 
    size?: string; 
  }) => (
    <button 
      data-testid="button" 
      onClick={onClick} 
      disabled={disabled} 
      data-variant={variant} 
      data-size={size}
    >
      {children}
    </button>
  ),
}));

jest.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange }: { 
    children: React.ReactNode; 
    value: string; 
    onValueChange: (value: string) => void; 
  }) => (
    <div data-testid="select" data-value={value}>
      <select onChange={(e) => onValueChange(e.target.value)} value={value}>
        {children}
      </select>
    </div>
  ),
  SelectContent: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SelectItem: ({ children, value }: { children: React.ReactNode; value: string }) => (
    <option value={value}>{children}</option>
  ),
  SelectTrigger: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SelectValue: ({ placeholder }: { placeholder?: string }) => <span>{placeholder}</span>,
}));

jest.mock('@/components/ui/toggle-group', () => ({
  ToggleGroup: ({ children, value }: { 
    children: React.ReactNode; 
    value: string;
  }) => (
    React.createElement('div', { 'data-testid': 'toggle-group', 'data-value': value }, children)
  ),
  ToggleGroupItem: ({ children, value, onValueChange }: { 
    children: React.ReactNode; 
    value: string; 
    onValueChange?: (value: string) => void; 
  }) => (
    React.createElement('button', {
      'data-testid': `toggle-group-item-${value}`,
      onClick: () => onValueChange?.(value)
    }, children)
  ),
}));

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  Grid3X3: ({ className }: { className?: string }) => <div data-testid="grid-icon" className={className} />,
  Presentation: ({ className }: { className?: string }) => <div data-testid="presentation-icon" className={className} />,
}));

describe('app/executive-summary/ExecutiveSummaryClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockSearchParams.set('view', 'slides');
    
    // Reset mock states
    mockCurrencyHook.currency = 'CAD';
    mockFilterHook.loadingFilters = false;
    mockFilterHook.filterError = null;
    mockKpiHook.loadingData = false;
    mockKpiHook.apiError = null;
  });

  describe('Component Rendering', () => {
    it('should render the main executive summary component', () => {
      render(<ExecutiveSummaryClient />);

      expect(screen.getByTestId('app-layout')).toBeDefined();
      expect(screen.getAllByText('Executive Summary')).toHaveLength(2);
      expect(screen.getByText('Comprehensive business performance overview')).toBeDefined();
    });

    it('should render page header with brand name', () => {
      render(<ExecutiveSummaryClient />);

      expect(screen.getByText('Performance highlights for Brand A')).toBeDefined();
    });

    it('should render view toggle buttons', () => {
      render(<ExecutiveSummaryClient />);

      expect(screen.getByTestId('toggle-group')).toBeDefined();
      expect(screen.getByTestId('toggle-group-item-slides')).toBeDefined();
      expect(screen.getByTestId('toggle-group-item-grid')).toBeDefined();
      expect(screen.getByTestId('presentation-icon')).toBeDefined();
      expect(screen.getByTestId('grid-icon')).toBeDefined();
    });

    it('should render export PDF button', () => {
      render(<ExecutiveSummaryClient />);

      const buttons = screen.getAllByTestId('button');
      const exportButton = buttons.find(btn => btn.textContent === 'Export PDF');
      expect(exportButton).toBeDefined();
    });
  });

  describe('Controls Section', () => {
    it('should render brand selector', () => {
      render(<ExecutiveSummaryClient />);

      expect(screen.getByTestId('simple-brand-selector')).toBeDefined();
      expect(screen.getByText('Brand:')).toBeDefined();
    });

    it('should render period type buttons', () => {
      render(<ExecutiveSummaryClient />);

      expect(screen.getByText('Month')).toBeDefined();
      expect(screen.getByText('Quarter')).toBeDefined();
      expect(screen.getByText('Year')).toBeDefined();
    });

    it('should render period selector', () => {
      render(<ExecutiveSummaryClient />);

      expect(screen.getByText('Month:')).toBeDefined();
      const selects = screen.getAllByTestId('select');
      expect(selects.length).toBeGreaterThan(0);
    });

    it('should render currency selector', () => {
      render(<ExecutiveSummaryClient />);

      expect(screen.getByText('Currency:')).toBeDefined();
      const selects = screen.getAllByTestId('select');
      const currencySelect = selects.find(select => 
        select.getAttribute('data-value') === 'CAD'
      );
      expect(currencySelect).toBeDefined();
    });
  });

  describe('Loading States', () => {
    it('should show loading message when filters are loading', () => {
      mockFilterHook.loadingFilters = true;
      
      render(<ExecutiveSummaryClient />);

      expect(screen.getByText('Loading configuration...')).toBeDefined();
    });

    it('should show loading message when data is loading', () => {
      mockKpiHook.loadingData = true;
      
      render(<ExecutiveSummaryClient />);

      expect(screen.getByText('Loading report data...')).toBeDefined();
    });

    it('should disable export button when loading', () => {
      mockKpiHook.loadingData = true;
      
      render(<ExecutiveSummaryClient />);

      const buttons = screen.getAllByTestId('button');
      const exportButton = buttons.find(btn => btn.textContent === 'Export PDF');
      expect(exportButton?.getAttribute('disabled')).toBe('');
    });
  });

  describe('Error States', () => {
    it('should display filter error', () => {
      mockFilterHook.filterError = new Error('Filter loading failed');
      
      render(<ExecutiveSummaryClient />);

      expect(screen.getByText('Configuration Error')).toBeDefined();
      expect(screen.getByText('Filter loading failed')).toBeDefined();
    });

    it('should display API error with retry button', () => {
      mockKpiHook.apiError = new Error('API request failed');
      
      render(<ExecutiveSummaryClient />);

      expect(screen.getByText('Data Loading Error')).toBeDefined();
      expect(screen.getByText('API request failed')).toBeDefined();
      
      const buttons = screen.getAllByTestId('button');
      const retryButton = buttons.find(btn => btn.textContent === 'Retry');
      expect(retryButton).toBeDefined();
    });

    it('should call fetchKpiData when retry button is clicked', () => {
      mockKpiHook.apiError = new Error('API request failed');
      
      render(<ExecutiveSummaryClient />);

      const buttons = screen.getAllByTestId('button');
      const retryButton = buttons.find(btn => btn.textContent === 'Retry');
      
      if (retryButton) {
        fireEvent.click(retryButton);
        expect(mockKpiHook.fetchKpiData).toHaveBeenCalled();
      }
    });
  });

  describe('View Switching', () => {
    it('should render slides view by default', () => {
      render(<ExecutiveSummaryClient />);

      expect(screen.getByTestId('scrollable-slide-view')).toBeDefined();
      expect(screen.queryByTestId('kpi-cards-grid')).toBeNull();
    });

    it('should switch to grid view when grid button is clicked', () => {
      render(<ExecutiveSummaryClient />);

      const gridButton = screen.getByTestId('toggle-group-item-grid');
      fireEvent.click(gridButton);

      // Should update URL
      expect(mockPush).toHaveBeenCalledWith('?view=grid', { scroll: false });
    });

    it('should render grid view when view param is grid', () => {
      mockSearchParams.set('view', 'grid');
      
      render(<ExecutiveSummaryClient />);

      expect(screen.getByTestId('kpi-cards-grid')).toBeDefined();
      expect(screen.queryByTestId('scrollable-slide-view')).toBeNull();
    });
  });

  describe('User Interactions', () => {
    it('should handle brand selection', () => {
      render(<ExecutiveSummaryClient />);

      const brandSelector = screen.getByTestId('simple-brand-selector');
      const select = brandSelector.querySelector('select');
      
      if (select) {
        fireEvent.change(select, { target: { value: 'Brand B' } });
        expect(mockFilterHook.setSelectedBrand).toHaveBeenCalledWith({ name: 'Brand B', id: 2 });
      }
    });

    it('should handle currency change', () => {
      render(<ExecutiveSummaryClient />);

      const selects = screen.getAllByTestId('select');
      const currencySelect = selects.find(select => 
        select.getAttribute('data-value') === 'CAD'
      );
      
      if (currencySelect) {
        const selectElement = currencySelect.querySelector('select');
        if (selectElement) {
          fireEvent.change(selectElement, { target: { value: 'USD' } });
          expect(mockCurrencyHook.setCurrency).toHaveBeenCalledWith('USD');
        }
      }
    });

    it('should handle period type change', () => {
      render(<ExecutiveSummaryClient />);

      const quarterButton = screen.getByText('Quarter');
      fireEvent.click(quarterButton);

      expect(mockTimePeriodHook.changePeriodType).toHaveBeenCalled();
    });

    it('should handle export PDF button click', () => {
      render(<ExecutiveSummaryClient />);

      const buttons = screen.getAllByTestId('button');
      const exportButton = buttons.find(btn => btn.textContent === 'Export PDF');
      
      if (exportButton) {
        fireEvent.click(exportButton);
        // Should show PDF export component
        expect(screen.getByTestId('pdf-export')).toBeDefined();
      }
    });
  });

  describe('Data Flow', () => {
    it('should pass correct props to KPI cards grid', () => {
      mockSearchParams.set('view', 'grid');
      
      render(<ExecutiveSummaryClient />);

      const kpiGrid = screen.getByTestId('kpi-cards-grid');
      expect(kpiGrid.getAttribute('data-currency')).toBe('CAD');
      expect(kpiGrid.textContent).toContain('1 items'); // From mocked data
    });

    it('should pass correct props to slide view', () => {
      render(<ExecutiveSummaryClient />);

      const slideView = screen.getByTestId('scrollable-slide-view');
      expect(slideView.getAttribute('data-currency')).toBe('CAD');
      expect(slideView.textContent).toContain('1 KPIs'); // From mocked data
    });

    it('should pass correct props to PDF export', () => {
      render(<ExecutiveSummaryClient />);

      const buttons = screen.getAllByTestId('button');
      const exportButton = buttons.find(btn => btn.textContent === 'Export PDF');
      
      if (exportButton) {
        fireEvent.click(exportButton);
        
        const pdfExport = screen.getByTestId('pdf-export');
        expect(pdfExport.getAttribute('data-brand')).toBe('Brand A');
        expect(pdfExport.getAttribute('data-currency')).toBe('CAD');
      }
    });
  });

  describe('URL Management', () => {
    it('should update URL when switching to slides view', () => {
      render(<ExecutiveSummaryClient />);

      const slidesButton = screen.getByTestId('toggle-group-item-slides');
      fireEvent.click(slidesButton);

      expect(mockPush).toHaveBeenCalledWith('?view=slides&slide=1', { scroll: false });
    });

    it('should read initial view from URL params', () => {
      mockSearchParams.set('view', 'grid');
      
      render(<ExecutiveSummaryClient />);

      const toggleGroup = screen.getByTestId('toggle-group');
      expect(toggleGroup.getAttribute('data-value')).toBe('grid');
    });
  });

  describe('Responsive Design', () => {
    it('should render with responsive layout classes', () => {
      render(<ExecutiveSummaryClient />);

      // Check for responsive classes in the header
      const headerWithClass = screen.getAllByText('Executive Summary')[1].closest('.flex');
      expect(headerWithClass).toBeTruthy();
    });
  });

  describe('Accessibility', () => {
    it('should render semantic headings', () => {
      render(<ExecutiveSummaryClient />);

      expect(screen.getAllByRole('heading', { level: 1 })).toHaveLength(2);
    });

    it('should provide aria labels for toggle buttons', () => {
      render(<ExecutiveSummaryClient />);

      const slidesButton = screen.getByTestId('toggle-group-item-slides');
      const gridButton = screen.getByTestId('toggle-group-item-grid');
      
      expect(slidesButton).toBeDefined();
      expect(gridButton).toBeDefined();
    });
  });

  describe('Performance', () => {
    it('should render efficiently', () => {
      const start = performance.now();
      render(<ExecutiveSummaryClient />);
      const end = performance.now();

      expect(end - start).toBeLessThan(100);
      expect(screen.getByTestId('app-layout')).toBeDefined();
    });

    it('should handle state changes efficiently', () => {
      const { rerender } = render(<ExecutiveSummaryClient />);

      const start = performance.now();
      
      // Simulate currency change
      mockCurrencyHook.currency = 'USD';
      rerender(<ExecutiveSummaryClient />);
      
      const end = performance.now();
      expect(end - start).toBeLessThan(50);
    });
  });
});
