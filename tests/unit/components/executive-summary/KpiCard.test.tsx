/**
 * @fileoverview Unit tests for components/executive-summary/KpiCard.tsx
 * Tests KPI card component functionality, data visualization, and user interactions
 * Coverage target: 90%+
 */

import { KpiSummaryData, Period, PeriodType, TimeSeriesItem, ValidCurrency } from '@/components/executive-summary/types';
import { fireEvent, render, screen } from '@testing-library/react';

import KpiCard from '@/components/executive-summary/KpiCard';
import React from 'react';

// Mock recharts components
jest.mock('recharts', () => ({
  BarChart: ({ children }: { children: React.ReactNode }) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
}));

// Mock chart components
jest.mock('@/components/ui/chart', () => ({
  ChartContainer: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="chart-container" className={className}>{children}</div>
  ),
  ChartTooltip: ({ children }: { children: React.ReactNode }) => <div data-testid="chart-tooltip">{children}</div>,
  ChartTooltipContent: ({ children }: { children: React.ReactNode }) => <div data-testid="chart-tooltip-content">{children}</div>,
}));

describe('components/executive-summary/KpiCard', () => {
  const mockKpiData: KpiSummaryData = {
    kpi: 'Gross Revenue',
    mtd: 150000,
    vsbudget: 15.5,
    vslastyear: -5.2,
  };

  const mockPeriod: Period = {
    type: PeriodType.MONTH,
    year: 2024,
    value: 1,
    label: 'January 2024',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-01-31'),
    isComplete: true,
  };

  const mockTimeSeries: TimeSeriesItem[] = [
    { date: '2024-01-01', value: 5000 },
    { date: '2024-01-02', value: 7500 },
    { date: '2024-01-03', value: 6000 },
    { date: '2024-01-04', value: 8000 },
    { date: '2024-01-05', value: 9500 },
  ];

  const defaultProps = {
    kpiData: mockKpiData,
    currency: 'CAD' as ValidCurrency,
    period: mockPeriod,
    timeSeries: mockTimeSeries,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render KPI card with basic information', () => {
      render(<KpiCard {...defaultProps} />);

      expect(screen.getByText('Gross Revenue')).toBeInTheDocument();
      expect(screen.getByText('CA$150,000')).toBeInTheDocument();
      expect(screen.getByText('15.5% vs Budget')).toBeInTheDocument();
      expect(screen.getByText('-5.2% vs Last Year')).toBeInTheDocument();
    });

    it('should render with USD currency formatting', () => {
      render(<KpiCard {...defaultProps} currency="USD" />);

      expect(screen.getByText('$150,000')).toBeInTheDocument();
    });

    it('should render chart when time series data is provided', () => {
      render(<KpiCard {...defaultProps} />);

      expect(screen.getByTestId('chart-container')).toBeInTheDocument();
      expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
      expect(screen.getByTestId('bar')).toBeInTheDocument();
    });

    it('should render no chart message when time series is empty', () => {
      render(<KpiCard {...defaultProps} timeSeries={[]} />);

      expect(screen.getByText('No chart data available')).toBeInTheDocument();
      expect(screen.queryByTestId('chart-container')).not.toBeInTheDocument();
    });

    it('should render no chart message when time series is undefined', () => {
      render(<KpiCard {...defaultProps} timeSeries={undefined} />);

      expect(screen.getByText('No chart data available')).toBeInTheDocument();
      expect(screen.queryByTestId('chart-container')).not.toBeInTheDocument();
    });
  });

  describe('Trend Indicators', () => {
    it('should show positive trend indicator for positive values', () => {
      const positiveKpiData = {
        ...mockKpiData,
        vsbudget: 10.5,
        vslastyear: 8.3,
      };

      render(<KpiCard {...defaultProps} kpiData={positiveKpiData} />);

      expect(screen.getByText('10.5% vs Budget')).toBeInTheDocument();
      expect(screen.getByText('8.3% vs Last Year')).toBeInTheDocument();
    });

    it('should show negative trend indicator for negative values', () => {
      const negativeKpiData = {
        ...mockKpiData,
        vsbudget: -15.2,
        vslastyear: -8.7,
      };

      render(<KpiCard {...defaultProps} kpiData={negativeKpiData} />);

      expect(screen.getByText('-15.2% vs Budget')).toBeInTheDocument();
      expect(screen.getByText('-8.7% vs Last Year')).toBeInTheDocument();
    });

    it('should show N/A for null trend values', () => {
      const nullKpiData = {
        ...mockKpiData,
        vsbudget: null,
        vslastyear: null,
      };

      render(<KpiCard {...defaultProps} kpiData={nullKpiData} />);

      expect(screen.getByText('N/A vs Budget')).toBeInTheDocument();
      expect(screen.getByText('N/A vs Last Year')).toBeInTheDocument();
    });

    it('should show neutral indicator for zero values', () => {
      const zeroKpiData = {
        ...mockKpiData,
        vsbudget: 0,
        vslastyear: 0,
      };

      render(<KpiCard {...defaultProps} kpiData={zeroKpiData} />);

      expect(screen.getByText('0.0% vs Budget')).toBeInTheDocument();
      expect(screen.getByText('0.0% vs Last Year')).toBeInTheDocument();
    });
  });

  describe('Chart Data Processing', () => {
    it('should process daily data for short periods', () => {
      const shortPeriod: Period = {
        ...mockPeriod,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-15'), // 15 days
      };

      render(<KpiCard {...defaultProps} period={shortPeriod} />);

      expect(screen.getByTestId('chart-container')).toBeInTheDocument();
    });

    it('should process weekly data for medium periods', () => {
      const mediumPeriod: Period = {
        ...mockPeriod,
        type: PeriodType.QUARTER,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-03-31'), // ~90 days
      };

      const quarterTimeSeries = Array.from({ length: 90 }, (_, i) => ({
        date: new Date(2024, 0, i + 1).toISOString().split('T')[0],
        value: Math.random() * 1000,
      }));

      render(<KpiCard {...defaultProps} period={mediumPeriod} timeSeries={quarterTimeSeries} />);

      expect(screen.getByTestId('chart-container')).toBeInTheDocument();
    });

    it('should process monthly data for long periods', () => {
      const longPeriod: Period = {
        ...mockPeriod,
        type: PeriodType.YEAR,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'), // 365 days
      };

      const yearTimeSeries = Array.from({ length: 365 }, (_, i) => ({
        date: new Date(2024, 0, i + 1).toISOString().split('T')[0],
        value: Math.random() * 1000,
      }));

      render(<KpiCard {...defaultProps} period={longPeriod} timeSeries={yearTimeSeries} />);

      expect(screen.getByTestId('chart-container')).toBeInTheDocument();
    });

    it('should handle null values in time series', () => {
      const timeSeriesWithNulls: TimeSeriesItem[] = [
        { date: '2024-01-01', value: 5000 },
        { date: '2024-01-02', value: null },
        { date: '2024-01-03', value: 6000 },
        { date: '2024-01-04', value: null },
        { date: '2024-01-05', value: 9500 },
      ];

      render(<KpiCard {...defaultProps} timeSeries={timeSeriesWithNulls} />);

      expect(screen.getByTestId('chart-container')).toBeInTheDocument();
    });

    it('should sort time series data by date', () => {
      const unsortedTimeSeries: TimeSeriesItem[] = [
        { date: '2024-01-05', value: 9500 },
        { date: '2024-01-01', value: 5000 },
        { date: '2024-01-03', value: 6000 },
        { date: '2024-01-02', value: 7500 },
        { date: '2024-01-04', value: 8000 },
      ];

      render(<KpiCard {...defaultProps} timeSeries={unsortedTimeSeries} />);

      expect(screen.getByTestId('chart-container')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should call onDoubleClick when card is double-clicked', () => {
      const mockOnDoubleClick = jest.fn();

      render(<KpiCard {...defaultProps} onDoubleClick={mockOnDoubleClick} />);

      const card = screen.getByText('Gross Revenue').closest('[data-slot="card"]');
      expect(card).toBeInTheDocument();

      fireEvent.doubleClick(card!);
      expect(mockOnDoubleClick).toHaveBeenCalledTimes(1);
    });

    it('should not error when onDoubleClick is not provided', () => {
      render(<KpiCard {...defaultProps} />);

      const card = screen.getByText('Gross Revenue').closest('[data-slot="card"]');
      expect(card).toBeInTheDocument();

      expect(() => {
        fireEvent.doubleClick(card!);
      }).not.toThrow();
    });
  });

  describe('Styling and Layout', () => {
    it('should apply custom className', () => {
      const customClass = 'custom-kpi-card';
      render(<KpiCard {...defaultProps} className={customClass} />);

      const card = screen.getByText('Gross Revenue').closest('[data-slot="card"]');
      expect(card).toHaveClass(customClass);
    });

    it('should have proper chart container height', () => {
      render(<KpiCard {...defaultProps} />);

      const chartContainer = screen.getByTestId('chart-container');
      expect(chartContainer).toHaveClass('h-[80px]');
    });

    it('should have proper card shadow classes', () => {
      render(<KpiCard {...defaultProps} />);

      const card = screen.getByText('Gross Revenue').closest('[data-slot="card"]');
      expect(card).toHaveClass('shadow-sm', 'hover:shadow-md', 'transition-shadow');
    });
  });

  describe('Data Formatting', () => {
    it('should format large currency values correctly', () => {
      const largeKpiData = {
        ...mockKpiData,
        mtd: 1500000, // 1.5 million
      };

      render(<KpiCard {...defaultProps} kpiData={largeKpiData} />);

      expect(screen.getByText('CA$1,500,000')).toBeInTheDocument();
    });

    it('should format small currency values correctly', () => {
      const smallKpiData = {
        ...mockKpiData,
        mtd: 123.45,
      };

      render(<KpiCard {...defaultProps} kpiData={smallKpiData} />);

      expect(screen.getByText('CA$123')).toBeInTheDocument();
    });

    it('should format zero currency values correctly', () => {
      const zeroKpiData = {
        ...mockKpiData,
        mtd: 0,
      };

      render(<KpiCard {...defaultProps} kpiData={zeroKpiData} />);

      expect(screen.getByText('CA$0')).toBeInTheDocument();
    });

    it('should format null currency values correctly', () => {
      const nullKpiData = {
        ...mockKpiData,
        mtd: null,
      };

      render(<KpiCard {...defaultProps} kpiData={nullKpiData} />);

      expect(screen.getByText('N/A')).toBeInTheDocument();
    });

    it('should format percentage values with one decimal place', () => {
      const preciseKpiData = {
        ...mockKpiData,
        vsbudget: 15.567,
        vslastyear: -5.234,
      };

      render(<KpiCard {...defaultProps} kpiData={preciseKpiData} />);

      expect(screen.getByText('15.6% vs Budget')).toBeInTheDocument();
      expect(screen.getByText('-5.2% vs Last Year')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty KPI name', () => {
      const emptyNameKpiData = {
        ...mockKpiData,
        kpi: '',
      };

      render(<KpiCard {...defaultProps} kpiData={emptyNameKpiData} />);

      expect(screen.getByText('')).toBeInTheDocument();
    });

    it('should handle very long KPI names', () => {
      const longNameKpiData = {
        ...mockKpiData,
        kpi: 'Very Long KPI Name That Should Be Handled Gracefully',
      };

      render(<KpiCard {...defaultProps} kpiData={longNameKpiData} />);

      expect(screen.getByText('Very Long KPI Name That Should Be Handled Gracefully')).toBeInTheDocument();
    });

    it('should handle negative currency values', () => {
      const negativeKpiData = {
        ...mockKpiData,
        mtd: -50000,
      };

      render(<KpiCard {...defaultProps} kpiData={negativeKpiData} />);

      expect(screen.getByText('-CA$50,000')).toBeInTheDocument();
    });

    it('should handle extreme percentage values', () => {
      const extremeKpiData = {
        ...mockKpiData,
        vsbudget: 999.9,
        vslastyear: -999.9,
      };

      render(<KpiCard {...defaultProps} kpiData={extremeKpiData} />);

      expect(screen.getByText('999.9% vs Budget')).toBeInTheDocument();
      expect(screen.getByText('-999.9% vs Last Year')).toBeInTheDocument();
    });

    it('should handle single data point in time series', () => {
      const singlePointTimeSeries: TimeSeriesItem[] = [
        { date: '2024-01-01', value: 5000 },
      ];

      render(<KpiCard {...defaultProps} timeSeries={singlePointTimeSeries} />);

      expect(screen.getByTestId('chart-container')).toBeInTheDocument();
    });

    it('should handle malformed dates in time series', () => {
      const malformedTimeSeries: TimeSeriesItem[] = [
        { date: 'invalid-date', value: 5000 },
        { date: '2024-01-02', value: 7500 },
      ];

      expect(() => {
        render(<KpiCard {...defaultProps} timeSeries={malformedTimeSeries} />);
      }).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<KpiCard {...defaultProps} />);

      const card = screen.getByText('Gross Revenue').closest('[data-slot="card"]');
      expect(card).toBeInTheDocument();
    });

    it('should be keyboard accessible when onDoubleClick is provided', () => {
      const mockOnDoubleClick = jest.fn();
      render(<KpiCard {...defaultProps} onDoubleClick={mockOnDoubleClick} />);

      const card = screen.getByText('Gross Revenue').closest('[data-slot="card"]');
      expect(card).toBeInTheDocument();

      // Card should be focusable if it has click handlers
      fireEvent.doubleClick(card!);
      expect(mockOnDoubleClick).toHaveBeenCalled();
    });
  });

  describe('Performance Tests', () => {
    it('should render efficiently with large time series data', () => {
      const largeTimeSeries = Array.from({ length: 1000 }, (_, i) => ({
        date: new Date(2024, 0, 1 + (i % 365)).toISOString().split('T')[0],
        value: Math.random() * 10000,
      }));

      const start = performance.now();
      render(<KpiCard {...defaultProps} timeSeries={largeTimeSeries} />);
      const end = performance.now();

      expect(end - start).toBeLessThan(100);
      expect(screen.getByTestId('chart-container')).toBeInTheDocument();
    });

    it('should handle rapid prop changes efficiently', () => {
      const { rerender } = render(<KpiCard {...defaultProps} />);

      const start = performance.now();

      for (let i = 0; i < 10; i++) {
        const newKpiData = {
          ...mockKpiData,
          mtd: mockKpiData.mtd + i * 1000,
          vsbudget: (mockKpiData.vsbudget || 0) + i,
        };

        rerender(<KpiCard {...defaultProps} kpiData={newKpiData} />);
      }

      const end = performance.now();
      expect(end - start).toBeLessThan(100);
    });
  });
});