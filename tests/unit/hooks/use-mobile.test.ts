/**
 * @fileoverview Unit tests for hooks/use-mobile.ts
 * Tests mobile detection hook functionality
 * Coverage target: 90%+
 */

import { act, renderHook } from '@testing-library/react';

import { useIsMobile } from '@/hooks/use-mobile';

// Mock window.matchMedia
const mockMatchMedia = jest.fn();
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: mockMatchMedia,
});

// Mock window.innerWidth
Object.defineProperty(window, 'innerWidth', {
  writable: true,
  value: 1024,
});

describe('hooks/use-mobile', () => {
  let mockMediaQueryList: {
    matches: boolean;
    addEventListener: jest.Mock;
    removeEventListener: jest.Mock;
  };

  beforeEach(() => {
    mockMediaQueryList = {
      matches: false,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    };
    mockMatchMedia.mockReturnValue(mockMediaQueryList);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('useIsMobile', () => {
    it('should return false for desktop width initially', () => {
      // Set desktop width
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        value: 1024,
      });

      const { result } = renderHook(() => useIsMobile());
      
      expect(result.current).toBe(false);
    });

    it('should return true for mobile width initially', () => {
      // Set mobile width
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        value: 320,
      });

      const { result } = renderHook(() => useIsMobile());
      
      expect(result.current).toBe(true);
    });

    it('should return true for width exactly at breakpoint boundary', () => {
      // Set width to exactly 767px (mobile breakpoint is < 768)
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        value: 767,
      });

      const { result } = renderHook(() => useIsMobile());
      
      expect(result.current).toBe(true);
    });

    it('should return false for width at desktop breakpoint', () => {
      // Set width to exactly 768px (desktop breakpoint is >= 768)
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        value: 768,
      });

      const { result } = renderHook(() => useIsMobile());
      
      expect(result.current).toBe(false);
    });

    it('should set up media query listener with correct breakpoint', () => {
      renderHook(() => useIsMobile());
      
      expect(mockMatchMedia).toHaveBeenCalledWith('(max-width: 767px)');
      expect(mockMediaQueryList.addEventListener).toHaveBeenCalledWith(
        'change',
        expect.any(Function)
      );
    });

    it('should update state when media query changes', () => {
      const { result } = renderHook(() => useIsMobile());
      
      // Get the change handler that was registered
      const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
      
      // Initially desktop
      expect(result.current).toBe(false);
      
      // Simulate window resize to mobile
      act(() => {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          value: 320,
        });
        changeHandler();
      });
      
      expect(result.current).toBe(true);
    });

    it('should update state when resizing from mobile to desktop', () => {
      // Start with mobile width
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        value: 320,
      });

      const { result } = renderHook(() => useIsMobile());
      
      // Get the change handler
      const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
      
      // Initially mobile
      expect(result.current).toBe(true);
      
      // Simulate window resize to desktop
      act(() => {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          value: 1024,
        });
        changeHandler();
      });
      
      expect(result.current).toBe(false);
    });

    it('should clean up event listener on unmount', () => {
      const { unmount } = renderHook(() => useIsMobile());
      
      expect(mockMediaQueryList.addEventListener).toHaveBeenCalled();
      
      unmount();
      
      expect(mockMediaQueryList.removeEventListener).toHaveBeenCalledWith(
        'change',
        expect.any(Function)
      );
    });

    it('should handle multiple resize events correctly', () => {
      const { result } = renderHook(() => useIsMobile());
      
      const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
      
      // Start desktop
      expect(result.current).toBe(false);
      
      // Resize to mobile
      act(() => {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          value: 400,
        });
        changeHandler();
      });
      expect(result.current).toBe(true);
      
      // Resize to tablet
      act(() => {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          value: 600,
        });
        changeHandler();
      });
      expect(result.current).toBe(true);
      
      // Resize to desktop
      act(() => {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          value: 900,
        });
        changeHandler();
      });
      expect(result.current).toBe(false);
    });

    it('should handle edge case widths correctly', () => {
      const { result } = renderHook(() => useIsMobile());
      
      const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
      
      // Test width of 0
      act(() => {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          value: 0,
        });
        changeHandler();
      });
      expect(result.current).toBe(true);
      
      // Test very large width
      act(() => {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          value: 9999,
        });
        changeHandler();
      });
      expect(result.current).toBe(false);
      
      // Test width of 1
      act(() => {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          value: 1,
        });
        changeHandler();
      });
      expect(result.current).toBe(true);
    });

    it('should return boolean value consistently', () => {
      const { result } = renderHook(() => useIsMobile());
      
      expect(typeof result.current).toBe('boolean');
      
      const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
      
      act(() => {
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          value: 320,
        });
        changeHandler();
      });
      
      expect(typeof result.current).toBe('boolean');
    });

    it('should handle same width multiple times without unnecessary updates', () => {
      const { result } = renderHook(() => useIsMobile());
      
      const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
      const initialValue = result.current;
      
      // Call change handler multiple times with same width
      act(() => {
        changeHandler();
        changeHandler();
        changeHandler();
      });
      
      expect(result.current).toBe(initialValue);
    });
  });

  describe('MOBILE_BREAKPOINT constant', () => {
    it('should use 768px as the mobile breakpoint', () => {
      renderHook(() => useIsMobile());
      
      // Verify the media query uses 767px (768 - 1)
      expect(mockMatchMedia).toHaveBeenCalledWith('(max-width: 767px)');
    });
  });

  describe('Performance', () => {
    it('should not cause memory leaks with multiple hook instances', () => {
      const hooks = [];
      
      // Create multiple hook instances
      for (let i = 0; i < 10; i++) {
        hooks.push(renderHook(() => useIsMobile()));
      }
      
      // Each should have its own event listener
      expect(mockMediaQueryList.addEventListener).toHaveBeenCalledTimes(10);
      
      // Unmount all hooks
      hooks.forEach(hook => hook.unmount());
      
      // All event listeners should be removed
      expect(mockMediaQueryList.removeEventListener).toHaveBeenCalledTimes(10);
    });

    it('should handle rapid resize events efficiently', () => {
      const { result } = renderHook(() => useIsMobile());
      
      const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
      
      const start = performance.now();
      
      // Simulate rapid resize events
      act(() => {
        for (let i = 0; i < 100; i++) {
          Object.defineProperty(window, 'innerWidth', {
            writable: true,
            value: i % 2 === 0 ? 320 : 1024,
          });
          changeHandler();
        }
      });
      
      const end = performance.now();
      
      expect(end - start).toBeLessThan(50); // Should complete quickly
      expect(typeof result.current).toBe('boolean');
    });
  });

  describe('Error handling', () => {
    it('should handle missing matchMedia gracefully', () => {
      // Temporarily remove matchMedia
      const originalMatchMedia = window.matchMedia;
      delete (window as unknown as { matchMedia?: unknown }).matchMedia;
      
      expect(() => {
        renderHook(() => useIsMobile());
      }).toThrow(); // This will throw, which is expected behavior
      
      // Restore matchMedia
      window.matchMedia = originalMatchMedia;
    });

    it('should handle addEventListener errors gracefully', () => {
      // Mock addEventListener to throw an error
      mockMediaQueryList.addEventListener.mockImplementation(() => {
        throw new Error('addEventListener failed');
      });
      
      expect(() => {
        renderHook(() => useIsMobile());
      }).toThrow('addEventListener failed');
    });
  });
});