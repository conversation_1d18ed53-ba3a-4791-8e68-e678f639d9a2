/**
 * Unit Tests for buildSqlQuery Function
 * 
 * These tests verify that the SQL query building function in the flexible-kpis endpoint
 * correctly constructs SQL queries based on various parameter combinations.
 */

// Import the function to test
// Note: We need to extract this function for unit testing
// For now, we'll create a mock implementation based on the original
// Later, we should refactor the original code to make this function more testable

// Import shared types
import {
  ValidatedParams
} from '../../../utils/test-types';

// Mock implementation of buildSqlQuery for testing
function buildSqlQuery(
  params: ValidatedParams,
  baseKpisToFetch: Set<string>,
  tokenInfo: { isImpersonating?: boolean, isSuperAdmin?: boolean, tokenBrands?: number[] }
): { query: string; queryParams: (string | number | number[])[] } {
  let sql = "";
  const queryParams: (string | number | number[])[] = [];
  let paramIndex = 1;
  const conditions: string[] = [];
  const selectList: string[] = [];
  const groupByList: string[] = [];

  // -- SELECT Clause --
  selectList.push(`DATE_TRUNC('${params.groupByTime}', k.date)::DATE::VARCHAR AS date`);
  groupByList.push("1");

  selectList.push("k.kpi_name");
  groupByList.push("2");

  if (params.groupByDimension) {
    if (params.groupByDimension === 'brand') {
      selectList.push("k.brand AS dimension");
      groupByList.push("3");
    } else if (params.groupByDimension === 'brandGroup') {
      selectList.push("b.group AS dimension");
      groupByList.push("3");
    }
  }

  const valueExpression = params.currency === 'USD'
    ? `k.kpi_value / (CASE WHEN k.raw_kpi_unit = 'CAD' THEN COALESCE(ex.exchange_rate, 1) ELSE 1 END)`
    : `k.kpi_value`;
  selectList.push(`SUM(${valueExpression})::FLOAT8 AS value`);

  // -- JOIN Clause --
  let joins = "FROM dwh_ai.ai_reporting_ds_kpis k";
  joins += " LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name";

  if (params.currency === 'USD') {
    joins += `
      LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
           ON ex.from_currency = 'USD'
          AND ex.to_currency = 'CAD'
          AND ex.snapshot_date::DATE = k.date::DATE`;
  }

  // -- WHERE Clause --
  if (params.startDate) {
    conditions.push(`k.date >= $${paramIndex++}`);
    queryParams.push(params.startDate);
  }
  if (params.endDate) {
    conditions.push(`k.date <= $${paramIndex++}`);
    queryParams.push(params.endDate);
  }

  // --- Brand Filtering Logic based on Token ---
  const urlBrandNames = params.brands;
  const tokenBrandIdsFromAuth = tokenInfo.tokenBrands;
  const isSuperAdminCtx = tokenInfo.isSuperAdmin ?? false;
  const isImpersonatingCtx = tokenInfo.isImpersonating ?? false;

  const isPrivilegedViewer = isSuperAdminCtx && !isImpersonatingCtx;

  if (isPrivilegedViewer) {
    // Super Admin, not impersonating: Filter by URL brands if provided
    if (urlBrandNames.length > 0) {
      const brandNamePlaceholders = urlBrandNames.map(() => `$${paramIndex++}`).join(',');
      conditions.push(`k.brand IN (${brandNamePlaceholders})`);
      queryParams.push(...urlBrandNames);
    }
  } else {
    // Regular user OR any Impersonating user
    if (!tokenBrandIdsFromAuth || tokenBrandIdsFromAuth.length === 0) {
      conditions.push("1=0");
    } else {
      if (urlBrandNames.length > 0) {
        const urlBrandNamePlaceholders = urlBrandNames.map(() => `$${paramIndex++}`).join(',');
        conditions.push(`k.brand IN (${urlBrandNamePlaceholders})`);
        queryParams.push(...urlBrandNames);

        const tokenBrandIdPlaceholders = tokenBrandIdsFromAuth.map(() => `$${paramIndex++}`).join(',');
        conditions.push(`b.brand_id IN (${tokenBrandIdPlaceholders})`);
        queryParams.push(...tokenBrandIdsFromAuth);
      } else {
        const tokenBrandIdPlaceholders = tokenBrandIdsFromAuth.map(() => `$${paramIndex++}`).join(',');
        conditions.push(`b.brand_id IN (${tokenBrandIdPlaceholders})`);
        queryParams.push(...tokenBrandIdsFromAuth);
      }
    }
  }

  // Conditionally add the 'Selling Actively' stage filter
  let applyStageFilter = true;
  if ((isImpersonatingCtx || !isSuperAdminCtx) && (tokenBrandIdsFromAuth && tokenBrandIdsFromAuth.length > 0)) {
    applyStageFilter = false;
  }

  if (applyStageFilter) {
    conditions.push(`b.stage = 'Selling Actively'`);
  }

  if (params.brandGroups.length > 0) {
    const groupPlaceholders = params.brandGroups.map(() => `$${paramIndex++}`).join(',');
    conditions.push(`b.group IN (${groupPlaceholders})`);
    queryParams.push(...params.brandGroups);
  }
  
  // Filter by sales channels if provided
  if (params.salesChannels.length > 0) {
    const channelConditions = params.salesChannels.map(() => `LOWER(k.sales_channel_type) = LOWER($${paramIndex++})`).join(' OR ');
    conditions.push(`(${channelConditions})`);
    queryParams.push(...params.salesChannels);
  }
  
  // Filter by countries if provided
  if (params.countryNames.length > 0) {
    const countryConditions = params.countryNames.map(() => `LOWER(k.country_name) = LOWER($${paramIndex++})`).join(' OR ');
    conditions.push(`(${countryConditions})`);
    queryParams.push(...params.countryNames);
  }

  if (baseKpisToFetch.size > 0) {
    const baseKpisArray = Array.from(baseKpisToFetch);
    const kpiPlaceholders = baseKpisArray.map((_, i) => `$${paramIndex + i}`).join(',');
    conditions.push(`k.kpi_name IN (${kpiPlaceholders})`);
    baseKpisArray.forEach(kpi => queryParams.push(kpi));
    paramIndex += baseKpisArray.length;
  } else {
    conditions.push("1=0");
  }

  // -- Assemble Query --
  sql = `
    SELECT
      ${selectList.join(',\n      ')}
    ${joins}
    WHERE ${conditions.length > 0 ? conditions.join(' AND ') : '1=1'}
    GROUP BY ${groupByList.join(', ')}
    ORDER BY ${groupByList.join(', ')} ASC;
  `;

  return { query: sql, queryParams: queryParams };
}

// Unit tests for buildSqlQuery
describe('buildSqlQuery', () => {
  // Test with minimal parameters
  it('should build a basic query with minimal parameters', () => {
    const params: ValidatedParams = {
      currency: 'CAD',
      brands: [],
      brandGroups: [],
      requestedKpis: ['Net Revenue', 'Gross Margin'],
      groupByTime: 'day',
      salesChannels: [],
      countryNames: []
    };
    
    const baseKpisToFetch = new Set(['Net Revenue', 'Gross Margin']);
    const tokenInfo = { isSuperAdmin: true, isImpersonating: false };
    
    const { query, queryParams } = buildSqlQuery(params, baseKpisToFetch, tokenInfo);
    
    // Verify the query structure
    expect(query).toContain("SELECT");
    expect(query).toContain("DATE_TRUNC('day', k.date)::DATE::VARCHAR AS date");
    expect(query).toContain("k.kpi_name");
    expect(query).toContain("SUM(k.kpi_value)::FLOAT8 AS value");
    expect(query).toContain("FROM dwh_ai.ai_reporting_ds_kpis k");
    expect(query).toContain("LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name");
    expect(query).toContain("b.stage = 'Selling Actively'");
    expect(query).toContain("k.kpi_name IN ($1,$2)");
    
    // Verify the query parameters
    expect(queryParams).toHaveLength(2);
    expect(queryParams).toContain('Net Revenue');
    expect(queryParams).toContain('Gross Margin');
  });
  
  // Test with date range
  it('should include date range parameters in the query', () => {
    const params: ValidatedParams = {
      startDate: '2025-01-01',
      endDate: '2025-01-31',
      currency: 'CAD',
      brands: [],
      brandGroups: [],
      requestedKpis: ['Net Revenue'],
      groupByTime: 'day',
      salesChannels: [],
      countryNames: []
    };
    
    const baseKpisToFetch = new Set(['Net Revenue']);
    const tokenInfo = { isSuperAdmin: true, isImpersonating: false };
    
    const { query, queryParams } = buildSqlQuery(params, baseKpisToFetch, tokenInfo);
    
    // Verify the query includes date conditions
    expect(query).toContain("k.date >= $1");
    expect(query).toContain("k.date <= $2");
    expect(query).toContain("k.kpi_name IN ($3)");
    
    // Verify the query parameters
    expect(queryParams).toHaveLength(3);
    expect(queryParams[0]).toBe('2025-01-01');
    expect(queryParams[1]).toBe('2025-01-31');
    expect(queryParams[2]).toBe('Net Revenue');
  });
  
  // Test with USD currency
  it('should include exchange rate join for USD currency', () => {
    const params: ValidatedParams = {
      currency: 'USD',
      brands: [],
      brandGroups: [],
      requestedKpis: ['Net Revenue'],
      groupByTime: 'day',
      salesChannels: [],
      countryNames: []
    };
    
    const baseKpisToFetch = new Set(['Net Revenue']);
    const tokenInfo = { isSuperAdmin: true, isImpersonating: false };
    
    const { query } = buildSqlQuery(params, baseKpisToFetch, tokenInfo);
    
    // Verify the query includes exchange rate join
    expect(query).toContain("LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex");
    expect(query).toContain("k.kpi_value / (CASE WHEN k.raw_kpi_unit = 'CAD' THEN COALESCE(ex.exchange_rate, 1) ELSE 1 END)");
  });
  
  // Test with brand dimension
  it('should include brand dimension in select and group by', () => {
    const params: ValidatedParams = {
      currency: 'CAD',
      brands: [],
      brandGroups: [],
      requestedKpis: ['Net Revenue'],
      groupByTime: 'day',
      groupByDimension: 'brand',
      salesChannels: [],
      countryNames: []
    };
    
    const baseKpisToFetch = new Set(['Net Revenue']);
    const tokenInfo = { isSuperAdmin: true, isImpersonating: false };
    
    const { query } = buildSqlQuery(params, baseKpisToFetch, tokenInfo);
    
    // Verify the query includes brand dimension
    expect(query).toContain("k.brand AS dimension");
    expect(query).toContain("GROUP BY 1, 2, 3");
  });
  
  // Test with brand group dimension
  it('should include brand group dimension in select and group by', () => {
    const params: ValidatedParams = {
      currency: 'CAD',
      brands: [],
      brandGroups: [],
      requestedKpis: ['Net Revenue'],
      groupByTime: 'day',
      groupByDimension: 'brandGroup',
      salesChannels: [],
      countryNames: []
    };
    
    const baseKpisToFetch = new Set(['Net Revenue']);
    const tokenInfo = { isSuperAdmin: true, isImpersonating: false };
    
    const { query } = buildSqlQuery(params, baseKpisToFetch, tokenInfo);
    
    // Verify the query includes brand group dimension
    expect(query).toContain("b.group AS dimension");
    expect(query).toContain("GROUP BY 1, 2, 3");
  });
  
  // Test with brand filtering
  it('should filter by brands when provided', () => {
    const params: ValidatedParams = {
      currency: 'CAD',
      brands: ['Brand1', 'Brand2'],
      brandGroups: [],
      requestedKpis: ['Net Revenue'],
      groupByTime: 'day',
      salesChannels: [],
      countryNames: []
    };
    
    const baseKpisToFetch = new Set(['Net Revenue']);
    const tokenInfo = { isSuperAdmin: true, isImpersonating: false };
    
    const { query, queryParams } = buildSqlQuery(params, baseKpisToFetch, tokenInfo);
    
    // Verify the query includes brand filtering
    expect(query).toContain("k.brand IN ($1,$2)");
    expect(query).toContain("k.kpi_name IN ($3)");
    
    // Verify the query parameters
    expect(queryParams).toHaveLength(3);
    expect(queryParams[0]).toBe('Brand1');
    expect(queryParams[1]).toBe('Brand2');
    expect(queryParams[2]).toBe('Net Revenue');
  });
  
  // Test with regular user (not super admin)
  it('should handle regular user access restrictions', () => {
    const params: ValidatedParams = {
      currency: 'CAD',
      brands: [],
      brandGroups: [],
      requestedKpis: ['Net Revenue'],
      groupByTime: 'day',
      salesChannels: [],
      countryNames: []
    };
    
    const baseKpisToFetch = new Set(['Net Revenue']);
    const tokenInfo = { 
      isSuperAdmin: false, 
      isImpersonating: false,
      tokenBrands: [1, 2, 3]
    };
    
    const { query, queryParams } = buildSqlQuery(params, baseKpisToFetch, tokenInfo);
    
    // Verify the query includes brand ID filtering
    expect(query).toContain("b.brand_id IN ($1,$2,$3)");
    expect(query).toContain("k.kpi_name IN ($4)");
    
    // Verify the query parameters
    expect(queryParams).toHaveLength(4);
    expect(queryParams[0]).toBe(1);
    expect(queryParams[1]).toBe(2);
    expect(queryParams[2]).toBe(3);
    expect(queryParams[3]).toBe('Net Revenue');
  });
});