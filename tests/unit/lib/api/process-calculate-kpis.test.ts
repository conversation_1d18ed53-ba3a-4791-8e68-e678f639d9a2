/**
 * Unit Tests for processAndCalculateKpis Function
 *
 * These tests verify that the KPI calculation logic in the flexible-kpis endpoint
 * correctly processes raw data and calculates derived KPIs.
 */

// Import shared types
import {
  ProcessedBaseKpiMap,
  RawDbData,
  ValidGroupByDimension
} from '../../../utils/test-types';

// Helper function to calculate percentages
function calculatePercent(numerator: number | null | undefined, denominator: number | null | undefined): number | null {
  if (denominator != null && denominator !== 0 && numerator != null) {
    // Return the decimal value (e.g., 0.25 for 25%)
    return numerator / denominator;
  } else {
    return null;
  }
}

// Mock implementation of processAndCalculateKpis for testing
function processAndCalculateKpis(
  dbRows: RawDbData[],
  requestedKpis: string[],
  groupByDimension?: ValidGroupByDimension
): { processedData: ProcessedBaseKpiMap; allDates: Set<string> } {
  const processedData: ProcessedBaseKpiMap = {};
  const allDates = new Set<string>();
  const allDimensions = new Set<string>();

  // 1. Populate base KPIs and collect all dates/dimensions
  dbRows.forEach(row => {
    const dimensionKey = groupByDimension ? (row.dimension ?? 'Unknown Dimension') : 'all';
    allDimensions.add(dimensionKey);
    allDates.add(row.date);

    if (!processedData[dimensionKey]) {
      processedData[dimensionKey] = {};
    }
    if (!processedData[dimensionKey][row.kpi_name]) {
      processedData[dimensionKey][row.kpi_name] = { timeSeriesMap: {}, total: 0 };
    }

    // Ensure value is treated as number before adding
    const valueToAdd = typeof row.value === 'number' ? row.value : 0;

    processedData[dimensionKey][row.kpi_name].timeSeriesMap[row.date] = valueToAdd;
    // Safely add to total, initializing if necessary
    processedData[dimensionKey][row.kpi_name].total = (processedData[dimensionKey][row.kpi_name].total ?? 0) + valueToAdd;
  });

  const sortedDates = Array.from(allDates).sort();

  // 2. Calculate derived KPIs
  allDimensions.forEach(dimKey => {
    // First, ensure all requested KPIs have a structure, even if they weren't in the DB results
    requestedKpis.forEach(kpiName => {
      if (!processedData[dimKey]) {
        processedData[dimKey] = {};
      }
      if (!processedData[dimKey][kpiName]) {
        processedData[dimKey][kpiName] = { timeSeriesMap: {}, total: null };
      }
    });

    // Helper functions scoped to current dimension
    const getBaseValue = (baseKpi: string, date: string): number | null => {
      return processedData[dimKey]?.[baseKpi]?.timeSeriesMap?.[date] ?? null;
    }
    const getBaseTotal = (baseKpi: string): number | null => {
      return processedData[dimKey]?.[baseKpi]?.total ?? null;
    }

    // Calculate percentage KPIs
    requestedKpis.forEach(kpiName => {
      // Skip if this KPI was directly fetched from the database and already has values
      // AND it's not a percentage KPI (which we need to calculate)
      if (!kpiName.startsWith('%') &&
          processedData[dimKey][kpiName]?.total !== null &&
          Object.keys(processedData[dimKey][kpiName]?.timeSeriesMap || {}).length > 0) {
        return;
      }

      // Calculate time series for each KPI
      sortedDates.forEach(date => {
        let calculatedValue: number | null = null;

        // Add specific calculation logic
        if (kpiName === 'Contribution Margin') {
          const gm = getBaseValue('Gross Margin', date);
          const ad = getBaseValue('Adspend', date);
          calculatedValue = (gm === null || ad === null) ? null : gm - ad;
        } else if (kpiName === '% Gross Margin') {
          calculatedValue = calculatePercent(getBaseValue('Gross Margin', date), getBaseValue('Net Revenue', date));
        } else if (kpiName === '% Contribution Margin') {
          const gm = getBaseValue('Gross Margin', date);
          const ad = getBaseValue('Adspend', date);
          const cm = (gm === null || ad === null) ? null : gm - ad;
          calculatedValue = calculatePercent(cm, getBaseValue('Net Revenue', date));
        } else if (kpiName === '% Adspend') {
          calculatedValue = calculatePercent(getBaseValue('Adspend', date), getBaseValue('Net Revenue', date));
        } else if (kpiName === '% Landed Cost') {
          calculatedValue = calculatePercent(getBaseValue('Landed Cost', date), getBaseValue('Net Revenue', date));
        } else if (kpiName === '% Fulfillment Cost') {
          calculatedValue = calculatePercent(getBaseValue('Fulfillment Cost', date), getBaseValue('Net Revenue', date));
        } else if (kpiName === '% Transaction Cost') {
          calculatedValue = calculatePercent(getBaseValue('Transaction Cost', date), getBaseValue('Net Revenue', date));
        } else if (kpiName === '% Discount') {
          calculatedValue = calculatePercent(getBaseValue('Discount', date), getBaseValue('Gross Revenue', date));
        } else if (kpiName === '% Refund') {
          calculatedValue = calculatePercent(getBaseValue('Refund', date), getBaseValue('Gross Revenue', date));
        }

        if (calculatedValue !== null) {
          processedData[dimKey][kpiName].timeSeriesMap[date] = calculatedValue;
        }
      });

      // Calculate summary/total
      let calculatedTotal: number | null = null;
      if (kpiName === 'Contribution Margin') {
        const gmT = getBaseTotal('Gross Margin');
        const adT = getBaseTotal('Adspend');
        calculatedTotal = (gmT === null || adT === null) ? null : gmT - adT;
      } else if (kpiName === '% Gross Margin') {
        calculatedTotal = calculatePercent(getBaseTotal('Gross Margin'), getBaseTotal('Net Revenue'));
      } else if (kpiName === '% Contribution Margin') {
        const gmT = getBaseTotal('Gross Margin');
        const adT = getBaseTotal('Adspend');
        const cmTotal = (gmT === null || adT === null) ? null : gmT - adT;
        calculatedTotal = calculatePercent(cmTotal, getBaseTotal('Net Revenue'));
      } else if (kpiName === '% Adspend') {
        calculatedTotal = calculatePercent(getBaseTotal('Adspend'), getBaseTotal('Net Revenue'));
      } else if (kpiName === '% Landed Cost') {
        calculatedTotal = calculatePercent(getBaseTotal('Landed Cost'), getBaseTotal('Net Revenue'));
      } else if (kpiName === '% Fulfillment Cost') {
        calculatedTotal = calculatePercent(getBaseTotal('Fulfillment Cost'), getBaseTotal('Net Revenue'));
      } else if (kpiName === '% Transaction Cost') {
        calculatedTotal = calculatePercent(getBaseTotal('Transaction Cost'), getBaseTotal('Net Revenue'));
      } else if (kpiName === '% Discount') {
        calculatedTotal = calculatePercent(getBaseTotal('Discount'), getBaseTotal('Gross Revenue'));
      } else if (kpiName === '% Refund') {
        calculatedTotal = calculatePercent(getBaseTotal('Refund'), getBaseTotal('Gross Revenue'));
      }

      if (calculatedTotal !== null) {
        processedData[dimKey][kpiName].total = calculatedTotal;
      }
    });
  });

  return { processedData, allDates };
}

// Unit tests for processAndCalculateKpis
describe('processAndCalculateKpis', () => {
  // Test with simple data, no dimensions
  it('should process basic KPI data without dimensions', () => {
    const dbRows: RawDbData[] = [
      { date: '2025-01-01', kpi_name: 'Net Revenue', value: 1000 },
      { date: '2025-01-02', kpi_name: 'Net Revenue', value: 2000 },
      { date: '2025-01-01', kpi_name: 'Gross Margin', value: 500 },
      { date: '2025-01-02', kpi_name: 'Gross Margin', value: 1000 }
    ];

    const requestedKpis = ['Net Revenue', 'Gross Margin', '% Gross Margin'];

    const { processedData, allDates } = processAndCalculateKpis(dbRows, requestedKpis);

    // Verify dates
    expect(allDates.size).toBe(2);
    expect(allDates.has('2025-01-01')).toBe(true);
    expect(allDates.has('2025-01-02')).toBe(true);

    // Verify processed data structure
    expect(processedData).toHaveProperty('all');
    expect(processedData.all).toHaveProperty('Net Revenue');
    expect(processedData.all).toHaveProperty('Gross Margin');
    expect(processedData.all).toHaveProperty('% Gross Margin');

    // Verify Net Revenue values
    expect(processedData.all['Net Revenue'].total).toBe(3000);
    expect(processedData.all['Net Revenue'].timeSeriesMap['2025-01-01']).toBe(1000);
    expect(processedData.all['Net Revenue'].timeSeriesMap['2025-01-02']).toBe(2000);

    // Verify Gross Margin values
    expect(processedData.all['Gross Margin'].total).toBe(1500);
    expect(processedData.all['Gross Margin'].timeSeriesMap['2025-01-01']).toBe(500);
    expect(processedData.all['Gross Margin'].timeSeriesMap['2025-01-02']).toBe(1000);

    // Verify % Gross Margin calculation
    expect(processedData.all['% Gross Margin'].total).toBe(0.5); // 1500 / 3000 = 0.5 (50%)
    expect(processedData.all['% Gross Margin'].timeSeriesMap['2025-01-01']).toBe(0.5); // 500 / 1000 = 0.5 (50%)
    expect(processedData.all['% Gross Margin'].timeSeriesMap['2025-01-02']).toBe(0.5); // 1000 / 2000 = 0.5 (50%)
  });

  // Test with dimensions
  it('should process KPI data with brand dimension', () => {
    const dbRows: RawDbData[] = [
      { date: '2025-01-01', kpi_name: 'Net Revenue', value: 1000, dimension: 'Brand1' },
      { date: '2025-01-01', kpi_name: 'Net Revenue', value: 500, dimension: 'Brand2' },
      { date: '2025-01-01', kpi_name: 'Gross Margin', value: 500, dimension: 'Brand1' },
      { date: '2025-01-01', kpi_name: 'Gross Margin', value: 200, dimension: 'Brand2' }
    ];

    const requestedKpis = ['Net Revenue', 'Gross Margin', '% Gross Margin'];

    const { processedData } = processAndCalculateKpis(dbRows, requestedKpis, 'brand');

    // Verify dimensions
    expect(processedData).toHaveProperty('Brand1');
    expect(processedData).toHaveProperty('Brand2');

    // Verify Brand1 values
    expect(processedData.Brand1['Net Revenue'].total).toBe(1000);
    expect(processedData.Brand1['Gross Margin'].total).toBe(500);
    expect(processedData.Brand1['% Gross Margin'].total).toBe(0.5); // 500 / 1000 = 0.5 (50%)

    // Verify Brand2 values
    expect(processedData.Brand2['Net Revenue'].total).toBe(500);
    expect(processedData.Brand2['Gross Margin'].total).toBe(200);
    expect(processedData.Brand2['% Gross Margin'].total).toBe(0.4); // 200 / 500 = 0.4 (40%)
  });

  // Test Contribution Margin calculation
  it('should correctly calculate Contribution Margin', () => {
    const dbRows: RawDbData[] = [
      { date: '2025-01-01', kpi_name: 'Net Revenue', value: 1000 },
      { date: '2025-01-01', kpi_name: 'Gross Margin', value: 500 },
      { date: '2025-01-01', kpi_name: 'Adspend', value: 200 }
    ];

    const requestedKpis = ['Net Revenue', 'Gross Margin', 'Adspend', 'Contribution Margin', '% Contribution Margin'];

    const { processedData } = processAndCalculateKpis(dbRows, requestedKpis);

    // Verify Contribution Margin calculation
    expect(processedData.all['Contribution Margin'].total).toBe(300); // 500 - 200 = 300
    expect(processedData.all['Contribution Margin'].timeSeriesMap['2025-01-01']).toBe(300);

    // Verify % Contribution Margin calculation
    expect(processedData.all['% Contribution Margin'].total).toBe(0.3); // 300 / 1000 = 0.3 (30%)
    expect(processedData.all['% Contribution Margin'].timeSeriesMap['2025-01-01']).toBe(0.3);
  });

  // Test handling of missing data
  it('should handle missing data gracefully', () => {
    const dbRows: RawDbData[] = [
      { date: '2025-01-01', kpi_name: 'Net Revenue', value: 1000 },
      // Missing Gross Margin for this date
    ];

    const requestedKpis = ['Net Revenue', 'Gross Margin', '% Gross Margin'];

    const { processedData } = processAndCalculateKpis(dbRows, requestedKpis);

    // Verify Net Revenue is present
    expect(processedData.all['Net Revenue'].total).toBe(1000);
    expect(processedData.all['Net Revenue'].timeSeriesMap['2025-01-01']).toBe(1000);

    // Verify Gross Margin is null
    expect(processedData.all['Gross Margin'].total).toBe(null);
    expect(processedData.all['Gross Margin'].timeSeriesMap['2025-01-01']).toBeUndefined();

    // Verify % Gross Margin is null
    expect(processedData.all['% Gross Margin'].total).toBe(null);
    expect(processedData.all['% Gross Margin'].timeSeriesMap['2025-01-01']).toBeUndefined();
  });

  // Test with zero values
  it('should handle zero values correctly in percentage calculations', () => {
    const dbRows: RawDbData[] = [
      { date: '2025-01-01', kpi_name: 'Net Revenue', value: 0 },
      { date: '2025-01-01', kpi_name: 'Gross Margin', value: 0 }
    ];

    const requestedKpis = ['Net Revenue', 'Gross Margin', '% Gross Margin'];

    const { processedData } = processAndCalculateKpis(dbRows, requestedKpis);

    // Verify Net Revenue and Gross Margin are zero
    expect(processedData.all['Net Revenue'].total).toBe(0);
    expect(processedData.all['Gross Margin'].total).toBe(0);

    // Verify % Gross Margin is null (division by zero)
    expect(processedData.all['% Gross Margin'].total).toBe(null);
  });

  // Test percentage calculations use correct denominators
  it('should use Gross Revenue for Discount and Refund percentages, Net Revenue for others', () => {
    const dbRows: RawDbData[] = [
      { date: '2025-01-01', kpi_name: 'Gross Revenue', value: 1000 },
      { date: '2025-01-01', kpi_name: 'Net Revenue', value: 800 }, // After discounts and refunds
      { date: '2025-01-01', kpi_name: 'Discount', value: 100 },
      { date: '2025-01-01', kpi_name: 'Refund', value: 100 },
      { date: '2025-01-01', kpi_name: 'Gross Margin', value: 400 },
      { date: '2025-01-01', kpi_name: 'Adspend', value: 80 }
    ];

    const requestedKpis = [
      'Gross Revenue', 'Net Revenue', 'Discount', 'Refund', 'Gross Margin', 'Adspend',
      '% Discount', '% Refund', '% Gross Margin', '% Adspend'
    ];

    const { processedData } = processAndCalculateKpis(dbRows, requestedKpis);

    // Verify % Discount uses Gross Revenue as denominator
    expect(processedData.all['% Discount'].total).toBe(0.1); // 100 / 1000 = 0.1 (10%)

    // Verify % Refund uses Gross Revenue as denominator
    expect(processedData.all['% Refund'].total).toBe(0.1); // 100 / 1000 = 0.1 (10%)

    // Verify % Gross Margin uses Net Revenue as denominator
    expect(processedData.all['% Gross Margin'].total).toBe(0.5); // 400 / 800 = 0.5 (50%)

    // Verify % Adspend uses Net Revenue as denominator
    expect(processedData.all['% Adspend'].total).toBe(0.1); // 80 / 800 = 0.1 (10%)
  });
});