import { getRedshiftPool, resetPool, testRedshiftConnection } from '@/lib/api/redshift';

import { Pool } from 'pg';

// Mock the pg module
const mockPool = {
  connect: jest.fn(),
  end: jest.fn(),
  on: jest.fn(),
};

const mockClient = {
  query: jest.fn(),
  release: jest.fn(),
};

jest.mock('pg', () => ({
  Pool: jest.fn(() => mockPool),
}));

describe('lib/api/redshift', () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    // Save original environment
    originalEnv = { ...process.env };
    
    // Reset the pool before each test
    resetPool();
    
    // Clear all mocks
    jest.clearAllMocks();
    
    // Setup default mock behavior
    mockPool.connect.mockResolvedValue(mockClient);
    mockClient.query.mockResolvedValue({ rows: [{ '?column?': 1 }] });
    
    // Mock console methods
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    jest.spyOn(console, 'warn').mockImplementation();
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    
    // Restore console methods
    jest.restoreAllMocks();
  });

  describe('getRedshiftPool', () => {
    it('should return null when required environment variables are missing', () => {
      // Remove all Redshift environment variables
      delete process.env.REDSHIFT_HOST;
      delete process.env.REDSHIFT_PORT;
      delete process.env.REDSHIFT_DATABASE;
      delete process.env.REDSHIFT_USER;
      delete process.env.REDSHIFT_PASSWORD;

      const pool = getRedshiftPool();

      expect(pool).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('Missing Redshift environment variables')
      );
      expect(console.warn).toHaveBeenCalledWith(
        'Redshift connection will not be available for testing'
      );
    });

    it('should return null when only some environment variables are missing', () => {
      // Set only some environment variables
      process.env.REDSHIFT_HOST = 'test-host';
      process.env.REDSHIFT_PORT = '5439';
      // Explicitly delete the missing ones to ensure they're undefined
      delete process.env.REDSHIFT_DATABASE;
      delete process.env.REDSHIFT_USER;
      delete process.env.REDSHIFT_PASSWORD;

      const pool = getRedshiftPool();

      expect(pool).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('REDSHIFT_DATABASE, REDSHIFT_USER, REDSHIFT_PASSWORD')
      );
    });

    it('should create pool when all environment variables are present', () => {
      // Set all required environment variables
      process.env.REDSHIFT_HOST = 'test-host.redshift.amazonaws.com';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const pool = getRedshiftPool();

      expect(pool).toBe(mockPool);
      expect(console.log).toHaveBeenCalledWith(
        'Creating Redshift connection pool with config:',
        expect.objectContaining({
          host: 'test-host.redshift.amazonaws.com',
          port: 5439,
          database: 'test-database',
          user: 'REDSHIFT_USER is set',
          password: 'REDSHIFT_PASSWORD is set',
          ssl: true
        })
      );
      expect(console.log).toHaveBeenCalledWith('Redshift connection pool created successfully.');
    });

    it('should use default port when REDSHIFT_PORT is not provided', () => {
      // Set all required environment variables except port
      process.env.REDSHIFT_HOST = 'test-host.redshift.amazonaws.com';
      delete process.env.REDSHIFT_PORT;
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const pool = getRedshiftPool();

      expect(pool).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('REDSHIFT_PORT')
      );
    });

    it('should return the same pool instance on subsequent calls', () => {
      // Set all required environment variables
      process.env.REDSHIFT_HOST = 'test-host.redshift.amazonaws.com';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const pool1 = getRedshiftPool();
      const pool2 = getRedshiftPool();

      expect(pool1).toBe(pool2);
      expect(Pool).toHaveBeenCalledTimes(1); // Pool constructor should only be called once
    });

    it('should handle pool creation errors gracefully', () => {
      // Set all required environment variables
      process.env.REDSHIFT_HOST = 'test-host.redshift.amazonaws.com';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      // Mock Pool constructor to throw an error
      const PoolMock = require('pg').Pool;
      PoolMock.mockImplementationOnce(() => {
        throw new Error('Pool creation failed');
      });

      const pool = getRedshiftPool();

      expect(pool).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        'Failed to create Redshift connection pool:',
        expect.any(Error)
      );
    });

    it('should configure pool with correct SSL settings', () => {
      // Set all required environment variables
      process.env.REDSHIFT_HOST = 'test-host.redshift.amazonaws.com';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const pool = getRedshiftPool();

      expect(pool).toBe(mockPool);
      expect(Pool).toHaveBeenCalledWith(
        expect.objectContaining({
          ssl: {
            rejectUnauthorized: false
          }
        })
      );
    });

    it('should set up error handler on pool', () => {
      // Set all required environment variables
      process.env.REDSHIFT_HOST = 'test-host.redshift.amazonaws.com';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const pool = getRedshiftPool();

      expect(pool).toBe(mockPool);
      expect(mockPool.on).toHaveBeenCalledWith('error', expect.any(Function));
    });

    it('should handle non-numeric port values', () => {
      // Set all required environment variables with invalid port
      process.env.REDSHIFT_HOST = 'test-host.redshift.amazonaws.com';
      process.env.REDSHIFT_PORT = 'invalid-port';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const pool = getRedshiftPool();

      expect(pool).toBe(mockPool);
      expect(console.log).toHaveBeenCalledWith(
        'Creating Redshift connection pool with config:',
        expect.objectContaining({
          port: NaN, // parseInt('invalid-port', 10) returns NaN
        })
      );
    });
  });

  describe('testRedshiftConnection', () => {
    it('should return false when pool is not available', async () => {
      // Ensure no environment variables are set
      delete process.env.REDSHIFT_HOST;
      delete process.env.REDSHIFT_PORT;
      delete process.env.REDSHIFT_DATABASE;
      delete process.env.REDSHIFT_USER;
      delete process.env.REDSHIFT_PASSWORD;

      const result = await testRedshiftConnection();

      expect(result).toBe(false);
      expect(console.error).toHaveBeenCalledWith('Redshift pool is not available.');
    });

    it('should return true when connection test succeeds', async () => {
      // Set all required environment variables
      process.env.REDSHIFT_HOST = 'test-host.redshift.amazonaws.com';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const result = await testRedshiftConnection();

      expect(result).toBe(true);
      expect(mockPool.connect).toHaveBeenCalled();
      expect(mockClient.query).toHaveBeenCalledWith('SELECT 1');
      expect(mockClient.release).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('Successfully connected to Redshift for testing.');
    });

    it('should return false and handle connection errors', async () => {
      // Set all required environment variables
      process.env.REDSHIFT_HOST = 'test-host.redshift.amazonaws.com';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const connectionError = new Error('Connection failed');
      mockPool.connect.mockRejectedValueOnce(connectionError);

      const result = await testRedshiftConnection();

      expect(result).toBe(false);
      expect(console.error).toHaveBeenCalledWith('Redshift connection test failed:', connectionError);
    });

    it('should return false and handle query errors', async () => {
      // Set all required environment variables
      process.env.REDSHIFT_HOST = 'test-host.redshift.amazonaws.com';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const queryError = new Error('Query failed');
      mockClient.query.mockRejectedValueOnce(queryError);

      const result = await testRedshiftConnection();

      expect(result).toBe(false);
      expect(mockClient.release).toHaveBeenCalled();
      expect(console.error).toHaveBeenCalledWith('Redshift connection test failed:', queryError);
    });

    it('should handle client release properly even when client is undefined', async () => {
      // Set all required environment variables
      process.env.REDSHIFT_HOST = 'test-host.redshift.amazonaws.com';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      // Mock connect to return undefined
      mockPool.connect.mockResolvedValueOnce(undefined);

      // This should not throw an error
      await expect(testRedshiftConnection()).resolves.toBe(false);
    });
  });

  describe('backward compatibility', () => {
    it('should export redshiftPool property for backward compatibility', async () => {
      // Set all required environment variables
      process.env.REDSHIFT_HOST = 'test-host.redshift.amazonaws.com';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const redshiftModule = await import('@/lib/api/redshift');
      
      expect((redshiftModule as any).redshiftPool).toBe(mockPool);
      expect((redshiftModule as any).redshiftPool).toBe(getRedshiftPool());
    });

    it('should return null for redshiftPool when environment variables are missing', async () => {
      // Remove all environment variables
      delete process.env.REDSHIFT_HOST;
      delete process.env.REDSHIFT_PORT;
      delete process.env.REDSHIFT_DATABASE;
      delete process.env.REDSHIFT_USER;
      delete process.env.REDSHIFT_PASSWORD;

      const redshiftModule = await import('@/lib/api/redshift');
      
      expect((redshiftModule as any).redshiftPool).toBeNull();
    });
  });

  describe('Environment variable validation', () => {
    it('should return null when REDSHIFT_HOST is missing', () => {
      delete process.env.REDSHIFT_HOST;
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const pool = getRedshiftPool();
      expect(pool).toBeNull();
    });

    it('should return null when REDSHIFT_PORT is missing', () => {
      process.env.REDSHIFT_HOST = 'test-host';
      delete process.env.REDSHIFT_PORT;
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const pool = getRedshiftPool();
      expect(pool).toBeNull();
    });

    it('should return null when REDSHIFT_DATABASE is missing', () => {
      process.env.REDSHIFT_HOST = 'test-host';
      process.env.REDSHIFT_PORT = '5439';
      delete process.env.REDSHIFT_DATABASE;
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const pool = getRedshiftPool();
      expect(pool).toBeNull();
    });

    it('should return null when REDSHIFT_USER is missing', () => {
      process.env.REDSHIFT_HOST = 'test-host';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      delete process.env.REDSHIFT_USER;
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const pool = getRedshiftPool();
      expect(pool).toBeNull();
    });

    it('should return null when REDSHIFT_PASSWORD is missing', () => {
      process.env.REDSHIFT_HOST = 'test-host';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      delete process.env.REDSHIFT_PASSWORD;

      const pool = getRedshiftPool();
      expect(pool).toBeNull();
    });

    it('should handle empty string environment variables as missing', () => {
      process.env.REDSHIFT_HOST = '';
      process.env.REDSHIFT_PORT = '';
      process.env.REDSHIFT_DATABASE = '';
      process.env.REDSHIFT_USER = '';
      process.env.REDSHIFT_PASSWORD = '';

      const pool = getRedshiftPool();
      expect(pool).toBeNull();
    });
  });

  describe('Performance and memory management', () => {
    it('should not create multiple pools when called multiple times', () => {
      process.env.REDSHIFT_HOST = 'test-host';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const pool1 = getRedshiftPool();
      const pool2 = getRedshiftPool();
      const pool3 = getRedshiftPool();

      expect(pool1).toBe(pool2);
      expect(pool2).toBe(pool3);
      expect(Pool).toHaveBeenCalledTimes(1);
    });

    it('should handle rapid successive calls efficiently', () => {
      process.env.REDSHIFT_HOST = 'test-host';
      process.env.REDSHIFT_PORT = '5439';
      process.env.REDSHIFT_DATABASE = 'test-database';
      process.env.REDSHIFT_USER = 'test-user';
      process.env.REDSHIFT_PASSWORD = 'test-password';

      const pools = Array.from({ length: 100 }, () => getRedshiftPool());
      
      // All pools should be the same instance
      expect(pools.every(pool => pool === pools[0])).toBe(true);
      expect(Pool).toHaveBeenCalledTimes(1);
    });
  });
});
