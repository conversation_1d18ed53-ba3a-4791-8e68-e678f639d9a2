/**
 * @fileoverview Unit tests for lib/auth-helpers.ts
 * Tests authentication helper functions for safe session handling
 * Coverage target: 90%+
 */

import { getSafeServerSession, isAuthenticated } from '@/lib/auth-helpers';

import type { Session } from 'next-auth';
import { getServerSession } from 'next-auth';

// Mock next-auth
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}));

// Mock auth options
jest.mock('@/lib/auth-options', () => ({
  authOptions: {
    providers: [],
    callbacks: {},
  },
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

// Helper function to create mock sessions with proper user structure
const createMockSession = (userOverrides: Partial<Session['user']> = {}): Session => ({
  user: {
    id: 'user-123',
    email: '<EMAIL>',
    name: 'Test User',
    roles: [],
    permissions: [],
    groups: [],
    brands: [],
    ...userOverrides,
  },
  expires: '2024-12-31T23:59:59.999Z',
});

describe('lib/auth-helpers', () => {
  let originalConsoleError: typeof console.error;
  let originalConsoleLog: typeof console.log;

  beforeEach(() => {
    // Mock console methods
    originalConsoleError = console.error;
    originalConsoleLog = console.log;
    console.error = jest.fn();
    console.log = jest.fn();

    // Reset mocks
    mockGetServerSession.mockReset();
  });

  afterEach(() => {
    // Restore console methods
    console.error = originalConsoleError;
    console.log = originalConsoleLog;
  });

  describe('getSafeServerSession', () => {
    it('should return session when getServerSession succeeds', async () => {
      const mockSession = createMockSession();

      mockGetServerSession.mockResolvedValue(mockSession);

      const result = await getSafeServerSession();

      expect(result).toBe(mockSession);
      expect(mockGetServerSession).toHaveBeenCalledWith(expect.any(Object));
    });

    it('should return null when getServerSession returns null', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const result = await getSafeServerSession();

      expect(result).toBeNull();
    });

    it('should return null and log when JWT decoding fails', async () => {
      const jwtError = new Error('JWE decrypt failed');
      jwtError.name = 'JWEDecryptionFailed';
      mockGetServerSession.mockRejectedValue(jwtError);

      const result = await getSafeServerSession();

      expect(result).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        '[Auth Helper] Error getting server session:',
        jwtError
      );
      expect(console.log).toHaveBeenCalledWith(
        '[Auth Helper] JWT decoding failed for unauthenticated user, returning null'
      );
    });

    it('should return null and log when JWT error occurs in message', async () => {
      const jwtError = new Error('Failed to decrypt JWE token');
      mockGetServerSession.mockRejectedValue(jwtError);

      const result = await getSafeServerSession();

      expect(result).toBeNull();
      expect(console.log).toHaveBeenCalledWith(
        '[Auth Helper] JWT decoding failed for unauthenticated user, returning null'
      );
    });

    it('should return null and log when JWE error occurs in error name', async () => {
      const jweError = new Error('Authentication failed');
      jweError.name = 'JWEError';
      mockGetServerSession.mockRejectedValue(jweError);

      const result = await getSafeServerSession();

      expect(result).toBeNull();
      expect(console.log).toHaveBeenCalledWith(
        '[Auth Helper] JWT decoding failed for unauthenticated user, returning null'
      );
    });

    it('should return null and log other authentication errors', async () => {
      const authError = new Error('Database connection failed');
      authError.name = 'DatabaseError';
      mockGetServerSession.mockRejectedValue(authError);

      const result = await getSafeServerSession();

      expect(result).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        '[Auth Helper] Authentication error:',
        {
          name: 'DatabaseError',
          message: 'Database connection failed',
        }
      );
    });

    it('should sanitize JWE error messages in authentication errors', async () => {
      const jweError = new Error('JWE token is invalid');
      jweError.name = 'AuthenticationError';
      mockGetServerSession.mockRejectedValue(jweError);

      const result = await getSafeServerSession();

      expect(result).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        '[Auth Helper] Authentication error:',
        {
          name: 'AuthenticationError',
          message: 'Authentication failed', // Sanitized message
        }
      );
    });

    it('should handle non-Error objects thrown', async () => {
      const nonError = 'String error';
      mockGetServerSession.mockRejectedValue(nonError);

      const result = await getSafeServerSession();

      expect(result).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        '[Auth Helper] Error getting server session:',
        nonError
      );
    });

    it('should handle errors without name or message properties', async () => {
      const minimalError = Object.create(Error.prototype);
      minimalError.name = undefined;
      minimalError.message = undefined;
      mockGetServerSession.mockRejectedValue(minimalError);

      const result = await getSafeServerSession();

      expect(result).toBeNull();
    });

    it('should handle JWT error in error name case-insensitively', async () => {
      const jwtError = new Error('Auth failed');
      jwtError.name = 'jwt-error';
      mockGetServerSession.mockRejectedValue(jwtError);

      const result = await getSafeServerSession();

      expect(result).toBeNull();
      expect(console.log).toHaveBeenCalledWith(
        '[Auth Helper] JWT decoding failed for unauthenticated user, returning null'
      );
    });

    it('should handle decrypt error in message case-insensitively', async () => {
      const decryptError = new Error('Failed to DECRYPT the token');
      mockGetServerSession.mockRejectedValue(decryptError);

      const result = await getSafeServerSession();

      expect(result).toBeNull();
      expect(console.log).toHaveBeenCalledWith(
        '[Auth Helper] JWT decoding failed for unauthenticated user, returning null'
      );
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when user has valid session with id', async () => {
      const mockSession = createMockSession();

      mockGetServerSession.mockResolvedValue(mockSession);

      const result = await isAuthenticated();

      expect(result).toBe(true);
    });

    it('should return false when session is null', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const result = await isAuthenticated();

      expect(result).toBe(false);
    });

    it('should return false when user is null', async () => {
      const sessionWithoutUser: Session = {
        user: null as any,
        expires: '2024-12-31T23:59:59.999Z',
      };

      mockGetServerSession.mockResolvedValue(sessionWithoutUser);

      const result = await isAuthenticated();

      expect(result).toBe(false);
    });

    it('should return false when user id is missing', async () => {
      const sessionWithoutId: Session = {
        user: {
          email: '<EMAIL>',
          name: 'Test User',
        } as any,
        expires: '2024-12-31T23:59:59.999Z',
      };

      mockGetServerSession.mockResolvedValue(sessionWithoutId);

      const result = await isAuthenticated();

      expect(result).toBe(false);
    });

    it('should return false when user id is empty string', async () => {
      const sessionWithEmptyId = createMockSession({ id: '' });

      mockGetServerSession.mockResolvedValue(sessionWithEmptyId);

      const result = await isAuthenticated();

      expect(result).toBe(false);
    });

    it('should return false and log error when getSafeServerSession throws', async () => {
      const error = new Error('Session retrieval failed');
      mockGetServerSession.mockRejectedValue(error);

      const result = await isAuthenticated();

      expect(result).toBe(false);
      // Both getSafeServerSession and isAuthenticated will log errors
      expect(console.error).toHaveBeenCalledWith(
        '[Auth Helper] Error getting server session:',
        error
      );
      expect(console.error).toHaveBeenCalledWith(
        '[Auth Helper] Authentication error:',
        {
          name: 'Error',
          message: 'Session retrieval failed'
        }
      );
    });

    it('should handle truthy user id values correctly', async () => {
      const sessionWithNumberId = createMockSession({ id: '123' });

      mockGetServerSession.mockResolvedValue(sessionWithNumberId);

      const result = await isAuthenticated();

      expect(result).toBe(true);
    });

    it('should handle falsy user id values correctly', async () => {
      const sessionWithZeroId = createMockSession({ id: '0' });

      mockGetServerSession.mockResolvedValue(sessionWithZeroId);

      const result = await isAuthenticated();

      expect(result).toBe(true); // '0' is truthy as a string
    });
  });

  describe('Error handling edge cases', () => {
    it('should handle undefined session gracefully in isAuthenticated', async () => {
      mockGetServerSession.mockResolvedValue(undefined as any);

      const result = await isAuthenticated();

      expect(result).toBe(false);
    });

    it('should handle session with undefined user gracefully', async () => {
      const sessionWithUndefinedUser: Session = {
        user: undefined as any,
        expires: '2024-12-31T23:59:59.999Z',
      };

      mockGetServerSession.mockResolvedValue(sessionWithUndefinedUser);

      const result = await isAuthenticated();

      expect(result).toBe(false);
    });

    it('should handle complex JWT error names', async () => {
      const complexJwtError = new Error('Token validation failed');
      complexJwtError.name = 'JWTError'; // Use a name that contains 'jwt'
      mockGetServerSession.mockRejectedValue(complexJwtError);

      const result = await getSafeServerSession();

      expect(result).toBeNull();
      expect(console.log).toHaveBeenCalledWith(
        '[Auth Helper] JWT decoding failed for unauthenticated user, returning null'
      );
    });

    it('should handle errors with both JWT in name and message', async () => {
      const jwtError = new Error('JWT decrypt operation failed');
      jwtError.name = 'JWTError';
      mockGetServerSession.mockRejectedValue(jwtError);

      const result = await getSafeServerSession();

      expect(result).toBeNull();
      expect(console.log).toHaveBeenCalledWith(
        '[Auth Helper] JWT decoding failed for unauthenticated user, returning null'
      );
    });
  });

  describe('Performance tests', () => {
    it('should handle multiple concurrent calls efficiently', async () => {
      const mockSession = createMockSession();

      mockGetServerSession.mockResolvedValue(mockSession);

      const start = performance.now();

      const promises = Array.from({ length: 100 }, () => Promise.all([
        getSafeServerSession(),
        isAuthenticated(),
      ]));

      await Promise.all(promises);

      const end = performance.now();

      expect(end - start).toBeLessThan(100); // Should complete quickly
    });

    it('should handle rapid successive calls without memory leaks', async () => {
      mockGetServerSession.mockResolvedValue(null);

      for (let i = 0; i < 1000; i++) {
        await getSafeServerSession();
        await isAuthenticated();
      }

      // Should complete without throwing or hanging
      expect(true).toBe(true);
    });
  });

  describe('Integration scenarios', () => {
    it('should work correctly in authentication flow', async () => {
      // Simulate unauthenticated user
      mockGetServerSession.mockResolvedValue(null);

      const session = await getSafeServerSession();
      const authenticated = await isAuthenticated();

      expect(session).toBeNull();
      expect(authenticated).toBe(false);
    });

    it('should work correctly with authenticated user', async () => {
      const mockSession = createMockSession();

      mockGetServerSession.mockResolvedValue(mockSession);

      const session = await getSafeServerSession();
      const authenticated = await isAuthenticated();

      expect(session).toBe(mockSession);
      expect(authenticated).toBe(true);
    });

    it('should handle authentication errors gracefully in flow', async () => {
      const jwtError = new Error('JWE token expired');
      mockGetServerSession.mockRejectedValue(jwtError);

      const session = await getSafeServerSession();
      const authenticated = await isAuthenticated();

      expect(session).toBeNull();
      expect(authenticated).toBe(false);
    });
  });
});