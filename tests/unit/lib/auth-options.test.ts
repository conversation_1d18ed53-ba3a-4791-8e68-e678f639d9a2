/**
 * @fileoverview Unit tests for lib/auth-options.ts
 * Tests NextAuth configuration, JWT callbacks, session management, and authentication flow
 * Coverage target: 90%+
 */

import { Account, NextAuthOptions, User as NextAuthUser, Profile } from 'next-auth';

import { AdapterUser } from 'next-auth/adapters';
import { JWT } from 'next-auth/jwt';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth-options';
import { getDb } from '@/lib/api/db';

// Mock dependencies
jest.mock('@/lib/api/db');
jest.mock('next-auth/providers/google', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    id: 'google',
    name: 'Google',
    type: 'oauth',
    clientId: 'test-client-id',
    clientSecret: 'test-client-secret',
  })),
}));
jest.mock('next-auth/jwt');

const mockGetDb = getDb as jest.MockedFunction<typeof getDb>;

// Mock database interface
const mockDb = {
  get: jest.fn(),
  all: jest.fn(),
  run: jest.fn(),
};

// Helper to create proper session objects
const createMockSession = (overrides: Partial<Session> = {}): Session => ({
  user: {
    id: 'test-id',
    name: 'Test User',
    email: '<EMAIL>',
    roles: [],
    permissions: [],
    groups: [],
    brands: [],
    ...overrides.user,
  },
  expires: new Date(Date.now() + ********).toISOString(),
  ...overrides,
});

// Helper to create proper JWT callback parameters
const createJwtCallbackParams = (overrides: any = {}) => ({
  token: {} as JWT,
  user: undefined as NextAuthUser | undefined,
  account: null as Account | null,
  profile: undefined as Profile | undefined,
  trigger: undefined as "signIn" | "signUp" | "update" | undefined,
  isNewUser: undefined as boolean | undefined,
  session: undefined as any,
  ...overrides,
});

// Helper to create proper session callback parameters
const createSessionCallbackParams = (overrides: any = {}) => ({
  session: createMockSession(),
  token: {} as JWT,
  user: {} as AdapterUser,
  newSession: undefined as any,
  trigger: undefined as "update" | undefined,
  ...overrides,
});

describe('lib/auth-options', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetDb.mockResolvedValue(mockDb as any);

    // Mock console methods to reduce test noise
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});

    // Set up environment variables
    process.env.GOOGLE_CLIENT_ID = 'test-client-id';
    process.env.GOOGLE_CLIENT_SECRET = 'test-client-secret';
    process.env.NEXTAUTH_SECRET = 'test-secret';
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Configuration Structure', () => {
    it('should have correct NextAuth configuration structure', () => {
      expect(authOptions).toBeDefined();
      expect(authOptions.providers).toBeDefined();
      expect(authOptions.pages).toBeDefined();
      expect(authOptions.callbacks).toBeDefined();
      expect(authOptions.session).toBeDefined();
      expect(authOptions.jwt).toBeDefined();
    });

    it('should configure Google provider correctly', () => {
      expect(authOptions.providers).toHaveLength(1);
      expect(authOptions.providers[0]).toBeDefined();
    });

    it('should have correct pages configuration', () => {
      expect(authOptions.pages?.signIn).toBe('/auth/signin');
    });

    it('should use JWT session strategy', () => {
      expect(authOptions.session?.strategy).toBe('jwt');
      expect(authOptions.session?.maxAge).toBe(30 * 24 * 60 * 60); // 30 days
    });
  });

  describe('JWT Callback', () => {
    const mockJwtCallback = authOptions.callbacks?.jwt;

    it('should handle initial user sign-in', async () => {
      const mockUser: NextAuthUser = {
        id: 'test-user-id',
        name: 'Test User',
        email: '<EMAIL>',
      };

      const mockToken: JWT = {
        sub: 'existing-sub',
        name: 'Existing Name',
        email: '<EMAIL>',
      };

      const params = createJwtCallbackParams({
        token: mockToken,
        user: mockUser,
        trigger: 'signIn' as const,
        account: {} as Account,
      });

      const result = await mockJwtCallback?.(params);

      expect(result).toEqual(expect.objectContaining({
        sub: 'test-user-id',
        name: 'Test User',
        email: '<EMAIL>',
        needsDbSync: true,
        isSuperAdmin: false,
      }));
    });

    it('should identify super admin user', async () => {
      const mockUser: NextAuthUser = {
        id: 'super-admin-id',
        name: 'Super Admin',
        email: '<EMAIL>',
      };

      const mockToken: JWT = {};

      const params = createJwtCallbackParams({
        token: mockToken,
        user: mockUser,
        trigger: 'signIn' as const,
        account: {} as Account,
      });

      const result = await mockJwtCallback?.(params);

      expect(result?.isSuperAdmin).toBe(true);
    });

    it('should handle impersonation start', async () => {
      const mockToken: JWT = {
        sub: 'admin-user-id',
        name: 'Admin User',
        email: '<EMAIL>',
        roles: ['Super Admin'],
        permissions: ['manage_users'],
        impersonateTargetUserId: 'target-user-id',
      };

      const params = createJwtCallbackParams({
        token: mockToken,
        trigger: 'update' as const,
        account: null,
        user: undefined,
      });

      const result = await mockJwtCallback?.(params);

      expect(result).toEqual(expect.objectContaining({
        sub: 'target-user-id',
        isImpersonating: true,
        originalUser: expect.objectContaining({
          id: 'admin-user-id',
          name: 'Admin User',
          email: '<EMAIL>',
          roles: ['Super Admin'],
          permissions: ['manage_users'],
        }),
      }));
      expect(result?.impersonateTargetUserId).toBeUndefined();
    });

    it('should handle impersonation revert', async () => {
      const mockToken: JWT = {
        sub: 'target-user-id',
        isImpersonating: true,
        revertImpersonation: true,
        originalUser: {
          id: 'admin-user-id',
          name: 'Admin User',
          email: '<EMAIL>',
          roles: ['Super Admin'],
          permissions: ['manage_users'],
        },
      };

      const params = createJwtCallbackParams({
        token: mockToken,
        trigger: 'update' as const,
        account: null,
        user: undefined,
      });

      const result = await mockJwtCallback?.(params);

      expect(result).toEqual(expect.objectContaining({
        sub: 'admin-user-id',
        name: 'Admin User',
        email: '<EMAIL>',
        roles: ['Super Admin'],
        permissions: ['manage_users'],
      }));
      expect(result?.isImpersonating).toBeUndefined();
      expect(result?.originalUser).toBeUndefined();
      expect(result?.revertImpersonation).toBeUndefined();
    });

    it('should handle update trigger with session data', async () => {
      const mockToken: JWT = {
        sub: 'user-id',
        name: 'User',
        email: '<EMAIL>',
      };

      const updateData = {
        customProperty: 'custom-value',
        roles: ['Updated Role'],
      };

      const params = createJwtCallbackParams({
        token: mockToken,
        trigger: 'update' as const,
        session: updateData,
        account: null,
        user: undefined,
      });

      const result = await mockJwtCallback?.(params);

      expect(result).toEqual(expect.objectContaining({
        ...mockToken,
        ...updateData,
      }));
    });

    it('should prevent impersonation for non-super-admin users', async () => {
      const mockToken: JWT = {
        sub: 'regular-user-id',
        name: 'Regular User',
        email: '<EMAIL>',
        roles: ['User'],
        impersonateTargetUserId: 'target-user-id',
      };

      const params = createJwtCallbackParams({
        token: mockToken,
        trigger: 'update' as const,
        account: null,
        user: undefined,
      });

      const result = await mockJwtCallback?.(params);

      expect(result?.isImpersonating).toBeUndefined();
      expect(result?.originalUser).toBeUndefined();
      expect(result?.sub).toBe('regular-user-id');
    });
  });

  describe('Session Callback', () => {
    const mockSessionCallback = authOptions.callbacks?.session;

    beforeEach(() => {
      // Reset database mocks
      mockDb.get.mockReset();
      mockDb.all.mockReset();
      mockDb.run.mockReset();
    });

    it('should handle missing session user or token sub', async () => {
      const mockSession = createMockSession({
        user: undefined as any,
      });

      const mockToken: JWT = {};

      const params = createSessionCallbackParams({
        session: mockSession,
        token: mockToken,
      });

      const result = await mockSessionCallback?.(params);

      expect(result).toEqual(mockSession);
    });

    it('should handle new user creation', async () => {
      const mockSession = createMockSession({
        user: {
          id: '',
          name: 'New User',
          email: '<EMAIL>',
          roles: [],
          permissions: [],
          groups: [],
          brands: [],
        },
      });

      const mockToken: JWT = {
        sub: 'new-user-id',
        name: 'New User',
        email: '<EMAIL>',
        needsDbSync: true,
        isSuperAdmin: false,
      };

      // Mock database responses for new user
      mockDb.get.mockResolvedValueOnce(undefined); // User doesn't exist
      mockDb.run.mockResolvedValueOnce({ lastID: 123 }); // User creation
      mockDb.run.mockResolvedValueOnce({}); // Login timestamp update
      mockDb.all.mockResolvedValueOnce([]); // No roles
      mockDb.all.mockResolvedValueOnce([]); // No groups
      mockDb.all.mockResolvedValueOnce([]); // No brands

      const params = createSessionCallbackParams({
        session: mockSession,
        token: mockToken,
      });

      const result = await mockSessionCallback?.(params);

      expect(mockDb.run).toHaveBeenCalledWith(
        'INSERT INTO Users (email, name, password_hash) VALUES (?, ?, ?)',
        ['<EMAIL>', 'New User', 'oauth-user-no-password']
      );
      expect(result?.user?.id).toBe('123');
      expect(result?.user?.roles).toEqual([]);
      expect(result?.user?.permissions).toEqual([]);
    });

    it('should handle existing user login', async () => {
      const mockSession = createMockSession({
        user: {
          id: '',
          name: 'Existing User',
          email: '<EMAIL>',
          roles: [],
          permissions: [],
          groups: [],
          brands: [],
        },
      });

      const mockToken: JWT = {
        sub: 'existing-user-id',
        name: 'Existing User',
        email: '<EMAIL>',
        needsDbSync: true,
        isSuperAdmin: false,
      };

      // Mock database responses for existing user
      mockDb.get.mockResolvedValueOnce({ id: 456 }); // User exists
      mockDb.run.mockResolvedValueOnce({}); // Login timestamp update
      mockDb.all.mockResolvedValueOnce([{ role_id: 1 }]); // User has roles
      mockDb.all.mockResolvedValueOnce([{ name: 'User' }]); // Role names
      mockDb.all.mockResolvedValueOnce([{ permission_name: 'view_dashboard' }]); // Permissions
      mockDb.all.mockResolvedValueOnce([{ group_id: 1 }]); // Groups
      mockDb.all.mockResolvedValueOnce([{ brand_id: 1 }]); // Direct brands
      mockDb.all.mockResolvedValueOnce([{ brand_id: 2 }]); // Group brands

      const params = createSessionCallbackParams({
        session: mockSession,
        token: mockToken,
      });

      const result = await mockSessionCallback?.(params);

      expect(mockDb.run).toHaveBeenCalledWith(
        'UPDATE Users SET lastLoginAt = ? WHERE id = ?',
        [expect.any(String), '456']
      );
      expect(result?.user?.id).toBe('456');
      expect(result?.user?.roles).toEqual(['User']);
      expect(result?.user?.permissions).toEqual(['view_dashboard']);
      expect(result?.user?.groups).toEqual([1]);
      expect(result?.user?.brands).toEqual([1, 2]);
    });

    it('should handle super admin user with fallback permissions', async () => {
      const mockSession = createMockSession({
        user: {
          id: '',
          name: 'Super Admin',
          email: '<EMAIL>',
          roles: [],
          permissions: [],
          groups: [],
          brands: [],
        },
      });

      const mockToken: JWT = {
        sub: 'super-admin-id',
        name: 'Super Admin',
        email: '<EMAIL>',
        needsDbSync: true,
        isSuperAdmin: true,
      };

      // Mock database responses for super admin
      mockDb.get.mockResolvedValueOnce({ id: 1 }); // User exists
      mockDb.all.mockResolvedValueOnce([
        { id: 1, name: 'Admin' },
        { id: 2, name: 'Super Admin' }
      ]); // Available roles
      mockDb.run.mockResolvedValueOnce({}); // Login timestamp
      mockDb.run.mockResolvedValueOnce({}); // Super Admin role assignment
      mockDb.run.mockResolvedValueOnce({}); // Admin role assignment
      mockDb.all.mockResolvedValueOnce([{ role_id: 1 }, { role_id: 2 }]); // User roles
      mockDb.all.mockResolvedValueOnce([
        { name: 'Admin' },
        { name: 'Super Admin' }
      ]); // Role names
      mockDb.all.mockResolvedValueOnce([]); // No permissions from DB (triggers fallback)
      mockDb.all.mockResolvedValueOnce([]); // Groups
      mockDb.all.mockResolvedValueOnce([]); // Direct brands
      mockDb.all.mockResolvedValueOnce([]); // Group brands

      const params = createSessionCallbackParams({
        session: mockSession,
        token: mockToken,
      });

      const result = await mockSessionCallback?.(params);

      expect(result?.user?.permissions).toEqual([
        'view_users', 'manage_users', 'view_roles', 'manage_roles',
        'view_permissions', 'manage_permissions', 'view_groups', 'manage_groups',
        'view_brands', 'manage_brands'
      ]);
      expect(result?.user?.roles).toEqual(['Admin', 'Super Admin']);
    });

    it('should handle impersonation session', async () => {
      const mockSession = createMockSession({
        user: {
          id: '',
          name: 'Admin User',
          email: '<EMAIL>',
          roles: [],
          permissions: [],
          groups: [],
          brands: [],
        },
      });

      const mockToken: JWT = {
        sub: 'target-user-id',
        isImpersonating: true,
        impersonateTargetUserId: 'target-user-id',
        originalUser: {
          id: 'admin-user-id',
          name: 'Admin User',
          email: '<EMAIL>',
          roles: ['Super Admin'],
          permissions: ['manage_users'],
        },
      };

      // Mock target user lookup
      mockDb.get.mockResolvedValueOnce({
        id: 999,
        name: 'Target User',
        email: '<EMAIL>'
      });
      mockDb.all.mockResolvedValueOnce([{ role_id: 3 }]); // Target user roles
      mockDb.all.mockResolvedValueOnce([{ name: 'Regular User' }]); // Role names
      mockDb.all.mockResolvedValueOnce([{ permission_name: 'view_dashboard' }]); // Permissions
      mockDb.all.mockResolvedValueOnce([]); // Groups
      mockDb.all.mockResolvedValueOnce([]); // Direct brands
      mockDb.all.mockResolvedValueOnce([]); // Group brands

      const params = createSessionCallbackParams({
        session: mockSession,
        token: mockToken,
      });

      const result = await mockSessionCallback?.(params);

      expect(result?.user?.name).toBe('Target User');
      expect(result?.user?.email).toBe('<EMAIL>');
      expect(result?.user?.isImpersonating).toBe(true);
      expect(result?.isImpersonating).toBe(true);
    });

    it('should handle database errors gracefully', async () => {
      const mockSession = createMockSession({
        user: {
          id: '',
          name: 'Test User',
          email: '<EMAIL>',
          roles: [],
          permissions: [],
          groups: [],
          brands: [],
        },
      });

      const mockToken: JWT = {
        sub: 'test-user-id',
        name: 'Test User',
        email: '<EMAIL>',
        needsDbSync: true,
        isSuperAdmin: false,
      };

      // Mock database error
      mockDb.get.mockRejectedValueOnce(new Error('Database connection failed'));

      const params = createSessionCallbackParams({
        session: mockSession,
        token: mockToken,
      });

      const result = await mockSessionCallback?.(params);

      expect(result?.user?.roles).toEqual([]);
      expect(result?.user?.permissions).toEqual([]);
      expect(result?.user?.groups).toEqual([]);
      expect(result?.user?.brands).toEqual([]);
    });

    it('should use cached data when not syncing', async () => {
      const mockSession = createMockSession({
        user: {
          id: '',
          name: 'Cached User',
          email: '<EMAIL>',
          roles: [],
          permissions: [],
          groups: [],
          brands: [],
        },
      });

      const mockToken: JWT = {
        sub: 'cached-user-id',
        name: 'Cached User',
        email: '<EMAIL>',
        roles: ['Cached Role'],
        permissions: ['cached_permission'],
        groups: [1, 2],
        brands: [3, 4],
        isSuperAdmin: false,
      };

      const params = createSessionCallbackParams({
        session: mockSession,
        token: mockToken,
      });

      const result = await mockSessionCallback?.(params);

      expect(mockDb.get).not.toHaveBeenCalled();
      expect(result?.user?.roles).toEqual(['Cached Role']);
      expect(result?.user?.permissions).toEqual(['cached_permission']);
      expect(result?.user?.groups).toEqual([1, 2]);
      expect(result?.user?.brands).toEqual([3, 4]);
    });
  });

  describe('JWT Encode/Decode', () => {
    it('should have custom JWT encode function', () => {
      expect(authOptions.jwt?.encode).toBeDefined();
      expect(typeof authOptions.jwt?.encode).toBe('function');
    });

    it('should have custom JWT decode function', () => {
      expect(authOptions.jwt?.decode).toBeDefined();
      expect(typeof authOptions.jwt?.decode).toBe('function');
    });

    it('should handle decode with missing token gracefully', async () => {
      const mockDecode = authOptions.jwt?.decode;

      if (mockDecode) {
        const result = await mockDecode({
          secret: 'test-secret',
          token: null as any,
        });

        expect(result).toBeNull();
      }
    });

    it('should handle decode with missing secret gracefully', async () => {
      const mockDecode = authOptions.jwt?.decode;

      if (mockDecode) {
        const result = await mockDecode({
          secret: '',
          token: 'test-token',
        });

        expect(result).toBeNull();
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle malformed token data', async () => {
      const mockJwtCallback = authOptions.callbacks?.jwt;

      const malformedToken = {
        // Missing required properties
        randomProperty: 'value',
      } as JWT;

      const params = createJwtCallbackParams({
        token: malformedToken,
        trigger: 'signIn' as const,
        account: {} as Account,
        user: undefined,
      });

      const result = await mockJwtCallback?.(params);

      expect(result).toBeDefined();
      expect(result?.isSuperAdmin).toBe(false);
    });

    it('should handle session callback with malformed session', async () => {
      const mockSessionCallback = authOptions.callbacks?.session;

      const malformedSession = {
        // Missing user property
        expires: new Date().toISOString(),
      } as Session;

      const mockToken: JWT = {
        sub: 'test-user-id',
      };

      const params = createSessionCallbackParams({
        session: malformedSession,
        token: mockToken,
      });

      const result = await mockSessionCallback?.(params);

      expect(result).toEqual(malformedSession);
    });

    it('should handle database query failures for roles', async () => {
      const mockSessionCallback = authOptions.callbacks?.session;

      const mockSession = createMockSession({
        user: {
          id: '',
          name: 'Test User',
          email: '<EMAIL>',
          roles: [],
          permissions: [],
          groups: [],
          brands: [],
        },
      });

      const mockToken: JWT = {
        sub: 'test-user-id',
        name: 'Test User',
        email: '<EMAIL>',
        needsDbSync: true,
        isSuperAdmin: false,
      };

      // Mock user exists but role query fails
      mockDb.get.mockResolvedValueOnce({ id: 123 });
      mockDb.run.mockResolvedValueOnce({});
      mockDb.all.mockRejectedValueOnce(new Error('Role query failed'));

      const params = createSessionCallbackParams({
        session: mockSession,
        token: mockToken,
      });

      const result = await mockSessionCallback?.(params);

      expect(result?.user?.roles).toEqual([]);
      expect(result?.user?.permissions).toEqual([]);
    });

    it('should handle super admin fallback on permission query failure', async () => {
      const mockSessionCallback = authOptions.callbacks?.session;

      const mockSession = createMockSession({
        user: {
          id: '',
          name: 'Super Admin',
          email: '<EMAIL>',
          roles: [],
          permissions: [],
          groups: [],
          brands: [],
        },
      });

      const mockToken: JWT = {
        sub: 'super-admin-id',
        name: 'Super Admin',
        email: '<EMAIL>',
        needsDbSync: true,
        isSuperAdmin: true,
      };

      // Mock user exists, roles exist, but permission query fails
      mockDb.get.mockResolvedValueOnce({ id: 1 });
      mockDb.all.mockResolvedValueOnce([{ id: 1, name: 'Super Admin' }]);
      mockDb.run.mockResolvedValue({});
      mockDb.all.mockResolvedValueOnce([{ role_id: 1 }]);
      mockDb.all.mockResolvedValueOnce([{ name: 'Super Admin' }]);
      mockDb.all.mockRejectedValueOnce(new Error('Permission query failed'));
      mockDb.all.mockResolvedValueOnce([]);
      mockDb.all.mockResolvedValueOnce([]);
      mockDb.all.mockResolvedValueOnce([]);

      const params = createSessionCallbackParams({
        session: mockSession,
        token: mockToken,
      });

      const result = await mockSessionCallback?.(params);

      expect(result?.user?.permissions).toEqual([
        'view_users', 'manage_users', 'view_roles', 'manage_roles',
        'view_permissions', 'manage_permissions', 'view_groups', 'manage_groups',
        'view_brands', 'manage_brands'
      ]);
    });
  });

  describe('Performance Tests', () => {
    it('should handle session callback efficiently', async () => {
      const mockSessionCallback = authOptions.callbacks?.session;

      const mockSession = createMockSession({
        user: {
          id: '',
          name: 'Performance User',
          email: '<EMAIL>',
          roles: [],
          permissions: [],
          groups: [],
          brands: [],
        },
      });

      const mockToken: JWT = {
        sub: 'performance-user-id',
        name: 'Performance User',
        email: '<EMAIL>',
        roles: ['User'],
        permissions: ['view_dashboard'],
        groups: [1],
        brands: [1],
        isSuperAdmin: false,
      };

      const start = performance.now();

      for (let i = 0; i < 10; i++) {
        const params = createSessionCallbackParams({
          session: mockSession,
          token: mockToken,
        });

        await mockSessionCallback?.(params);
      }

      const end = performance.now();
      expect(end - start).toBeLessThan(100);
    });

    it('should handle JWT callback efficiently', async () => {
      const mockJwtCallback = authOptions.callbacks?.jwt;

      const mockToken: JWT = {
        sub: 'performance-user-id',
        name: 'Performance User',
        email: '<EMAIL>',
      };

      const start = performance.now();

      for (let i = 0; i < 100; i++) {
        const params = createJwtCallbackParams({
          token: mockToken,
          trigger: 'update' as const,
          account: null,
          user: undefined,
        });

        await mockJwtCallback?.(params);
      }

      const end = performance.now();
      expect(end - start).toBeLessThan(50);
    });
  });
});