/**
 * @fileoverview Unit tests for lib/chart-utils.tsx
 * Tests chart utility functions including formatting, colors, and calculations
 * Coverage target: 90%+
 */

import {
  CHART_COLORS,
  formatCompactCurrency,
  formatCompactNumber,
  formatCurrency,
  formatDate,
  formatKpiValue,
  formatNumber,
  formatPercent,
  formatRawPercent,
  getBrandColor,
  getContrastTextColor,
  getGradientDefinition,
  getRandomColor,
} from '@/lib/chart-utils';

describe('lib/chart-utils', () => {
  describe('CHART_COLORS', () => {
    it('should contain all expected color properties', () => {
      const expectedColors = [
        'blue', 'cyan', 'azure', 'purple', 'pink', 'orange', 'yellow',
        'green', 'red', 'gray', 'indigo', 'teal', 'amber', 'emerald', 'lime', 'rose'
      ];
      
      expectedColors.forEach(color => {
        expect(CHART_COLORS).toHaveProperty(color);
        expect(typeof CHART_COLORS[color as keyof typeof CHART_COLORS]).toBe('string');
        expect(CHART_COLORS[color as keyof typeof CHART_COLORS]).toMatch(/^#[0-9A-F]{6}$/i);
      });
    });

    it('should have valid hex color values', () => {
      Object.values(CHART_COLORS).forEach(color => {
        expect(color).toMatch(/^#[0-9A-F]{6}$/i);
      });
    });
  });

  describe('formatNumber', () => {
    it('should format positive numbers with thousands separator', () => {
      expect(formatNumber(1234)).toBe('1,234');
      expect(formatNumber(1234567)).toBe('1,234,567');
    });

    it('should format negative numbers', () => {
      expect(formatNumber(-1234)).toBe('-1,234');
    });

    it('should format zero', () => {
      expect(formatNumber(0)).toBe('0');
    });

    it('should handle decimal numbers', () => {
      expect(formatNumber(1234.56)).toBe('1,234.56');
    });

    it('should return default value for null', () => {
      expect(formatNumber(null)).toBe('N/A');
      expect(formatNumber(null, 'No Data')).toBe('No Data');
    });

    it('should return default value for undefined', () => {
      expect(formatNumber(undefined)).toBe('N/A');
      expect(formatNumber(undefined, 'No Data')).toBe('No Data');
    });
  });

  describe('formatCurrency', () => {
    it('should format CAD currency by default', () => {
      expect(formatCurrency(1234.56)).toBe('$1,234.56');
    });

    it('should format USD currency', () => {
      expect(formatCurrency(1234.56, 'USD')).toBe('$1,234.56');
    });

    it('should format GBP currency', () => {
      expect(formatCurrency(1234.56, 'GBP')).toBe('£1,234.56');
    });

    it('should handle negative amounts', () => {
      expect(formatCurrency(-1234.56)).toBe('$-1,234.56');
    });

    it('should handle zero', () => {
      expect(formatCurrency(0)).toBe('$0.00');
    });

    it('should format with two decimal places', () => {
      expect(formatCurrency(1234)).toBe('$1,234.00');
      expect(formatCurrency(1234.1)).toBe('$1,234.10');
    });

    it('should return default value for null/undefined', () => {
      expect(formatCurrency(null)).toBe('N/A');
      expect(formatCurrency(undefined, 'CAD', 'No Data')).toBe('No Data');
    });
  });

  describe('formatPercent', () => {
    it('should convert decimal to percentage', () => {
      expect(formatPercent(0.1234)).toBe('12.34%');
      expect(formatPercent(0.5)).toBe('50.00%');
      expect(formatPercent(1.0)).toBe('100.00%');
    });

    it('should handle negative percentages', () => {
      expect(formatPercent(-0.1234)).toBe('-12.34%');
    });

    it('should handle zero', () => {
      expect(formatPercent(0)).toBe('0.00%');
    });

    it('should handle values greater than 1', () => {
      expect(formatPercent(1.5)).toBe('150.00%');
    });

    it('should return default value for null/undefined', () => {
      expect(formatPercent(null)).toBe('N/A');
      expect(formatPercent(undefined, 'No Data')).toBe('No Data');
    });
  });

  describe('formatRawPercent', () => {
    it('should format raw percentage values', () => {
      expect(formatRawPercent(12.34)).toBe('12.34%');
      expect(formatRawPercent(50)).toBe('50.00%');
      expect(formatRawPercent(100)).toBe('100.00%');
    });

    it('should handle negative percentages', () => {
      expect(formatRawPercent(-12.34)).toBe('-12.34%');
    });

    it('should handle zero', () => {
      expect(formatRawPercent(0)).toBe('0.00%');
    });

    it('should return default value for null/undefined', () => {
      expect(formatRawPercent(null)).toBe('N/A');
      expect(formatRawPercent(undefined, 'No Data')).toBe('No Data');
    });
  });

  describe('formatDate', () => {
    it('should format valid date strings', () => {
      const result = formatDate('2024-01-15');
      expect(result).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/); // MM/DD/YYYY or M/D/YYYY
    });

    it('should handle ISO date strings', () => {
      const result = formatDate('2024-01-15T10:30:00Z');
      expect(result).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
    });

    it('should return original string for invalid dates', () => {
      expect(formatDate('invalid-date')).toBe('invalid-date');
      expect(formatDate('not-a-date')).toBe('not-a-date');
    });

    it('should return default value for empty string', () => {
      expect(formatDate('')).toBe('N/A');
      expect(formatDate('', 'No Date')).toBe('No Date');
    });

    it('should handle different date formats', () => {
      expect(formatDate('01/15/2024')).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
      expect(formatDate('January 15, 2024')).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
    });
  });

  describe('getRandomColor', () => {
    it('should return a valid color from CHART_COLORS', () => {
      const color = getRandomColor();
      const colorValues = Object.values(CHART_COLORS);
      expect(colorValues).toContain(color);
    });

    it('should return hex color format', () => {
      const color = getRandomColor();
      expect(color).toMatch(/^#[0-9A-F]{6}$/i);
    });

    it('should potentially return different colors on multiple calls', () => {
      const colors = Array.from({ length: 10 }, () => getRandomColor());
      const uniqueColors = new Set(colors);
      // With 16 colors available, we should get some variety in 10 calls
      expect(uniqueColors.size).toBeGreaterThan(1);
    });
  });

  describe('getContrastTextColor', () => {
    it('should return white for dark colors', () => {
      expect(getContrastTextColor('#000000')).toBe('#FFFFFF');
      expect(getContrastTextColor('#2563EB')).toBe('#FFFFFF'); // Dark blue
      expect(getContrastTextColor('#9333EA')).toBe('#FFFFFF'); // Purple
    });

    it('should return black for light colors', () => {
      expect(getContrastTextColor('#FFFFFF')).toBe('#000000');
      expect(getContrastTextColor('#FFFF00')).toBe('#000000'); // Yellow
      expect(getContrastTextColor('#00FFFF')).toBe('#000000'); // Cyan
    });

    it('should handle colors without # prefix', () => {
      // Note: The function expects # prefix, but let's test edge case
      expect(() => getContrastTextColor('FFFFFF')).not.toThrow();
    });

    it('should handle all CHART_COLORS', () => {
      Object.values(CHART_COLORS).forEach(color => {
        const contrast = getContrastTextColor(color);
        expect(['#000000', '#FFFFFF']).toContain(contrast);
      });
    });
  });

  describe('getBrandColor', () => {
    it('should return specific colors for brand names with color keywords', () => {
      expect(getBrandColor('Blue Brand', 0)).toBe(CHART_COLORS.blue);
      expect(getBrandColor('Green Company', 0)).toBe(CHART_COLORS.green);
      expect(getBrandColor('Red Corp', 0)).toBe(CHART_COLORS.red);
      expect(getBrandColor('Purple Inc', 0)).toBe(CHART_COLORS.purple);
      expect(getBrandColor('Orange Ltd', 0)).toBe(CHART_COLORS.orange);
      expect(getBrandColor('Yellow Co', 0)).toBe(CHART_COLORS.yellow);
    });

    it('should be case insensitive for color matching', () => {
      expect(getBrandColor('BLUE BRAND', 0)).toBe(CHART_COLORS.blue);
      expect(getBrandColor('blue brand', 0)).toBe(CHART_COLORS.blue);
      expect(getBrandColor('Blue Brand', 0)).toBe(CHART_COLORS.blue);
    });

    it('should use index-based color for brands without color keywords', () => {
      const colorKeys = Object.keys(CHART_COLORS);
      expect(getBrandColor('Generic Brand', 0)).toBe(CHART_COLORS[colorKeys[0] as keyof typeof CHART_COLORS]);
      expect(getBrandColor('Another Brand', 1)).toBe(CHART_COLORS[colorKeys[1] as keyof typeof CHART_COLORS]);
    });

    it('should cycle through colors when index exceeds available colors', () => {
      const colorKeys = Object.keys(CHART_COLORS);
      const totalColors = colorKeys.length;
      expect(getBrandColor('Brand', totalColors)).toBe(CHART_COLORS[colorKeys[0] as keyof typeof CHART_COLORS]);
      expect(getBrandColor('Brand', totalColors + 1)).toBe(CHART_COLORS[colorKeys[1] as keyof typeof CHART_COLORS]);
    });
  });

  describe('formatKpiValue', () => {
    it('should format percentage KPIs', () => {
      expect(formatKpiValue(0.1234, '%Conversion Rate')).toBe('12.34%');
      expect(formatKpiValue(0.5, '%Growth')).toBe('50.00%');
    });

    it('should format currency-based KPIs', () => {
      expect(formatKpiValue(1234.56, 'Revenue')).toBe('$1,234.56');
      expect(formatKpiValue(1000, 'Margin')).toBe('$1,000.00');
      expect(formatKpiValue(500, 'Cost')).toBe('$500.00');
      expect(formatKpiValue(200, 'Spend')).toBe('$200.00');
      expect(formatKpiValue(5, 'ROAS')).toBe('$5.00');
      expect(formatKpiValue(100, 'Adspend')).toBe('$100.00');
      expect(formatKpiValue(25, 'CPA')).toBe('$25.00');
      expect(formatKpiValue(50, 'Discount')).toBe('$50.00');
    });

    it('should format currency with different currencies', () => {
      expect(formatKpiValue(1234.56, 'Revenue', 'USD')).toBe('$1,234.56');
      expect(formatKpiValue(1234.56, 'Revenue', 'GBP')).toBe('£1,234.56');
    });

    it('should format other numerical KPIs', () => {
      expect(formatKpiValue(1234, 'Orders')).toBe('1,234');
      expect(formatKpiValue(5678, 'Clicks')).toBe('5,678');
    });

    it('should return N/A for null/undefined values', () => {
      expect(formatKpiValue(null, 'Revenue')).toBe('N/A');
      expect(formatKpiValue(undefined as unknown as number | null, 'Orders')).toBe('N/A');
    });
  });

  describe('formatCompactCurrency', () => {
    it('should format large numbers with compact notation', () => {
      expect(formatCompactCurrency(1234567, 'CAD')).toBe('$1.2M');
      expect(formatCompactCurrency(1234, 'CAD')).toBe('$1.2K');
      expect(formatCompactCurrency(1234567890, 'CAD')).toBe('$1.2B');
    });

    it('should handle different currencies', () => {
      expect(formatCompactCurrency(1234567, 'USD')).toBe('$1.2M');
      expect(formatCompactCurrency(1234567, 'GBP')).toBe('£1.2M');
    });

    it('should handle small numbers', () => {
      expect(formatCompactCurrency(123, 'CAD')).toBe('$123');
    });

    it('should return default value for null/undefined', () => {
      expect(formatCompactCurrency(null)).toBe('N/A');
      expect(formatCompactCurrency(undefined, 'CAD', 'No Data')).toBe('No Data');
    });
  });

  describe('formatCompactNumber', () => {
    it('should format large numbers with compact notation', () => {
      expect(formatCompactNumber(1234567)).toBe('1.2M');
      expect(formatCompactNumber(1234)).toBe('1.2K');
      expect(formatCompactNumber(1234567890)).toBe('1.2B');
    });

    it('should handle small numbers', () => {
      expect(formatCompactNumber(123)).toBe('123');
    });

    it('should return default value for null/undefined', () => {
      expect(formatCompactNumber(null)).toBe('N/A');
      expect(formatCompactNumber(undefined, 'No Data')).toBe('No Data');
    });
  });

  describe('getGradientDefinition', () => {
    it('should return gradient definition object with correct structure', () => {
      const gradient = getGradientDefinition('test-gradient', '#FF0000');
      
      expect(gradient).toHaveProperty('id', 'test-gradient');
      expect(gradient).toHaveProperty('x1', 0);
      expect(gradient).toHaveProperty('y1', 0);
      expect(gradient).toHaveProperty('x2', 0);
      expect(gradient).toHaveProperty('y2', 1);
      expect(gradient).toHaveProperty('children');
      expect(Array.isArray(gradient.children)).toBe(true);
      expect(gradient.children).toHaveLength(2);
    });

    it('should create gradient with provided color', () => {
      const color = '#FF0000';
      const gradient = getGradientDefinition('test', color);
      
      // Check that the color is used in the gradient stops
      expect(gradient.children[0].props.stopColor).toBe(color);
      expect(gradient.children[1].props.stopColor).toBe(color);
    });

    it('should have correct opacity values', () => {
      const gradient = getGradientDefinition('test', '#FF0000');
      
      expect(gradient.children[0].props.stopOpacity).toBe(0.8);
      expect(gradient.children[1].props.stopOpacity).toBe(0.1);
    });
  });

  describe('Performance tests', () => {
    it('should handle large numbers efficiently in formatNumber', () => {
      const start = performance.now();
      for (let i = 0; i < 1000; i++) {
        formatNumber(Math.random() * 1000000);
      }
      const end = performance.now();
      expect(end - start).toBeLessThan(100);
    });

    it('should handle many color requests efficiently', () => {
      const start = performance.now();
      for (let i = 0; i < 1000; i++) {
        getRandomColor();
        getBrandColor(`Brand ${i}`, i);
      }
      const end = performance.now();
      expect(end - start).toBeLessThan(50);
    });
  });

  describe('Edge cases', () => {
    it('should handle very large numbers', () => {
      expect(formatNumber(Number.MAX_SAFE_INTEGER)).toContain(',');
      expect(formatCurrency(Number.MAX_SAFE_INTEGER)).toContain('$');
    });

    it('should handle very small numbers', () => {
      expect(formatPercent(0.0001)).toBe('0.01%');
      expect(formatCurrency(0.01)).toBe('$0.01');
    });

    it('should handle special number values', () => {
      expect(formatNumber(Infinity)).toBe('∞');
      expect(formatNumber(-Infinity)).toBe('-∞');
      expect(formatNumber(NaN)).toBe('NaN');
    });

    it('should handle empty brand names', () => {
      expect(() => getBrandColor('', 0)).not.toThrow();
      expect(getBrandColor('', 0)).toBeTruthy();
    });
  });
});