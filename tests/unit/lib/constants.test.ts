/**
 * @fileoverview Unit tests for lib/constants.ts
 * Tests application constants including KPI lists, chart configs, and market definitions
 * Coverage target: 90%+
 */

import {
  ALL_AVAILABLE_KPIS_LIST,
  CORE_MARKETS,
  CORE_MARKETS_COUNTRY_MAPPING,
  KPI_CHART_CONFIG_COLORS,
} from '@/lib/constants';

describe('lib/constants', () => {
  describe('ALL_AVAILABLE_KPIS_LIST', () => {
    it('should be an array of strings', () => {
      expect(Array.isArray(ALL_AVAILABLE_KPIS_LIST)).toBe(true);
      ALL_AVAILABLE_KPIS_LIST.forEach(kpi => {
        expect(typeof kpi).toBe('string');
      });
    });

    it('should contain expected KPI categories', () => {
      const expectedKpis = [
        'Gross Revenue', 'Discount', 'Refund', 'Net Revenue', 'Landed Cost', 
        'Fulfillment Cost', 'Transaction Cost', 'Gross Margin', 'Adspend', 
        'Contribution Margin'
      ];
      
      expectedKpis.forEach(kpi => {
        expect(ALL_AVAILABLE_KPIS_LIST).toContain(kpi);
      });
    });

    it('should contain percentage versions of KPIs', () => {
      const expectedPercentageKpis = [
        '% Discount', '% Refund', '% Landed Cost', '% Fulfillment Cost',
        '% Transaction Cost', '% Gross Margin', '% Adspend', '% Contribution Margin'
      ];
      
      expectedPercentageKpis.forEach(kpi => {
        expect(ALL_AVAILABLE_KPIS_LIST).toContain(kpi);
      });
    });

    it('should have the correct total number of KPIs', () => {
      expect(ALL_AVAILABLE_KPIS_LIST).toHaveLength(18);
    });

    it('should not contain duplicate KPIs', () => {
      const uniqueKpis = new Set(ALL_AVAILABLE_KPIS_LIST);
      expect(uniqueKpis.size).toBe(ALL_AVAILABLE_KPIS_LIST.length);
    });

    it('should not contain empty strings', () => {
      ALL_AVAILABLE_KPIS_LIST.forEach(kpi => {
        expect(kpi.trim()).not.toBe('');
        expect(kpi.length).toBeGreaterThan(0);
      });
    });

    it('should have consistent naming conventions', () => {
      ALL_AVAILABLE_KPIS_LIST.forEach(kpi => {
        // Should not start or end with whitespace
        expect(kpi).toBe(kpi.trim());
        
        // Percentage KPIs should start with %
        if (kpi.startsWith('%')) {
          expect(kpi).toMatch(/^% [A-Z]/);
        } else {
          // Non-percentage KPIs should start with capital letter
          expect(kpi).toMatch(/^[A-Z]/);
        }
      });
    });

    it('should maintain consistent structure', () => {
      const originalLength = ALL_AVAILABLE_KPIS_LIST.length;
      
      // Test that the array has the expected structure
      expect(ALL_AVAILABLE_KPIS_LIST).toHaveLength(18);
      expect(originalLength).toBe(18);
    });
  });

  describe('KPI_CHART_CONFIG_COLORS', () => {
    it('should be an object', () => {
      expect(typeof KPI_CHART_CONFIG_COLORS).toBe('object');
      expect(KPI_CHART_CONFIG_COLORS).not.toBeNull();
    });

    it('should have a default color property', () => {
      expect(KPI_CHART_CONFIG_COLORS).toHaveProperty('default');
      expect(typeof KPI_CHART_CONFIG_COLORS.default).toBe('string');
    });

    it('should have valid CSS custom property format', () => {
      expect(KPI_CHART_CONFIG_COLORS.default).toBe('hsl(var(--chart-1))');
    });

    it('should be extensible for additional colors', () => {
      // Test that the object structure allows for additional properties
      const testColors = { ...KPI_CHART_CONFIG_COLORS, secondary: 'hsl(var(--chart-2))' };
      expect(testColors).toHaveProperty('default');
      expect(testColors).toHaveProperty('secondary');
    });
  });

  describe('CORE_MARKETS', () => {
    it('should have the correct structure', () => {
      expect(CORE_MARKETS).toHaveProperty('countries');
      expect(CORE_MARKETS).toHaveProperty('displayName');
      expect(CORE_MARKETS).toHaveProperty('id');
    });

    it('should have countries as an array', () => {
      expect(Array.isArray(CORE_MARKETS.countries)).toBe(true);
    });

    it('should contain expected core market countries', () => {
      const expectedCountries = [
        'United States', 
        'United States of America', 
        'Canada', 
        'United Kingdom', 
        'France'
      ];
      
      expectedCountries.forEach(country => {
        expect(CORE_MARKETS.countries).toContain(country);
      });
    });

    it('should have correct display name', () => {
      expect(CORE_MARKETS.displayName).toBe('Core Markets');
    });

    it('should have correct id', () => {
      expect(CORE_MARKETS.id).toBe('core-markets');
    });

    it('should have consistent structure', () => {
      // Test that the object has the expected structure
      expect(CORE_MARKETS.displayName).toBe('Core Markets');
      expect(CORE_MARKETS.id).toBe('core-markets');
      expect(Array.isArray(CORE_MARKETS.countries)).toBe(true);
    });

    it('should have valid country names', () => {
      CORE_MARKETS.countries.forEach(country => {
        expect(typeof country).toBe('string');
        expect(country.trim()).not.toBe('');
        expect(country.length).toBeGreaterThan(0);
      });
    });

    it('should not contain duplicate countries', () => {
      const uniqueCountries = new Set(CORE_MARKETS.countries);
      expect(uniqueCountries.size).toBe(CORE_MARKETS.countries.length);
    });
  });

  describe('CORE_MARKETS_COUNTRY_MAPPING', () => {
    it('should be an object with string keys and values', () => {
      expect(typeof CORE_MARKETS_COUNTRY_MAPPING).toBe('object');
      expect(CORE_MARKETS_COUNTRY_MAPPING).not.toBeNull();
      
      Object.entries(CORE_MARKETS_COUNTRY_MAPPING).forEach(([key, value]) => {
        expect(typeof key).toBe('string');
        expect(typeof value).toBe('string');
      });
    });

    it('should contain expected country mappings', () => {
      const expectedMappings = {
        'USA': 'United States',
        'US': 'United States',
        'United States of America': 'United States of America',
        'UK': 'United Kingdom',
        'France': 'France',
        'Canada': 'Canada'
      };
      
      Object.entries(expectedMappings).forEach(([key, value]) => {
        expect(CORE_MARKETS_COUNTRY_MAPPING).toHaveProperty(key);
        expect(CORE_MARKETS_COUNTRY_MAPPING[key as keyof typeof CORE_MARKETS_COUNTRY_MAPPING]).toBe(value);
      });
    });

    it('should map all values to countries in CORE_MARKETS', () => {
      Object.values(CORE_MARKETS_COUNTRY_MAPPING).forEach(mappedCountry => {
        expect(CORE_MARKETS.countries).toContain(mappedCountry);
      });
    });

    it('should handle common country abbreviations', () => {
      expect(CORE_MARKETS_COUNTRY_MAPPING['USA']).toBe('United States');
      expect(CORE_MARKETS_COUNTRY_MAPPING['US']).toBe('United States');
      expect(CORE_MARKETS_COUNTRY_MAPPING['UK']).toBe('United Kingdom');
    });

    it('should be case-sensitive', () => {
      expect(CORE_MARKETS_COUNTRY_MAPPING).toHaveProperty('USA');
      expect(CORE_MARKETS_COUNTRY_MAPPING).not.toHaveProperty('usa');
      expect(CORE_MARKETS_COUNTRY_MAPPING).toHaveProperty('UK');
      expect(CORE_MARKETS_COUNTRY_MAPPING).not.toHaveProperty('uk');
    });

    it('should have consistent structure', () => {
      // Test that the mapping has the expected structure
      expect(typeof CORE_MARKETS_COUNTRY_MAPPING).toBe('object');
      expect(CORE_MARKETS_COUNTRY_MAPPING).not.toBeNull();
      
      // Test expected mappings exist
      expect(CORE_MARKETS_COUNTRY_MAPPING['USA']).toBe('United States');
      expect(CORE_MARKETS_COUNTRY_MAPPING['UK']).toBe('United Kingdom');
    });

    it('should not contain empty keys or values', () => {
      Object.entries(CORE_MARKETS_COUNTRY_MAPPING).forEach(([key, value]) => {
        expect(key.trim()).not.toBe('');
        expect(value.trim()).not.toBe('');
        expect(key.length).toBeGreaterThan(0);
        expect(value.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Constants integration', () => {
    it('should have consistent country references between CORE_MARKETS and mapping', () => {
      // All mapped values should exist in CORE_MARKETS.countries
      Object.values(CORE_MARKETS_COUNTRY_MAPPING).forEach(country => {
        expect(CORE_MARKETS.countries).toContain(country);
      });
    });

    it('should provide comprehensive coverage for major markets', () => {
      // Should cover major English-speaking markets and France
      const majorMarkets = ['United States', 'Canada', 'United Kingdom', 'France'];
      majorMarkets.forEach(market => {
        expect(CORE_MARKETS.countries).toContain(market);
      });
    });

    it('should have KPIs that cover the full business metrics spectrum', () => {
      // Should have revenue metrics
      expect(ALL_AVAILABLE_KPIS_LIST.some(kpi => kpi.includes('Revenue'))).toBe(true);
      
      // Should have cost metrics
      expect(ALL_AVAILABLE_KPIS_LIST.some(kpi => kpi.includes('Cost'))).toBe(true);
      
      // Should have margin metrics
      expect(ALL_AVAILABLE_KPIS_LIST.some(kpi => kpi.includes('Margin'))).toBe(true);
      
      // Should have percentage versions
      expect(ALL_AVAILABLE_KPIS_LIST.some(kpi => kpi.startsWith('%'))).toBe(true);
    });
  });

  describe('Performance and memory', () => {
    it('should not create new objects on each access', () => {
      const markets1 = CORE_MARKETS;
      const markets2 = CORE_MARKETS;
      expect(markets1).toBe(markets2);
      
      const mapping1 = CORE_MARKETS_COUNTRY_MAPPING;
      const mapping2 = CORE_MARKETS_COUNTRY_MAPPING;
      expect(mapping1).toBe(mapping2);
    });

    it('should handle frequent access efficiently', () => {
      const start = performance.now();
      
      for (let i = 0; i < 1000; i++) {
        // Access properties to test performance
        void ALL_AVAILABLE_KPIS_LIST.length;
        void CORE_MARKETS.countries.length;
        void Object.keys(CORE_MARKETS_COUNTRY_MAPPING).length;
      }
      
      const end = performance.now();
      expect(end - start).toBeLessThan(10); // Should be very fast
    });
  });
});