/**
 * @fileoverview Unit tests for lib/utils.ts
 * Tests utility functions including className merging and string slugification
 * Coverage target: 90%+
 */

import { cn, slugify } from '@/lib/utils';

describe('lib/utils', () => {
  describe('cn function', () => {
    it('should merge class names correctly', () => {
      const result = cn('px-4', 'py-2', 'bg-blue-500');
      expect(result).toContain('px-4');
      expect(result).toContain('py-2');
      expect(result).toContain('bg-blue-500');
    });

    it('should handle conditional classes', () => {
      const result = cn('base-class', true && 'conditional-class', false && 'hidden-class');
      expect(result).toContain('base-class');
      expect(result).toContain('conditional-class');
      expect(result).not.toContain('hidden-class');
    });

    it('should handle conflicting Tailwind classes', () => {
      const result = cn('px-4', 'px-8');
      // twMerge should keep only the last conflicting class
      expect(result).toContain('px-8');
      expect(result).not.toContain('px-4');
    });

    it('should handle arrays of classes', () => {
      const result = cn(['px-4', 'py-2'], 'bg-red-500');
      expect(result).toContain('px-4');
      expect(result).toContain('py-2');
      expect(result).toContain('bg-red-500');
    });

    it('should handle objects with conditional classes', () => {
      const result = cn({
        'active': true,
        'disabled': false,
        'px-4': true
      });
      expect(result).toContain('active');
      expect(result).toContain('px-4');
      expect(result).not.toContain('disabled');
    });

    it('should handle undefined and null values', () => {
      const result = cn('base', undefined, null, 'end');
      expect(result).toContain('base');
      expect(result).toContain('end');
    });

    it('should handle empty input', () => {
      const result = cn();
      expect(result).toBe('');
    });

    it('should handle mixed input types', () => {
      const result = cn(
        'base',
        ['array-class'],
        { 'object-class': true },
        undefined,
        'final'
      );
      expect(result).toContain('base');
      expect(result).toContain('array-class');
      expect(result).toContain('object-class');
      expect(result).toContain('final');
    });
  });

  describe('slugify function', () => {
    it('should convert basic text to slug', () => {
      expect(slugify('Hello World')).toBe('hello-world');
    });

    it('should handle text with special characters', () => {
      expect(slugify('Hello, World!')).toBe('hello-world');
    });

    it('should handle text with multiple spaces', () => {
      expect(slugify('Hello    World   Test')).toBe('hello-world-test');
    });

    it('should handle text with numbers', () => {
      expect(slugify('Test 123 Page')).toBe('test-123-page');
    });

    it('should handle text with underscores', () => {
      expect(slugify('test_page_name')).toBe('test_page_name');
    });

    it('should handle text with hyphens', () => {
      expect(slugify('already-hyphenated')).toBe('already-hyphenated');
    });

    it('should handle uppercase text', () => {
      expect(slugify('UPPERCASE TEXT')).toBe('uppercase-text');
    });

    it('should handle mixed case text', () => {
      expect(slugify('MiXeD CaSe TeXt')).toBe('mixed-case-text');
    });

    it('should handle text with leading/trailing spaces', () => {
      expect(slugify('  spaced text  ')).toBe('spaced-text');
    });

    it('should handle text with multiple consecutive hyphens', () => {
      expect(slugify('text--with--multiple--hyphens')).toBe('text-with-multiple-hyphens');
    });

    it('should handle empty string', () => {
      expect(slugify('')).toBe('');
    });

    it('should handle string with only spaces', () => {
      expect(slugify('   ')).toBe('');
    });

    it('should handle string with only special characters', () => {
      expect(slugify('!@#$%^&*()')).toBe('');
    });

    it('should handle complex real-world examples', () => {
      expect(slugify('Brand Deep-Dive Analysis 2024!')).toBe('brand-deep-dive-analysis-2024');
      expect(slugify('E-commerce Dashboard (Q1 Report)')).toBe('e-commerce-dashboard-q1-report');
      expect(slugify('Marketing KPI\'s & Metrics')).toBe('marketing-kpis-metrics');
    });

    it('should handle non-English characters', () => {
      expect(slugify('Café & Restaurant')).toBe('caf-restaurant');
      expect(slugify('naïve approach')).toBe('nave-approach');
    });

    it('should be consistent with multiple calls', () => {
      const input = 'Test String 123!';
      const result1 = slugify(input);
      const result2 = slugify(input);
      expect(result1).toBe(result2);
      expect(result1).toBe('test-string-123');
    });
  });

  describe('Performance tests', () => {
    it('should handle large class lists efficiently', () => {
      const start = performance.now();
      const largeClassList = Array.from({ length: 1000 }, (_, i) => `class-${i}`);
      cn(...largeClassList);
      const end = performance.now();
      
      // Should complete within reasonable time (100ms)
      expect(end - start).toBeLessThan(100);
    });

    it('should handle large strings in slugify efficiently', () => {
      const start = performance.now();
      const largeString = 'A'.repeat(10000) + ' ' + 'B'.repeat(10000);
      slugify(largeString);
      const end = performance.now();
      
      // Should complete within reasonable time (50ms)
      expect(end - start).toBeLessThan(50);
    });
  });

  describe('Edge cases', () => {
    it('should handle numeric inputs in cn', () => {
      const result = cn('base', 0, 1, 'end');
      expect(result).toContain('base');
      expect(result).toContain('1');
      expect(result).toContain('end');
      expect(result).not.toContain('0');
    });

    it('should handle boolean inputs in cn', () => {
      const result = cn('base', true, false, 'end');
      expect(result).toContain('base');
      expect(result).toContain('end');
    });

    it('should handle number input in slugify', () => {
      expect(slugify(123 as unknown as string)).toBe('123');
    });
  });
});