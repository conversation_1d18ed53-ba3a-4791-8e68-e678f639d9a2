/**
 * Load Environment Variables for Tests
 * 
 * This file loads environment variables from .env.local for testing purposes.
 * It ensures that the Redshift connection is properly configured in the test environment.
 */

import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables from .env.local
export function loadEnvForTests(): void {
  console.log('Loading environment variables for tests...');
  
  // Path to .env.local file
  const envLocalPath = path.resolve(process.cwd(), '.env.local');
  
  // Check if .env.local exists
  if (fs.existsSync(envLocalPath)) {
    console.log('.env.local file found, loading environment variables...');
    
    // Load environment variables from .env.local
    const envConfig = dotenv.parse(fs.readFileSync(envLocalPath));
    
    // Set environment variables if they're not already set
    for (const key in envConfig) {
      if (!process.env[key]) {
        process.env[key] = envConfig[key];
      }
    }
    
    // Log Redshift environment variables status (without exposing sensitive values)
    console.log('Redshift environment variables status:');
    console.log('REDSHIFT_HOST:', process.env.REDSHIFT_HOST ? 'Set' : 'Not set');
    console.log('REDSHIFT_PORT:', process.env.REDSHIFT_PORT ? 'Set' : 'Not set');
    console.log('REDSHIFT_DATABASE:', process.env.REDSHIFT_DATABASE ? 'Set' : 'Not set');
    console.log('REDSHIFT_USER:', process.env.REDSHIFT_USER ? 'Set' : 'Not set');
    console.log('REDSHIFT_PASSWORD:', process.env.REDSHIFT_PASSWORD ? 'Set (presence check only)' : 'Not set');
  } else {
    console.warn('.env.local file not found, environment variables may not be properly set');
  }
}