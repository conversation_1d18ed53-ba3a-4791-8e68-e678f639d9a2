/**
 * Performance Testing Utilities
 * 
 * Provides utilities for measuring performance metrics, memory usage,
 * and creating performance benchmarks for the NOLK v4 application.
 */

import { performance } from 'perf_hooks';

export interface PerformanceMetrics {
  startTime: number;
  endTime: number;
  duration: number;
  memoryUsage?: NodeJS.MemoryUsage;
  customMetrics?: Record<string, number>;
}

export interface ComponentPerformanceMetrics extends PerformanceMetrics {
  renderTime: number;
  componentName: string;
  propsSize?: number;
  childrenCount?: number;
}

export interface APIPerformanceMetrics extends PerformanceMetrics {
  endpoint: string;
  method: string;
  statusCode: number;
  responseSize?: number;
  queryTime?: number;
}

export interface LoadTestMetrics {
  concurrentUsers: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  throughput: number; // requests per second
  errorRate: number;
}

/**
 * Performance measurement utility class
 */
export class PerformanceMeasurer {
  private startTime: number = 0;
  private metrics: Record<string, number> = {};

  start(): void {
    this.startTime = performance.now();
  }

  end(): number {
    const endTime = performance.now();
    return endTime - this.startTime;
  }

  measure<T>(name: string, fn: () => T): T;
  measure<T>(name: string, fn: () => Promise<T>): Promise<T>;
  measure<T>(name: string, fn: () => T | Promise<T>): T | Promise<T> {
    const start = performance.now();
    
    try {
      const result = fn();
      
      if (result instanceof Promise) {
        return result.finally(() => {
          const end = performance.now();
          this.metrics[name] = end - start;
        });
      } else {
        const end = performance.now();
        this.metrics[name] = end - start;
        return result;
      }
    } catch (error) {
      const end = performance.now();
      this.metrics[name] = end - start;
      throw error;
    }
  }

  getMetrics(): Record<string, number> {
    return { ...this.metrics };
  }

  reset(): void {
    this.metrics = {};
    this.startTime = 0;
  }
}

/**
 * Memory usage measurement utilities
 */
export class MemoryProfiler {
  private initialMemory: NodeJS.MemoryUsage;

  constructor() {
    this.initialMemory = process.memoryUsage();
  }

  getCurrentMemoryUsage(): NodeJS.MemoryUsage {
    return process.memoryUsage();
  }

  getMemoryDelta(): NodeJS.MemoryUsage {
    const current = process.memoryUsage();
    return {
      rss: current.rss - this.initialMemory.rss,
      heapTotal: current.heapTotal - this.initialMemory.heapTotal,
      heapUsed: current.heapUsed - this.initialMemory.heapUsed,
      external: current.external - this.initialMemory.external,
      arrayBuffers: current.arrayBuffers - this.initialMemory.arrayBuffers,
    };
  }

  formatMemoryUsage(memory: NodeJS.MemoryUsage): string {
    return `RSS: ${(memory.rss / 1024 / 1024).toFixed(2)}MB, ` +
           `Heap: ${(memory.heapUsed / 1024 / 1024).toFixed(2)}MB / ${(memory.heapTotal / 1024 / 1024).toFixed(2)}MB, ` +
           `External: ${(memory.external / 1024 / 1024).toFixed(2)}MB`;
  }

  reset(): void {
    this.initialMemory = process.memoryUsage();
  }
}

/**
 * Component performance measurement hook
 */
export function createComponentPerformanceMeasurer() {
  const measurer = new PerformanceMeasurer();
  const memoryProfiler = new MemoryProfiler();

  return {
    measureRender: <T>(componentName: string, renderFn: () => T): ComponentPerformanceMetrics => {
      const startTime = performance.now();
      const initialMemory = memoryProfiler.getCurrentMemoryUsage();
      
      const result = renderFn();
      
      const endTime = performance.now();
      const finalMemory = memoryProfiler.getCurrentMemoryUsage();
      
      return {
        componentName,
        startTime,
        endTime,
        duration: endTime - startTime,
        renderTime: endTime - startTime,
        memoryUsage: {
          rss: finalMemory.rss - initialMemory.rss,
          heapTotal: finalMemory.heapTotal - initialMemory.heapTotal,
          heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
          external: finalMemory.external - initialMemory.external,
          arrayBuffers: finalMemory.arrayBuffers - initialMemory.arrayBuffers,
        }
      };
    },
    
    measureAsync: async <T>(
      componentName: string, 
      asyncFn: () => Promise<T>
    ): Promise<ComponentPerformanceMetrics> => {
      const startTime = performance.now();
      const initialMemory = memoryProfiler.getCurrentMemoryUsage();
      
      await asyncFn();
      
      const endTime = performance.now();
      const finalMemory = memoryProfiler.getCurrentMemoryUsage();
      
      return {
        componentName,
        startTime,
        endTime,
        duration: endTime - startTime,
        renderTime: endTime - startTime,
        memoryUsage: {
          rss: finalMemory.rss - initialMemory.rss,
          heapTotal: finalMemory.heapTotal - initialMemory.heapTotal,
          heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
          external: finalMemory.external - initialMemory.external,
          arrayBuffers: finalMemory.arrayBuffers - initialMemory.arrayBuffers,
        }
      };
    }
  };
}

/**
 * API performance measurement utilities
 */
export async function measureAPIPerformance(
  endpoint: string,
  method: string,
  requestFn: () => Promise<Response>
): Promise<APIPerformanceMetrics> {
  const startTime = performance.now();
  const initialMemory = process.memoryUsage();
  
  try {
    const response = await requestFn();
    const endTime = performance.now();
    const finalMemory = process.memoryUsage();
    
    return {
      endpoint,
      method,
      startTime,
      endTime,
      duration: endTime - startTime,
      statusCode: response.status,
      memoryUsage: {
        rss: finalMemory.rss - initialMemory.rss,
        heapTotal: finalMemory.heapTotal - initialMemory.heapTotal,
        heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
        external: finalMemory.external - initialMemory.external,
        arrayBuffers: finalMemory.arrayBuffers - initialMemory.arrayBuffers,
      }
    };
  } catch (error) {
    const endTime = performance.now();
    const finalMemory = process.memoryUsage();
    
    return {
      endpoint,
      method,
      startTime,
      endTime,
      duration: endTime - startTime,
      statusCode: 0,
      memoryUsage: {
        rss: finalMemory.rss - initialMemory.rss,
        heapTotal: finalMemory.heapTotal - initialMemory.heapTotal,
        heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
        external: finalMemory.external - initialMemory.external,
        arrayBuffers: finalMemory.arrayBuffers - initialMemory.arrayBuffers,
      }
    };
  }
}

/**
 * Load testing utilities
 */
export class LoadTester {
  async runLoadTest(
    testFn: () => Promise<number>, // Returns response time in ms
    concurrentUsers: number,
    duration: number // in seconds
  ): Promise<LoadTestMetrics> {
    const startTime = Date.now();
    const endTime = startTime + (duration * 1000);
    const responseTimes: number[] = [];
    let successfulRequests = 0;
    let failedRequests = 0;

    const workers = Array.from({ length: concurrentUsers }, async () => {
      while (Date.now() < endTime) {
        try {
          const responseTime = await testFn();
          responseTimes.push(responseTime);
          successfulRequests++;
        } catch (error) {
          failedRequests++;
        }
        
        // Small delay to prevent overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    });

    await Promise.all(workers);

    const totalRequests = successfulRequests + failedRequests;
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0;
    const minResponseTime = responseTimes.length > 0 ? Math.min(...responseTimes) : 0;
    const maxResponseTime = responseTimes.length > 0 ? Math.max(...responseTimes) : 0;
    const actualDuration = (Date.now() - startTime) / 1000;
    const throughput = totalRequests / actualDuration;
    const errorRate = totalRequests > 0 ? (failedRequests / totalRequests) * 100 : 0;

    return {
      concurrentUsers,
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime,
      minResponseTime,
      maxResponseTime,
      throughput,
      errorRate
    };
  }
}

/**
 * Performance assertion utilities
 */
export class PerformanceAssertions {
  static expectResponseTime(actual: number, expected: number, tolerance: number = 0.1): void {
    const maxAllowed = expected * (1 + tolerance);
    if (actual > maxAllowed) {
      throw new Error(
        `Response time ${actual}ms exceeds expected ${expected}ms (with ${tolerance * 100}% tolerance: ${maxAllowed}ms)`
      );
    }
  }

  static expectMemoryUsage(actual: number, maxMB: number): void {
    const actualMB = actual / 1024 / 1024;
    if (actualMB > maxMB) {
      throw new Error(
        `Memory usage ${actualMB.toFixed(2)}MB exceeds maximum allowed ${maxMB}MB`
      );
    }
  }

  static expectThroughput(actual: number, minimum: number): void {
    if (actual < minimum) {
      throw new Error(
        `Throughput ${actual.toFixed(2)} requests/sec is below minimum ${minimum} requests/sec`
      );
    }
  }

  static expectErrorRate(actual: number, maximum: number): void {
    if (actual > maximum) {
      throw new Error(
        `Error rate ${actual.toFixed(2)}% exceeds maximum allowed ${maximum}%`
      );
    }
  }
}

/**
 * Performance benchmark storage and comparison
 */
export interface PerformanceBenchmark {
  name: string;
  timestamp: number;
  metrics: Record<string, number>;
  environment: {
    nodeVersion: string;
    platform: string;
    arch: string;
  };
}

export class BenchmarkManager {
  private benchmarks: PerformanceBenchmark[] = [];

  addBenchmark(name: string, metrics: Record<string, number>): void {
    this.benchmarks.push({
      name,
      timestamp: Date.now(),
      metrics,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
      }
    });
  }

  getBenchmarks(name?: string): PerformanceBenchmark[] {
    if (name) {
      return this.benchmarks.filter(b => b.name === name);
    }
    return [...this.benchmarks];
  }

  getLatestBenchmark(name: string): PerformanceBenchmark | undefined {
    const benchmarks = this.getBenchmarks(name);
    return benchmarks.sort((a, b) => b.timestamp - a.timestamp)[0];
  }

  compareBenchmarks(name: string, current: Record<string, number>): {
    improved: string[];
    degraded: string[];
    unchanged: string[];
  } {
    const latest = this.getLatestBenchmark(name);
    if (!latest) {
      return { improved: [], degraded: [], unchanged: [] };
    }

    const improved: string[] = [];
    const degraded: string[] = [];
    const unchanged: string[] = [];

    Object.keys(current).forEach(metric => {
      const currentValue = current[metric];
      const previousValue = latest.metrics[metric];
      
      if (previousValue === undefined) {
        unchanged.push(metric);
      } else {
        const change = ((currentValue - previousValue) / previousValue) * 100;
        if (Math.abs(change) < 5) { // Less than 5% change
          unchanged.push(metric);
        } else if (change < 0) { // Lower is better for performance metrics
          improved.push(metric);
        } else {
          degraded.push(metric);
        }
      }
    });

    return { improved, degraded, unchanged };
  }
}

/**
 * Global performance utilities
 */
export const globalPerformanceUtils = {
  measurer: new PerformanceMeasurer(),
  memoryProfiler: new MemoryProfiler(),
  loadTester: new LoadTester(),
  assertions: PerformanceAssertions,
  benchmarkManager: new BenchmarkManager(),
};