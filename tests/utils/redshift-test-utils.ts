/**
 * Redshift Test Utilities
 * 
 * This file contains utilities for testing Redshift database interactions.
 * It provides functions for connecting to the test Redshift database,
 * executing queries, and managing test data.
 */

import { PoolClient, QueryResultRow } from 'pg';

import { getRedshiftPool } from '@/lib/api/redshift';

// Type definitions
export interface TestQueryResult<T extends QueryResultRow = Record<string, unknown>> {
  rows: T[];
  rowCount: number;
}

/**
 * Execute a query against the test Redshift database
 * 
 * @param sql The SQL query to execute
 * @param params The query parameters
 * @returns The query result
 */
export async function executeTestQuery<T extends QueryResultRow = Record<string, unknown>>(
  sql: string,
  params: unknown[] = []
): Promise<TestQueryResult<T>> {
  const redshiftPool = getRedshiftPool();
  
  if (!redshiftPool) {
    throw new Error('Redshift pool is not available for testing');
  }
  
  let client: PoolClient | undefined;
  try {
    client = await redshiftPool.connect();
    const result = await client.query<T>(sql, params);
    return {
      rows: result.rows,
      rowCount: result.rowCount || 0
    };
  } catch (error) {
    console.error('Error executing test query:', error);
    throw error;
  } finally {
    if (client) {
      client.release();
    }
  }
}

/**
 * Check if the Redshift connection is available for testing
 *
 * @returns True if the connection is available, false otherwise
 */
export async function isRedshiftAvailable(): Promise<boolean> {
  console.log('Checking Redshift availability...');
  
  const redshiftPool = getRedshiftPool();
  
  if (!redshiftPool) {
    console.log('Redshift pool is null or undefined');
    return false;
  }
  
  console.log('Redshift pool exists, attempting to connect...');
  
  let client: PoolClient | undefined;
  try {
    client = await redshiftPool.connect();
    console.log('Successfully connected to Redshift pool');
    
    const result = await client.query('SELECT 1');
    console.log('Successfully executed test query:', result.rows);
    return true;
  } catch (error) {
    console.error('Redshift connection test failed:', error);
    return false;
  } finally {
    if (client) {
      client.release();
      console.log('Released Redshift client');
    }
  }
}

/**
 * Skip tests if Redshift is not available
 * 
 * This function can be used in beforeAll or beforeEach hooks to skip tests
 * if the Redshift connection is not available.
 */
export async function skipIfRedshiftUnavailable(): Promise<void> {
  const available = await isRedshiftAvailable();
  if (!available) {
    console.warn('Skipping tests: Redshift connection is not available');
    // Just return without running the test
    return;
  }
}

/**
 * Get the test Redshift database name
 * 
 * @returns The name of the test database
 */
export function getTestDatabaseName(): string {
  // Get the database name from the current environment variables
  // This ensures we get the most up-to-date value
  return process.env.REDSHIFT_DATABASE || 'unknown_database';
}