/**
 * Test Setup Utilities
 * 
 * This file contains utilities for setting up and tearing down test data.
 * It provides functions for initializing the test environment and cleaning up after tests.
 */

import { executeTestQuery, isRedshiftAvailable } from './redshift-test-utils';

import { QueryResultRow } from 'pg';
import { loadEnvForTests } from './load-env';

/**
 * Initialize the test environment
 * 
 * This function should be called in the beforeAll hook of test suites
 * that require database access.
 */
export async function initializeTestEnvironment(): Promise<void> {
  // Load environment variables from .env.local
  loadEnvForTests();
  
  // Check if Redshift is available
  const available = await isRedshiftAvailable();
  if (!available) {
    console.warn('Redshift is not available for testing - some tests will be skipped');
  } else {
    console.log('Test environment initialized successfully with Redshift connection');
  }
}

/**
 * Clean up the test environment
 * 
 * This function should be called in the afterAll hook of test suites
 * to clean up any resources used during testing.
 */
export async function cleanupTestEnvironment(): Promise<void> {
  // Add any cleanup logic here
  console.log('Test environment cleaned up successfully');
}

/**
 * Create a temporary table for testing
 * 
 * @param tableName The name of the temporary table
 * @param schema The schema definition for the table
 */
export async function createTemporaryTable(
  tableName: string,
  schema: string
): Promise<void> {
  const sql = `CREATE TEMPORARY TABLE ${tableName} (${schema})`;
  await executeTestQuery(sql);
}

/**
 * Insert test data into a table
 * 
 * @param tableName The name of the table
 * @param columns The column names
 * @param values The values to insert
 */
export async function insertTestData(
  tableName: string,
  columns: string[],
  values: unknown[][]
): Promise<void> {
  // Create placeholders for the values
  const placeholders = values.map((row, rowIndex) => 
    `(${row.map((_, colIndex) => `$${rowIndex * columns.length + colIndex + 1}`).join(', ')})`
  ).join(', ');
  
  // Flatten the values array
  const flatValues = values.flat();
  
  // Build and execute the insert query
  const sql = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES ${placeholders}`;
  await executeTestQuery(sql, flatValues);
}

/**
 * Query test data from a table
 * 
 * @param tableName The name of the table
 * @param columns The columns to select
 * @param whereClause The WHERE clause (optional)
 * @param params The query parameters (optional)
 * @returns The query result
 */
export async function queryTestData<T extends QueryResultRow = Record<string, unknown>>(
  tableName: string,
  columns: string[] = ['*'],
  whereClause: string = '',
  params: unknown[] = []
): Promise<T[]> {
  const whereStatement = whereClause ? `WHERE ${whereClause}` : '';
  const sql = `SELECT ${columns.join(', ')} FROM ${tableName} ${whereStatement}`;
  const result = await executeTestQuery<T>(sql, params);
  return result.rows;
}

/**
 * Delete test data from a table
 * 
 * @param tableName The name of the table
 * @param whereClause The WHERE clause (optional)
 * @param params The query parameters (optional)
 */
export async function deleteTestData(
  tableName: string,
  whereClause: string = '',
  params: unknown[] = []
): Promise<void> {
  const whereStatement = whereClause ? `WHERE ${whereClause}` : '';
  const sql = `DELETE FROM ${tableName} ${whereStatement}`;
  await executeTestQuery(sql, params);
}

/**
 * Drop a temporary table
 * 
 * @param tableName The name of the temporary table
 */
export async function dropTemporaryTable(tableName: string): Promise<void> {
  const sql = `DROP TABLE IF EXISTS ${tableName}`;
  await executeTestQuery(sql);
}