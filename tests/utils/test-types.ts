/**
 * Shared Types for Tests
 * 
 * This file contains type definitions used across multiple test files.
 */

// Common types from the flexible-kpis endpoint
export type ValidGroupByTime = 'day' | 'week' | 'month' | 'quarter' | 'year';
export type ValidGroupByDimension = 'brand' | 'brandGroup';
export type ValidCurrency = 'CAD' | 'USD';

// Raw Data Structure from DB
export type RawDbData = {
  date: string; // Truncated date
  kpi_name: string;
  value: number;
  dimension?: string; // Populated if groupByDimension is used (e.g., brand name)
};

// Intermediate Processing Structure
export type TimeSeriesDataPoint = { date: string; value: number | null };
export type KpiSummary = {
  value: number | null;
};

// Structure to hold processed base KPI data before calculating derived KPIs
// Nested structure: [dimension?][kpiName][date] = value
export type ProcessedBaseKpiMap = {
  [dimensionValue: string]: { // Key is dimension value (e.g., brand name) or a default key if no dimension
    [kpiName: string]: {
      timeSeriesMap: { [date: string]: number | null }; // Allow nulls from calculation
      total: number | null; // Allow nulls from calculation
    };
  };
};

// Validated parameters type
export type ValidatedParams = {
  startDate?: string;
  endDate?: string;
  currency: ValidCurrency;
  brands: string[];
  brandGroups: string[];
  requestedKpis: string[];
  groupByTime: ValidGroupByTime;
  groupByDimension?: ValidGroupByDimension;
  salesChannels: string[];
  countryNames: string[];
};