{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@/components": ["./components"], "@/components/*": ["./components/*"], "@/components/admin/*": ["./components/admin/*"], "@/components/ui/*": ["./components/ui/*"], "@/lib": ["./lib"], "@/lib/*": ["./lib/*"], "@/app/*": ["./app/*"], "components": ["./components"], "components/*": ["./components/*"], "components/admin/*": ["./components/admin/*"], "components/admin/AdminLayout": ["./components/admin/AdminLayout"], "components/admin/groups/DeleteConfirmationDialog": ["./components/admin/groups/DeleteConfirmationDialog"], "components/admin/groups/GroupForm": ["./components/admin/groups/GroupForm"], "components/ui/*": ["./components/ui/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}