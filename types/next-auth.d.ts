import { DefaultSession, DefaultUser } from "next-auth"
import { DefaultJWT } from "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      roles: string[]
      permissions: string[]
      groups: number[]
      brands: number[]
      isSuperAdmin?: boolean
      isImpersonating?: boolean
      originalUser?: {
        id: string
        name?: string | null
        email?: string | null
        roles: string[]
        permissions: string[]
      }
    } & DefaultSession["user"]
    isImpersonating?: boolean
    originalUserId?: string
  }

  interface User extends DefaultUser {
    id: string
    roles?: string[]
    permissions?: string[]
    groups?: number[]
    brands?: number[]
    isSuperAdmin?: boolean
  }
}

declare module "next-auth/jwt" {
  interface JWT extends DefaultJWT {
    roles?: string[]
    permissions?: string[]
    groups?: number[]
    brands?: number[]
    isSuperAdmin?: boolean
    isImpersonating?: boolean
    originalUser?: {
      id: string
      name?: string | null
      email?: string | null
      roles: string[]
      permissions: string[]
    }
    impersonateTargetUserId?: string
    revertImpersonation?: boolean
  }
}
